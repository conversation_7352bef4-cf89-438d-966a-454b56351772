spring:
  transaction:
      default-timeout: 30
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          type: com.zaxxer.hikari.HikariDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *************************************************************************************************************************************************************
          username: mcoin_user
          password: Zhu@123456
          # url: ***************************************************************************************************************************************************************
          # username: mcoin
          # password: Zhu@123456
          hikari:
            connection-timeout: 1000       # 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 默认:30秒
            minimum-idle: 20                 # 最小连接数
            maximum-pool-size: 64           # 最大连接数
            auto-commit: true               # 自动提交
            idle-timeout: 600000            # 连接超时的最大时长（毫秒），超时则被释放（retired），默认:10分钟
            pool-name: masterDateSourceHikariCP     # 连接池名字
            max-lifetime: 1800000           # 连接的生命时长（毫秒），超时而且没被使用则被释放（retired），默认:30分钟 1800000ms
            connection-test-query: SELECT 1
        slave:
          type: com.zaxxer.hikari.HikariDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *************************************************************************************************************************************************************
          username: mcoin_user
          password: Zhu@123456
          hikari:
            connection-timeout: 1000       # 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 默认:30秒
            minimum-idle: 20                # 最小连接数
            maximum-pool-size: 64           # 最大连接数
            auto-commit: true               # 自动提交
            idle-timeout: 600000            # 连接超时的最大时长（毫秒），超时则被释放（retired），默认:10分钟
            pool-name: slaveDateSourceHikariCP     # 连接池名字
            max-lifetime: 1800000           # 连接的生命时长（毫秒），超时而且没被使用则被释放（retired），默认:30分钟 1800000ms
            connection-test-query: SELECT 1
