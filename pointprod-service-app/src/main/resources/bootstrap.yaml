spring:
  profiles:
    active: @profile.name@
  application:
    name: pointprod-service
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  # 添加本地配置文件导入，配置先从本地文件获取，本地配置文件不存在时，才从nacos获取，如果本地文件和nacos文件都有则nacos优先
  config:
    import: 
      - optional:classpath:<EMAIL>@.yaml
      - optional:classpath:<EMAIL>@.yaml
      - optional:classpath:<EMAIL>@.yaml
      - optional:classpath:<EMAIL>@.yaml
      - optional:classpath:<EMAIL>@.yaml
      - optional:classpath:<EMAIL>@.yaml
      - optional:classpath:<EMAIL>@.yaml
      - optional:classpath:<EMAIL>@.yaml
  cloud:
    nacos:
      config:
        server-addr: @config.server-addr@
        file-extension: yaml
        username: @config.username@
        password: @config.passwd@
        namespace: @config.namespace@
        group: @config.group@
        shared-configs:
          - {dataId: <EMAIL>@.yaml, refresh: true, group: @config.group@}
          - {dataId: <EMAIL>@.yaml, group: @config.group@}
          - {dataId: <EMAIL>@.yaml, group: @config.group@}
          - {dataId: <EMAIL>@.yaml, group: @config.group@}
          - {dataId: <EMAIL>@.yaml, refresh: true, group: @config.group@}
          - {dataId: <EMAIL>@.yaml, group: @config.group@}
          - {dataId: <EMAIL>@.yaml, group: @config.group@}
          - {dataId: <EMAIL>@.yaml, group: @config.group@}

      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: @config.username@
        password: @config.passwd@
        namespace: @config.namespace@
        group: @config.group@
    sentinel:
      eager: true
      metric:
        file-total-count: 2
      transport:
        dashboard: @spring.cloud.sentinel.transport.dashboard@
      datasource:
        flow:
          nacos:
            server-addr: ${spring.cloud.nacos.config.server-addr}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            username: ${spring.cloud.nacos.discovery.username}
            password: ${spring.cloud.nacos.discovery.password}
            data-id: ${spring.application.name}-flow-rules
            group-id: B_SENTINEL
            data-type: json
            rule-type: FLOW
        degrade:
          nacos:
            server-addr: ${spring.cloud.nacos.config.server-addr}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            username: ${spring.cloud.nacos.discovery.username}
            password: ${spring.cloud.nacos.discovery.password}
            data-id: ${spring.application.name}-degrade-rules
            group-id: B_SENTINEL
            data-type: json
            rule-type: DEGRADE
        param-flow:
          nacos:
            server-addr: ${spring.cloud.nacos.config.server-addr}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            username: ${spring.cloud.nacos.discovery.username}
            password: ${spring.cloud.nacos.discovery.password}
            data-id: ${spring.application.name}-param-flow-rules
            group-id: B_SENTINEL
            data-type: json
            rule-type: PARAM_FLOW
        system:
          nacos:
            server-addr: ${spring.cloud.nacos.config.server-addr}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            username: ${spring.cloud.nacos.discovery.username}
            password: ${spring.cloud.nacos.discovery.password}
            data-id: ${spring.application.name}-system-rules
            group-id: B_SENTINEL
            data-type: json
            rule-type: SYSTEM
        authority:
          nacos:
            server-addr: ${spring.cloud.nacos.config.server-addr}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            username: ${spring.cloud.nacos.discovery.username}
            password: ${spring.cloud.nacos.discovery.password}
            data-id: ${spring.application.name}-authority-rules
            group-id: B_SENTINEL
            data-type: json
            rule-type: AUTHORITY
  jackson:
    deserialization:
      fail-on-unknown-properties: false
      fail-on-ignored-properties: false
      fail-on-null-for-primitives: false
      accept-single-value-as-array: true
    serialization:
      fail-on-empty-beans: false
    mapper:
      sort-properties-alphabetically: true
    parser:
      allow-single-quotes: true

server:
  port: 6010
  servlet:
    context-path: /pointprod
  shutdown: graceful
  tomcat:
    threads:
      max: 200
      min-spare: 50

sls:
  endpoint: ${sls.endpoint}
  accessKeyId: ${sls.accessKeyId}
  accessKeySecret: ${sls.accessKeySecret}
  project: ${sls.project}
  logStore: ${sls.logStore}
