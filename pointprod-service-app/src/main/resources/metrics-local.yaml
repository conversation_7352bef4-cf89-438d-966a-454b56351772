management:
    security:
        enabled: false
    endpoint:
        metrics:
            enabled: true
        prometheus:
            enabled: true
        health:
            probes:
                enabled: true
                show-details: never
            show-details: never
    endpoints:
        web:
            base-path: /
            exposure:
                include: info,health,prometheus,service-registry
    health:
        livenessState:
            enabled: true
        readinessState:
            enabled: true
        db:
            enabled: false
        redis:
            enabled: false
        elasticsearch:
            enabled: false
        rabbit:
            enabled: false
    metrics:
        tags:
            application: ${spring.application.name}
        export:
            prometheus:
                enabled: true
                pushgateway:
                    enabled: false
                    base-url: 172.31.158.159:9091
                    push-rate: 15s
                    job: ${spring.application.name}