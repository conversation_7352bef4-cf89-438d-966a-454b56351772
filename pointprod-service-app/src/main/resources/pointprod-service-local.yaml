# mCoin積分系統
mcoin:
  url: https://mcoin-mall-internal-dev.macaupass-devops.com
  appid: mc8g5LiBfIpJcIvUWn
  publickey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxuiAcGf/DZASDToHatxeZmpwIp712DwWeIEwaQCdsmY9hGILb8jJOl3qD9bU4gD8znFGK9SLhvRAQn044wY7+xs6is+R1vGVpg6KYzIz71i8AHcPvKkMuEz9BSQJp6HjVmK48fLaIlwZBqMHq5/G6wfFi6N+2utuoqNgTJQW42eo2wyQoE+L4NKsusm25EIN7ZMfuK1ZXWvBFZ0bIQJVyRvo0gIuneZEL1RXPEGwDKnFpsWWDKc/pPDMqKRD2QNkeI61c/4hFID5q9Tb9CfWk3UZbCWH6O3UnoPqQDJMPnFVaOhpoBHqYH1acowE8FcXM0ZSePswJkUChfrBJpFdXwIDAQAB
  privatekey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDf++HcqMvHnzDRcL3yMTZfhuYF7Xf4Qx3PhoKR6lrlSTtgxfCEfxvcFJQ+q8LP7NicyuE/9FSy07vWEC6mlPtJlq1yiYghDgvJynEiw5fwAk7b7MggcAfsGgTY+OLcxLGsKGt3Q9dOj4nVc3tMI69tci/6rmrzO0PPsonffgooAghFC7s+cGiON7/blfZRvBBQ2NBh8rdRVZMpQZb8nlYAZ29Zx1HkLktsOd6FrCNO7kHY4P1RtroD95vArl3YwxGpAa6CzZs1E4jk16bT1m0zoHkMt8eBuz9mqmcyqF5w85EmZsHrpVvhnIFY/ptrUP6EK9UIBPXh54amHo0lgpqrAgMBAAECggEBAJ8QtFAOl9mGVhcHJ/3tsR2aIZUoFcD7eRo9/lA9zJt0rHSHXc3aryBWhQkU1d7v5s1Cz0Cp9dShxY26JEctGmAiX78tqL1AymJeIIZ9vVM3cGWC/IT8ysODntmvtvztuvf2JIuoZCloioxJ3NAvr4/cPfKbF1zxQ7Emq/9J9VB+/LTWyFmiFGaS4bb11FW6QHY4MaCzBoiAXIKJ860Nr8i7lix3BErF2TlGm/odeKg+0xn6RVjNwQ8RPOU6ZQRP73eMNq2OhDNG8F8LNGXBvktIdo1KXGI4SBFeQADXMRGRMlB+G3HPg7MfrcYnbA4lx8MNyL9hw0zJxlpCbzt3MMECgYEA9xJmc4l1c95DOf616vXFpYgIo/43sCZWnYthxfS/DDg7DKaY26gpra5lKH2OyQtxxp0qX8qtzSckmVC4/jW8H2eCn8G1R3hFOgX7Y3Wj06s4esoWi9hY7lF6WSwudkoa9RPWWsCfzu6WFt739W1RTg/rxCqnDjaBZvLIOcWfV3sCgYEA6BPmsgcOFgb27jTXF/UlTTNdDPoyNw6esl0e9cSYQFdGhLx1qn1OnmJnbt0J2tqNZKqJ/R5gx00hRQAu353OuOwkiqsyY63IlB7rpTcF4ZJQVNO5vqJZ0pB2C6HSulnmQyeVlbDipc7CP/s++LAMQ4IoZKma7DiIknX7OD9BypECgYAVYSM6Zi+iqh35G8BUJ5ZFv6K3xhy9gmPGWDRKs+YAQbFiY9wgTcnlfIzGVy8O2I2s2Ra8mUY21WdGWQTZAn9X3FYiStnL6G1dGv1o2tolS9CkV25iBYOUg7ppkvgmRj1U7bWDvt1VQ7H7IqokM6Rwc9I79FDmWvMRnHqU/TPPlwKBgH8z0UGI7maSYKwFmFOQUWa0HW9sfzOANumKctrAa7bwXz2H0nKlBf937jtsuecT3WXst39eNCtpEjAwvoBgjZr8C7dZyF+sNAFDxWMj+nw95vvnpKphcBwihCEyDD+J4NS0EKAgeMnqvru06ToDvGUQJTWvZLPO9MRaGF0nBteRAoGBAJ61axSDUhK136MCBv2q+wye2AnX80XwH5xp5O70+Tn1+v10TlsF3bRs4RvpXTZ5Zi6EF/TVilZpDbmqCieI0rKpmF0fCNNFMo7xYKke0Lx7wTsp5vnsqYuD2lGS4HWfkmBLGue0PLvoK9C7yFyERGJGih46tnz8+yVtOBkCPrXt
  mall:
    url: https://mcoin-mall-internal-dev.macaupass-devops.com/mcoin-mall 

# mPay系统
mPay:
  gateway:
    url: https://gateway-mp-dev.macaupass-devops.com:7443
  push:
    url: https://mpay-dev.macaupass-devops.com:8882/

# 风控系统, 内容安全服务
risk:
  url: https://risk-internal-dev.mpass.club

order:
  # 转赠留言内容安全检查开关
  needValidateMemo: true
  # 支付超时时间
  expireSeconds: 900
  # 收银台icon配置
  payViewMcoinChannelIcon: https://mcoin-prd.oss-cn-hongkong.aliyuncs.com/image/show/mCion_cashier_logo.png


mybatis-plus:
  mapper-locations: classpath*:com/agtech/**/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: ASSIGN_ID
      table-underline: true
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    call-setters-on-nulls: true

logging:
  level:
    org.springframework.web.servlet.DispatcherServlet: debug
    com.baomidou.mybatisplus: DEBUG
    #项目mapper目录
    com.agtech.**.mapper: DEBUG
    com.agtech.pointprod.**.mapper: DEBUG
    com.agtech.pointprod.order.service.infrastructure.dal.mapper: DEBUG
    com.agtech.pointprod.service.infrastructure.dal.mapper: DEBUG

secretKey:
  config:
    mpay:
      clientId: '00000082'
      privateKey: 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCJ1uUTAfRW6ddTJyglFCb6zydCwyx00Ku1wWnrKhDpNNijpQNECtCyMJamx6fRKILltjshMDYyX/rMhtlfn7uG41brbmCbuSavVCIHER1iSiMhygn22/ohYKxfhGQyk7rf6XBTFBQ+x0nB8VtOdr2Lu8RqaEleoA4mlll31Pf4GHWnCdjAmdlBJrqcAWpglRWu8TPy5uJEUg3oy06XUVK5IpBKhr1GlrBzihrG13k2tddZYfEIaKutCV9T+Gsf1BY1prcS5AXEbC4LedYnNDwDh7LuB4q4VgKs1L8tySqGKhlb8ZMfvWklEsirseM9Bx87+AFXZNiXo4zuRWP7xzulAgMBAAECggEAdwhHrCFs8r1fgGR5K4P6oW7Q1bucIYiuL/hF0pnyHqHPT4pJhrHcRXTmNEKIbnwQhfTXnTJ4Kwptl7//6a8UD0k1n3wjG3dJq/D4raee+2lQa3aBIlgW6koEsTuwF2kx4PlDUcsuLISG8l/OXLT3vLq6xLUoXvnTHomJ24zjUq/wPlpAgBHmqM2vxTAZHifLa/noUhqNklmArONqQ0F1IE9BfzxUCQNFxGwLcWmSAzJGDKo9loQPLFHa1yfp+9Gp7Cfmnd2UjdNko1MciI0YYNZxINN6e6tAMRpGbWwKms0h3unx9jnwd/RTsf8+vt9OxDsgcsgvoV1yj9gfSHrqZQKBgQC9ban188Uy4sdGilk0tS5FRDhJWoShLygJGfpYe2vn/HkIcENfWwE4aMI/LwaTJ0QickfKZ+qGcUG0mujauzk0yDw3xc0Xt04tQh+bBtoyretTUtbKjAuNfax7dmLcUOdUrbVyjhG2YwpjywLcjJPh7zu93stEMQnHtKG8yoDFiwKBgQC6R+th+3Ew+HI9qZfTGq1Gy2F0qp45/Gcjbw+ITRpFUPIPKZJ4ZgQ3LIMmABfYhLc4fN/DGbGHHOq45ioWo4IwTVwVE06jRgtFC6FGjvaapwXxyowfDfQt/mzIN10T5Lysre2Y/B1y43KlIEl7ciZKICYoNGOh1ALz3mnVt5UJjwKBgQCuvgMTSBSZpGl/wRAZdyl/7DagNEg1CHM+MiCmjvQzxMtB5Y3A4Qp3JuxJrO+7v3Uy8YkYRlQqQUADgwNbA2r6Ldye9nEDd60+Qsk1EVUnexiifMB3iEj/9Pavzb3Uzy0XCEdhrXzZavOiqxuwqF2jBjVuKaAI/9OtzguFRKkVkQKBgQCe7QcD9EqQ1hZZR1yRrbvRn2jI6VvO6ulvkKw2xFk6dCHgD76324mTLPXDMOcnQaszlU4unLgaJvCWyT91SxCidw2tqWg44mRxBsgUc5ovPXpT3FOJlxURPHTAqINPmqHhzQ3drJLPVv+To6UtnajPKviC6pdqFIBkYQf4XwTXPwKBgGx8LM/SEelVCp8Ehpian7u7dSkBGIjpzvHwMvGiWe57238C+sCpuIH18c+J2X0biI2Sdpm6omnexTD1P4p8ni4JkEJ0VWbTXkfQHMDP37twECbQpm3WAc9YQkdkDcJqMm5Vr8ASL43DdDjIx39xR/8aaPVTaGxT1sHiUrTIHpt5'
      publicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApUygS7QAjss+kN47szjHh1MMAMLOgVGXJo1kF7WeQtoJKyLS4l+UEYolKGCfCecTLcxmVubNqvpXPjt5uAgWzCtIFRQvYY4eRq9IaVZQCkSIDzT4+5hD9tNRsLf0rlY3tabdAG0ghqWOjDJDr0gYEo0tYtodwiqhAcMaplOAMFnMNmfoijxSPn+ntRUw2YyXBnFDVZt9hAB+HMA4YlaguPtOy1GGZmX3QpRQDUaf7QgNI0Roh5qeVVhrP+caYkqZn6st1b67ckseE4UnAJJ0ALK8STyIDuB0fKnuotmPNF5cOpwnHCxx8y2YiQ+kJuoPuffP9cbUjPCFg3D/P8XJSwIDAQAB'

feign:
  sentinel:
    enabled: true
  client:
    config:
      default:
        connect-timeout: 1000
        read-timeout: 3000
      mCoinClient:
        logger-level: FULL
      mPayClient:
        logger-level: FULL
      mCoinMallClient:
        logger-level: FULL
      mPayPushClient:
        logger-level: FULL


# 异步线程池配置
thread-pool:
  post-transaction-message:
    core-pool-size: 2
    max-pool-size: 8
    queue-capacity: 200
    await-termination-seconds: 60
    keep-alive-seconds: 300
  payment:
    core-pool-size: 30
    max-pool-size: 50
    queue-capacity: 1000
    await-termination-seconds: 60
    keep-alive-seconds: 300
  order-service:
    core-pool-size: 30
    max-pool-size: 50
    queue-capacity: 1000
    await-termination-seconds: 60
    keep-alive-seconds: 300
  backend:
    core-pool-size: 4
    max-pool-size: 8
    queue-capacity: 200
    await-termination-seconds: 60
    keep-alive-seconds: 300

task-retry:
  # 重试批处理大小配置
  batch-size: 100                         # 每批处理的失败任务数量，默认100
  # 查询数据开始时间配置
  query-start-days: 30                    # 查询数据开始时间（天数），默认30天
  
  strategy:
    # 默认重试策略配置
    default-config:
      max-retry-count: 3                     # 默认最大重试次数
      initial-delay-seconds: 60              # 默认初始重试间隔（秒）
      max-delay-seconds: 3600                # 默认最大重试间隔（秒）
      backoff-multiplier: 2.0                # 默认重试间隔倍数（指数退避）
      enable-exponential-backoff: true       # 默认启用指数退避
    
    # 针对不同任务类型的重试策略配置
    task-configs:
      # MQ订单状态变更消息-重试策略
      MQ_DELIVERY_ORDER_STATUS_CHANGED:
        max-retry-count: 3                   # 订单状态变更消息重试5次
        initial-delay-seconds: 60            # 初始延迟60秒
        max-delay-seconds: 1800              # 最大延迟30分钟
        backoff-multiplier: 2.0              # 指数退避倍数2.0
        enable-exponential-backoff: false    # 启用指数退避
      
      # MQ投递订单延迟关单消息-重试策略
      MQ_DELIVERY_ORDER_PAYMENT_TIMEOUT:
        max-retry-count: 3                   # 消息重试3次
        initial-delay-seconds: 60            # 初始延迟60秒
        max-delay-seconds: 1800              # 最大延迟30分钟
        backoff-multiplier: 3.0              # 指数退避倍数3.0
        enable-exponential-backoff: false     # 启用指数退避

      # MQ消费订单资金累加消息-重试策略
      MQ_CONSUME_ORDER_PAYMENT_SUCCESS_FUND_ACCUMULATION:
        max-retry-count: 3                   # 消息重试3次
        initial-delay-seconds: 60            # 初始延迟60秒
        max-delay-seconds: 1800              # 最大延迟30分钟
        backoff-multiplier: 3.0              # 指数退避倍数3.0
        enable-exponential-backoff: false     # 启用指数退避

      # MQ消费订单转赠关系消息-重试策略
      MQ_CONSUME_ORDER_PAYMENT_SUCCESS_TRANSFER_RELATION:
        max-retry-count: 3                   # 消息重试3次
        initial-delay-seconds: 60            # 初始延迟60秒
        max-delay-seconds: 1800              # 最大延迟30分钟
        backoff-multiplier: 3.0              # 指数退避倍数3.0
        enable-exponential-backoff: false     # 启用指数退避

      # MQ消费订单状态创建消息-重试策略
      MQ_CONSUME_ORDER_STATUS_CREATED:
        max-retry-count: 3                   # 消息重试3次
        initial-delay-seconds: 60            # 初始延迟60秒
        max-delay-seconds: 1800              # 最大延迟30分钟
        backoff-multiplier: 3.0              # 指数退避倍数3.0
        enable-exponential-backoff: false     # 启用指数退避

      # MQ消费订单支付超时消息-重试策略
      MQ_CONSUME_ORDER_PAYMENT_TIMEOUT:
        max-retry-count: 3                   # 消息重试3次
        initial-delay-seconds: 60            # 初始延迟60秒
        max-delay-seconds: 1800              # 最大延迟30分钟
        backoff-multiplier: 3.0              # 指数退避倍数3.0
        enable-exponential-backoff: false     # 启用指数退避

      # MQ投递支付结果未知查询消息-重试策略
      MQ_DELIVERY_PAYMENT_RESULT_UNKNOWN_QUERY:
        max-retry-count: 3                   # 消息重试3次
        initial-delay-seconds: 60            # 初始延迟60秒
        max-delay-seconds: 1800              # 最大延迟30分钟
        backoff-multiplier: 3.0              # 指数退避倍数3.0
        enable-exponential-backoff: false     # 启用指数退避

      # MQ消费支付结果未知查询消息-重试策略
      MQ_CONSUME_PAYMENT_RESULT_UNKNOWN_QUERY:
        retry-type: DELAY_QUEUE              # 重试类型：延迟队列
        max-retry-count: 10                  # 消息重试10次
        initial-delay-seconds: 1             # 初始延迟60秒
        max-delay-seconds: 900               # 最大延迟15分钟
        backoff-multiplier: 2.0              # 指数退避倍数3.0
        enable-exponential-backoff: true     # 启用指数退避

