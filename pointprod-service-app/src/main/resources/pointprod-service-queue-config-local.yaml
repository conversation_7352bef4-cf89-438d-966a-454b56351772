rabbitmq:
  queues:
    config:
      order-status-changed:
        exchange: "order.status.changed.exchange"
        routing-key: "order.status.changed.key"
        queue: "order.status.changed.queue"
      
      order-status-created:
        exchange: "order.status.created.exchange"
        routing-key: "order.status.created.key"
        queue: "order.status.created.queue"
      
      order-payment-timeout:
        exchange: "order.payment.timeout.exchange"
        routing-key: "order.payment.timeout.key"
        queue: "order.payment.timeout.queue"
      # 订单支付成功-付款人队列
      order-payment-success-payer:
        exchange: "order.payment.success.payer.exchange"
        routing-key: "order.payment.success.payer.key"
        delay-exchange: "order.payment.success.payer.delay.exchange"
        delay-routing-key: "order.payment.success.payer.delay.key"
        queue: "order.payment.success.payer.queue"

      # 订单支付成功-收款人队列
      order-payment-success-payee:
        exchange: "order.payment.success.payee.exchange"
        routing-key: "order.payment.success.payee.key"
        delay-exchange: "order.payment.success.payee.delay.exchange"
        delay-routing-key: "order.payment.success.payee.delay.key"
        queue: "order.payment.success.payee.queue"

      # 订单支付成功-转出获赠累计队列
      order-payment-success-fund-accumulation:
        exchange: "order.payment.success.fund.accumulation.exchange"
        routing-key: "order.payment.success.fund.accumulation.key"
        queue: "order.payment.success.fund.accumulation.queue"

      # 订单状态变更绑定到订单支付成功-转赠关系队列
      order-payment-success-transfer-relation:
        exchange: "order.payment.success.transfer.relation.exchange"
        routing-key: "order.payment.success.transfer.relation.key"
        queue: "order.payment.success.transfer.relation.queue"

      # 订单支付结果未知查询队列
      payment-result-unknown-query:
        exchange: "payment.result.unknown.query.exchange"
        routing-key: "payment.result.unknown.query.key"
        delay-exchange: "payment.result.unknown.query.delay.exchange"
        delay-routing-key: "payment.result.unknown.query.delay.key"
        queue: "payment.result.unknown.query.queue"
