spring:
  redis:
    database: 0
    host: r-3nsrf0bmir06ps5crbpd.redis.rds.aliyuncs.com
    lettuce:
      pool:
        # 连接池中的最小空闲连接数
        min-idle: 2
        # 连接池中的最大空闲连接数
        max-idle: 8
        # 连接池的最大活跃连接数（同时可分配的连接数）
        max-active: 8
        # 最大等待时间，当没有可用连接时，连接池等待获取连接的最大时间（毫秒）-1表示永不超时，一直等待直到有连接可用
        max-wait: 1000
        # 检查连接是否有效的间隔时间（毫秒），仅在测试模式下有效
        test-on-borrow: false
        # 在归还给连接池前进行连接有效性检查，如果无效则抛弃此连接
        test-on-return: false
        # 在空闲时检查连接的有效性，如果无效则抛弃此连接
        test-while-idle: true
        # 空闲连接的最小生存时间（毫秒），达到此值后空闲连接将被关闭
        min-evictable-idle-time-millis: 60000
        # 定期检查空闲连接的时间间隔（毫秒）
        time-between-eviction-runs-millis: 30000
      client-resources:
        # 连接超时时间（单位：毫秒）
        connect-timeout-millis: 1000
        # Socket读写超时时间（单位：毫秒）
        socket-timeout-millis: 1000
      shutdown-timeout: 100ms
    password: Admin@123
    port: 6379
    