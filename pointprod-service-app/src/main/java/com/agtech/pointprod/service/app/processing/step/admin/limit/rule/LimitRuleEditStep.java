package com.agtech.pointprod.service.app.processing.step.admin.limit.rule;

import com.agtech.common.result.GenericResult;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.limit.service.domain.gateway.AccumulateRuleGateway;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.service.app.util.LimitRuleUtils;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TransferRuleStatusEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.TransferRuleGateway;
import com.agtech.pointprod.service.domain.model.TransferRule;
import com.agtech.pointprod.service.facade.dto.req.AccumulateRuleAdminReq;
import com.agtech.pointprod.service.infrastructure.common.enums.UserLevelEnum;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Transfer rule admin processing step
 * This class handles both limit rules (via accumulateRuleGateway) and transfer rules (via TransferRuleGateway)
 * Business logic: First maintain CumulateRule, then maintain TransferRule
 */
@Component
@Slf4j
public class LimitRuleEditStep {

    @Resource
    private TransferRuleGateway transferRuleGateway;

    @Resource
    private AccumulateRuleGateway accumulateRuleGateway;

    /**
     * 更新規則
     */
    @Transactional(rollbackFor = Exception.class)
    public GenericResult<Boolean> process(AccumulateRuleAdminReq req) {
        // Step 1: Update CumulateRule
        AccumulateRule accumulateRule = accumulateRuleGateway.getAccumulateRuleByRuleId(req.getRuleId());
        if(accumulateRule == null){
            throw new PointProdBizException(PointProdBizErrorCodeEnum.RULE_NOT_EXIST,PointProdBizErrorCodeEnum.RULE_NOT_EXIST.getResultMsg());
        }

        if (TransferRuleStatusEnum.VALID.getValue().equals(req.getStatus())) {
            log.info("Updating rule to VALID status. Will disable other enabled rules with same level: {}", req.getTitle());
            accumulateRuleGateway.disableOtherAccumulateRules(UserLevelEnum.getLevelValueByKey(req.getTitle()));
            transferRuleGateway.disableOtherRules(req.getTitle() ,req.getUsername());
        } else {
            //如提交狀態是未啟用，則需檢查同等級的規則是否都已停用
            if(accumulateRule.getStatus().equals(TransferRuleStatusEnum.VALID.getValue())){
                checkLastEnabledRule(req.getTitle());
            }
        }
        accumulateRule.setTitle(UserLevelEnum.getLevelValueByKey(req.getTitle()));
        accumulateRule.setSceneCode(req.getSceneCode());
        accumulateRule.setStatus(req.getStatus());
        String amountRange = LimitRuleUtils.buildAmountRange(req);
        String countRange = LimitRuleUtils.buildCountRange(req);
        accumulateRule.setAmountRange(amountRange);
        accumulateRule.setCountRange(countRange);
        String expression = LimitRuleUtils.buildExpression(req.getTitle());
        accumulateRule.setExpression(expression);
        accumulateRule.setGmtModified(ZonedDateUtil.now());
        boolean success = accumulateRuleGateway.updateAccumulateRule(accumulateRule);
        if (!success) {
            log.error("AccumulateRule updated fail, ruleId: {}", accumulateRule.getRuleId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.DB_UPDATE_FAIL,PointProdBizErrorCodeEnum.DB_UPDATE_FAIL.getResultMsg());
        }
        log.info("CumulateRule updated successfully, ruleId: {}", req.getRuleId());
        // Step 2: Update TransferRule
        TransferRule rule = transferRuleGateway.getTransferRuleDetail(accumulateRule.getRuleId());
        rule.setOptionalInfo(LimitRuleUtils.buildOptionalInfo(req.getGearVal()));
        rule.setLevelKey(req.getTitle());
        rule.setStatus(req.getStatus());
        rule.setModifier(req.getUsername());
        rule.setGmtModified(ZonedDateUtil.now());
        success = transferRuleGateway.updateTransferRule(rule);
        if (!success) {
            log.error("TransferRule updated fail, ruleId: {}", accumulateRule.getRuleId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.DB_UPDATE_FAIL,PointProdBizErrorCodeEnum.DB_UPDATE_FAIL.getResultMsg());
        }
        log.info("TransferRule updated successfully, ruleId: {}", accumulateRule.getRuleId());
        return GenericResult.success(true);
    }

    
    /**
     * 檢查同等級都是未開啟狀態
     * @param userLevel User level
     */
    private void checkLastEnabledRule(String userLevel) {
        // Check if it's the last enabled CumulateRule
        boolean isLastEnabled = accumulateRuleGateway.isLastEnabledAccumulateRule(UserLevelEnum.getLevelValueByKey(userLevel));
        if (isLastEnabled) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.CHECK_RULE_LAST_ENABLE, PointProdBizErrorCodeEnum.CHECK_RULE_LAST_ENABLE.getResultMsg());
        }
    }
    

} 