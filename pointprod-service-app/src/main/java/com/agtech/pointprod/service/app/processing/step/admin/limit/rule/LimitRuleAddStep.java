package com.agtech.pointprod.service.app.processing.step.admin.limit.rule;

import com.agtech.common.result.GenericResult;
import com.agtech.common.util.CollectionUtil;
import com.agtech.pointprod.limit.service.domain.gateway.AccumulateConfigGateway;
import com.agtech.pointprod.limit.service.domain.gateway.AccumulateRuleGateway;
import com.agtech.pointprod.limit.service.domain.gateway.condition.AccumulateConfigQueryCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateConfig;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.limit.service.facade.dto.enums.SceneCodeEnum;
import com.agtech.pointprod.service.app.util.LimitRuleUtils;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TransferRuleStatusEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.TransferRuleGateway;
import com.agtech.pointprod.service.domain.model.TransferRule;
import com.agtech.pointprod.service.facade.dto.req.AccumulateRuleAdminReq;
import com.agtech.pointprod.service.infrastructure.common.enums.UserLevelEnum;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * Transfer rule admin processing step
 * This class handles both limit rules (via accumulateRuleGateway) and transfer rules (via TransferRuleGateway)
 * Business logic: First maintain CumulateRule, then maintain TransferRule
 */
@Component
@Slf4j
public class LimitRuleAddStep {

    @Resource
    private TransferRuleGateway transferRuleGateway;

    @Resource
    private AccumulateRuleGateway accumulateRuleGateway;

    @Resource
    private AccumulateConfigGateway accumulateConfigGateway;

    @Resource
    private BizSequenceRepository bizSequenceRepository;



    /**
     * 新增限額規則
     */
    @Transactional(rollbackFor = Exception.class)
    public GenericResult<Boolean> process(AccumulateRuleAdminReq req) {
        //檢查租戶配置
        AccumulateConfigQueryCondition condition = new AccumulateConfigQueryCondition(SceneCodeEnum.MCOIN_TRANSFER_LIMIT);
        List<AccumulateConfig> configs = accumulateConfigGateway.queryAccumulateConfigs(condition);
        if (CollectionUtil.isEmpty(configs)) {
            log.error("租戶配置為空");
            throw new PointProdBizException(PointProdBizErrorCodeEnum.CONFIG_NOT_EXIST,PointProdBizErrorCodeEnum.CONFIG_NOT_EXIST.getResultMsg());
        }
        //檢查該等級是否存在啟用的規則
        boolean isExistValid = accumulateRuleGateway.isAccumulateRuleByLevelAndValidExists(UserLevelEnum.getLevelValueByKey(req.getTitle()));
        // 狀態開啟需把其他規則改為未開啟
        if (TransferRuleStatusEnum.VALID.getValue().equals(req.getStatus()) && isExistValid) {
            String userLevel = req.getTitle();
            log.info("Adding rule with VALID status. Will disable other enabled rules with same level: {}", req.getTitle());
            accumulateRuleGateway.disableOtherAccumulateRules(UserLevelEnum.getLevelValueByKey(userLevel));
            transferRuleGateway.disableOtherRules(userLevel,req.getUsername());
        }
        String accumulateRuleId = bizSequenceRepository.getBizId(null, SequenceCodeEnum.CUMULATE_RULE);
        // Step 1: Create and save CumulateRule
        AccumulateRule accumulateRule = new AccumulateRule();
        accumulateRule.setRuleId(accumulateRuleId);
        accumulateRule.setTitle(UserLevelEnum.getLevelValueByKey(req.getTitle()));
        accumulateRule.setSceneCode(SceneCodeEnum.MCOIN_TRANSFER_LIMIT.getCode());
        accumulateRule.setStatus(req.getStatus());
        accumulateRule.setTntInstId(configs.get(0).getTntInstId());
        String amountRange = LimitRuleUtils.buildAmountRange(req);
        String countRange = LimitRuleUtils.buildCountRange(req);
        accumulateRule.setAmountRange(amountRange);
        accumulateRule.setCountRange(countRange);
        String expression = LimitRuleUtils.buildExpression(req.getTitle());
        accumulateRule.setExpression(expression);
        boolean success = accumulateRuleGateway.createAccumulateRule(accumulateRule);
        if (!success) {
            log.error("AccumulateRule add fail, ruleId: {}", accumulateRule.getRuleId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.DB_UPDATE_FAIL,PointProdBizErrorCodeEnum.DB_UPDATE_FAIL.getResultMsg());
        }
        String transRuleId = bizSequenceRepository.getBizId(null, SequenceCodeEnum.TRANSFER_RULE);
        log.info("TransferRule added successfully, ruleId: {}", transRuleId);
        // Step 2: Create and save TransferRule
        TransferRule rule = new TransferRule();
        rule.setLevelKey(req.getTitle());
        rule.setStatus(req.getStatus());
        rule.setCreator(req.getUsername());
        rule.setModifier(req.getUsername());
        if (req.getGearVal() != null && !req.getGearVal().isEmpty()) {
            rule.setOptionalInfo(LimitRuleUtils.buildOptionalInfo(req.getGearVal()));
        }
        rule.setTransferRuleId(transRuleId);
        rule.setLimitRuleIds(accumulateRuleId);
        success = transferRuleGateway.addTransferRule(rule);
        if (!success) {
            log.error("TransferRule add fail, ruleId: {}", accumulateRule.getRuleId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.DB_UPDATE_FAIL,PointProdBizErrorCodeEnum.DB_UPDATE_FAIL.getResultMsg());
        }
        log.info("AccumulateRule added successfully, ruleId: {}", accumulateRuleId);
        return GenericResult.success(true);
    }

} 