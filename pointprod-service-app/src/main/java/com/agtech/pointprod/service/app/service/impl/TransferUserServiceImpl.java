package com.agtech.pointprod.service.app.service.impl;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.biz.TransferUserBizService;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.service.TransferUserService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.PayeeInfoValidateReq;
import com.agtech.pointprod.service.facade.dto.req.PayerValidateReq;
import com.agtech.pointprod.service.facade.dto.req.QueryHistoryPayeeListReq;
import com.agtech.pointprod.service.facade.dto.req.QueryTransferUserInfoReq;
import com.agtech.pointprod.service.facade.dto.rsp.PayeeInfoValidateRsp;
import com.agtech.pointprod.service.facade.dto.rsp.PayerValidateRsp;
import com.agtech.pointprod.service.facade.dto.rsp.QueryHistoryPayeeListRsp;
import com.agtech.pointprod.service.facade.dto.rsp.QueryTransferUserInfoRsp;
import com.alibaba.fastjson2.JSONObject;
import com.zat.gateway.component.result.model.GwResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.*;

@Slf4j
@Service
public class TransferUserServiceImpl implements TransferUserService {
    @Resource
    private TransferUserBizService transferUserBizService;

    @Override
    public GwResult<QueryTransferUserInfoRsp> queryTransferUserInfo(QueryTransferUserInfoReq req) {
        log.info("queryTransferUserInfo req={}", JSONObject.toJSONString(req));

        final ResultContainer<GenericResult<QueryTransferUserInfoRsp>> container = new ResultContainer<>(new GenericResult<>());
        PointProdServiceTemplate.execute(SERVICE_NAME_QUERY_TRANSFER_USER_INFO, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能為空", "userId param missing");
            }

            @Override
            public void process() {
                GenericResult<QueryTransferUserInfoRsp> result = transferUserBizService.queryTransferUserInfo(req);
                container.setResult(result);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }

    @Override
    public GwResult<PayeeInfoValidateRsp> payeeInfoValidate(PayeeInfoValidateReq req) {
        final ResultContainer<GenericResult<PayeeInfoValidateRsp>> container = new ResultContainer<>(new GenericResult<>());
        PointProdServiceTemplate.execute(SERVICE_NAME_PAYEE_INFO_VALIDATE, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能為空", "userId param missing");
                AssertUtil.assertNotBlank(req.getAreaCode(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "areaCode不能為空", "areaCode param missing");
                AssertUtil.assertNotBlank(req.getPhone(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "phone不能為空", "phone param missing");
            }

            @Override
            public void process() {
                GenericResult<PayeeInfoValidateRsp> result = transferUserBizService.payeeInfoValidate(req);
                container.setResult(result);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }

    @Override
    public GwResult<QueryHistoryPayeeListRsp> queryHistoryPayeeList(QueryHistoryPayeeListReq req) {
        log.info("queryHistoryPayeeList req={}", JSONObject.toJSONString(req));

        final ResultContainer<GenericResult<QueryHistoryPayeeListRsp>> container = new ResultContainer<>(new GenericResult<>());
        PointProdServiceTemplate.execute(SERVICE_NAME_QUERY_HISTORY_PAYEE_LIST, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                AssertUtil.assertTrue(ObjectUtils.isNotEmpty(req.getRecentDay()), PointProdBizErrorCodeEnum.PARAMS_MISSING,
                        "recentDay不能為空", "recentDay param is empty");
                AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能為空", "userId param missing");
            }

            @Override
            public void process() {
                GenericResult<QueryHistoryPayeeListRsp> result = transferUserBizService.queryHistoryPayeeList(req);
                container.setResult(result);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }

    @Override
    public GwResult<PayerValidateRsp> payerValidate(PayerValidateReq req) {
        log.info("payerValidate req={}", JSONObject.toJSONString(req));
        
        final ResultContainer<GenericResult<PayerValidateRsp>> container = new ResultContainer<>(new GenericResult<>());
        PointProdServiceTemplate.execute(SERVICE_NAME_PAYER_INFO_VALIDATE, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能為空", "userId param missing");
            }

            @Override
            public void process() {
                GenericResult<PayerValidateRsp> result = transferUserBizService.payerValidate(req);
                container.setResult(result);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
}
