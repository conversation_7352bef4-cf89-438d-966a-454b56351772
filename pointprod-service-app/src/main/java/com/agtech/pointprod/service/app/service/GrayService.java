package com.agtech.pointprod.service.app.service;

import com.agtech.pointprod.service.facade.dto.req.GrayRequest;
import com.agtech.pointprod.service.facade.dto.rsp.GrayResponse;
import com.zat.gateway.component.result.model.GwResult;

/**
 * 灰度服务接口
 * 
 * <AUTHOR>
 * @version v1.0, 2025/6/11 21:01
 */
public interface GrayService {

    /**
     * 检查灰度状态
     * 
     * @param request 灰度请求
     * @return 灰度响应
     */
    GwResult<GrayResponse> checkGray(GrayRequest request);
} 