package com.agtech.pointprod.service.app.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.agtech.pointprod.service.app.service.NotificationService;
import com.agtech.pointprod.service.facade.dto.req.NotificationDetailReq;
import com.agtech.pointprod.service.facade.dto.req.QueryNotificationReq;
import com.agtech.pointprod.service.facade.dto.req.ReadNotificationReq;
import com.agtech.pointprod.service.facade.dto.rsp.NotificationDetailRsp;
import com.agtech.pointprod.service.facade.dto.rsp.QueryNotificationRsp;
import com.agtech.pointprod.service.facade.dto.rsp.ReadNotificationRsp;
import com.zat.gateway.component.result.model.GwResult;

/**
 * 通知控制器
 */
@RestController
@RequestMapping("/fund/notice")
public class NotificationController {
    
    @Resource
    private NotificationService notificationService;
    
    /**
     * 查询通知列表
     * @param req 查询请求参数
     * @param userId 用户ID
     * @param language 语言
     * @return 通知列表响应
     */
    @PostMapping("/query")
    public GwResult<QueryNotificationRsp> queryNotificationList(@RequestBody QueryNotificationReq req, 
                                                              @RequestHeader(name = "MPayCustId", required = false) String userId,
                                                              @RequestHeader(name = "Lang", required = false) String language) {
        req.setUserId(userId);
        req.setLang(language);
        return notificationService.queryNotificationList(req);
    }
    
    /**
     * 阅读通知
     * @param req 阅读通知请求参数
     * @param userId 用户ID
     * @param language 语言
     * @return 阅读通知响应
     * @apiNote 此接口需要提供notificationIds或readAllByType参数之一：
     *         - notificationIds：标记指定的通知为已读
     *         - readAllByType：标记指定类型的所有通知为已读，取值为ResourceTypeEnum
     */
    @PostMapping("/read")
    public GwResult<ReadNotificationRsp> readNotification(@RequestBody ReadNotificationReq req,
                                                        @RequestHeader(name = "MPayCustId", required = false) String userId,
                                                        @RequestHeader(name = "Lang", required = false) String language) {
        req.setUserId(userId);
        req.setLang(language);
        return notificationService.readNotification(req);
    }
    
    /**
     * 查询通知详情
     * @param req 通知详情请求参数
     * @param userId 用户ID
     * @param language 语言
     * @return 通知详情响应
     */
    @PostMapping("/detail")
    public GwResult<NotificationDetailRsp> getNotificationDetail(@RequestBody NotificationDetailReq req,
                                                               @RequestHeader(name = "MPayCustId", required = false) String userId,
                                                               @RequestHeader(name = "Lang", required = false) String language) {
        req.setUserId(userId);
        req.setLang(language);
        return notificationService.getNotificationDetail(req);
    }
} 