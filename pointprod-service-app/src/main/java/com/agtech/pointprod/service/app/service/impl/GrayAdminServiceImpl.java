package com.agtech.pointprod.service.app.service.impl;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.processing.step.gray.GrayStep;
import com.agtech.pointprod.service.app.service.GrayAdminService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.facade.dto.req.GrayRequest;
import com.agtech.pointprod.service.facade.dto.rsp.GrayResponse;
import com.zat.gateway.component.result.model.GwResult;

import lombok.extern.slf4j.Slf4j;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_CHECK_GRAY_ADMIN;

/**
 * 灰度管理服务实现类
 * 
 */
@Slf4j
@Service
public class GrayAdminServiceImpl implements GrayAdminService {

    @Resource
    private GrayStep grayStep;

    @Override
    public GwResult<GrayResponse> checkGray(GrayRequest request) {
        GenericResult<GrayResponse> result = GenericResult.success(null);
        
        final ResultContainer<GenericResult<GrayResponse>> resultContainer = new ResultContainer<>(result);
        PointProdServiceTemplate.execute(SERVICE_NAME_CHECK_GRAY_ADMIN, result, new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                if (StringUtils.isBlank(request.getFeatureKey())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.GRAY_FEATURE_KEY_REQUIRED);
                }
                if (StringUtils.isBlank(request.getUserId())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.GRAY_CUST_ID_REQUIRED);
                }

            }

            @Override
            public void process() {
                resultContainer.setResult(grayStep.process(request));
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(request, result, timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(resultContainer.getResult());
    }
    
} 