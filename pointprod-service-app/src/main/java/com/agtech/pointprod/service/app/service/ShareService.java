package com.agtech.pointprod.service.app.service;

import com.agtech.pointprod.service.facade.dto.req.ShareLinkReq;
import com.agtech.pointprod.service.facade.dto.rsp.ShareLinkRsp;
import com.zat.gateway.component.result.model.GwResult;

/**
 * 分享服务接口
 */
public interface ShareService {
    
    /**
     * 创建分享链接
     * @param req 分享链接请求
     * @return 分享链接响应
     */
    GwResult<ShareLinkRsp> createShareLink(ShareLinkReq req);
} 