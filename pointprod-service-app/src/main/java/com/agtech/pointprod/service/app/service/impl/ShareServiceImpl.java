package com.agtech.pointprod.service.app.service.impl;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.processing.ShareLinkContext;
import com.agtech.pointprod.service.app.processing.step.ShareStep;
import com.agtech.pointprod.service.app.service.ShareService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.ShareLinkReq;
import com.agtech.pointprod.service.facade.dto.rsp.ShareLinkRsp;
import com.zat.gateway.component.result.model.GwResult;

import lombok.extern.slf4j.Slf4j;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_CREATE_SHARE_LINK;

/**
 * 分享服务实现
 */
@Slf4j
@Service
public class ShareServiceImpl implements ShareService {
    
    @Resource
    private ShareStep shareStep;
    
    @Override
    public GwResult<ShareLinkRsp> createShareLink(ShareLinkReq req) {
        log.info("createShareLink req={}", req);
        final ResultContainer<GenericResult<ShareLinkRsp>> container = new ResultContainer<>(new GenericResult<ShareLinkRsp>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_CREATE_SHARE_LINK, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                if (StringUtils.isBlank(req.getUserId())) {
                    AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能为空", "userId param missing");
                }
            }

            @Override
            public void process() {
                // 创建上下文
                ShareLinkContext context = new ShareLinkContext();
                context.setRequest(req);
                context.setUserId(req.getUserId());
                
                // 执行业务步骤
                shareStep.validateParameters(context);
                shareStep.validateOrder(context);
                shareStep.createShareRecord(context);
                shareStep.buildResponse(context);
                
                // 设置结果
                container.setResult(context.getResult());
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
} 