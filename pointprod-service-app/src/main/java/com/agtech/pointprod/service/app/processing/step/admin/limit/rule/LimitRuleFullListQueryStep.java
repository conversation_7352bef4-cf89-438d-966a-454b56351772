package com.agtech.pointprod.service.app.processing.step.admin.limit.rule;

import com.agtech.common.result.GenericResult;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.limit.service.domain.gateway.AccumulateRuleGateway;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.service.app.util.LimitRuleUtils;
import com.agtech.pointprod.service.domain.common.enums.TransferRuleStatusEnum;
import com.agtech.pointprod.service.domain.gateway.TransferRuleGateway;
import com.agtech.pointprod.service.domain.model.TransferRule;
import com.agtech.pointprod.service.facade.dto.req.AccumulateRuleAdminReq;
import com.agtech.pointprod.service.facade.dto.rsp.AccumulateRuleAdminRsp;
import com.agtech.pointprod.service.facade.dto.rsp.AccumulateRulePageResponse;
import com.agtech.pointprod.service.infrastructure.common.enums.UserLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Transfer rule admin processing step
 * This class handles both limit rules (via accumulateRuleGateway) and transfer rules (via TransferRuleGateway)
 * Business logic: First maintain CumulateRule, then maintain TransferRule
 */
@Component
@Slf4j
public class LimitRuleFullListQueryStep {

    @Resource
    private TransferRuleGateway transferRuleGateway;

    @Resource
    private AccumulateRuleGateway accumulateRuleGateway;

    /**
     * 規則列表
     */
    public GenericResult<AccumulateRulePageResponse> process(AccumulateRuleAdminReq req) {
        Integer pageNum = req.getPageNum();
        Integer pageSize = req.getPageSize();
        String title = req.getTitle();
        String status = req.getStatus();
        List<AccumulateRule> accumulateRules = accumulateRuleGateway.queryAccumulateRulesWithPagination(
            title, status, pageNum, pageSize);

        List<AccumulateRuleAdminRsp> rspList = accumulateRules.stream()
                .map(this::convertToRsp)
                .collect(Collectors.toList());
        // Create paginated response
        Integer totalCount = accumulateRuleGateway.countAccumulateRules(title, status);
        AccumulateRulePageResponse pageResponse = AccumulateRulePageResponse.of(
                rspList,
                totalCount,
                pageNum,
                pageSize
        );
        return GenericResult.success(pageResponse);

    }

    /**
     * Convert model to response
     * @param accumulateRule TransferRule
     * @return AccumulateRuleAdminRsp
     */
    private AccumulateRuleAdminRsp convertToRsp(AccumulateRule accumulateRule) {
        AccumulateRuleAdminRsp rsp = new AccumulateRuleAdminRsp();
        rsp.setRuleId(accumulateRule.getRuleId());
        rsp.setTitle(UserLevelEnum.getLevelKeyByValue(accumulateRule.getTitle()));
        rsp.setTitleDesc(accumulateRule.getTitle());
        rsp.setSceneCode(accumulateRule.getSceneCode());
        rsp.setStatus(accumulateRule.getStatus());
        rsp.setStatusDesc(TransferRuleStatusEnum.getDisplayNameByCode(accumulateRule.getStatus()));
        LimitRuleUtils.parseAmountRange(accumulateRule.getAmountRange(),rsp);
        LimitRuleUtils.parseCountRange(accumulateRule.getCountRange(),rsp);
        TransferRule transferRule = transferRuleGateway.getTransferRuleDetail(accumulateRule.getRuleId());
        rsp.setGearVal(LimitRuleUtils.parseOptionalInfo(transferRule.getOptionalInfo()));
        rsp.setGmtCreate(ZonedDateUtil.formatDate(accumulateRule.getGmtCreate()));
        rsp.setModifier(transferRule.getModifier());
        rsp.setGmtModified(ZonedDateUtil.formatDate(accumulateRule.getGmtModified()));
        return rsp;
    }
} 