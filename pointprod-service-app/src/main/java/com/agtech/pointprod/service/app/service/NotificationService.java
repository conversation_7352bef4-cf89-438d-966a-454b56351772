package com.agtech.pointprod.service.app.service;

import com.agtech.pointprod.service.facade.dto.req.NotificationDetailReq;
import com.agtech.pointprod.service.facade.dto.req.NotificationPushReq;
import com.agtech.pointprod.service.facade.dto.req.QueryNotificationReq;
import com.agtech.pointprod.service.facade.dto.req.ReadNotificationReq;
import com.agtech.pointprod.service.facade.dto.rsp.NotificationDetailRsp;
import com.agtech.pointprod.service.facade.dto.rsp.QueryNotificationRsp;
import com.agtech.pointprod.service.facade.dto.rsp.ReadNotificationRsp;
import com.zat.gateway.component.result.model.GwResult;

/**
 * 通知服务接口
 */
public interface NotificationService {
    
    /**
     * 查询通知列表
     * 
     * @param req 查询请求参数
     * @return 通知列表响应
     */
    GwResult<QueryNotificationRsp> queryNotificationList(QueryNotificationReq req);
    
    /**
     * 阅读通知
     * 
     * @param req 阅读通知请求参数
     * @return 阅读通知响应
     */
    GwResult<ReadNotificationRsp> readNotification(ReadNotificationReq req);
    
    /**
     * 查询通知详情
     *
     * @param req 通知详情请求参数
     * @return 通知详情响应
     */
    GwResult<NotificationDetailRsp> getNotificationDetail(NotificationDetailReq req);
    
    /**
     * 处理通知推送消息
     * 
     * @param req 通知推送请求参数
     * @return 处理是否成功
     */
    boolean processNotificationPush(NotificationPushReq req);
}