package com.agtech.pointprod.service.app.listeners;

import java.io.IOException;

import javax.annotation.Resource;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.model.OrderStatusChangeMessage;
import com.agtech.pointprod.service.app.listeners.base.TaskRetryBaseListener;
import com.agtech.pointprod.service.app.service.TransferRelationService;
import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;

import lombok.extern.slf4j.Slf4j;

/**
 * 转赠关系监听器
 * 监听订单支付成功事件，保存转赠关系
 * <AUTHOR>
 */
@Slf4j
@Component
public class TransferRelationListener extends TaskRetryBaseListener<OrderStatusChangeMessage> {

    @Resource
    private TransferRelationService transferRelationService;

    @RabbitListener(queues = "${rabbitmq.queues.config.order-payment-success-transfer-relation.queue}")
    public void onTransferRelationOrderPaymentSuccess(Message message) throws IOException {
        log.info("TransferRelationOrderPaymentSuccess listener triggered");
        super.onMessage(message, OrderStatusChangeMessage.class);
    }

    @Override
    protected boolean doCheck(OrderStatusChangeMessage message, MessageProperties properties) {
        if(message == null){
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR, "message body empty or parse error");
        }
        if(!FundOrderStatusEnum.SUCCESS.equals(message.getOrderStatus())){
            log.info("[order-payment-success-transfer-relation] order status is not success");
            return false;
        }
        return true;
    }

    @Override
    public void doProcess(OrderStatusChangeMessage message, MessageProperties properties) {
        transferRelationService.saveTransferRelation(message);
        log.info("[order-payment-success-transfer-relation] transfer relation save success");
    }

    @Override
    protected MessageQueueEnum getMessageQueueEnum() {
        return MessageQueueEnum.ORDER_PAYMENT_SUCCESS_TRANSFER_RELATION;
    }

    @Override
    protected TaskResouceTypeEnum getTaskResouceTypeEnum() {
        return TaskResouceTypeEnum.MQ_CONSUME_ORDER_PAYMENT_SUCCESS_TRANSFER_RELATION;
    }
}