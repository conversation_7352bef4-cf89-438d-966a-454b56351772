package com.agtech.pointprod.service.app.config.queue;

import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.infrastructure.config.RabbitMQConfig;
import lombok.AllArgsConstructor;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 订单支付成功转赠关系队列配置
 *
 * <AUTHOR>
 */
@Configuration
@AllArgsConstructor
public class OrderPaymentSuccessTransferRelationConfig {

    private final RabbitMQConfig rabbitMQConfig;

    @Bean
    public Queue orderPaymentSuccessTransferRelationQueue() {
        return new Queue(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_TRANSFER_RELATION.getConfigKey()).getQueue());
    }

    @Bean
    public Exchange orderPaymentSuccessTransferRelationExchange() {
        return new DirectExchange(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_TRANSFER_RELATION.getConfigKey()).getExchange());
    }

    @Bean
    public Binding orderPaymentSuccessTransferRelationBinding() {
        return BindingBuilder.bind(orderPaymentSuccessTransferRelationQueue()).to(orderPaymentSuccessTransferRelationExchange())
                .with(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_TRANSFER_RELATION.getConfigKey()).getRoutingKey()).noargs();
    }
}