package com.agtech.pointprod.service.app.processing;

import java.util.List;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.domain.model.TransferRecord;
import com.agtech.pointprod.service.facade.dto.rsp.TransferRecordsListRsp;

import lombok.Getter;
import lombok.Setter;

/**
 * 转赠记录列表查询上下文
 */
@Getter
@Setter
public class TransferRecordsListContext {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 资金流向类型 (PAY:转出, ACCEPT:收到)
     */
    private String subOrderType;
    
    /**
     * 当前页数
     */
    private Integer currentPage;
    
    /**
     * 每页数量
     */
    private Integer perPage;
    
    /**
     * 转赠记录列表
     */
    private List<TransferRecord> transferRecords;
    
    /**
     * 记录总数
     */
    private Long total;
    
    /**
     * 最后一页页数
     */
    private Integer lastPage;
    
    /**
     * 查询结果
     */
    private GenericResult<TransferRecordsListRsp> result;
} 