package com.agtech.pointprod.service.app.processing.step;

import com.agtech.pointprod.service.domain.common.enums.TransferredUserStatusEnum;
import com.agtech.pointprod.service.domain.gateway.TransferredUserGateway;
import com.agtech.pointprod.service.facade.dto.req.UpdateTransferredUserStatusReq;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/26 09:57
 */
@Component
public class TransferredUserStatusUpdateStep {

    @Resource
    private TransferredUserGateway transferredUserGateway;

    public void process(UpdateTransferredUserStatusReq updateTransferredUserStatusReq){
        if (TransferredUserStatusEnum.ABLE.getCode().equals(updateTransferredUserStatusReq.getStatus())){
            transferredUserGateway.ableUser(updateTransferredUserStatusReq.getUserId(), updateTransferredUserStatusReq.getOperator());
        }else {
            transferredUserGateway.disabledUser(updateTransferredUserStatusReq.getUserId(), updateTransferredUserStatusReq.getOperator());
        }
    }

}
