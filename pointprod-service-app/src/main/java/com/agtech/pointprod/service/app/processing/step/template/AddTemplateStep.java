package com.agtech.pointprod.service.app.processing.step.template;

import com.agtech.pointprod.service.app.converter.TemplateDTOConverter;
import com.agtech.pointprod.service.domain.gateway.TemplateGateway;
import com.agtech.pointprod.service.facade.dto.req.template.AddTemplateReq;
import com.agtech.pointprod.service.facade.dto.rsp.template.AddTemplateRsp;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/24 19:35
 */
@Component
public class AddTemplateStep{

    @Resource
    protected TemplateGateway templateGateway;

    @Resource
    private TemplateDTOConverter templateDTOConverter;

    public AddTemplateRsp process(AddTemplateReq addTemplateReq){
        AddTemplateRsp addTemplateRsp = new AddTemplateRsp();
        addTemplateRsp.setTemplateId(templateGateway.createTemplate(templateDTOConverter.addTemplate2Template(addTemplateReq)));
        return addTemplateRsp;
    }

}
