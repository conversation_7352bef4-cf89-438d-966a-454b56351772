package com.agtech.pointprod.service.app.processing.step.admin.limit.rule;

import com.agtech.common.result.GenericResult;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.limit.service.domain.gateway.AccumulateRuleGateway;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.service.app.util.LimitRuleUtils;
import com.agtech.pointprod.service.domain.common.enums.TransferRuleStatusEnum;
import com.agtech.pointprod.service.domain.gateway.TransferRuleGateway;
import com.agtech.pointprod.service.domain.model.TransferRule;
import com.agtech.pointprod.service.facade.dto.req.AccumulateRuleAdminReq;
import com.agtech.pointprod.service.facade.dto.rsp.AccumulateRuleAdminRsp;
import com.agtech.pointprod.service.infrastructure.common.enums.UserLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Transfer rule admin processing step
 * This class handles both limit rules (via accumulateRuleGateway) and transfer rules (via TransferRuleGateway)
 * Business logic: First maintain CumulateRule, then maintain TransferRule
 */
@Component
@Slf4j
public class LimitRuleDetailStep {

    @Resource
    private TransferRuleGateway transferRuleGateway;

    @Resource
    private AccumulateRuleGateway accumulateRuleGateway;

    /**
     * 規則詳情
     */
    public GenericResult<AccumulateRuleAdminRsp> process(AccumulateRuleAdminReq req) {
        AccumulateRule accumulateRule = accumulateRuleGateway.getAccumulateRuleByRuleId(req.getRuleId());
        if (accumulateRule == null) {
            return GenericResult.success(new AccumulateRuleAdminRsp());
        }
        AccumulateRuleAdminRsp rsp = convertToRsp(accumulateRule);
        return GenericResult.success(rsp);

    }
    
    /**
     * Convert model to response
     * @param accumulateRule TransferRule
     * @return AccumulateRuleAdminRsp
     */
    private AccumulateRuleAdminRsp convertToRsp(AccumulateRule accumulateRule) {
        AccumulateRuleAdminRsp rsp = new AccumulateRuleAdminRsp();
        rsp.setRuleId(accumulateRule.getRuleId());
        rsp.setTitle(UserLevelEnum.getLevelKeyByValue(accumulateRule.getTitle()));
        rsp.setTitleDesc(accumulateRule.getTitle());
        rsp.setSceneCode(accumulateRule.getSceneCode());
        rsp.setStatus(accumulateRule.getStatus());
        rsp.setStatusDesc(TransferRuleStatusEnum.getDisplayNameByCode(accumulateRule.getStatus()));
        LimitRuleUtils.parseAmountRange(accumulateRule.getAmountRange(),rsp);
        LimitRuleUtils.parseCountRange(accumulateRule.getCountRange(),rsp);
        TransferRule transferRule = transferRuleGateway.getTransferRuleDetail(accumulateRule.getRuleId());
        rsp.setGearVal(LimitRuleUtils.parseOptionalInfo(transferRule.getOptionalInfo()));
        rsp.setGmtCreate(ZonedDateUtil.formatDate(accumulateRule.getGmtCreate()));
        rsp.setModifier(transferRule.getModifier());
        rsp.setGmtModified(ZonedDateUtil.formatDate(accumulateRule.getGmtModified()));
        return rsp;
    }
} 