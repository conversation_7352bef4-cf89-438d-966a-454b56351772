package com.agtech.pointprod.service.app.processing.step.template;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.app.converter.TemplateDTOConverter;
import com.agtech.pointprod.service.domain.gateway.TemplateGateway;
import com.agtech.pointprod.service.domain.model.Template;
import com.agtech.pointprod.service.facade.dto.req.template.QueryTemplateInfoListReq;
import com.agtech.pointprod.service.facade.dto.rsp.template.QueryTemplateInfoListRsp;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/24 15:53
 */
@Component
public class TemplateFullInfoListQueryStep {

    @Resource
    private TemplateGateway templateGateway;

    @Resource
    private TemplateDTOConverter templateDTOConverter;

    public PageInfoDTO<QueryTemplateInfoListRsp> process(QueryTemplateInfoListReq request){
        PageInfoDTO<Template> pageInfoDTO = templateGateway.queryTemplateListByPage(request);
        List<QueryTemplateInfoListRsp> list = templateDTOConverter.templateList2InfoList(pageInfoDTO.getRecords());
        return PageInfoDTO.of(pageInfoDTO.getTotalCount(), pageInfoDTO.getPageSize(), pageInfoDTO.getCurrentPage(), list);
    }


}
