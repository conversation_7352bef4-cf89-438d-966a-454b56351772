package com.agtech.pointprod.service.app.processing.step;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.agtech.common.result.GenericResult;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;
import com.agtech.pointprod.order.service.domain.service.OrderDomainService;
import com.agtech.pointprod.order.service.domain.service.PayDomainService;
import com.agtech.pointprod.service.app.processing.ShareLinkContext;
import com.agtech.pointprod.service.domain.common.enums.ChannelTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.ContentTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.NumbersEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.ShareRecordsTypeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.ShareGateway;
import com.agtech.pointprod.service.domain.model.UserId;
import com.agtech.pointprod.service.domain.model.share.ContentReference;
import com.agtech.pointprod.service.domain.model.share.ShareCode;
import com.agtech.pointprod.service.domain.model.share.ShareRecord;
import com.agtech.pointprod.service.domain.model.share.ShareUrl;
import com.agtech.pointprod.service.domain.service.ShareDomainService;
import com.agtech.pointprod.service.facade.dto.req.ShareLinkReq;
import com.agtech.pointprod.service.facade.dto.rsp.ShareLinkRsp;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 分享步骤处理类
 */
@Component
@Slf4j
public class ShareStep {
    
    @Resource
    private OrderDomainService orderDomainService;
    
    @Resource
    private PayDomainService payDomainService;
    
    @Resource
    private ShareGateway shareGateway;
    
    @Resource
    private ShareDomainService shareDomainService;

    @Resource
    private BizSequenceRepository bizSequenceRepository;

    /**
     * 参数校验
     * @param context 分享链接上下文
     */
    public void validateParameters(ShareLinkContext context) {
        ShareLinkReq req = context.getRequest();
        
        // 基础参数校验
        if (StringUtils.isBlank(req.getContentId())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SHARE_CONTENT_ID_REQUIRED);
        }
        if (StringUtils.isBlank(req.getContentType())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SHARE_CONTENT_TYPE_REQUIRED);
        }
        if (StringUtils.isBlank(req.getChannelType())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SHARE_CHANNEL_TYPE_REQUIRED);
        }
        
        // 验证内容类型
        try {
            ContentTypeEnum.fromValue(req.getContentType());
        } catch (Exception e) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.INVALID_CONTENT_TYPE);
        }
        
        // 验证渠道类型
        try {
            ChannelTypeEnum.fromValue(req.getChannelType());
        } catch (Exception e) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.INVALID_CHANNEL_TYPE);
        }
    }
    
    
    /**
     * 验证订单权限 - 只有付款人可以分享链接
     * @param context 分享链接上下文
     */
    public void validateOrder(ShareLinkContext context) {
        ShareLinkReq req = context.getRequest();
        String userId = context.getUserId();
        String orderId = req.getContentId();
        
        // 查询订单信息 - 使用领域服务获取数据
        Map<String, BaseOrderInfo> fundOrderMap = orderDomainService.queryFundOrdersByIds(
            Collections.singletonList(orderId)
        );
        BaseOrderInfo fundOrder = fundOrderMap.get(orderId);
        if (fundOrder == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        
        // 验证当前用户是否是付款人（actorUserId）- 应用层职责
        if (!userId.equals(fundOrder.getActorUserId())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_NOT_EXIST);
        }

        // 将订单信息和付款人收款人信息存储到上下文中
        context.setFundOrder(fundOrder);
    }
    
    /**
     * 创建分享记录 - 创建SEND和ACCEPT两条记录
     * @param context 分享链接上下文
     */
    @Transactional
    public void createShareRecord(ShareLinkContext context) {
        ShareLinkReq req = context.getRequest();
        String contentId = req.getContentId();
        String contentType = req.getContentType();
        String channelType = req.getChannelType();

        // 使用领域服务获取支付相关信息
        BaseOrderInfo baseOrderInfo = orderDomainService.getFundOrder(req.getContentId());
        List<AcceptOrderInfo> acceptOrderInfoList = baseOrderInfo.getAcceptOrderInfoList();
        List<PayOrderInfo> payOrderInfoList = baseOrderInfo.getPayOrderInfoList();

        String payerUserId = payOrderInfoList.get(NumbersEnum.ZERO.getIntValue()).getPayer().getUserId();
        String payeeUserId = acceptOrderInfoList.get(NumbersEnum.ZERO.getIntValue()).getPayee().getUserId();

        List<ShareRecord> shareRecords = new ArrayList<>();

        // 1. 根据业务唯一id查询分享记录
        ShareRecord existingSendRecord = shareGateway.findByUniqueBusinessKey(
            contentId, contentType, ShareRecordsTypeEnum.SEND.getValue(), channelType, payerUserId);
        ShareRecord existingAcceptRecord = shareGateway.findByUniqueBusinessKey(
            contentId, contentType, ShareRecordsTypeEnum.ACCEPT.getValue(), channelType, payeeUserId);

        Date now = ZonedDateUtil.now();

        // 检查记录是否过期
        if (existingSendRecord != null
            && StringUtils.isNotBlank(existingSendRecord.getShareCode())
            ) {

            // 检查任一记录是否过期
            if (existingSendRecord.isExpired(now) || existingAcceptRecord.isExpired(now)) {
                log.info("Share records expired for contentId={}, contentType={}, updating expiration", contentId, contentType);

                // 更新过期时间（3个月后过期）
                Date newExpireAt = ZonedDateUtil.add(now, Calendar.MONTH, NumbersEnum.THREE.getIntValue());

                // 更新发送记录的过期时间
                existingSendRecord.extend(newExpireAt);
                shareGateway.updateShareRecord(existingSendRecord);

                // 更新接收记录的过期时间
                existingAcceptRecord.extend(newExpireAt);
                shareGateway.updateShareRecord(existingAcceptRecord);
            }

            // 返回现有记录（可能已更新过期时间）
            shareRecords.add(existingSendRecord);
            shareRecords.add(existingAcceptRecord);
            context.setShareRecords(shareRecords);
            return;
        }

        // 需要创建至少一条新记录
        if (existingSendRecord == null || existingAcceptRecord == null) {
            // 设置过期时间（3个月后过期）
            Date expireAt = ZonedDateUtil.add(now, Calendar.MONTH, NumbersEnum.THREE.getIntValue());

            // 为发送方和接收方生成唯一的分享记录ID
            String sendRecordId = existingSendRecord != null ?
                existingSendRecord.getShareRecordsId() :
                bizSequenceRepository.getBizId(payerUserId, SequenceCodeEnum.SHARE_RECORD);

            String acceptRecordId = existingAcceptRecord != null ?
                existingAcceptRecord.getShareRecordsId() :
                bizSequenceRepository.getBizId(payeeUserId, SequenceCodeEnum.SHARE_RECORD);

            // 转换为领域对象
            ContentReference contentRef = ContentReference.of(contentId, contentType);
            UserId payerUserIdObj = UserId.of(payerUserId);
            UserId payeeUserIdObj = UserId.of(payeeUserId);

            // 通过领域服务创建一对分享记录，直接传入ID和过期时间
            List<ShareRecord> newRecords = shareDomainService.createShareRecordPair(
                contentRef,
                channelType,
                payerUserIdObj,
                payeeUserIdObj,
                sendRecordId,
                acceptRecordId,
                expireAt,
                ShareCode.empty(),
                ShareUrl.empty()
            );

            // 确保有两条记录
            if (newRecords.size() < 2) {
                log.error("Domain service failed to create both share records. contentId={}, contentType={}", contentId, contentType);
                throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR, "Failed to create share records");
            }

            try {
                // 直接循环保存所有记录
                for (ShareRecord record : newRecords) {
                    shareGateway.saveShareRecord(record);
                }
            } catch (DuplicateKeyException e) {
                log.info("Duplicate share records found for contentId={}, contentType={}", contentId, contentType);
                // 继续执行，查询带锁记录
            }
        }

        // 4. 带锁根据业务唯一id查询出记录（确保排他访问）
        ShareRecord lockedSendRecord = shareGateway.findByUniqueBusinessKeyForUpdate(
            contentId, contentType, ShareRecordsTypeEnum.SEND.getValue(), channelType, payerUserId);
        ShareRecord lockedAcceptRecord = shareGateway.findByUniqueBusinessKeyForUpdate(
            contentId, contentType, ShareRecordsTypeEnum.ACCEPT.getValue(), channelType, payeeUserId);

        if (lockedSendRecord == null || lockedAcceptRecord == null) {
            log.error("Failed to find share records after insertion. contentId={}, contentType={}", contentId, contentType);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR, "Failed to create share records");
        }

        // 5. 如果没有shareCode，则生成并更新
        ShareCode shareCode;
        ShareUrl shareUrl;

        if (StringUtils.isBlank(lockedSendRecord.getShareCode())) {
            // 生成分享码和链接
            long sequenceNumber = bizSequenceRepository.nextValue(SequenceCodeEnum.SHARE_CODE.getBizName());
            shareCode = shareDomainService.generateShareCode(sequenceNumber);
            shareUrl = shareDomainService.buildShareUrl(shareCode);
        } else {
            // 使用已有的分享码和链接
            shareCode = ShareCode.of(lockedSendRecord.getShareCode());
            shareUrl = shareDomainService.buildShareUrl(shareCode);
        }

        // 检查是否需要更新发送记录
        if (StringUtils.isBlank(lockedSendRecord.getShareCode())) {
            lockedSendRecord.setShareInfo(shareCode, shareUrl);
            lockedAcceptRecord.setShareInfo(shareCode, shareUrl);
            shareGateway.updateShareRecord(lockedSendRecord);
            shareGateway.updateShareRecord(lockedAcceptRecord);
        }


        // 6. 返回最终记录
        shareRecords.add(lockedSendRecord);
        shareRecords.add(lockedAcceptRecord);
        context.setShareRecords(shareRecords);
    }

    /**
     * 构建响应结果 - 应用层职责
     * @param context 分享链接上下文
     */
    public void buildResponse(ShareLinkContext context) {
        ShareLinkReq req = context.getRequest();
        List<ShareRecord> shareRecords = context.getShareRecords();
        
        // 从分享记录中获取分享链接（两条记录的分享链接是相同的）
        String shareUrl = shareRecords.isEmpty() ? "" : shareRecords.get(0).getShareUrl();
        
        ShareLinkRsp response = new ShareLinkRsp();
        response.setOrderId(req.getContentId());
        response.setShareUrl(shareUrl);
        
        GenericResult<ShareLinkRsp> result = GenericResult.success(response);
        context.setResult(result);
    }
} 