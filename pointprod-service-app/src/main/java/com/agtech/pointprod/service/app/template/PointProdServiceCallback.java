package com.agtech.pointprod.service.app.template;

import com.agtech.common.result.BaseResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.ResultUtil;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 15:56
 */
public interface PointProdServiceCallback {

    /**
     * 参数校验
     */
    void checkParameter();

    /**
     * 业务流程处理
     */
    void process();

    /**
     * 摘要日志处理
     *
     * @param timeCost 耗时（ms）
     * @return 日志摘要
     */
    BaseDigestLog composeDigestLog(long timeCost);

    default void processAfterPointProdBizException (BaseResult result, PointProdBizException ex){
        ResultUtil.mappingAndFillFailureResult(result, ex);
    }

    default void processAfterThrowable (BaseResult result, Throwable throwable){
        ResultUtil.fillFailureResult(result, throwable);
    }

}
