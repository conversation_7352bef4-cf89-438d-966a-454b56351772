package com.agtech.pointprod.service.app.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.agtech.pointprod.service.app.aop.PointContextInterceptor;

/**
 * 设置拦截器
 * 
 * <AUTHOR>
 * @version $Id: WebConfig.java, v 0.1 2025年6月23日 17:20:19 zhongqiang Exp $
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

	@Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new PointContextInterceptor())
                .addPathPatterns("/**"); // 添加需要拦截的路径模式，这里拦截所有路径作为示例。
    }
	
}
