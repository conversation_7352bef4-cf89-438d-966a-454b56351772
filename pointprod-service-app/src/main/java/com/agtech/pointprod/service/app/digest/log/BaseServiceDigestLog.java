package com.agtech.pointprod.service.app.digest.log;

import com.agtech.common.util.log.BaseDigestLog;
import lombok.Getter;
import lombok.Setter;

/**
 * 摘要日志基础服务类
 * 
 * <AUTHOR>
 * @version $Id: BaseServiceDigestLog.java, v 0.1 2024年7月17日 16:40:28 zhongqiang Exp $
 */
@Getter
@Setter
public abstract class BaseServiceDigestLog extends BaseDigestLog {
	/**
	 * 构造业务信息摘要
	 * @see BaseDigestLog#composeBizInfo(StringBuilder)
	 */
	@Override
	protected void composeBizInfo(StringBuilder buffer) {
	    composeServiceBizInfo(buffer);
	}
	
	/**
	 * 
	 * 
	 * @param paramStringBuilder
	 */
	protected void composeServiceBizInfo(StringBuilder paramStringBuilder){

	}
}
