package com.agtech.pointprod.service.app.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.web.filter.CommonsRequestLoggingFilter;

import javax.servlet.http.HttpServletRequest;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.*;

/**
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
public class HttpRequestLogging extends CommonsRequestLoggingFilter {

    @Override
    protected boolean shouldLog(HttpServletRequest request) {
        return log.isInfoEnabled();
    }
    @Override
    protected void beforeRequest(HttpServletRequest request, String message) {
        MDC.put(MDC_KEY_SERVICE, request.getRequestURI());
        MDC.put(MDC_KEY_REQUEST_TIME, String.valueOf(System.currentTimeMillis()));
        //log.info(message);
    }
    @Override
    protected void afterRequest(HttpServletRequest request, String message) {
        log.info(message);
        handlerMonitor();
    }

    private void handlerMonitor() {
        String responseBodyStr = "";
        try {
            if (StringUtils.isNotEmpty(MDC.get(MDC_KEY_REQUEST_TIME))) {
                MDC.put(MDC_KEY_ELAPSED_TIME, String.valueOf(System.currentTimeMillis() - Long.parseLong(MDC.get(MDC_KEY_REQUEST_TIME))));
            }
            if (StringUtils.isNotEmpty(MDC.get(MDC_KEY_HTTP_RESPONSE_PAYLOAD))) {
                responseBodyStr = MDC.get(MDC_KEY_HTTP_RESPONSE_PAYLOAD);
                JSONObject responseBodyJson = JSON.parseObject(responseBodyStr);
                if (responseBodyJson.containsKey("result")) {
                    JSONObject result = responseBodyJson.getJSONObject("result");
                    MDC.put(MDC_KEY_CODE, result.getString(MDC_KEY_CODE));
                    if (result.containsKey(MDC_KEY_SUB_CODE)) {
                        MDC.put(MDC_KEY_SUB_CODE, result.getString(MDC_KEY_SUB_CODE));
                    }
                    if (result.containsKey(MDC_KEY_SUB_MSG)) {
                        MDC.put(MDC_KEY_SUB_MSG, result.getString(MDC_KEY_SUB_MSG));
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            log.info("response [payload={}]", responseBodyStr);
            mdcRemove();
        }
    }

    private void mdcRemove() {
        try {
            MDC.remove(MDC_KEY_SERVICE);
            MDC.remove(MDC_KEY_REQUEST_TIME);
            MDC.remove(MDC_KEY_ELAPSED_TIME);
            MDC.remove(MDC_KEY_CODE);
            MDC.remove(MDC_KEY_SUB_MSG);
            MDC.remove(MDC_KEY_SUB_CODE);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
