package com.agtech.pointprod.service.app.config.queue;

import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.infrastructure.config.RabbitMQConfig;
import lombok.AllArgsConstructor;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 订单支付成功转出获赠累计队列配置
 *
 * <AUTHOR>
 */
@Configuration
@AllArgsConstructor
public class OrderPaymentSuccessFundAccumulationConfig {

    private final RabbitMQConfig rabbitMQConfig;

    @Bean
    public Queue orderPaymentSuccessFundAccumulationQueue() {
        return new Queue(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_FUND_ACCUMULATION.getConfigKey()).getQueue());
    }

    @Bean
    public Exchange orderPaymentSuccessFundAccumulationExchange() {
        return new DirectExchange(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_FUND_ACCUMULATION.getConfigKey()).getExchange());
    }

    @Bean
    public Binding orderPaymentSuccessFundAccumulationBinding() {
        return BindingBuilder.bind(orderPaymentSuccessFundAccumulationQueue()).to(orderPaymentSuccessFundAccumulationExchange())
                .with(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_FUND_ACCUMULATION.getConfigKey()).getRoutingKey()).noargs();
    }
}
