package com.agtech.pointprod.service.app.service.impl;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.dto.PageInfoDTO;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.processing.step.template.*;
import com.agtech.pointprod.service.app.service.TemplateService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TemplateBizTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.TemplateStatusEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.SortItemDTO;
import com.agtech.pointprod.service.facade.dto.req.template.*;
import com.agtech.pointprod.service.facade.dto.rsp.template.AddTemplateRsp;
import com.agtech.pointprod.service.facade.dto.rsp.template.QueryTemplateInfoListRsp;
import com.agtech.pointprod.service.facade.dto.rsp.template.QueryTemplateRsp;
import com.agtech.pointprod.service.infrastructure.common.enums.sort.ItemSortTypeEnum;
import com.agtech.pointprod.service.infrastructure.common.enums.sort.TemplateSortKeyEnum;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.zat.gateway.component.result.model.GwResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.*;

@Slf4j
@Service
public class TemplateServiceImpl implements TemplateService {

    @Resource
    private TemplateQueryStep templateQueryStep;

    @Resource
    private TemplateFullInfoListQueryStep templateFullInfoListQueryStep;

    @Resource
    private AddTemplateStep addTemplateStep;

    @Resource
    private UpdateTemplateStep updateTemplateStep;

    @Resource
    private DeleteTemplateStep deleteTemplateStep;


    @Override
    public GwResult<PageInfoDTO<QueryTemplateRsp>> queryValidTemplateListByPage(QueryTemplateReq request) {
        final ResultContainer<GenericResult<PageInfoDTO<QueryTemplateRsp>>> container = new ResultContainer<>(GenericResult.success(null));
        PointProdServiceTemplate.execute(SERVICE_NAME_QUERY_VALID_TEMPLATE_LIST_BY_PAGE, container.getResult(), new PointProdServiceCallback() {

            @Override
            public void checkParameter() {
                AssertUtil.assertNotNull(request, PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數不能為空", "request param error");
            }

            @Override
            public void process() {
                container.getResult().setValue(templateQueryStep.process(request.getBizType(),request.getLang(), request.getPage(),  request.getPageNum()));
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                BaseDigestLog baseDigestLog = SimpleDigestLogWrapper.wrap(request, container.getResult(), timeCost);
                return baseDigestLog;
            }

        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }


    @Override
    public GwResult<PageInfoDTO<QueryTemplateInfoListRsp>> queryTemplateFullInfoList(QueryTemplateInfoListReq request) {
        final ResultContainer<GenericResult<PageInfoDTO<QueryTemplateInfoListRsp>>> container = new ResultContainer<>(GenericResult.success(null));
        PointProdServiceTemplate.execute(SERVICE_NAME_QUERY_TEMPLATE_FULL_INFO_LIST, container.getResult(), new PointProdServiceCallback() {

            @Override
            public void checkParameter() {
                AssertUtil.assertNotNull(request, PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數不能為空", "request param error");
                AssertUtil.assertTrue(StringUtils.isEmpty(request.getStatus()) || TemplateStatusEnum.containCode(request.getStatus()), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[Status]無效", "request param[Status] error");
                if (CollectionUtils.isNotEmpty(request.getSortItems())){
                    for (SortItemDTO sortItemDTO : request.getSortItems()){
                        AssertUtil.assertTrue(TemplateSortKeyEnum.containCode(sortItemDTO.getSortKey()), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[SortKey]無效[{0}]", "request param[SortKey] error[{0}]", sortItemDTO.getSortKey());
                        AssertUtil.assertTrue(ItemSortTypeEnum.containCode(sortItemDTO.getSortType()), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[{0}][SortType]無效[{1}]", "request param[{0}][SortType] error[{1}]", sortItemDTO.getSortKey(), sortItemDTO.getSortType());
                    }
                }
            }

            @Override
            public void process() {
                container.getResult().setValue(templateFullInfoListQueryStep.process(request));
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(request, container.getResult(), timeCost);
            }

        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }

    @Override
    public GwResult<AddTemplateRsp> add(AddTemplateReq addTemplateReq) {
        final ResultContainer<GenericResult<AddTemplateRsp>> container = new ResultContainer<>(GenericResult.success(null));
        PointProdServiceTemplate.execute(SERVICE_NAME_ADD_TEMPLATE, container.getResult(), new PointProdServiceCallback() {

            @Override
            public void checkParameter() {
                checkAddTemplateInfo(addTemplateReq);
            }

            @Override
            public void process() {
                container.getResult().setValue(addTemplateStep.process(addTemplateReq));
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(addTemplateReq, container.getResult(), timeCost);
            }

        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }

    @Override
    public GwResult<Void> update(UpdateTemplateReq updateTemplateReq) {
        final ResultContainer<GenericResult<Void>> container = new ResultContainer<>(GenericResult.success(null));
        PointProdServiceTemplate.execute(SERVICE_NAME_UPDATE_TEMPLATE, container.getResult(), new PointProdServiceCallback() {

            @Override
            public void checkParameter() {
                checkAddTemplateInfo(updateTemplateReq);
                AssertUtil.assertTrue(StringUtils.isNotEmpty(updateTemplateReq.getTemplateId()), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[TemplateId]無效", "request param[TemplateId] error");
            }

            @Override
            public void process() {
                updateTemplateStep.process(updateTemplateReq);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(updateTemplateReq, container.getResult(), timeCost);
            }

        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }

    @Override
    public GwResult<Void> delete(DeleteTemplateReq deleteTemplateReq) {
        final ResultContainer<GenericResult<Void>> container = new ResultContainer<>(GenericResult.success(null));
        PointProdServiceTemplate.execute(SERVICE_NAME_DELETE_TEMPLATE, container.getResult(), new PointProdServiceCallback() {

            @Override
            public void checkParameter() {
                AssertUtil.assertNotNull(deleteTemplateReq, PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數不能為空", "request param error");
                AssertUtil.assertNotBlank(deleteTemplateReq.getTemplateId(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[templateId]不能為空", "request param[templateId] error");
            }

            @Override
            public void process() {
                deleteTemplateStep.process(deleteTemplateReq);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(deleteTemplateReq, container.getResult(), timeCost);
            }

        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }

    private void checkAddTemplateInfo(AddTemplateReq templateReq){
        AssertUtil.assertNotNull(templateReq, PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數不能為空", "request param error");
        AssertUtil.assertNotNull(templateReq.getStartTime(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[StartTime]無效", "request param[StartTime] error");
        AssertUtil.assertNotNull(templateReq.getEndTime(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[EndTime]無效", "request param[EndTime] error");
        AssertUtil.assertNotNull(templateReq.getSort(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[Sort]無效", "request param[Sort] error");
        AssertUtil.assertNotNull(templateReq.getOperator(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[Operator]無效", "request param[Operator] error");

        AssertUtil.assertNotBlank(templateReq.getTemplateContent(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[TemplateContent]無效", "request param[TemplateContent] error");
        AssertUtil.assertNotBlank(templateReq.getTemplateContentEn(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[TemplateContentEn]無效", "request param[TemplateContentEn] error");
        AssertUtil.assertNotBlank(templateReq.getStatus(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[Status]無效", "request param[Status] error");
        AssertUtil.assertNotBlank(templateReq.getBizType(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[BizType]無效", "request param[BizType] error");


        AssertUtil.assertTrue(TemplateStatusEnum.containRealCode(templateReq.getStatus()), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[Status]無效", "request param[Status] error");
        AssertUtil.assertTrue(TemplateBizTypeEnum.containCode(templateReq.getBizType()), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[BizType]無效", "request param[BizType] error");
        AssertUtil.assertFalse(templateReq.getStartTime().after(templateReq.getEndTime()), PointProdBizErrorCodeEnum.PARAMS_ERROR, "啟用時間晚於失效時間", "startTime is after endTime");

    }
} 