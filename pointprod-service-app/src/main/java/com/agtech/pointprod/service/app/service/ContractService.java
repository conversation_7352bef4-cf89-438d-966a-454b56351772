package com.agtech.pointprod.service.app.service;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.facade.dto.req.QueryContractReq;
import com.agtech.pointprod.service.facade.dto.req.UserUpdateContractReq;
import com.agtech.pointprod.service.facade.dto.rsp.QueryContractRsp;
import com.agtech.pointprod.service.facade.dto.rsp.UserUpdateContractRsp;
import com.zat.gateway.component.result.model.GwResult;

public interface ContractService {
    GwResult<QueryContractRsp> queryContract(QueryContractReq req);

    GwResult<UserUpdateContractRsp> userUpdate(UserUpdateContractReq req);
}
