package com.agtech.pointprod.service.app.service.impl;

import javax.annotation.Resource;

import com.agtech.pointprod.service.app.processing.step.admin.contract.ContractLastStep;
import com.agtech.pointprod.service.app.processing.step.admin.contract.ContractUpdateOrAddStep;
import com.zat.gateway.component.result.model.GwResult;
import org.springframework.stereotype.Service;
import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.service.ContractAdminService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.facade.dto.req.ContractAdminReq;
import com.agtech.pointprod.service.facade.dto.rsp.ContractAdminRsp;
import com.agtech.pointprod.service.domain.gateway.ContractGateway;
import com.agtech.common.result.GenericResult;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_GET_LATEST_VALID_CONTRACT;
import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_UPDATE_CONTRACT;

/**
 * Contract admin service implementation
 */
@Service
@Slf4j
public class ContractAdminServiceImpl implements ContractAdminService {
    
    @Resource
    private ContractGateway contractGateway;

    @Resource
    private ContractLastStep contractLastStepStep;

    @Resource
    private ContractUpdateOrAddStep contractUpdateOrAddStep;
    
    @Override
    public GwResult<ContractAdminRsp> getLatestValidContract(ContractAdminReq req) {
        final ResultContainer<GenericResult<ContractAdminRsp>> container = new ResultContainer<>(new GenericResult<>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_GET_LATEST_VALID_CONTRACT, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                if (StringUtils.isEmpty(req.getContractType())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
            }
            
            @Override
            public void process() {
                GenericResult<ContractAdminRsp> result = contractLastStepStep.process(req);
                container.setResult(result);
            }
            
            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
    
    @Override
    public GwResult<Boolean> updateContract(ContractAdminReq req) {
        final ResultContainer<GenericResult<Boolean>> container = new ResultContainer<>(new GenericResult<>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_UPDATE_CONTRACT, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                if (StringUtils.isEmpty(req.getContractType())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
                
                if (StringUtils.isEmpty(req.getTitle()) || StringUtils.isEmpty(req.getContent())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
            }
            
            @Override
            public void process() {
                GenericResult<Boolean> result = contractUpdateOrAddStep.process(req);
                container.setResult(result);
            }
            
            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
    
    
} 