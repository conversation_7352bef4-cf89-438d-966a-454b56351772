package com.agtech.pointprod.service.app.controller;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.app.service.TemplateService;
import com.agtech.pointprod.service.facade.dto.req.template.AddTemplateReq;
import com.agtech.pointprod.service.facade.dto.req.template.DeleteTemplateReq;
import com.agtech.pointprod.service.facade.dto.req.template.QueryTemplateInfoListReq;
import com.agtech.pointprod.service.facade.dto.req.template.QueryTemplateReq;
import com.agtech.pointprod.service.facade.dto.req.template.UpdateTemplateReq;
import com.agtech.pointprod.service.facade.dto.rsp.template.AddTemplateRsp;
import com.agtech.pointprod.service.facade.dto.rsp.template.QueryTemplateInfoListRsp;
import com.agtech.pointprod.service.facade.dto.rsp.template.QueryTemplateRsp;
import com.zat.gateway.component.result.model.GwResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Slf4j
@RestController
@RequestMapping("/fund/template")
public class TemplateController {

    @Resource
    private TemplateService templateService;

    @PostMapping("/list/query")
    public GwResult<PageInfoDTO<QueryTemplateRsp>> queryValidTemplateListByPage(@RequestBody QueryTemplateReq request, @RequestHeader(name = "Lang", required = false) String language) {
        request.setLang(language);
        return templateService.queryValidTemplateListByPage(request);
    }

    @PostMapping("/fullInfo/list/query")
    public GwResult<PageInfoDTO<QueryTemplateInfoListRsp>> queryTemplateFullInfoList(@RequestBody QueryTemplateInfoListReq request) {
        return templateService.queryTemplateFullInfoList(request);
    }

    @PostMapping("add")
    public GwResult<AddTemplateRsp> add(@RequestBody AddTemplateReq addTemplateReq){
        return templateService.add(addTemplateReq);
    }


    @PostMapping("update")
    public GwResult<Void> update(@RequestBody UpdateTemplateReq updateTemplateReq){
        return templateService.update(updateTemplateReq);
    }


    @PostMapping("delete")
    public GwResult<Void> delete(@RequestBody DeleteTemplateReq deleteTemplateReq){
        return templateService.delete(deleteTemplateReq);
    }

}
