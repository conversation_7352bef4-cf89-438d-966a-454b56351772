package com.agtech.pointprod.service.app.processing.step.admin.contract;

import com.agtech.common.result.GenericResult;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.ContractGateway;
import com.agtech.pointprod.service.domain.model.Contract;
import com.agtech.pointprod.service.facade.dto.req.ContractAdminReq;
import com.agtech.pointprod.service.facade.dto.rsp.ContractAdminRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Contract admin processing step
 * <AUTHOR>
 */
@Component
@Slf4j
public class ContractLastStep {

    @Resource
    private ContractGateway contractGateway;
    
    /**
     * Get latest valid contract
     */
    public GenericResult<ContractAdminRsp> process(ContractAdminReq req) {
        Contract contract = contractGateway.queryLatestContractByBizType(req.getContractType());
        ContractAdminRsp rsp = new ContractAdminRsp();
        if (contract == null) {
            return GenericResult.success(rsp);
        }
        rsp.setId(contract.getId());
        rsp.setContractId(contract.getContractId());
        rsp.setTitle(contract.getTitle());
        rsp.setTitleEn(contract.getTitleEn());
        rsp.setContentUrl(contract.getContentUrl());
        rsp.setContentEnUrl(contract.getContentEnUrl());
        rsp.setContractType(contract.getContractType());
        rsp.setVersion(contract.getVersion());
        rsp.setValidTime(ZonedDateUtil.formatDate(contract.getValidTime(), ZonedDateUtil.DEFAULT_DATE_PATTERN));
        rsp.setGmtCreate(ZonedDateUtil.formatDate(contract.getGmtCreate()));
        rsp.setGmtModified(ZonedDateUtil.formatDate(contract.getGmtModified()));
        return GenericResult.success(rsp);
    }

} 