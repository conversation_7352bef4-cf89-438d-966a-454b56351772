package com.agtech.pointprod.service.app.processing.step;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;
import com.agtech.pointprod.order.service.domain.model.PayeeParticipantInfo;
import com.agtech.pointprod.order.service.domain.model.PayerParticipantInfo;
import com.agtech.pointprod.order.service.domain.service.OrderDomainService;
import com.agtech.pointprod.order.service.domain.service.PayDomainService;
import com.agtech.pointprod.service.app.processing.NotificationPushContext;
import com.agtech.pointprod.service.domain.common.enums.NotificationStatusEnum;
import com.agtech.pointprod.service.domain.common.enums.NotificationTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.PushStatusEnum;
import com.agtech.pointprod.service.domain.common.enums.ResourceTypeEnum;
import com.agtech.pointprod.service.domain.gateway.NotificationGateway;
import com.agtech.pointprod.service.domain.model.NotificationRecord;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.NotificationPushReq;

import lombok.extern.slf4j.Slf4j;

/**
 * 通知推送处理步骤
 */
@Slf4j
@Component
public class NotificationPushStep {

    @Resource
    private NotificationGateway notificationGateway;
    
    @Resource
    private OrderDomainService orderDomainService;
    
    @Resource
    private PayDomainService payDomainService;
    
    /**
     * 从请求对象构建通知记录
     * @param context 推送上下文
     * @param req 通知推送请求
     */
    public void buildNotificationRecordFromRequest(NotificationPushContext context, NotificationPushReq req) {
        // 1. 通过orderId查询订单信息
    	BaseOrderInfo fundOrder = orderDomainService.getFundOrder(req.getOrderId());
        
        AssertUtil.assertNotNull(fundOrder, PointProdBizErrorCodeEnum.FUND_ORDER_NOT_FOUND, "Order not found for orderId: " + req.getOrderId(), "Order not found for orderId: " + req.getOrderId());
        
        // 存储到上下文中，供后续步骤使用
        context.setFundOrder(fundOrder);
        
        // 3. 根据通知类型确定目标用户ID列表
        List<String> userIds = new ArrayList<>();
        
        if (NotificationTypeEnum.SEND.getCode().equals(req.getNotificationType())) {
            // 发送通知给所有付款人
            List<PayOrderInfo> payOrderInfoList = fundOrder.getPayOrderInfoList();
            if (payOrderInfoList != null && !payOrderInfoList.isEmpty()) {
                for (PayOrderInfo payOrderInfo : payOrderInfoList) {
                    PayerParticipantInfo payer = payOrderInfo.getPayer();
                    if (payer != null && payer.getUserId() != null) {
                        userIds.add(payer.getUserId());
                    }
                }
            }
        } else if (NotificationTypeEnum.ACCEPT.getCode().equals(req.getNotificationType())) {
            // 发送通知给所有收款人
            List<AcceptOrderInfo> acceptOrderInfoList = fundOrder.getAcceptOrderInfoList();
            if (acceptOrderInfoList != null && !acceptOrderInfoList.isEmpty()) {
                for (AcceptOrderInfo acceptOrderInfo : acceptOrderInfoList) {
                    PayeeParticipantInfo payee = acceptOrderInfo.getPayee();
                    if (payee != null && payee.getUserId() != null) {
                        userIds.add(payee.getUserId());
                    }
                }
            }
        }
        
        AssertUtil.assertTrue(!userIds.isEmpty(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "No users found for orderId: " + req.getOrderId() + ", notificationType: " + req.getNotificationType(), "No users found for orderId: " + req.getOrderId() + ", notificationType: " + req.getNotificationType());
        
        // 4. 为每个用户构建通知记录
        List<NotificationRecord> notifications = new ArrayList<>();
        
        for (String userId : userIds) {
            NotificationRecord notification = new NotificationRecord();
            notification.setNotificationType(req.getNotificationType());
            notification.setResourceId(req.getOrderId());
            notification.setResourceType(ResourceTypeEnum.TRANSFER_SUCCESS.getValue());
            notification.setUserId(userId);
            notification.setStatus(NotificationStatusEnum.UNREAD.getCode());
            notification.setPushStatus(PushStatusEnum.INIT.getValue());
            notification.setResourceTime(fundOrder.getCompleteTime());
            notifications.add(notification);
        }
        
        context.setNotifications(notifications);
        context.setUserIds(userIds);
        
        log.info("Built {} notification records from request for orderId: {}, notificationType: {}, userIds: {}", 
                notifications.size(), req.getOrderId(), req.getNotificationType(), userIds);
    }

    /**
     * 创建通知记录
     * @param context 推送上下文
     */
    @Transactional
    public void createNotificationRecord(NotificationPushContext context) {
        List<NotificationRecord> successNotifications = new ArrayList<>();
        
        for (int i = 0; i < context.getNotifications().size(); i++) {
            NotificationRecord notification = context.getNotifications().get(i);
            String userId = context.getUserIds().get(i);
            
            try {
                notificationGateway.createNotification(notification, userId);
                successNotifications.add(notification);
            } catch (DuplicateKeyException e) {
                log.info("DuplicateKeyException userId: {}, resourceId: {}, notificationType: {}", 
                        userId, notification.getResourceId(), notification.getNotificationType(), e);
                // 通过不带锁的唯一键查询将已存在的通知查询出来再放到successNotifications里
                List<NotificationRecord> existingNotifications = notificationGateway.queryNotification(
                        notification.getResourceId(), 
                        ResourceTypeEnum.TRANSFER_SUCCESS.getValue(),
                        userId,
                        notification.getNotificationType());
                
                if (existingNotifications != null && !existingNotifications.isEmpty()) {
                    // 将已存在的通知添加到成功列表中
                    successNotifications.add(existingNotifications.get(0));
                    log.info("Added existing notification: {} to success list", existingNotifications.get(0).getNotificationId());
                } else {
                    log.error("No existing notification found after DuplicateKeyException, adding original notification");
                }
            } catch (Exception e) {
                log.error("Failed to create notification for userId: {}, resourceId: {}", 
                        userId, notification.getResourceId(), e);
            }
        }
        
        context.setSuccessNotifications(successNotifications);
        // 只有当所有通知都创建成功时，才算整体创建成功
        context.setCreateSuccess(!successNotifications.isEmpty() && successNotifications.size() == context.getNotifications().size());
        
        log.info("Successfully created {} notification records out of {} attempts", 
                successNotifications.size(), context.getNotifications().size());
    }

    /**
     * 推送通知到MPay并更新状态
     * @param context 推送上下文
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void pushNotificationAndUpdateStatus(NotificationPushContext context) {
        List<NotificationRecord> successPushes = new ArrayList<>();
        BaseOrderInfo fundOrder = context.getFundOrder();
        
        if (fundOrder == null) {
            // 如果上下文中没有订单信息，则重新获取
            String resourceId = context.getNotifications().get(0).getResourceId();
            fundOrder = orderDomainService.getFundOrder(resourceId);
            if (fundOrder == null) {
                log.error("Order not found for resourceId: {}", resourceId);
                return;
            }
        }
        
        for (NotificationRecord notification : context.getSuccessNotifications()) {
            String userId = notification.getUserId();
            
            // 先查询是否已存在push_status = SUCCESS的记录，避免重复推送
            List<NotificationRecord> existingNotifications = notificationGateway.queryNotificationForUpdate(
                    notification.getResourceId(), 
                    ResourceTypeEnum.TRANSFER_SUCCESS.getValue(),
                    userId,
                    notification.getNotificationType());
            
            // 检查是否已有成功推送的记录
            boolean alreadyPushed = false;
            for (NotificationRecord existingNotification : existingNotifications) {
                if (PushStatusEnum.SUCCESS.getValue().equals(existingNotification.getPushStatus())) {
                    log.info("Notification already pushed successfully, skipping duplicate push - resourceId: {}, notificationId: {}, userId: {}", 
                            notification.getResourceId(), existingNotification.getNotificationId(), userId);
                    successPushes.add(notification);
                    alreadyPushed = true;
                    break;
                }
            }
            
            if (alreadyPushed) {
                continue;
            }
            
            try {
                // 调用网关进行真实的推送操作（先获取appToken，再推送消息）
                boolean pushResult = notificationGateway.pushNotificationToUser(notification, fundOrder);
                
                if (pushResult) {
                    // 推送成功，更新push_status为SUCCESS
                    boolean updateResult = notificationGateway.updateNotificationPushStatus(
                            notification.getNotificationId(), PushStatusEnum.SUCCESS.getValue());
                    
                    if (updateResult) {
                        successPushes.add(notification);
                        log.info("Successfully pushed and updated notification status - notificationId: {}, userId: {}", 
                                notification.getNotificationId(), userId);
                    } else {
                        log.error("Push succeeded but failed to update status - notificationId: {}, userId: {}", 
                                notification.getNotificationId(), userId);
                    }
                } else {
                    // 推送失败，更新push_status为FAILED
                    notificationGateway.updateNotificationPushStatus(
                            notification.getNotificationId(), PushStatusEnum.FAILED.getValue());
                    log.error("Failed to push notification - notificationId: {}, userId: {}", 
                            notification.getNotificationId(), userId);
                }
                
            } catch (Exception e) {
                log.error("Exception occurred while pushing notification - notificationId: {}, userId: {}", 
                        notification.getNotificationId(), userId, e);
                
                // 异常情况下，更新push_status为FAILED
                try {
                    notificationGateway.updateNotificationPushStatus(
                            notification.getNotificationId(), PushStatusEnum.FAILED.getValue());
                } catch (Exception updateException) {
                    log.error("Failed to update push status to FAILED - notificationId: {}", 
                            notification.getNotificationId(), updateException);
                }
            }
        }
        
        // 只有当所有创建成功的通知都推送成功时，才算整体推送成功
        context.setPushSuccess(!successPushes.isEmpty() && successPushes.size() == context.getSuccessNotifications().size());
        context.setSuccessPushedNotifications(successPushes);
        
        log.info("Successfully pushed {} notifications out of {} attempts", 
                successPushes.size(), context.getSuccessNotifications().size());
    }
} 