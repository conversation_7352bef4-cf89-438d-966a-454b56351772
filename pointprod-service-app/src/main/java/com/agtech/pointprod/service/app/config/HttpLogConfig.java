package com.agtech.pointprod.service.app.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CommonsRequestLoggingFilter;

/**
 * <AUTHOR>
 * @since 2.0.3
 */
@Configuration
@Slf4j
public class HttpLogConfig {

    @Bean
    public CommonsRequestLoggingFilter requestLoggingFilter() {
        CommonsRequestLoggingFilter loggingFilter = new HttpRequestLogging();
        loggingFilter.setIncludeClientInfo(false);
        loggingFilter.setIncludeQueryString(true);
        loggingFilter.setIncludePayload(true);
        loggingFilter.setIncludeHeaders(true);
        loggingFilter.setMaxPayloadLength(640000);
        loggingFilter.setAfterMessagePrefix("request [");
        loggingFilter.setBeforeMessagePrefix("request [");
        return loggingFilter;
    }

}
