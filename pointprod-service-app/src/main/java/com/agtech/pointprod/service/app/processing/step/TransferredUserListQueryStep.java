package com.agtech.pointprod.service.app.processing.step;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.domain.gateway.TransferredUserGateway;
import com.agtech.pointprod.service.facade.dto.req.QueryTransferredUserListReq;
import com.agtech.pointprod.service.facade.dto.rsp.QueryTransferredUserListRsp;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 14:49
 */
@Component
public class TransferredUserListQueryStep {

    @Resource
    private TransferredUserGateway transferredUserGateway;

    public PageInfoDTO<QueryTransferredUserListRsp> process(QueryTransferredUserListReq request){
        return transferredUserGateway.queryTemplateListByPage(request);
    }

}
