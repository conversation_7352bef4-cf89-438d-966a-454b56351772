package com.agtech.pointprod.service.app.processing;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.NotificationRecord;
import com.agtech.pointprod.service.facade.dto.rsp.NotificationDetailRsp;

import lombok.Getter;
import lombok.Setter;

/**
 * 通知详情上下文
 */
@Getter
@Setter
public class NotificationDetailContext {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 通知ID
     */
    private String notificationId;
    
    /**
     * 通知记录
     */
    private NotificationRecord notificationRecord;
    
    /**
     * 订单信息
     */
    private BaseOrderInfo fundOrder;
    
    /**
     * 付款人信息
     */
    private MPayUserInfo payerUserInfo;
    
    /**
     * 响应结果
     */
    private GenericResult<NotificationDetailRsp> result;
} 