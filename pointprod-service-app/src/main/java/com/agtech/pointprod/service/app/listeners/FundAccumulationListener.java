package com.agtech.pointprod.service.app.listeners;

import java.io.IOException;

import javax.annotation.Resource;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.model.OrderStatusChangeMessage;
import com.agtech.pointprod.service.app.listeners.base.TaskRetryBaseListener;
import com.agtech.pointprod.service.app.service.FundAccumulationService;
import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FundAccumulationListener extends TaskRetryBaseListener<OrderStatusChangeMessage>{

    @Resource
    private FundAccumulationService fundAccumulationService;

    @RabbitListener(queues = "${rabbitmq.queues.config.order-payment-success-fund-accumulation.queue}")
    public void onFundAccumulationOrderPaymentSuccess(Message message) throws IOException {
        log.info("FundAccumulationOrderPaymentSuccess listener triggered");
        super.onMessage(message, OrderStatusChangeMessage.class);
    }

    @Override
    protected boolean doCheck(OrderStatusChangeMessage message, MessageProperties properties) {
        if(message == null){
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR, "message body empty or parse error");
        }
        if(!FundOrderStatusEnum.SUCCESS.equals(message.getOrderStatus())){
            log.info("[order-payment-success-fund-accumulation] order status is not success");
            return false;
        }
        return true;
    }

    @Override
    public void doProcess(OrderStatusChangeMessage message, MessageProperties properties) {
        fundAccumulationService.transferOneStageAccumulation(message);
        log.info("[order-payment-success-fund-accumulation] transfer one stage accumulation success");
    }

    @Override
    protected MessageQueueEnum getMessageQueueEnum() {
        return MessageQueueEnum.ORDER_PAYMENT_SUCCESS_FUND_ACCUMULATION;
    }

    @Override
    protected TaskResouceTypeEnum getTaskResouceTypeEnum() {
        return TaskResouceTypeEnum.MQ_CONSUME_ORDER_PAYMENT_SUCCESS_FUND_ACCUMULATION;
    }

    
}
