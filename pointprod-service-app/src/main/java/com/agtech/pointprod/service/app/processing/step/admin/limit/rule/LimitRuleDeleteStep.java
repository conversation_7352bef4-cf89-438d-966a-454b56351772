package com.agtech.pointprod.service.app.processing.step.admin.limit.rule;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.limit.service.domain.gateway.AccumulateRuleGateway;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TransferRuleStatusEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.TransferRuleGateway;
import com.agtech.pointprod.service.facade.dto.req.AccumulateRuleAdminReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Transfer rule admin processing step
 * This class handles both limit rules (via accumulateRuleGateway) and transfer rules (via TransferRuleGateway)
 * Business logic: First maintain CumulateRule, then maintain TransferRule
 * <AUTHOR>
 * @version $Id: LimitRuleDeleteStep.java, v 0.1 2025年7月4日 15:47:11 dujunliang Exp $
 */
@Component
@Slf4j
public class LimitRuleDeleteStep {

    @Resource
    private TransferRuleGateway transferRuleGateway;

    @Resource
    private AccumulateRuleGateway accumulateRuleGateway;


    /**
     * 規則刪除
     */
    @Transactional(rollbackFor = Exception.class)
    public GenericResult<Boolean> process(AccumulateRuleAdminReq req) {
        AccumulateRule existingRule = accumulateRuleGateway.getAccumulateRuleByRuleId(req.getRuleId());
        if (existingRule == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.RULE_NOT_EXIST,PointProdBizErrorCodeEnum.RULE_NOT_EXIST.getResultMsg());
        }
        if (TransferRuleStatusEnum.VALID.getValue().equals(existingRule.getStatus())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.RULE_DELETE_STATUS_ERROR, PointProdBizErrorCodeEnum.RULE_DELETE_STATUS_ERROR.getResultMsg());
        }
        boolean success = accumulateRuleGateway.deleteAccumulateRule(req.getRuleId());
        if (!success) {
            log.error("Failed to delete CumulateRule, ruleId: {}", req.getRuleId());
        }
        log.info("CumulateRule marked as deleted, ruleId: {}", req.getRuleId());
        success = transferRuleGateway.deleteTransferRule(req.getRuleId(), req.getUsername());
        if (!success) {
            log.error("Failed to delete CumulateRule, ruleId: {}", req.getRuleId());
        }
        log.info("TransferRule deleted successfully, ruleId: {}", req.getRuleId());
        return GenericResult.success(true);
    }


} 