package com.agtech.pointprod.service.app.service;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.facade.dto.req.ContractAdminReq;
import com.agtech.pointprod.service.facade.dto.rsp.ContractAdminRsp;
import com.agtech.common.result.GenericResult;
import com.zat.gateway.component.result.model.GwResult;

/**
 * Contract admin service interface
 */
public interface ContractAdminService {
    /**
     * Get latest valid contract by type
     */
    GwResult<ContractAdminRsp> getLatestValidContract(ContractAdminReq req);
    
    /**
     * Update or create new contract
     */
    GwResult<Boolean> updateContract(ContractAdminReq req);
} 