package com.agtech.pointprod.service.app.processing;

import java.util.List;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.service.domain.model.share.ShareRecord;
import com.agtech.pointprod.service.facade.dto.req.ShareLinkReq;
import com.agtech.pointprod.service.facade.dto.rsp.ShareLinkRsp;

import lombok.Getter;
import lombok.Setter;

/**
 * 分享链接上下文
 */
@Getter
@Setter
public class ShareLinkContext {
    
    /**
     * 请求参数
     */
    private ShareLinkReq request;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 验证的订单信息
     */
    private BaseOrderInfo fundOrder;
    
    /**
     * 创建的分享记录列表（包含SEND和ACCEPT两条记录）
     */
    private List<ShareRecord> shareRecords;
    
    /**
     * 响应结果
     */
    private GenericResult<ShareLinkRsp> result;
    
    // Constructors
    public ShareLinkContext() {}
}
