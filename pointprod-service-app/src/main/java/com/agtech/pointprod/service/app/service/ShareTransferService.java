package com.agtech.pointprod.service.app.service;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.facade.dto.req.ShareTransferReq;
import com.agtech.pointprod.service.facade.dto.rsp.ShareTransferRsp;

/**
 * 短链转长链服务
 */
public interface ShareTransferService {
    /**
     * 短链转长链
     * @param req 请求参数
     * @return 长链URL
     */
    GenericResult<ShareTransferRsp> transferShortToLong(ShareTransferReq req);
} 