package com.agtech.pointprod.service.app.service;

import com.agtech.pointprod.service.facade.dto.req.GrayRequest;
import com.agtech.pointprod.service.facade.dto.rsp.GrayResponse;
import com.zat.gateway.component.result.model.GwResult;

/**
 * 灰度管理服务接口
 * 
 * <AUTHOR>
 * @version v1.0, 2025/1/15
 */
public interface GrayAdminService {

    /**
     * 检查灰度状态（管理端）
     * 
     * @param request 灰度请求
     * @return 灰度响应
     */
    GwResult<GrayResponse> checkGray(GrayRequest request);
} 