package com.agtech.pointprod.service.app.processing.step;

import java.text.SimpleDateFormat;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.service.OrderDomainService;
import com.agtech.pointprod.order.service.facade.dto.FundAmountDTO;
import com.agtech.pointprod.service.app.processing.NotificationDetailContext;
import com.agtech.pointprod.service.domain.common.enums.NotificationStatusEnum;
import com.agtech.pointprod.service.domain.common.enums.NotificationTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.ResourceTypeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.NotificationGateway;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.NotificationRecord;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.rsp.NotificationDetailRsp;

/**
 * 通知详情步骤
 */
@Component
public class NotificationDetailStep {

    @Resource
    private NotificationGateway notificationGateway;
    
    @Resource
    private OrderDomainService orderDomainService;
    
    /**
     * 查询通知详情
     * 
     * @param context 通知详情上下文
     * @return 如果通知已读或者类型不是ACCEPT，返回true表示应该终止流程；否则返回false表示继续流程
     */
    public boolean queryNotificationDetail(NotificationDetailContext context) {
        String notificationId = context.getNotificationId();
        String userId = context.getUserId();
        
        // 通过notificationId和userId查询通知
        NotificationRecord notification = notificationGateway.queryByNotificationId(notificationId, userId);
        
        // 验证通知是否存在
        if (notification == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.NOTIFICATION_NOT_EXIST);
        }
                
        // 验证通知状态是否为已读或类型不是ACCEPT
        if (NotificationStatusEnum.READ.getCode().equals(notification.getStatus()) 
                || !NotificationTypeEnum.ACCEPT.getCode().equals(notification.getNotificationType())) {
            // 通知已读或类型不是ACCEPT，设置空响应并返回true表示终止流程
            context.setResult(GenericResult.success(new NotificationDetailRsp()));
            return true; // 终止流程
        }
        
        // 设置通知记录到上下文
        context.setNotificationRecord(notification);
        return false; // 继续流程
    }
    
    /**
     * 查询订单信息
     * 
     * @param context 通知详情上下文
     */
    public void queryOrderInfo(NotificationDetailContext context) {
        NotificationRecord notification = context.getNotificationRecord();
        
        // 检查资源类型是否为转账成功
        if (!ResourceTypeEnum.TRANSFER_SUCCESS.getValue().equals(notification.getResourceType())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.NOTIFICATION_RESOURCE_TYPE_ERROR);
        }
        
        // 通过资源ID查询订单
        String resourceId = notification.getResourceId();
        BaseOrderInfo fundOrder = orderDomainService.getFundOrder(resourceId);
        
        // 验证订单是否存在
        if (fundOrder == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_NOT_EXISTS);
        }
        
        // 检查订单状态是否为成功
        AssertUtil.assertTrue(FundOrderStatusEnum.SUCCESS.equals(fundOrder.getOrderStatus()),
                PointProdBizErrorCodeEnum.ORDER_STATUS_ERROR);
        
        // 设置订单到上下文
        context.setFundOrder(fundOrder);
    }
    
    /**
     * 获取付款人信息
     * 
     * @param context 通知详情上下文
     */
    public void getPayerUserInfo(NotificationDetailContext context) {
        BaseOrderInfo fundOrder = context.getFundOrder();
        
        // 获取付款人ID
        String payerUserId = fundOrder.getActorUserId();
        
        // 通过付款人ID查询用户信息
        MPayUserInfo payerUserInfo = orderDomainService.queryMpayUserInfo(payerUserId);
        
        // 验证用户信息是否存在
        if (payerUserInfo == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.USER_NOT_EXISTS);
        }
        
        // 设置付款人信息到上下文
        context.setPayerUserInfo(payerUserInfo);
    }
    
    /**
     * 构建响应
     * 
     * @param context 通知详情上下文
     */
    public void buildResponse(NotificationDetailContext context) {
        NotificationRecord notification = context.getNotificationRecord();
        BaseOrderInfo fundOrder = context.getFundOrder();
        MPayUserInfo payerUserInfo = context.getPayerUserInfo();
        
        // 创建响应对象
        NotificationDetailRsp rsp = new NotificationDetailRsp();
        NotificationDetailRsp.NotificationDetail detail = new NotificationDetailRsp.NotificationDetail();
        
        // 设置通知详情
        detail.setNotificationId(notification.getNotificationId());
        detail.setOrderId(fundOrder.getFundOrderId());
        
        // 设置付款人信息
        detail.setPayerUserNickName(payerUserInfo.getNickName());
        detail.setPayerUserHeadImg(payerUserInfo.getHeadLogo());
        detail.setPayerAreaCode(payerUserInfo.getAreaCode());
        detail.setPayerPhone(payerUserInfo.getPhone());
        
        // 设置支付时间
        if (fundOrder.getCompleteTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            detail.setPaidTime(sdf.format(fundOrder.getCompleteTime()));
        }
        
        // 设置金额信息
        FundAmountDTO fundAmount = new FundAmountDTO();
        fundAmount.setCurrencyCode("MCOIN"); // Default to MCOIN as per FundAmountDTO default
        fundAmount.setAmount(fundOrder.getFundAmount().getAmount());
        detail.setFundAmount(fundAmount);
        
        // 设置留言
        detail.setMemo(fundOrder.getMemo());
        
        // 设置响应
        rsp.setNotification(detail);
        
        // 设置结果到上下文
        context.setResult(GenericResult.success(rsp));
    }
} 