package com.agtech.pointprod.service.app.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zat.gateway.component.result.model.GwResult;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Resource;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.MDC_KEY_HTTP_RESPONSE_PAYLOAD;

/**
 * <AUTHOR>
 * @since 2.0.3
 */
@ControllerAdvice
@Slf4j
public class HttpResponseLogging implements ResponseBodyAdvice<Object> {
    @Resource
    private ObjectMapper jacksonObjectMapper;

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true ;
    }
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
            ServerHttpResponse response) {
        if (body instanceof GwResult ){
            try {
                MDC.put(MDC_KEY_HTTP_RESPONSE_PAYLOAD, jacksonObjectMapper.writeValueAsString(body));
            } catch (JsonProcessingException e) {
                log.error("转换日志异常", e);
            }
        }
        return body;
    }
}
