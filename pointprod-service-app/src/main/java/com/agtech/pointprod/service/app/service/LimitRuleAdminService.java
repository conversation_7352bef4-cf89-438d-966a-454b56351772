package com.agtech.pointprod.service.app.service;

import com.agtech.pointprod.service.facade.dto.req.AccumulateRuleAdminReq;
import com.agtech.pointprod.service.facade.dto.rsp.AccumulateRuleAdminRsp;
import com.agtech.pointprod.service.facade.dto.rsp.AccumulateRulePageResponse;
import com.zat.gateway.component.result.model.GwResult;

/**
 * Transfer rule admin service interface
 * This service handles both limit rules (via AccumulateProductGateway) and transfer rules (via TransferRuleGateway)
 */
public interface LimitRuleAdminService {
    
    /**
     * Query transfer rule list
     * 
     * @param req Request parameters
     * @return List of transfer rules
     */
    GwResult<AccumulateRulePageResponse> selectCumulateRuleList(AccumulateRuleAdminReq req);
    
    /**
     * Add transfer rule
     * 
     * @param req Request parameters
     * @return Operation result
     */
    GwResult<Boolean> addCumulateRule(AccumulateRuleAdminReq req);
    
    /**
     * Update transfer rule
     * 
     * @param req Request parameters
     * @return Operation result
     */
    GwResult<Boolean> updateCumulateRule(AccumulateRuleAdminReq req);
    
    /**
     * Get transfer rule detail
     * 
     * @param req Request parameters
     * @return Transfer rule detail
     */
    GwResult<AccumulateRuleAdminRsp> getCumulateRuleDetail(AccumulateRuleAdminReq req);
    
    /**
     * Delete transfer rule
     * 
     * @param req Request parameters
     * @return Operation result
     */
    GwResult<Boolean> deleteCumulateRule(AccumulateRuleAdminReq req);
} 