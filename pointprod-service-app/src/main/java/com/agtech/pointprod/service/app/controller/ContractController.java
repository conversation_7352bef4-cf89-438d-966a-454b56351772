package com.agtech.pointprod.service.app.controller;

import com.agtech.pointprod.service.app.service.ContractService;
import com.agtech.pointprod.service.facade.dto.req.QueryContractReq;
import com.agtech.pointprod.service.facade.dto.req.UserUpdateContractReq;
import com.agtech.pointprod.service.facade.dto.rsp.QueryContractRsp;
import com.agtech.pointprod.service.facade.dto.rsp.UserUpdateContractRsp;
import com.zat.gateway.component.result.model.GwResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/fund/contract")
public class ContractController {
    @Resource
    private ContractService contractService;

    @PostMapping("/query")
    public GwResult<QueryContractRsp> queryContract(@RequestBody QueryContractReq req, @RequestHeader(name = "MPayCustId", required = false) String userId, @RequestHeader(name = "Lang", required = false) String language) {
        req.setLang(language);
        req.setUserId(userId);
        return contractService.queryContract(req);
    }

    @PostMapping("/user/update")
    public GwResult<UserUpdateContractRsp> userUpdate(@RequestBody UserUpdateContractReq req, @RequestHeader(name = "MPayCustId", required = false) String userId) {
        req.setUserId(userId);
        return contractService.userUpdate(req);
    }
}
