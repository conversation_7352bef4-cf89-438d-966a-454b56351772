package com.agtech.pointprod.service.app.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.agtech.pointprod.service.app.service.TransferRecordsService;
import com.agtech.pointprod.service.facade.dto.req.TransferRecordsListReq;
import com.agtech.pointprod.service.facade.dto.rsp.TransferRecordsListRsp;
import com.zat.gateway.component.result.model.GwResult;

@RestController
@RequestMapping("/fund/order")
public class TransferRecordsController {
    
    @Resource
    private TransferRecordsService transferRecordsService;
    
    /**
     * 查询转赠记录列表
     *
     * @param req 查询请求参数
     * @param userId 用户ID
     * @param language 语言
     * @return 转赠记录列表响应
     */
    @PostMapping("/list")
    public GwResult<TransferRecordsListRsp> queryTransferRecordsList(@RequestBody TransferRecordsListReq req, 
                                                                   @RequestHeader(name = "MPayCustId", required = false) String userId,
                                                                   @RequestHeader(name = "Lang", required = false) String language) {
        req.setUserId(userId);
        req.setLang(language);
        return transferRecordsService.queryTransferRecordsList(req);
    }
} 