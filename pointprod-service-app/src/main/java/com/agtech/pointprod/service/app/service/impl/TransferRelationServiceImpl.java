package com.agtech.pointprod.service.app.service.impl;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.order.service.domain.model.OrderStatusChangeMessage;
import com.agtech.pointprod.service.app.biz.TransferRelationBizService;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.service.TransferRelationService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_SAVE_TRANSFER_RELATION;

@Service
@Slf4j
public class TransferRelationServiceImpl implements TransferRelationService {

    @Resource
    private TransferRelationBizService transferRelationBizService;

    @Override
    public void saveTransferRelation(OrderStatusChangeMessage message) {
        final ResultContainer<GenericResult<String>> container = new ResultContainer<>(GenericResult.success(null));
        PointProdServiceTemplate.execute(SERVICE_NAME_SAVE_TRANSFER_RELATION, container.getResult(), new PointProdServiceCallback() {

            @Override
            public void checkParameter() {
                AssertUtil.assertNotNull(message, PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數不能為空", "request param error");
            }

            @Override
            public void process() {
                transferRelationBizService.saveTransferRelation(message);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(message, container.getResult(), timeCost);
            }
        });
    }
}