package com.agtech.pointprod.service.app.processing.step.template;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.order.service.domain.common.constants.LangConstants;
import com.agtech.pointprod.service.domain.gateway.TemplateGateway;
import com.agtech.pointprod.service.domain.model.Template;
import com.agtech.pointprod.service.facade.dto.rsp.template.QueryTemplateRsp;
import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Component
@Slf4j
public class TemplateQueryStep {

    @Resource
    private TemplateGateway templateGateway;

    public PageInfoDTO<QueryTemplateRsp> process(String bizType,String lang , Integer pageNo, Integer pageSize) {
        PageInfoDTO<Template> pageInfoDTO =
                templateGateway.queryValidTemplateListByPage(bizType, pageNo, pageSize);
        List<Template> records = pageInfoDTO.getRecords();
        // convert list
        List<QueryTemplateRsp> list = new ArrayList<>();
        records.forEach(c -> {
            QueryTemplateRsp queryTemplateRsp = new QueryTemplateRsp();
            queryTemplateRsp.setTemplateId(c.getTemplateId());
            String templateContent = StringUtils.equals(lang, LangConstants.EN_GB)
                    ? c.getTemplateContentEn(): c.getTemplateContent() ;
            queryTemplateRsp.setTemplateContent(templateContent);
            list.add(queryTemplateRsp);
        });
        return PageInfoDTO.of(pageInfoDTO.getTotalCount(), pageInfoDTO.getPageSize()
                , pageInfoDTO.getCurrentPage(), list);
    }

}
