package com.agtech.pointprod.service.app.controller.job;

import com.agtech.pointprod.order.service.app.service.PaymentService;
import com.agtech.pointprod.order.service.facade.dto.rsp.PaymentRsp;
import com.agtech.pointprod.service.domain.service.MessageReliabilityDomainService;
import com.alibaba.fastjson2.JSON;
import com.zat.gateway.component.result.enums.GwResultCode;
import com.zat.gateway.component.result.model.GwResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.zat.gateway.component.result.enums.GwResultCode.SUCCESS;

/**
 * <AUTHOR>
 * @date 2025-06-18
 */
@RestController
@RequestMapping("/fund/job")
@Slf4j
public class TaskRetryController {

    @Resource
    private MessageReliabilityDomainService messageReliabilityDomainService;

    @PostMapping("/task-retry")
    public GwResult<Object> taskRetry(){
        log.info("task retry request");

        messageReliabilityDomainService.retryMessages();
        return GwResult.success(SUCCESS.getSubCode(), SUCCESS.getSubMsg(), new Object());
    }

}
