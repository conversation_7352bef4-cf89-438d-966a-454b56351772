package com.agtech.pointprod.service.app.processing.step.gray;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.facade.dto.req.GrayRequest;
import com.agtech.pointprod.service.facade.dto.rsp.GrayResponse;
import com.agtech.pointprod.service.infrastructure.common.enums.GrayEnums;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class GrayStep {

    @Resource
    private Environment environment;

    public GenericResult<GrayResponse> process(GrayRequest request) {
        log.info("开始处理灰度请求，featureKey: {}, custId: {}", request.getFeatureKey(), request.getUserId());
        
        // 1. 初始化gray = false
        boolean gray = false;
        
        // 2. 根据featureKey动态读取nacos配置
        String lowFeatureKey = request.getFeatureKey().toLowerCase();
        String switchKey = environment.getProperty("point.gray.switch." + lowFeatureKey);
        if (StringUtils.isBlank(switchKey)) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.GRAY_SWITCH_CONFIG_NOT_EXISTS);
        }
        String whiteList = getConfigValue("point.gray.white." + lowFeatureKey, "");
        String rate = getConfigValue("point.gray.rate." + lowFeatureKey, GrayEnums.DEFAULT_RATE.getStringValue());
        
        log.info("灰度配置 - featureKey: {}, 白名单: {}, 比例: {}, 开关: {}", lowFeatureKey, whiteList, rate, switchKey);
        
        // 3. 根据开关状态决定逻辑
        int switchValue = Integer.parseInt(switchKey);
        
        if (switchValue == GrayEnums.GRAY_SWITCH_OFF.getValue()) {
            // 关闭
            log.info("灰度开关关闭，返回 gray = false");
            gray = false;
        } else if (switchValue == GrayEnums.GRAY_SWITCH_ON.getValue()) {
            // 灰度中
            log.info("灰度开关开启，进入灰度逻辑");
            gray = processGrayLogic(request.getUserId(), whiteList, rate);
        } else if (switchValue == GrayEnums.GRAY_SWITCH_FULL.getValue()) {
            // 全量开放
            log.info("灰度全量开放，返回 gray = true");
            gray = true;
        } else {
            log.error("未知的开关状态: {}, 默认返回 gray = false", switchValue);
            gray = false;
        }
        
        // 4. 返回结果
        GrayResponse response = new GrayResponse();
        response.setGray(gray);
        
        log.info("灰度处理完成，返回结果: {}", gray);
        return GenericResult.success(response);
    }
    
    /**
     * 动态获取配置值
     * 
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    private String getConfigValue(String key, String defaultValue) {
        String value = environment.getProperty(key, defaultValue);
        log.debug("获取配置 {} = {}", key, value);
        return value;
    }
    
    /**
     * 处理灰度逻辑：检查白名单和hash比例
     * 
     * @param custId 用户ID
     * @param whiteList 白名单配置
     * @param rate 比例配置
     * @return 是否开启灰度
     */
    public boolean processGrayLogic(String custId, String whiteList, String rate) {
        if (StringUtils.isEmpty(custId)) {
            log.error("用户ID为空，返回 gray = false");
            return false;
        }
        
        // 检查白名单
        if (!StringUtils.isEmpty(whiteList)) {
            List<String> whiteListArray = Arrays.asList(whiteList.split(","));
            if (whiteListArray.contains(custId)) {
                log.info("用户 {} 在白名单中，返回 gray = true", custId);
                return true;
            }
        }
        
        // 通过hash取余比例比重
        if (!StringUtils.isEmpty(rate)) {
            try {
                int rateValue = Integer.parseInt(rate);
                if (rateValue <= GrayEnums.RATE_MIN.getValue() || rateValue > GrayEnums.RATE_MAX.getValue()) {
                    log.error("灰度比例配置异常: {}, 返回 gray = false", rateValue);
                    return false;
                }
                
                // 使用custId的hash值取余100，判断是否小于配置的比例
                int hashMod = (custId.hashCode() & Integer.MAX_VALUE) % GrayEnums.HASH_MOD_BASE.getValue();
                boolean inGrayRange = hashMod < rateValue;
                
                log.info("用户 {} hash取余结果: {}, 灰度比例: {}, 是否命中灰度: {}", 
                         custId, hashMod, rateValue, inGrayRange);
                         
                return inGrayRange;
            } catch (NumberFormatException e) {
                log.error("灰度比例配置格式错误: {}", rate, e);
                return false;
            }
        }
        
        log.info("未配置灰度比例，返回 gray = false");
        return false;
    }
}
