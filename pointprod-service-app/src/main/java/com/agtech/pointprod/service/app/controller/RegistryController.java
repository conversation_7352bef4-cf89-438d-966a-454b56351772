package com.agtech.pointprod.service.app.controller;


import com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.CompletableFuture;

@ConditionalOnClass(NacosAutoServiceRegistration.class)
@RestController
@RequestMapping("/registry")
@RequiredArgsConstructor
@Slf4j
@Profile("!test")
public class RegistryController {
    private final NacosAutoServiceRegistration nacosAutoServiceRegistration;
    private final ApplicationContext context;
    private static final String LOCAL_HOST = "localhost";
    private static final String LOCAL_HOST_IP = "127.0.0.1";

    @Value("${stopService.waitTime:10000}")
    private int waitTime;

    @PostMapping("/deregister")
    public ResponseEntity<Boolean> deregister(HttpServletRequest request) {
        if (!LOCAL_HOST.equalsIgnoreCase(request.getServerName()) &&
                !LOCAL_HOST_IP.equalsIgnoreCase(request.getServerName())) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(false);
        }

        //异步从Nacos注销节点，等待一段时间后关闭容器
        CompletableFuture.runAsync(()->{
            log.info("Ready to stop service");
            nacosAutoServiceRegistration.stop();
            log.info("Nacos instance has been de-registered");
            log.info("Waiting {} milliseconds...", waitTime);
            try {
                Thread.sleep(waitTime);
            } catch (InterruptedException e) {
                log.info("interrupted!", e);
                Thread.currentThread().interrupt();
            }
            log.info("Closing application...");
            SpringApplication.exit(context);
            ((ConfigurableApplicationContext) context).close();
        });
        return ResponseEntity.ok(true);
    }
}