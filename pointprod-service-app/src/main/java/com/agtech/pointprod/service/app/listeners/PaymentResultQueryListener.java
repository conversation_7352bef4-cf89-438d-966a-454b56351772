package com.agtech.pointprod.service.app.listeners;

import java.io.IOException;

import javax.annotation.Resource;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import com.agtech.pointprod.order.service.app.service.PaymentService;
import com.agtech.pointprod.order.service.domain.model.PaymentResultUnknownQuery;
import com.agtech.pointprod.service.app.listeners.base.TaskRetryBaseListener;
import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class PaymentResultQueryListener extends TaskRetryBaseListener<PaymentResultUnknownQuery>{

    @Resource
    private PaymentService paymentService;

    @RabbitListener(queues = "${rabbitmq.queues.config.payment-result-unknown-query.queue}")
    public void onPaymentResultUnknownQuery(Message message) throws IOException {
        log.info("PaymentResultQueryListener listener triggered");
        super.onMessage(message, PaymentResultUnknownQuery.class);
    }

    @Override
    protected boolean doCheck(PaymentResultUnknownQuery message, MessageProperties properties) {
        if(message == null){
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR, "message body empty or parse error");
        }
        if(message.getResourceId() == null){
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR, "resource id empty");
        }
        return true;
    }

    @Override
    protected void doProcess(PaymentResultUnknownQuery message, MessageProperties properties) {
        paymentService.transferQuery(message);
        log.info("PaymentResultQueryListener doProcess done");
    }

    @Override
    protected MessageQueueEnum getMessageQueueEnum() {
        return MessageQueueEnum.PAYMENT_RESULT_UNKNOWN_QUERY;
    }

    @Override
    protected TaskResouceTypeEnum getTaskResouceTypeEnum() {
        return TaskResouceTypeEnum.MQ_CONSUME_PAYMENT_RESULT_UNKNOWN_QUERY;
    }

}
