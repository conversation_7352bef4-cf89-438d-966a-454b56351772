package com.agtech.pointprod.service.app.service;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.facade.dto.req.template.*;
import com.agtech.pointprod.service.facade.dto.rsp.template.AddTemplateRsp;
import com.agtech.pointprod.service.facade.dto.rsp.template.QueryTemplateInfoListRsp;
import com.agtech.pointprod.service.facade.dto.rsp.template.QueryTemplateRsp;
import com.zat.gateway.component.result.model.GwResult;

public interface TemplateService {
    GwResult<PageInfoDTO<QueryTemplateRsp>> queryValidTemplateListByPage(QueryTemplateReq req);

    GwResult<PageInfoDTO<QueryTemplateInfoListRsp>> queryTemplateFullInfoList(QueryTemplateInfoListReq request);

    GwResult<AddTemplateRsp> add(AddTemplateReq addTemplateReq);

    GwResult<Void> update(UpdateTemplateReq updateTemplateReq);

    GwResult<Void> delete(DeleteTemplateReq deleteTemplateReq);

} 