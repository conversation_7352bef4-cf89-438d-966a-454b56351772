package com.agtech.pointprod.service.app.digest;

import com.agtech.common.result.BaseResult;
import com.agtech.pointprod.service.app.digest.log.BaseServiceDigestLog;

/**
 * 服务摘要日志打包Helper
 * 
 * <AUTHOR>
 * @version $Id: ServiceDigestLogWrapHelper.java, v 0.1 2024年7月17日 17:03:17
 *          zhongqiang Exp $
 */
public class ServiceDigestLogWrapHelper {

	private ServiceDigestLogWrapHelper(){}

	/**
	 * 填充基础摘要信息
	 * 
	 * @param digestLog 摘要日志
	 * @param result    返回结果
	 * @param timeCost  接口处理耗时
	 */
	public static void fillBaseDigestInfo(BaseServiceDigestLog digestLog, BaseResult result,
										  long timeCost) {
		digestLog.setElapse(timeCost);
		digestLog.setSuccess(result.isSuccess());
		digestLog.setErrorContext(result.getErrorContext());
		digestLog.setReadableResultCode(result.getReadableResultCode());
	}
}
