package com.agtech.pointprod.service.app.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.agtech.pointprod.service.app.service.ShareService;
import com.agtech.pointprod.service.facade.dto.req.ShareLinkReq;
import com.agtech.pointprod.service.facade.dto.rsp.ShareLinkRsp;
import com.zat.gateway.component.result.model.GwResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 分享控制器
 */
@RestController
@RequestMapping("/fund/share")
@Slf4j
public class ShareController {
    
    @Resource
    private ShareService shareService;

    /**
     * 创建分享链接
     * @param req 分享链接请求
     * @param userId 用户ID
     * @param language 语言
     * @return 分享链接响应
     */
    @PostMapping("/link/generate")
    public GwResult<ShareLinkRsp> createShareLink(@RequestBody ShareLinkReq req,
                                                @RequestHeader(name = "MPayCustId", required = false) String userId,
                                                @RequestHeader(name = "Lang", required = false) String language) {
        req.setUserId(userId);
        req.setLang(language);
        return shareService.createShareLink(req);
    }
} 