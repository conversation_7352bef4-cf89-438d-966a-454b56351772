package com.agtech.pointprod.service.app.controller.admin;

import com.agtech.pointprod.service.app.service.LimitRuleAdminService;
import com.agtech.pointprod.service.facade.dto.req.AccumulateRuleAdminReq;
import com.agtech.pointprod.service.facade.dto.rsp.AccumulateRuleAdminRsp;
import com.agtech.pointprod.service.facade.dto.rsp.AccumulateRulePageResponse;
import com.alibaba.fastjson2.JSONObject;
import com.zat.gateway.component.result.model.GwResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Transfer Rule Admin RPC Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/transfer/rule")
public class LimitRuleAdminController {
    @Resource
    private LimitRuleAdminService limitRuleAdminService;
    /**
     * Query transfer rule list
     */
    @PostMapping("/list")
    public GwResult<AccumulateRulePageResponse> list(@RequestBody AccumulateRuleAdminReq req) {
        log.info("admin LimitRuleAdminController list request: {}", JSONObject.toJSONString(req));
        GwResult<AccumulateRulePageResponse> result = limitRuleAdminService.selectCumulateRuleList(req);
        log.info("admin LimitRuleAdminController list response: {}", JSONObject.toJSONString(result));
        return result;
    }
    
    /**
     * Add new transfer rule
     */
    @PostMapping("/add")
    public GwResult<Boolean> add(@RequestBody AccumulateRuleAdminReq req) {
        log.info("admin LimitRuleAdminController add request: {}", JSONObject.toJSONString(req));
        GwResult<Boolean> result = limitRuleAdminService.addCumulateRule(req);
        log.info("admin LimitRuleAdminController add response: {}", JSONObject.toJSONString(result));
        return result;
    }
    
    /**
     * Update transfer rule
     */
    @PostMapping("/update")
    public GwResult<Boolean> update(@RequestBody AccumulateRuleAdminReq req) {
        log.info("admin LimitRuleAdminController update request: {}", JSONObject.toJSONString(req));
        GwResult<Boolean> result = limitRuleAdminService.updateCumulateRule(req);
        log.info("admin LimitRuleAdminController update response: {}", JSONObject.toJSONString(result));
        return result;
    }
    
    /**
     * Get transfer rule detail
     */
    @PostMapping("/detail")
    public GwResult<AccumulateRuleAdminRsp> detail(@RequestBody AccumulateRuleAdminReq req) {
        log.info("admin LimitRuleAdminController detail request: {}", JSONObject.toJSONString(req));
        GwResult<AccumulateRuleAdminRsp> result = limitRuleAdminService.getCumulateRuleDetail(req);
        log.info("admin LimitRuleAdminController detail response: {}", JSONObject.toJSONString(result));
        return result;
    }
    
    /**
     * Delete transfer rule
     * Can only delete disabled rules, and there must be other enabled rules at the same level
     */
    @PostMapping("/delete")
    public GwResult<Boolean> delete(@RequestBody AccumulateRuleAdminReq req) {
        log.info("admin LimitRuleAdminController delete request: {}", JSONObject.toJSONString(req));
        GwResult<Boolean> result = limitRuleAdminService.deleteCumulateRule(req);
        log.info("admin LimitRuleAdminController delete response: {}", JSONObject.toJSONString(result));
        return result;
    }
} 