package com.agtech.pointprod.service.app.converter;

import com.agtech.pointprod.service.domain.model.Template;
import com.agtech.pointprod.service.facade.dto.req.template.AddTemplateReq;
import com.agtech.pointprod.service.facade.dto.req.template.UpdateTemplateReq;
import com.agtech.pointprod.service.facade.dto.rsp.template.QueryTemplateInfoListRsp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/24 19:48
 */
@Mapper(componentModel = "spring")
public interface TemplateDTOConverter {

    List<QueryTemplateInfoListRsp> templateList2InfoList(List<Template> templates);

    @Mapping(source = "operator", target = "creator")
    @Mapping(source = "operator", target = "modifier")
    Template addTemplate2Template(AddTemplateReq addTemplateReq);

    @Mapping(source = "operator", target = "modifier")
    Template updateTemplate2Template(UpdateTemplateReq updateTemplateReq);

}
