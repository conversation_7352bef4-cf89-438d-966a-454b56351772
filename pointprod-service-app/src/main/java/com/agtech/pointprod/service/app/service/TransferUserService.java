package com.agtech.pointprod.service.app.service;

import com.agtech.pointprod.service.facade.dto.req.PayeeInfoValidateReq;
import com.agtech.pointprod.service.facade.dto.req.PayerValidateReq;
import com.agtech.pointprod.service.facade.dto.req.QueryHistoryPayeeListReq;
import com.agtech.pointprod.service.facade.dto.req.QueryTransferUserInfoReq;
import com.agtech.pointprod.service.facade.dto.rsp.PayeeInfoValidateRsp;
import com.agtech.pointprod.service.facade.dto.rsp.PayerValidateRsp;
import com.agtech.pointprod.service.facade.dto.rsp.QueryHistoryPayeeListRsp;
import com.agtech.pointprod.service.facade.dto.rsp.QueryTransferUserInfoRsp;
import com.zat.gateway.component.result.model.GwResult;

public interface TransferUserService {
    GwResult<QueryHistoryPayeeListRsp> queryHistoryPayeeList(QueryHistoryPayeeListReq req);

    GwResult<QueryTransferUserInfoRsp> queryTransferUserInfo(QueryTransferUserInfoReq req);

    GwResult<PayeeInfoValidateRsp> payeeInfoValidate(PayeeInfoValidateReq req);
    
    GwResult<PayerValidateRsp> payerValidate(PayerValidateReq req);
}
