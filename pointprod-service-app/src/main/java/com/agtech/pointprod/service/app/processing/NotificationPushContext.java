package com.agtech.pointprod.service.app.processing;

import java.util.List;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.service.domain.model.NotificationRecord;

import lombok.Getter;
import lombok.Setter;

/**
 * 通知推送上下文
 */
@Getter
@Setter
public class NotificationPushContext {
    
    /**
     * 原始消息体（仅用于日志记录）
     */
    private String messageBody;
    
    /**
     * 通知ID
     */
    private String notificationId;
    
    /**
     * 用户ID列表
     */
    private List<String> userIds;
    
    /**
     * 通知记录列表
     */
    private List<NotificationRecord> notifications;
    
    /**
     * 成功创建的通知记录列表
     */
    private List<NotificationRecord> successNotifications;
    
    /**
     * 成功推送的通知记录列表
     */
    private List<NotificationRecord> successPushedNotifications;
    
    /**
     * 创建是否成功
     */
    private Boolean createSuccess;
    
    /**
     * 推送是否成功
     */
    private Boolean pushSuccess;
    
    /**
     * 订单基础信息
     */
    private BaseOrderInfo fundOrder;
    
    /**
     * 兼容旧版，返回第一个通知记录
     * @return 第一个通知记录，如果不存在则返回null
     */
    public NotificationRecord getNotification() {
        return notifications != null && !notifications.isEmpty() ? notifications.get(0) : null;
    }
    
    /**
     * 兼容旧版，返回第一个用户ID
     * @return 第一个用户ID，如果不存在则返回null
     */
    public String getUserId() {
        return userIds != null && !userIds.isEmpty() ? userIds.get(0) : null;
    }
} 