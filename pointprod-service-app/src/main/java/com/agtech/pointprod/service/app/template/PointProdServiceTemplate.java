package com.agtech.pointprod.service.app.template;

import com.agtech.common.lang.util.StringUtil;
import com.agtech.common.result.BaseResult;
import com.agtech.common.result.ErrorContext;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.MessageUtil;
import com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants;
import com.alibaba.fastjson2.JSONObject;
import com.zat.gateway.component.result.enums.GwResultCode;
import com.zat.gateway.component.result.model.GwResult;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import static com.zat.gateway.component.result.enums.GwResultCode.FAILED_SYSTEM;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 15:57
 */
@Slf4j
public class PointProdServiceTemplate {

    private PointProdServiceTemplate(){}

    /**
     * execute
     * 对于非业务异常，抛出的是系统错误的错误码
     * 摘要日志样例数据：
     * 成功日志
     * [(PointProdServiceTemplate.main,0ms,Y,-|-,1)] digestLog:{"elapse":0,"errorContext":{"errorStack":[]},"invocationSignature":"PointProdServiceTemplate.main","request":"123","response":"456","success":true,"version":"1"}
     * 失败日志
     * pointprod service process, occur unknown exception:测试异常
     *      错误栈...
     *
     * 失败摘要日志
     * [(PointProdServiceTemplate.main,16ms,N,SYSTEM_ERROR|AE0522090049@system error@,1)] digestLog:{"elapse":16,"errorContext":{"errorStack":[{"codeLevel":"5","codeType":"2","errorName":"system error","errorSpecific":"049","systemCode":"2090"}]},"invocationSignature":"PointProdServiceTemplate.main","readableResultCode":"SYSTEM_ERROR","request":"123","success":false,"version":"1"}
     * @param serviceName	服务名：类名.方法名，例如：NyTestService.test
     * @param result   		返回结果，出现异常时填充异常信息
     * @param callback 		回调，业务处理回调，composeDigestLog方法返回摘要日志对象
     */
    public static void execute(String serviceName, BaseResult result, PointProdServiceCallback callback) {
        MDC.put(MDCConstants.MDC_KEY_SERVICE_NAME, serviceName);
        long begin = System.currentTimeMillis();
        try {
            callback.checkParameter();
            callback.process();
        } catch (PointProdBizException ex) {
            log.error("pointprod service process, occur biz exception:{}", ex.getMessage(), ex);
            callback.processAfterPointProdBizException(result, ex);
        } catch (Throwable throwable) {
            log.error(MessageUtil.format("pointprod service process, occur unknown exception:{0}", throwable.getMessage()), throwable);
            callback.processAfterThrowable(result, throwable);
        } finally {
            long timeCost = System.currentTimeMillis() - begin;
            BaseDigestLog baseDigestLog = callback.composeDigestLog(timeCost);
            if (baseDigestLog != null) {
                try {
                    // 设置MDC
                    setMDC(baseDigestLog);
                    baseDigestLog.setInvocationSignature(serviceName);
                    log.info("[{}] {} digestLog:{}", serviceName, baseDigestLog.toDigest(), JSONObject.toJSONString(baseDigestLog));
                } finally {
                    // 移除MDC
                    removeMDC();
                }
            }
        }
    }

    private static void setMDC(BaseDigestLog baseDigestLog){
        try {
            MDC.put(MDCConstants.MDC_KEY_ELAPSED_TIME, String.valueOf(baseDigestLog.getElapse()));
            if (baseDigestLog.isSuccess()) {
                MDC.put(MDCConstants.MDC_KEY_CODE, "Y");
                MDC.put(MDCConstants.MDC_KEY_SUB_CODE, GwResultCode.SUCCESS.getSubCode());
                MDC.put(MDCConstants.MDC_KEY_SUB_MSG, GwResultCode.SUCCESS.getSubMsg());
            } else {
                MDC.put(MDCConstants.MDC_KEY_CODE, "N");
                ErrorContext errorContext = baseDigestLog.getErrorContext();
                if(errorContext != null) {
                    MDC.put(MDCConstants.MDC_KEY_SUB_CODE, errorContext.fetchCurrentErrorCode());
                    MDC.put(MDCConstants.MDC_KEY_SUB_MSG, errorContext.fetchCurrentErrorMsg());
                }
            }
        } catch (Exception e) {
            log.error("设置MDC异常", e);
        }
    }

    private static void removeMDC(){
        try {
            MDC.remove(MDCConstants.MDC_KEY_SERVICE_NAME);
            MDC.remove(MDCConstants.MDC_KEY_ELAPSED_TIME);
            MDC.remove(MDCConstants.MDC_KEY_CODE);
            MDC.remove(MDCConstants.MDC_KEY_SUB_CODE);
            MDC.remove(MDCConstants.MDC_KEY_SUB_MSG);
        } catch (Exception e) {
            log.error("移除MDC异常", e);
        }
    }

    /**
     * 将GenericResult返回结果转换为GwResult
     * 
     * @param <T>
     * @param result
     * @return
     */
    @SuppressWarnings("unchecked")
	public static <T> GwResult<T> convertToGwResult(GenericResult<T> result){
    	if(result.isSuccess()) {
            return GwResult.success(GwResultCode.SUCCESS.getSubCode(), GwResultCode.SUCCESS.getSubMsg(), result.getValue());
    	}
    	ErrorContext errorContext = result.getErrorContext();
    	if(errorContext != null) {
    		return GwResult.failOfBusiness(errorContext.fetchCurrentErrorCode(), errorContext.fetchCurrentErrorMsg());
    	}
    	return GwResult.failOfSystem(FAILED_SYSTEM.getSubCode(), FAILED_SYSTEM.getSubMsg());
    }


}
