package com.agtech.pointprod.service.app.processing.step;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.agtech.common.result.GenericResult;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.order.service.domain.common.enums.SubOrderTypeEnum;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.service.OrderDomainService;
import com.agtech.pointprod.order.service.domain.service.PayDomainService;
import com.agtech.pointprod.order.service.facade.dto.FundAmountDTO;
import com.agtech.pointprod.service.app.processing.TransferRecordsListContext;
import com.agtech.pointprod.service.domain.common.enums.NumbersEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.MPayUserInfoGateway;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.TransferRecord;
import com.agtech.pointprod.service.facade.dto.req.TransferRecordsListReq;
import com.agtech.pointprod.service.facade.dto.rsp.TransferRecordsListRsp;

import lombok.extern.slf4j.Slf4j;

/**
 * 转赠记录处理步骤
 */
@Slf4j
@Component
public class TransferRecordStep {
    
    @Resource
    private PayDomainService payDomainService;
    
    @Resource
    private OrderDomainService orderDomainService;
    
    @Resource
    private MPayUserInfoGateway mPayUserInfoGateway;
    
    /**
     * 构建上下文
     * 
     * @param req 请求参数
     * @return 构建好的上下文
     */
    public TransferRecordsListContext buildContext(TransferRecordsListReq req) {
        TransferRecordsListContext context = new TransferRecordsListContext();
        context.setUserId(req.getUserId());
        context.setSubOrderType(req.getSubOrderType());
        context.setCurrentPage(req.getCurrentPage());
        context.setPerPage(req.getPerPage() == null ? NumbersEnum.TWENTY.getIntValue() : req.getPerPage());
        return context;
    }
    
    /**
     * 查询转赠记录列表
     * 
     * @param context 上下文
     */
    public void queryTransferRecordList(TransferRecordsListContext context) {
        Date now = ZonedDateUtil.now();
        // 计算3个月前的时间（业务规则：只查询3个月内的记录）
        Date threeMonthsAgo = ZonedDateUtil.add(now, Calendar.MONTH, -NumbersEnum.THREE.getIntValue());
        
        // 1. 查询FundPay记录列表
        List<String> fundOrderIds = payDomainService.queryFundPayList(
                context.getUserId(), 
                context.getSubOrderType(),
                threeMonthsAgo,
                context.getCurrentPage(), 
                context.getPerPage()
        );
        
        if (CollectionUtils.isEmpty(fundOrderIds)) {
            context.setTransferRecords(Collections.emptyList());
            context.setTotal(0L);
            context.setLastPage(NumbersEnum.ZERO.getIntValue());
            return;
        }
        
        // 3. 批量查询订单信息
        Map<String, BaseOrderInfo> fundOrderMap = orderDomainService.queryFundOrdersByIds(fundOrderIds);
        
        // 4. 按照原始顺序构建转赠记录
        List<TransferRecord> transferRecords = buildTransferRecords(fundOrderIds, fundOrderMap, context.getUserId());
        
        // 5. 统计总数
        Long total = payDomainService.countTransferRecords(
                context.getUserId(), 
                context.getSubOrderType(),
                threeMonthsAgo
        );
        
        // 计算最后一页页数
        Integer lastPage = (int) Math.ceil((double) total / context.getPerPage());
        
        context.setTransferRecords(transferRecords);
        context.setTotal(total);
        context.setLastPage(lastPage);
    }
    
    /**
     * 构建转赠记录列表
     * 
     * @param fundPayDomainList 原始有序的FundPay列表
     * @param fundOrderMap 订单信息映射
     * @param userId 当前用户ID
     * @return 转赠记录列表
     */
    private List<TransferRecord> buildTransferRecords(List<String> fundOrderIds, Map<String, BaseOrderInfo> fundOrderMap, String userId) {
        if (fundOrderMap == null || fundOrderMap.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 收集所有用户ID，用于批量查询
        List<String> userIds = new ArrayList<>();
        for (Map.Entry<String, BaseOrderInfo> entry : fundOrderMap.entrySet()) {
            BaseOrderInfo baseOrderInfo = entry.getValue();
            if (baseOrderInfo != null) {
                // 收集付款方用户ID
                if (baseOrderInfo.getPayOrderInfoList() != null) {
                    for (com.agtech.pointprod.order.service.domain.model.PayOrderInfo payOrderInfo : baseOrderInfo.getPayOrderInfoList()) {
                        if (payOrderInfo.getPayer() != null && payOrderInfo.getPayer().getUserId() != null) {
                            userIds.add(payOrderInfo.getPayer().getUserId());
                        }
                    }
                }
                
                // 收集收款方用户ID
                if (baseOrderInfo.getAcceptOrderInfoList() != null) {
                    for (com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo acceptOrderInfo : baseOrderInfo.getAcceptOrderInfoList()) {
                        if (acceptOrderInfo.getPayee() != null && acceptOrderInfo.getPayee().getUserId() != null) {
                            userIds.add(acceptOrderInfo.getPayee().getUserId());
                        }
                    }
                }
            }
        }
        
        // 批量查询用户信息
        List<MPayUserInfo> userInfoList = new ArrayList<>();
        // 对用户ID列表进行去重
        List<String> distinctUserIds = userIds.stream()
                .distinct()
                .collect(Collectors.toList());
                
        userInfoList = mPayUserInfoGateway.getUserMsgList(distinctUserIds);
        if(CollectionUtils.isEmpty(userInfoList)) {
            log.error("Failed to get user information for userIds: {}", distinctUserIds);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
        Map<String, MPayUserInfo> userInfoMap = userInfoList.stream()
                .collect(Collectors.toMap(MPayUserInfo::getCustId, v -> v, (v1, v2) -> v1));
        
        List<TransferRecord> result = new ArrayList<>();
        
        // 按照原始顺序遍历FundPayDomainList以保持顺序
        for (String orderId : fundOrderIds) {
            BaseOrderInfo baseOrderInfo = fundOrderMap.get(orderId);
            
            if (baseOrderInfo == null) {
                continue;
            }
            
            TransferRecord record = new TransferRecord();
            
            // 设置基本信息
            record.setFundOrderId(orderId);
            record.setUserId(userId);
            
            // 设置订单状态和备注
            record.setFundOrderStatus(baseOrderInfo.getOrderStatus() != null ? baseOrderInfo.getOrderStatus().getCode() : null);
            record.setMsg(baseOrderInfo.getMemo());
            
            // 设置时间信息
            if (baseOrderInfo.getCompleteTime() != null) {
                record.setPaidTime(baseOrderInfo.getCompleteTime());
            }
            record.setGmtCreate(baseOrderInfo.getCreatedTime());
            
            // 设置付款和收款信息
            List<TransferRecord.PayOrderInfo> payOrderInfoList = new ArrayList<>();
            List<TransferRecord.PayOrderInfo> acceptOrderInfoList = new ArrayList<>();
            
            // 标记用户角色
            boolean isUserPayer = false;
            boolean isUserPayee = false;
            
            // 处理付款信息
            if (baseOrderInfo.getPayOrderInfoList() != null) {
                for (com.agtech.pointprod.order.service.domain.model.PayOrderInfo payOrderInfo : baseOrderInfo.getPayOrderInfoList()) {
                    if (payOrderInfo.getPayer() != null) {
                        String payerUserId = payOrderInfo.getPayer().getUserId();
                        MPayUserInfo mPayUserInfo = userInfoMap.get(payerUserId);
                        
                        TransferRecord.PayOrderInfo userInfo = new TransferRecord.PayOrderInfo();
                        userInfo.setUserId(payerUserId);
                        
                        if (mPayUserInfo != null) {
                            // 从MPayUserInfo获取用户信息
                            userInfo.setNickName(mPayUserInfo.getNickName() != null ? mPayUserInfo.getNickName() : "");
                            userInfo.setHeadImg(mPayUserInfo.getHeadLogo() != null ? mPayUserInfo.getHeadLogo() : "");
                            userInfo.setAreaCode(mPayUserInfo.getAreaCode());
                            userInfo.setPhone(mPayUserInfo.getPhone());
                        }
                        
                        // 检查当前用户是否为付款人
                        if (userId.equals(payOrderInfo.getPayer().getUserId())) {
                            isUserPayer = true;
                            // 设置付款方金额
                            if (payOrderInfo.getPayAmount() != null) {
                                record.setFundAmount(payOrderInfo.getPayAmount().getAmount());
                            }
                        }
                        
                        payOrderInfoList.add(userInfo);
                    }
                }
            }
            
            // 处理收款信息 
            if (baseOrderInfo.getAcceptOrderInfoList() != null) {
                for (com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo acceptOrderInfo : baseOrderInfo.getAcceptOrderInfoList()) {
                    if (acceptOrderInfo.getPayee() != null) {
                        String payeeUserId = acceptOrderInfo.getPayee().getUserId();
                        MPayUserInfo mPayUserInfo = userInfoMap.get(payeeUserId);
                        
                        TransferRecord.PayOrderInfo userInfo = new TransferRecord.PayOrderInfo();
                        userInfo.setUserId(payeeUserId);
                        
                        if (mPayUserInfo != null) {
                            // 从MPayUserInfo获取用户信息
                            userInfo.setNickName(mPayUserInfo.getNickName() != null ? mPayUserInfo.getNickName() : "");
                            userInfo.setHeadImg(mPayUserInfo.getHeadLogo() != null ? mPayUserInfo.getHeadLogo() : "");
                            userInfo.setAreaCode(mPayUserInfo.getAreaCode());
                            userInfo.setPhone(mPayUserInfo.getPhone());
                        }
                        
                        // 检查当前用户是否为收款人
                        if (userId.equals(acceptOrderInfo.getPayee().getUserId())) {
                            isUserPayee = true;
                            // 设置收款方金额
                            if (acceptOrderInfo.getAcceptAmount() != null) {
                                record.setFundAmount(acceptOrderInfo.getAcceptAmount().getAmount());
                            }
                        }
                        
                        acceptOrderInfoList.add(userInfo);
                    }
                }
            }
            
            if (isUserPayer) {
                record.setUserOrderRole(SubOrderTypeEnum.PAY.getCode()); 
            } else if (isUserPayee) {
                record.setUserOrderRole(SubOrderTypeEnum.ACCEPT.getCode()); 
            }
            
            record.setPayOrderInfoList(payOrderInfoList);
            record.setAcceptOrderInfoList(acceptOrderInfoList);
            
            result.add(record);
        }
        
        return result;
    }
    
    /**
     * 构建响应结果
     * 
     * @param context 上下文
     */
    public void buildResponse(TransferRecordsListContext context) {
        TransferRecordsListRsp response = new TransferRecordsListRsp();
        
        // 设置分页信息
        response.setCurrentPage(context.getCurrentPage());
        response.setLastPage(context.getLastPage());
        response.setPerPage(context.getPerPage());
        response.setTotal(context.getTotal());
        
        // 转换转赠记录列表
        List<TransferRecordsListRsp.TransferRecordItem> items = context.getTransferRecords().stream()
                .map(this::convertToTransferRecordItem)
                .collect(Collectors.toList());
        
        response.setList(items);
        context.setResult(GenericResult.success(response));
    }
    
    /**
     * 转换转赠记录为响应项
     * 
     * @param record 转赠记录
     * @return 响应项
     */
    private TransferRecordsListRsp.TransferRecordItem convertToTransferRecordItem(TransferRecord record) {
        TransferRecordsListRsp.TransferRecordItem item = new TransferRecordsListRsp.TransferRecordItem();
        
        item.setOrderId(record.getFundOrderId());
        item.setUserOrderRole(record.getUserOrderRole());
        item.setFundOrderStatus(record.getFundOrderStatus());
        item.setMemo(record.getMsg());
        
        // 设置FundAmountDTO对象
        item.setFundAmount(new FundAmountDTO("mCoin", record.getFundAmount()));
        
        // 格式化支付时间 - 使用ZonedDateUtil
        if (record.getPaidTime() != null) {
            item.setPaidTime(ZonedDateUtil.formatDate(record.getPaidTime()));
        }
        
        // 转换付款和收款信息
        List<TransferRecordsListRsp.PayOrderInfo> payOrderInfoList = new ArrayList<>();
        List<TransferRecordsListRsp.PayOrderInfo> acceptOrderInfoList = new ArrayList<>();
        
        // 转换付款方信息
        if (record.getPayOrderInfoList() != null) {
            for (TransferRecord.PayOrderInfo payInfo : record.getPayOrderInfoList()) {
                TransferRecordsListRsp.PayOrderInfo rspPayInfo = new TransferRecordsListRsp.PayOrderInfo();
                rspPayInfo.setNickName(payInfo.getNickName());
                rspPayInfo.setHeadImg(payInfo.getHeadImg());
                rspPayInfo.setPhone(payInfo.getPhone());
                rspPayInfo.setAreaCode(payInfo.getAreaCode());
                payOrderInfoList.add(rspPayInfo);
            }
        }
        
        // 转换收款方信息
        if (record.getAcceptOrderInfoList() != null) {
            for (TransferRecord.PayOrderInfo acceptInfo : record.getAcceptOrderInfoList()) {
                TransferRecordsListRsp.PayOrderInfo rspAcceptInfo = new TransferRecordsListRsp.PayOrderInfo();
                rspAcceptInfo.setNickName(acceptInfo.getNickName());
                rspAcceptInfo.setHeadImg(acceptInfo.getHeadImg());
                rspAcceptInfo.setPhone(acceptInfo.getPhone());
                rspAcceptInfo.setAreaCode(acceptInfo.getAreaCode());
                acceptOrderInfoList.add(rspAcceptInfo);
            }
        }
        
        item.setPayOrderInfoList(payOrderInfoList);
        item.setAcceptOrderInfoList(acceptOrderInfoList);
        
        return item;
    }
}