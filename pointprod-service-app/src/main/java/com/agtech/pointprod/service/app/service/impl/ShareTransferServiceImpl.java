package com.agtech.pointprod.service.app.service.impl;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.service.ShareTransferService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.ShareTransferReq;
import com.agtech.pointprod.service.facade.dto.rsp.ShareTransferRsp;
import com.alibaba.fastjson2.JSONObject;

import lombok.extern.slf4j.Slf4j;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_TRANSFER_SHORT_TO_LONG_SHARE_LINK;

/**
 * 短链转长链服务实现
 */
@Slf4j
@Service
public class ShareTransferServiceImpl implements ShareTransferService {
    
    @Value("${point.share.origin.domain:}")
    private String shareOriginLinkConfig;
    
    @Value("${point.h5.domain:}")
    private String shareTransferLinkConfig;

    @Value("${point.gateway.domain:}")
    private String gatewayUrl;

    @Value("${mPay.push.appId:}")
    private String mPayPushAppId;

    /**
     * 分享码参数名
     */
    private static final String SHARE_CODE_PARAM = "/transfer/index.html#/records?type=ACCEPT&shareCode=";
    
    /**
     * 页面ID参数名
     */
    private static final String PAGE_ID_PARAM = "/callApp/index.html?pageid=";

    private static final String GATEWAY_PATH = "/gateway/gateway-auth/mpay/auth/authorize?appId=";

    private static final String GATEWAY_REDIRECT = "&redirectUrl=";



    @Override
    public GenericResult<ShareTransferRsp> transferShortToLong(ShareTransferReq req) {
        log.info("transferShortToLong req={}", JSONObject.toJSONString(req));
        final ResultContainer<GenericResult<ShareTransferRsp>> container = new ResultContainer<>(new GenericResult<ShareTransferRsp>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_TRANSFER_SHORT_TO_LONG_SHARE_LINK, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
            }

            @Override
            public void process() {
                // 从nacos配置读取基础长链地址
                if (StringUtils.isBlank(shareOriginLinkConfig)) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.SHARE_SHORT_LINK_CONFIG_NOT_FOUND);
                }
                if (StringUtils.isBlank(shareTransferLinkConfig)) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.SHARE_TRANSFER_LINK_CONFIG_NOT_FOUND);
                }
                AssertUtil.assertNotBlank(gatewayUrl, PointProdBizErrorCodeEnum.PARAMS_ERROR, "網關地址不能為空", "gatewayUrl param error");
                String longUrl = buildLongUrl(shareOriginLinkConfig, shareTransferLinkConfig, gatewayUrl, req.getShortLink());
                
                ShareTransferRsp rsp = new ShareTransferRsp();
                rsp.setLongUrl(longUrl);
                
                container.setResult(GenericResult.success(rsp));
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        
        return container.getResult();
    }
    
    /**
     * 构建长链URL
     * @param shortLinkBase 短链基础配置
     * @param transferLinkBase 转换链接基础配置
     * @param shortLink 短链标识
     * @return 完整的长链URL
     */
    private String buildLongUrl(String shortLinkBase, String transferLinkBase, String gatewayUrl, String shortLink) {
        try {
            String inUrl = transferLinkBase + SHARE_CODE_PARAM + shortLink;
            String encodedInUrl = URLEncoder.encode(inUrl, "UTF-8");

            String innerUrl = gatewayUrl + GATEWAY_PATH + mPayPushAppId + GATEWAY_REDIRECT + encodedInUrl;
            
            // URL编码内部URL
            String encodedInnerUrl = URLEncoder.encode(innerUrl, "UTF-8");
            
            return shortLinkBase + PAGE_ID_PARAM + encodedInnerUrl;
            
        } catch (UnsupportedEncodingException e) {
            log.error("URL编码失败, shortLink={}", shortLink, e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.URL_ENCODING_FAILED);
        }
    }
} 