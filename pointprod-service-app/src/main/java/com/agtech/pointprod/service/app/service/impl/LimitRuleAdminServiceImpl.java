package com.agtech.pointprod.service.app.service.impl;

import javax.annotation.Resource;

import com.agtech.pointprod.service.app.processing.step.admin.limit.rule.*;
import com.agtech.pointprod.service.app.service.LimitRuleAdminService;
import com.agtech.pointprod.service.domain.common.enums.TransferRuleStatusEnum;
import com.agtech.pointprod.service.facade.dto.rsp.AccumulateRulePageResponse;
import com.agtech.pointprod.service.infrastructure.common.enums.UserLevelEnum;
import com.zat.gateway.component.result.model.GwResult;
import org.springframework.stereotype.Service;
import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.facade.dto.req.AccumulateRuleAdminReq;
import com.agtech.pointprod.service.facade.dto.rsp.AccumulateRuleAdminRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.*;

/**
 * Transfer rule admin service implementation
 * This service handles both limit rules (via AccumulateProductGateway) and transfer rules (via TransferRuleGateway)
 * Business logic flow: First maintain CumulateRule, then organize parameters to maintain TransferRule
 */
@Service
@Slf4j
public class LimitRuleAdminServiceImpl implements LimitRuleAdminService {

    @Resource
    private LimitRuleFullListQueryStep limitRuleFullListQueryStep;

    @Resource
    private LimitRuleEditStep limitRuleEditStep;

    @Resource
    private LimitRuleAddStep limitRuleAddStep;

    @Resource
    private LimitRuleDeleteStep limitRuleDeleteStep;

    @Resource
    private LimitRuleDetailStep limitRuleDetailStep;


    
    @Override
    public GwResult<AccumulateRulePageResponse> selectCumulateRuleList(AccumulateRuleAdminReq req) {
        final ResultContainer<GenericResult<AccumulateRulePageResponse>> container = new ResultContainer<>(new GenericResult<>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_LIMIT_SELECT_CUMULATE_RULE_LIST, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                // No specific parameters to check for list operation
            }
            
            @Override
            public void process() {
                GenericResult<AccumulateRulePageResponse> result = limitRuleFullListQueryStep.process(req);
                container.setResult(result);
            }
            
            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });

        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
    
    @Override
    public GwResult<Boolean> addCumulateRule(AccumulateRuleAdminReq req) {
        final ResultContainer<GenericResult<Boolean>> container = new ResultContainer<>(new GenericResult<>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_LIMIT_ADD_CUMULATE_RULE, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                
                if (StringUtils.isEmpty(req.getTitle())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
                
                // Check if user level exists in UserLevelEnum
                if (!UserLevelEnum.isValidLevelKey(req.getTitle())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }
                
                if (req.getTransferAmount() == null || req.getTransferAmountDay() == null) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
                
                // Check transferAmount is a positive integer with max 6 digits
                if (req.getTransferAmount() <= 0 || req.getTransferAmount() > 999999) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }
                
                // Check transferAmountDay is a positive integer with max 6 digits
                if (req.getTransferAmountDay() <= 0 || req.getTransferAmountDay() > 999999) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }
                
                // Validate gear values
                if (req.getGearVal() == null || req.getGearVal().isEmpty()) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
                
                // Check gearVal values are all positive integers with max 6 digits
                for (Integer gearValue : req.getGearVal()) {
                    if (gearValue == null || gearValue <= 0 || gearValue > 999999) {
                        throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                    }
                }
                
                // Validate transfer count parameters
                if (req.getTransferCount() == null || req.getTransferCountDay() == null) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
                
                // Check transferCount is a positive integer with max 6 digits
                if (req.getTransferCount() <= 0 || req.getTransferCount() > 999999) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }
                
                // Check transferCountDay is a positive integer with max 6 digits
                if (req.getTransferCountDay() <= 0 || req.getTransferCountDay() > 999999) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }
                
                // Validate status field is not empty
                if (StringUtils.isEmpty(req.getStatus())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
                
                // Validate status value is valid according to TransferRuleStatusEnum
                if (TransferRuleStatusEnum.getByValue(req.getStatus()) == null) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }
                
                // Validate username for audit
                if (StringUtils.isEmpty(req.getUsername())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
            }
            
            @Override
            public void process() {
                GenericResult<Boolean> result = limitRuleAddStep.process(req);
                container.setResult(result);
            }
            
            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
    
    @Override
    public GwResult<Boolean> updateCumulateRule(AccumulateRuleAdminReq req) {
        final ResultContainer<GenericResult<Boolean>> container = new ResultContainer<>(new GenericResult<>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_LIMIT_UPDATE_CUMULATE_RULE, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                if (StringUtils.isEmpty(req.getRuleId()) || StringUtils.isEmpty(req.getTitle())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
                // Check if user level exists in UserLevelEnum
                if (!UserLevelEnum.isValidLevelKey(req.getTitle())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }

                if (req.getTransferAmount() == null || req.getTransferAmountDay() == null) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }

                // Check transferAmount is a positive integer with max 6 digits
                if (req.getTransferAmount() <= 0 || req.getTransferAmount() > 999999) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }

                // Check transferAmountDay is a positive integer with max 6 digits
                if (req.getTransferAmountDay() <= 0 || req.getTransferAmountDay() > 999999) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }

                // Validate gear values
                if (req.getGearVal() == null || req.getGearVal().isEmpty()) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }

                // Check gearVal values are all positive integers with max 6 digits
                for (Integer gearValue : req.getGearVal()) {
                    if (gearValue == null || gearValue <= 0 || gearValue > 999999) {
                        throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                    }
                }

                // Validate transfer count parameters
                if (req.getTransferCount() == null || req.getTransferCountDay() == null) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }

                // Check transferCount is a positive integer with max 6 digits
                if (req.getTransferCount() <= 0 || req.getTransferCount() > 999999) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }

                // Check transferCountDay is a positive integer with max 6 digits
                if (req.getTransferCountDay() <= 0 || req.getTransferCountDay() > 999999) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }

                // Validate status field is not empty
                if (StringUtils.isEmpty(req.getStatus())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }

                // Validate status value is valid according to TransferRuleStatusEnum
                if (TransferRuleStatusEnum.getByValue(req.getStatus()) == null) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }

                // Validate username for audit
                if (StringUtils.isEmpty(req.getUsername())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
            }
            
            @Override
            public void process() {
                GenericResult<Boolean> result = limitRuleEditStep.process(req);
                container.setResult(result);
            }
            
            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
    
    @Override
    public GwResult<AccumulateRuleAdminRsp> getCumulateRuleDetail(AccumulateRuleAdminReq req) {
        final ResultContainer<GenericResult<AccumulateRuleAdminRsp>> container = new ResultContainer<>(new GenericResult<>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_LIMIT_GET_CUMULATE_RULE_DETAIL, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                if (StringUtils.isEmpty(req.getRuleId())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
            }
            
            @Override
            public void process() {
                GenericResult<AccumulateRuleAdminRsp> result = limitRuleDetailStep.process(req);
                container.setResult(result);
            }
            
            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
    
    @Override
    public GwResult<Boolean> deleteCumulateRule(AccumulateRuleAdminReq req) {
        final ResultContainer<GenericResult<Boolean>> container = new ResultContainer<>(new GenericResult<>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_LIMIT_DELETE_CUMULATE_RULE, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                if (StringUtils.isEmpty(req.getRuleId())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
                
                if (StringUtils.isEmpty(req.getUsername())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_MISSING);
                }
            }
            
            @Override
            public void process() {
                GenericResult<Boolean> result = limitRuleDeleteStep.process(req);
                container.setResult(result);
            }
            
            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
} 