package com.agtech.pointprod.service.app.listeners.base;

import com.agtech.pointprod.service.infrastructure.common.utils.ExplicitTransaction;
import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.config.RetryStrategyProperties.RetryType;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.model.MqTaskConfig;
import com.agtech.pointprod.service.domain.model.TaskData;
import com.agtech.pointprod.service.domain.model.TaskRetry;
import com.agtech.pointprod.service.domain.service.MessageReliabilityDomainService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;

import javax.annotation.Resource;

@Slf4j
public abstract class TaskRetryBaseListener <T extends TaskData> {

    @Resource
    protected MessageReliabilityDomainService messageReliabilityDomainService;
    
    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private ExplicitTransaction transaction;

    protected void onMessage(Message message, Class<T> dataClass){
        String msg = new String(message.getBody());
        MessageProperties properties = message.getMessageProperties();
        log.info("TaskRetryBaseListener onMessage message={}, properties: {}", msg, properties);

        if (StringUtils.isBlank(properties.getMessageId())) {
            log.error("TaskRetryBaseListener onMessage messageId is null");
            return;
        }
        RetryType retryType = null;
        TaskRetry ensureMessageConsume = null;
        try{
            T taskData = objectMapper.readValue(msg, dataClass);
            if (doCheck(taskData, properties)) {
                // 确保消息已消费
                ensureMessageConsume = messageReliabilityDomainService.ensureMessageConsume(properties.getMessageId(),
                         taskData.getResourceId(), getTaskResouceTypeEnum(), getMessageQueueEnum(), taskData);
                if (ensureMessageConsume == null) {
                    log.error("TaskRetryBaseListener onMessage ensureMessageDelivery error");
                    return;
                }
                if (ensureMessageConsume.isFinished()) {
                    log.info("message already consumed, taskRetryId={}", ensureMessageConsume.getTaskRetryId());
                    return;
                }
                retryType = ((MqTaskConfig)ensureMessageConsume.getTaskConfig()).getRetryType();
                 // 处理业务逻辑
                doProcess(taskData, properties);
                // 标记消息已成功投递
                boolean markConsumeFinish = messageReliabilityDomainService.markConsumeFinish(ensureMessageConsume.getTaskRetryId(), dataClass);
                log.info("message marked as consumed {}, taskRetryId={}",
                        markConsumeFinish, ensureMessageConsume.getTaskRetryId());
                if (!markConsumeFinish) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR, "message marked as consumed failed");
                }
            } else {
                log.info("TaskRetryBaseListener onMessage doCheck false");
            }
        } catch(Exception e) {
            log.error("TaskRetryBaseListener onMessage error", e);
            // 如果没有超过最大次数，且没有标记为失败，更新重试次数
            if (retryType == RetryType.DELAY_QUEUE && ensureMessageConsume != null) {
                boolean sendDelayMessageAndIncrRetryCount = messageReliabilityDomainService.sendDelayMessageAndIncrRetryCount(ensureMessageConsume.getTaskRetryId(), dataClass);
                if (!sendDelayMessageAndIncrRetryCount) {
                    log.error("sendDelayMessageAndIncrRetryCount fail, taskRetryId={}", ensureMessageConsume.getTaskRetryId());
                }
            }
            // 标记消息重试失败
            if (ensureMessageConsume != null) {
                boolean exceededRetryTaskAsFailedById = messageReliabilityDomainService.markExceededRetryTaskAsFailedById(ensureMessageConsume.getTaskRetryId(), dataClass);
                log.info("message marked as failed: {}, taskRetryId={}", exceededRetryTaskAsFailedById, ensureMessageConsume.getTaskRetryId());
            }
            
        }

    }
    protected abstract boolean doCheck(T message, MessageProperties properties);

    protected abstract void doProcess(T message, MessageProperties properties);

    protected abstract TaskResouceTypeEnum getTaskResouceTypeEnum();

    protected abstract MessageQueueEnum getMessageQueueEnum();

}
