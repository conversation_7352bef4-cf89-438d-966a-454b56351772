package com.agtech.pointprod.service.app.service.impl;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.biz.GrayBizService;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.service.GrayService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.GrayRequest;
import com.agtech.pointprod.service.facade.dto.rsp.GrayResponse;
import com.zat.gateway.component.result.model.GwResult;

import lombok.extern.slf4j.Slf4j;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_CHECK_GRAY_USER;

/**
 * 灰度服务实现类
 * 
 */
@Slf4j
@Service
public class GrayServiceImpl implements GrayService {

    @Resource
    private GrayBizService grayBizService;

    @Override
    public GwResult<GrayResponse> checkGray(GrayRequest request) {
        GenericResult<GrayResponse> genericResult = GenericResult.success(null);
        
        final ResultContainer<GenericResult<GrayResponse>> resultContainer = new ResultContainer<>(genericResult);
        PointProdServiceTemplate.execute(SERVICE_NAME_CHECK_GRAY_USER, genericResult, new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                AssertUtil.assertNotBlank(request.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能为空", "userId param missing");
                
                if (StringUtils.isBlank(request.getFeatureKey())) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.GRAY_FEATURE_KEY_REQUIRED);
                }
            }

            @Override
            public void process() {
                // 设置 custid
                resultContainer.setResult(grayBizService.checkGray(request));
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(request, genericResult, timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(resultContainer.getResult());
    }
} 