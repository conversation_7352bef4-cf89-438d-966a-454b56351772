package com.agtech.pointprod.service.app.controller;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.agtech.pointprod.service.app.service.TransferUserService;
import com.agtech.pointprod.service.facade.dto.req.PayeeInfoValidateReq;
import com.agtech.pointprod.service.facade.dto.req.PayerValidateReq;
import com.agtech.pointprod.service.facade.dto.req.QueryHistoryPayeeListReq;
import com.agtech.pointprod.service.facade.dto.req.QueryTransferUserInfoReq;
import com.agtech.pointprod.service.facade.dto.rsp.PayeeInfoValidateRsp;
import com.agtech.pointprod.service.facade.dto.rsp.PayerValidateRsp;
import com.agtech.pointprod.service.facade.dto.rsp.QueryHistoryPayeeListRsp;
import com.agtech.pointprod.service.facade.dto.rsp.QueryTransferUserInfoRsp;
import com.zat.gateway.component.result.model.GwResult;

@RestController
@RequestMapping("/fund/user")
public class TransferUserController {
    @Resource
    private TransferUserService transferUserService;

    /**
     * 查询付款方信息
     *
     * @param req
     * @return
     */
    @PostMapping("/query")
    public GwResult<QueryTransferUserInfoRsp> queryTransferUserInfo(@RequestBody(required = false) QueryTransferUserInfoReq req, @RequestHeader(name = "MPayCustId", required = false) String userId) {
        if (ObjectUtils.isEmpty(req)) {
            req = new QueryTransferUserInfoReq();
        }
        req.setUserId(userId);
        return transferUserService.queryTransferUserInfo(req);
    }

    /**
     * 查询收款历史用户列表
     *
     * @param req
     * @return
     */
    @PostMapping("/recent/list/query")
    public GwResult<QueryHistoryPayeeListRsp> queryHistoryTransferUserList(@RequestBody QueryHistoryPayeeListReq req, @RequestHeader(name = "MPayCustId", required = false) String userId) {
        req.setUserId(userId);
        return transferUserService.queryHistoryPayeeList(req);
    }

    /**
     * 验证收款方信息
     *
     * @param req
     * @return
     */
    @PostMapping("/validate")
    public GwResult<PayeeInfoValidateRsp> payeeInfoValidate(@RequestBody PayeeInfoValidateReq req, @RequestHeader(name = "MPayCustId", required = false) String userId) {
        req.setUserId(userId);
        return transferUserService.payeeInfoValidate(req);
    }

    /**
     * 验证付款方转赠资格
     *
     * @param req
     * @return
     */
    @PostMapping("/payer/validate")
    public GwResult<PayerValidateRsp> payerValidate(@RequestBody(required = false) PayerValidateReq req
                                                    , @RequestHeader(name = "MPayCustId", required = false) String userId
                                                    , @RequestHeader(name = "Lang", required = false) String language) {
        if(null == req) {
            req = new PayerValidateReq();
        }
        req.setUserId(userId);
        req.setLang(language);
        return transferUserService.payerValidate(req);
    }
}
