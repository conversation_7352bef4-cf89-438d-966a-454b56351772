package com.agtech.pointprod.service.app.service.impl;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.biz.ContractBizService;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.service.ContractService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.ContractConfirmStatusEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.QueryContractReq;
import com.agtech.pointprod.service.facade.dto.req.UserUpdateContractReq;
import com.agtech.pointprod.service.facade.dto.rsp.QueryContractRsp;
import com.agtech.pointprod.service.facade.dto.rsp.UserUpdateContractRsp;
import com.alibaba.fastjson2.JSONObject;
import com.zat.gateway.component.result.model.GwResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_QUERY_CONTRACT;
import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_USER_UPDATE_CONTRACT;

@Slf4j
@Service
public class ContractServiceImpl implements ContractService {
    @Resource
    private ContractBizService contractBizService;

    @Override
    public GwResult<QueryContractRsp> queryContract(QueryContractReq req) {
        log.info("queryContract req={}", JSONObject.toJSONString(req));

        final ResultContainer<GenericResult<QueryContractRsp>> container = new ResultContainer<>(new GenericResult<QueryContractRsp>());
        PointProdServiceTemplate.execute(SERVICE_NAME_QUERY_CONTRACT, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                AssertUtil.assertNotBlank(req.getBizType(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "bizType不能為空", "bizType param missing");
                AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能為空", "userId param missing");
            }

            @Override
            public void process() {
                GenericResult<QueryContractRsp> result = contractBizService.queryContract(req);
                container.setResult(result);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }

    @Override
    public GwResult<UserUpdateContractRsp> userUpdate(UserUpdateContractReq req) {
        log.info("userUpdate req={}", JSONObject.toJSONString(req));

        final ResultContainer<GenericResult<UserUpdateContractRsp>> container = new ResultContainer<>(new GenericResult<>());
        PointProdServiceTemplate.execute(SERVICE_NAME_USER_UPDATE_CONTRACT, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                AssertUtil.assertNotBlank(req.getStatus(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "status不能為空", "status param missing");
                AssertUtil.assertTrue(ObjectUtils.isNotEmpty(ContractConfirmStatusEnum.get(req.getStatus())), PointProdBizErrorCodeEnum.PARAMS_ERROR, "status傳值錯誤", "status param error");
                AssertUtil.assertNotBlank(req.getContractId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "contractId不能為空", "contractId param missing");
                AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能為空", "userId param missing");
            }

            @Override
            public void process() {
                GenericResult<UserUpdateContractRsp> result = contractBizService.userUpdate(req);
                container.setResult(result);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
}
