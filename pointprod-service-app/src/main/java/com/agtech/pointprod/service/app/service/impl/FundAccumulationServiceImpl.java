package com.agtech.pointprod.service.app.service.impl;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.order.service.domain.model.OrderStatusChangeMessage;
import com.agtech.pointprod.service.app.biz.FundAccumulationBizService;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.service.FundAccumulationService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_TRANSFER_ONE_STAGE_ACCUMULATION;

@Service
@Slf4j
public class FundAccumulationServiceImpl implements FundAccumulationService {

    @Resource
    private FundAccumulationBizService fundAccumulationBizService;

    @Override
    public void transferOneStageAccumulation(OrderStatusChangeMessage message) {
        final ResultContainer<GenericResult<String>> container = new ResultContainer<>(GenericResult.success(null));
        PointProdServiceTemplate.execute(SERVICE_NAME_TRANSFER_ONE_STAGE_ACCUMULATION, container.getResult(), new PointProdServiceCallback() {

            @Override
            public void checkParameter() {
                AssertUtil.assertNotNull(message, PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數不能為空", "request param error");
            }

            @Override
            public void process() {
                fundAccumulationBizService.transferOneStageAccumulation( message);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(message, container.getResult(), timeCost);
            }
        });
    }
}
