package com.agtech.pointprod.service.app.controller;

import java.io.IOException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.app.service.ShareTransferService;
import com.agtech.pointprod.service.facade.dto.req.ShareTransferReq;
import com.agtech.pointprod.service.facade.dto.rsp.ShareTransferRsp;

import lombok.extern.slf4j.Slf4j;

/**
 * 短链转长链控制器
 */
@RestController
@RequestMapping("/fund/share/transfer")
@Slf4j
public class ShareTransferController {
    
    @Resource
    private ShareTransferService shareTransferService;

    /**
     * 短链转长链并重定向
     * @param shortLink 短链标识
     * @param response HTTP响应对象
     * @return 重定向响应
     */
    @GetMapping("/{shortLink}")
    public ResponseEntity<Void> transferAndRedirect(@PathVariable String shortLink, HttpServletResponse response) {
        try {
            log.info("接收到短链转长链请求, shortLink={}", shortLink);
            
            ShareTransferReq req = new ShareTransferReq();
            req.setShortLink(shortLink);
            
            GenericResult<ShareTransferRsp> result = shareTransferService.transferShortToLong(req);
            
            if (result.isSuccess() && result.getValue() != null) {
                String longUrl = result.getValue().getLongUrl();
                log.info("短链转长链成功, shortLink={}, longUrl={}", shortLink, longUrl);
                
                // 302重定向到长链
                response.sendRedirect(longUrl);
                return ResponseEntity.status(HttpStatus.FOUND).build();
            } else {
                log.error("短链转长链失败, shortLink={}, error={}", shortLink, result.getMsg());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
            }
        } catch (IOException e) {
            log.error("重定向失败, shortLink={}", shortLink, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            log.error("短链转长链异常, shortLink={}", shortLink, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
} 