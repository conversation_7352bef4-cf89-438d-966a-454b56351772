package com.agtech.pointprod.service.app.controller;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.agtech.pointprod.service.facade.dto.req.GrayAdminRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.agtech.pointprod.service.app.service.GrayAdminService;
import com.agtech.pointprod.service.facade.dto.req.GrayRequest;
import com.agtech.pointprod.service.facade.dto.rsp.GrayResponse;
import com.alibaba.fastjson2.JSONObject;
import com.zat.gateway.component.result.model.GwResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 灰度管理控制器
 * 
 */
@Slf4j
@RestController
@RequestMapping("/api/management")
public class GrayAdminController {

    @Resource
    private GrayAdminService grayAdminService;

    /**
     * 灰度检查接口（管理端）
     *
     * @param request 灰度请求
     * @return 灰度响应
     */
    @PostMapping(value = "/gray", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    public GwResult<GrayResponse> gray(@Valid @RequestBody GrayAdminRequest request) {
        log.info("admin gray request: {}", JSONObject.toJSONString(request));
        GrayRequest gray = new GrayRequest();
        gray.setFeatureKey(request.getFeatureKey());
        gray.setUserId(request.getCustId());
        GwResult<GrayResponse> result = grayAdminService.checkGray(gray);
        log.info("admin gray response: {}", JSONObject.toJSONString(result));
        return result;
    }
} 