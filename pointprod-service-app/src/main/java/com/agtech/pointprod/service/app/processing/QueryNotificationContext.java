package com.agtech.pointprod.service.app.processing;

import java.util.List;
import java.util.Map;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.NotificationRecord;
import com.agtech.pointprod.service.facade.dto.rsp.QueryNotificationRsp;

import lombok.Getter;
import lombok.Setter;

/**
 * 查询通知上下文
 */
@Getter
@Setter
public class QueryNotificationContext {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 分享码
     */
    private String shareCode;
    
    /**
     * 分享码对应的订单ID
     */
    private String shareCodeOrderId;
    
    /**
     * 分享码对应的用户角色
     */
    private String shareCodeRole;
    
    /**
     * 通知记录列表
     */
    private List<NotificationRecord> notificationRecords;
    
    /**
     * 分享码是否过期
     */
    private boolean isShareCodeExpired;
    
    /**
     * 分享码通知状态 (READ/UNREAD)
     */
    private String shareCodeNotificationStatus;
    
    /**
     * 查询结果
     */
    private GenericResult<QueryNotificationRsp> result;
    
    /**
     * 资金订单信息Map
     */
    private Map<String, BaseOrderInfo> fundOrderMap;
    
    /**
     * 用户信息Map
     */
    private Map<String, MPayUserInfo> userInfoMap;
} 