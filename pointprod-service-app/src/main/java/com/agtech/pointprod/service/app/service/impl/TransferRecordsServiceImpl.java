package com.agtech.pointprod.service.app.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.order.service.domain.common.enums.SubOrderTypeEnum;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.processing.TransferRecordsListContext;
import com.agtech.pointprod.service.app.processing.step.TransferRecordStep;
import com.agtech.pointprod.service.app.service.TransferRecordsService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.NumbersEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.TransferRecordsListReq;
import com.agtech.pointprod.service.facade.dto.rsp.TransferRecordsListRsp;
import com.zat.gateway.component.result.model.GwResult;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_QUERY_TRANSFER_RECORDS_LIST;

/**
 * 转赠记录服务实现
 * 
 * <AUTHOR>
 */
@Service
public class TransferRecordsServiceImpl implements TransferRecordsService {
    
    @Resource
    private TransferRecordStep transferRecordStep;
    
    @Override
    public GwResult<TransferRecordsListRsp> queryTransferRecordsList(TransferRecordsListReq req) {
        final ResultContainer<GenericResult<TransferRecordsListRsp>> container = new ResultContainer<>(new GenericResult<>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_QUERY_TRANSFER_RECORDS_LIST, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                
                // 校验type必须是在enum枚举范围
                if (StringUtils.hasText(req.getSubOrderType()) && SubOrderTypeEnum.fromValue(req.getSubOrderType()) == null) {
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                }

                // 校验userId不能为空
                AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "用戶id不能為空", "userId param error");
                
                // 校验perPage不能超过20
                if (req.getPerPage() != null) {
                    AssertUtil.assertTrue(req.getPerPage() <= NumbersEnum.TWENTY.getIntValue(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "每頁條數不能超過20", "perPage cannot exceed 20");
                }
            }
            
            @Override
            public void process() {
                // 构建上下文
                TransferRecordsListContext context = transferRecordStep.buildContext(req);
                
                // 查询转赠记录列表
                transferRecordStep.queryTransferRecordList(context);
                
                // 构建响应
                transferRecordStep.buildResponse(context);
                
                container.setResult(context.getResult());
            }
            
            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
} 