package com.agtech.pointprod.service.app;

import com.agtech.pointprod.service.infrastructure.integration.mpay.translator.MPayFeignConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 15:21
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.agtech"},excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = MPayFeignConfig.class))
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {
        "com.agtech.pointprod.service.infrastructure.integration.*"
})
@EnableAspectJAutoProxy(exposeProxy = true)
@Slf4j
@EnableTransactionManagement
public class PointProdServiceAppApplication {

    public static void main(String[] args) {
        SpringApplication.run(PointProdServiceAppApplication.class, args);
        log.info("启动成功！！！");
        Runtime.getRuntime().addShutdownHook(new Thread(() -> log.info("shutting down...")));
    }

}
