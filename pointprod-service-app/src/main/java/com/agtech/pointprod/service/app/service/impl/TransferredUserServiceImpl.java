package com.agtech.pointprod.service.app.service.impl;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.dto.PageInfoDTO;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.processing.step.TransferredUserListQueryStep;
import com.agtech.pointprod.service.app.processing.step.TransferredUserStatusUpdateStep;
import com.agtech.pointprod.service.app.service.TransferredUserService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TransferredUserStatusEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.QueryTransferredUserListReq;
import com.agtech.pointprod.service.facade.dto.req.UpdateTransferredUserStatusReq;
import com.agtech.pointprod.service.facade.dto.rsp.QueryTransferredUserListRsp;
import com.zat.gateway.component.result.model.GwResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_QUERY_TRANSFERRED_USER_LIST;
import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.SERVICE_NAME_UPDATE_USER_BLACK_LIST_STATUS;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 14:42
 */
@Service
public class TransferredUserServiceImpl implements TransferredUserService {

    @Resource
    private TransferredUserListQueryStep queryTransferUserManageListStep;

    @Resource
    private TransferredUserStatusUpdateStep transferredUserStatusUpdateStep;


    @Override
    public GwResult<PageInfoDTO<QueryTransferredUserListRsp>> queryUserList(QueryTransferredUserListReq request) {
        final ResultContainer<GenericResult<PageInfoDTO<QueryTransferredUserListRsp>>> container = new ResultContainer<>(GenericResult.success(null));
        PointProdServiceTemplate.execute(SERVICE_NAME_QUERY_TRANSFERRED_USER_LIST, container.getResult(), new PointProdServiceCallback() {

            @Override
            public void checkParameter() {
                AssertUtil.assertNotNull(request, PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數不能為空", "request param error");
            }

            @Override
            public void process() {
                container.getResult().setValue(queryTransferUserManageListStep.process(request));
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(request, container.getResult(), timeCost);
            }

        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }

    @Override
    public GwResult<Void> updateStatus(UpdateTransferredUserStatusReq request) {
        final ResultContainer<GenericResult<Void>> container = new ResultContainer<>(GenericResult.success(null));
        PointProdServiceTemplate.execute(SERVICE_NAME_UPDATE_USER_BLACK_LIST_STATUS, container.getResult(), new PointProdServiceCallback() {

            @Override
            public void checkParameter() {
                AssertUtil.assertNotNull(request, PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數不能為空", "request param error");
                AssertUtil.assertNotNull(request.getOperator(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[Operator]不能為空", "request param[Operator] error");

                AssertUtil.assertNotBlank(request.getUserId(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[UserId]不能為空", "request param[UserId] error");
                AssertUtil.assertNotBlank(request.getStatus(), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[Status]不能為空", "request param[Status] error");

                AssertUtil.assertTrue(TransferredUserStatusEnum.containCode(request.getStatus()), PointProdBizErrorCodeEnum.PARAMS_ERROR, "請求參數[Status]無效", "request param[Status] invalid");
            }

            @Override
            public void process() {
                transferredUserStatusUpdateStep.process(request);
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(request, container.getResult(), timeCost);
            }

        });
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
}
