package com.agtech.pointprod.service.app.processing.step.template;

import com.agtech.pointprod.service.app.converter.TemplateDTOConverter;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.gateway.TemplateGateway;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.template.UpdateTemplateReq;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/24 19:35
 */
@Component
public class UpdateTemplateStep {

    @Resource
    protected TemplateGateway templateGateway;

    @Resource
    private TemplateDTOConverter templateDTOConverter;

    public void process(UpdateTemplateReq updateTemplateReq){
        boolean success = templateGateway.updateTemplate(templateDTOConverter.updateTemplate2Template(updateTemplateReq));
        AssertUtil.assertTrue(success, PointProdBizErrorCodeEnum.SYS_ERROR, "模板更新失敗[{0}]", "Template updating failed[{0}]", updateTemplateReq.getTemplateId());
    }

}
