package com.agtech.pointprod.service.app.digest.wrapper;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.app.digest.ServiceDigestLogWrapHelper;
import com.agtech.pointprod.service.app.digest.log.SimpleDigestLog;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/12/5 15:17
 */
public class SimpleDigestLogWrapper {

    private SimpleDigestLogWrapper(){}
    
    /**
     * BaseReq 里的信息比较多，使用此方法需要注意
     *
     * @param request
     * @param result
     * @param timeCost
     * @return
     */
    public static SimpleDigestLog wrap(Object request, GenericResult<?> result, long timeCost){
        SimpleDigestLog digestLog = new SimpleDigestLog();
        ServiceDigestLogWrapHelper.fillBaseDigestInfo(digestLog, result, timeCost);
        digestLog.setRequest(request);
        if (result != null && result.getValue() != null) {
            digestLog.setResponse(result.getValue());
        }
        return digestLog;
    }

}
