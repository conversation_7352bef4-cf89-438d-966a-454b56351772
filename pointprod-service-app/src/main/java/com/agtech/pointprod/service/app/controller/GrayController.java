package com.agtech.pointprod.service.app.controller;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.agtech.pointprod.service.app.service.GrayService;
import com.agtech.pointprod.service.facade.dto.req.GrayRequest;
import com.agtech.pointprod.service.facade.dto.rsp.GrayResponse;
import com.alibaba.fastjson2.JSONObject;
import com.zat.gateway.component.result.model.GwResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 灰度控制器
 * 
 */
@Slf4j
@RestController
public class GrayController {

    @Resource
    private GrayService grayService;

    /**
     * 灰度检查接口
     *
     * @param request 灰度请求
     * @param userId 用户ID
     * @param language 语言
     * @return 灰度响应
     */
    @PostMapping(value = "/gray")
    public GwResult<GrayResponse> gray(@Valid @RequestBody GrayRequest request, 
                                      @RequestHeader(name = "MPayCustId", required = false) String userId,
                                      @RequestHeader(name = "Lang", required = false) String language) {
        log.info("gray request: {}", JSONObject.toJSONString(request));
        request.setUserId(userId);
        request.setLang(language);
        GwResult<GrayResponse> result = grayService.checkGray(request);
        log.info("gray response: {}", JSONObject.toJSONString(result));
        return result;
    }
} 