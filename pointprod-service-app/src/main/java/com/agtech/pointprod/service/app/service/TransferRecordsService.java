package com.agtech.pointprod.service.app.service;

import com.agtech.pointprod.service.facade.dto.req.TransferRecordsListReq;
import com.agtech.pointprod.service.facade.dto.rsp.TransferRecordsListRsp;
import com.zat.gateway.component.result.model.GwResult;

/**
 * 转赠记录服务接口
 * 
 * <AUTHOR>
 */
public interface TransferRecordsService {
    
    /**
     * 查询转赠记录列表
     * 
     * @param req 查询请求
     * @return 转赠记录列表响应
     */
    GwResult<TransferRecordsListRsp> queryTransferRecordsList(TransferRecordsListReq req);
} 