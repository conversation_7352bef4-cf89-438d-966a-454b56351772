package com.agtech.pointprod.service.app.controller;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.app.service.TransferredUserService;
import com.agtech.pointprod.service.facade.dto.req.QueryTransferredUserListReq;
import com.agtech.pointprod.service.facade.dto.req.UpdateTransferredUserStatusReq;
import com.agtech.pointprod.service.facade.dto.rsp.QueryTransferredUserListRsp;
import com.zat.gateway.component.result.model.GwResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 11:25
 */
@RestController
@RequestMapping("/fund/transferredUser")
public class TransferredUserController {

    @Resource
    private TransferredUserService transferredUserService;


    @PostMapping("/list/query")
    public GwResult<PageInfoDTO<QueryTransferredUserListRsp>> list(@RequestBody QueryTransferredUserListReq transferUserManageListReq){
        return transferredUserService.queryUserList(transferUserManageListReq);
    }


    @PostMapping("/status/update")
    public GwResult<Void> updateStatus(@RequestBody UpdateTransferredUserStatusReq updateTransferredUserStatusReq){
        return transferredUserService.updateStatus(updateTransferredUserStatusReq);
    }

}
