package com.agtech.pointprod.service.app.processing.step;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.order.service.domain.common.constants.LangConstants;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.PayerParticipantInfo;
import com.agtech.pointprod.order.service.domain.service.OrderDomainService;
import com.agtech.pointprod.order.service.domain.service.PayDomainService;
import com.agtech.pointprod.order.service.facade.dto.FundAmountDTO;
import com.agtech.pointprod.service.app.processing.QueryNotificationContext;
import com.agtech.pointprod.service.app.processing.ReadNotificationContext;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.ResourceTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.ShareCodeStatusEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.MPayUserInfoGateway;
import com.agtech.pointprod.service.domain.gateway.NotificationGateway;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.NotificationRecord;
import com.agtech.pointprod.service.domain.model.NotificationRecordList;
import com.agtech.pointprod.service.facade.dto.rsp.QueryNotificationRsp;
import com.agtech.pointprod.service.facade.dto.rsp.ReadNotificationRsp;

import lombok.extern.slf4j.Slf4j;

/**
 * 通知处理步骤
 */
@Component
@Slf4j
public class NotificationStep {
    
    @Resource
    private NotificationGateway notificationGateway;

    @Resource
    private OrderDomainService orderDomainService;
    
    @Resource
    private PayDomainService payDomainService;
    
    @Resource
    private MPayUserInfoGateway mPayUserInfoGateway;
    
    /**
     * 查询通知列表
     * @param context 查询上下文
     */
    public void queryNotificationList(QueryNotificationContext context) {
        NotificationRecordList result = notificationGateway.queryNotificationList(
                context.getUserId(), 
                context.getShareCode(),
                ResourceTypeEnum.TRANSFER_SUCCESS.getValue()
        );
        context.setNotificationRecords(result.getNotificationRecords());
        context.setShareCodeExpired(result.isShareCodeExpired());
        context.setShareCodeNotificationStatus(result.getShareCodeNotificationStatus());
        context.setShareCodeOrderId(result.getShareCodeOrderId());
        context.setShareCodeRole(result.getShareCodeRole());
    }
    
    /**
     * 阅读通知
     * @param context 阅读通知上下文
     */
    @Transactional
    public void readNotification(ReadNotificationContext context) {
        boolean success = notificationGateway.batchUpdateNotificationToRead(
                context.getNotificationIds(), 
                context.getUserId()
        );
        context.setSuccess(success);
    }
    
    /**
     * 根据类型阅读所有通知
     * @param context 阅读通知上下文
     */
    @Transactional
    public void readNotificationByType(ReadNotificationContext context) {
        boolean success = notificationGateway.batchUpdateNotificationByTypeToRead(
                context.getReadAllByType(),
                context.getUserId()
        );
        context.setSuccess(success);
    }
    
    /**
     * 批量查询订单信息
     * @param context 查询上下文
     */
    public void queryOrderInfo(QueryNotificationContext context) {
        List<NotificationRecord> notificationRecords = context.getNotificationRecords();
        if (CollectionUtils.isEmpty(notificationRecords)) {
            context.setFundOrderMap(Collections.emptyMap());
            return;
        }
        
        // 提取所有的资源ID（订单ID）
        List<String> fundOrderIds = notificationRecords.stream()
                .map(NotificationRecord::getResourceId)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(fundOrderIds)) {
            context.setFundOrderMap(Collections.emptyMap());
            return;
        }
        
        // 批量查询订单信息
        Map<String, BaseOrderInfo> fundOrderMap = orderDomainService.queryFundOrdersByIds(fundOrderIds);
        context.setFundOrderMap(fundOrderMap);
        
        // 收集所有付款方用户ID
        List<String> payerUserIds = new ArrayList<>();
        for (BaseOrderInfo order : fundOrderMap.values()) {
            if (order != null && order.getPayOrderInfoList() != null) {
                order.getPayOrderInfoList().stream()
                    .filter(payOrder -> payOrder != null && payOrder.getPayer() != null)
                    .map(payOrder -> payOrder.getPayer().getUserId())
                    .filter(StringUtils::hasText)
                    .forEach(payerUserIds::add);
            }
        }
        
        // 批量查询用户信息
        List<MPayUserInfo> userInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(payerUserIds)) {
                // 对用户ID列表进行去重
                List<String> distinctUserIds = payerUserIds.stream()
                        .distinct()
                        .collect(Collectors.toList());
                        
                userInfoList = mPayUserInfoGateway.getUserMsgList(distinctUserIds);
                if(CollectionUtils.isEmpty(userInfoList)) {
                    log.error("Failed to get user information for payerUserIds={}", distinctUserIds);
                    throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
                }
        }
        
        Map<String, MPayUserInfo> userInfoMap = userInfoList.stream()
                .collect(Collectors.toMap(MPayUserInfo::getCustId, v -> v, (v1, v2) -> v1));
                
        context.setUserInfoMap(userInfoMap);
    }
    
    /**
     * 构建响应结果（无语言参数，兼容原有调用，默认中文）
     * @param context 查询上下文
     */
    public void buildResponse(QueryNotificationContext context) {
        buildResponse(context, LangConstants.ZH_MO);
    }
    
    /**
     * 构建响应结果
     * @param context 查询上下文
     * @param lang 语言代码
     */
    public void buildResponse(QueryNotificationContext context, String lang) {
        QueryNotificationRsp response = new QueryNotificationRsp();
        
        // 将NotificationRecord转换为NotificationItem
        List<QueryNotificationRsp.NotificationItem> notificationItems = context.getNotificationRecords().stream()
                .map(record -> convertToNotificationItem(record, context.getFundOrderMap(), context.getUserInfoMap(), context.getUserId()))
                .collect(Collectors.toList());
        
        response.setNotifications(notificationItems);
        
        // 设置分享码状态
        if (StringUtils.hasText(context.getShareCode())) {
            // 设置分享码状态 (VALID or EXPIRED)
            if (context.isShareCodeExpired()) {
                response.setShareCodeStatus(ShareCodeStatusEnum.EXPIRED.getValue());
            } else {
                response.setShareCodeStatus(ShareCodeStatusEnum.VALID.getValue());
            }
            
            // 设置分享码通知状态 - 直接使用从gateway获取的状态
            response.setShareCodeNotificationStatus(context.getShareCodeNotificationStatus());
            
            // 设置用户角色 - 直接使用从gateway获取的角色
            response.setShareCodeRole(context.getShareCodeRole());
        }
        
        context.setResult(GenericResult.success(response));
    }
    
    /**
     * 构建阅读通知响应结果
     * @param context 阅读通知上下文
     */
    public void buildReadResponse(ReadNotificationContext context) {
        ReadNotificationRsp response = new ReadNotificationRsp();
        response.setSuccess(context.getSuccess());
        context.setResult(GenericResult.success(response));
    }
    
    /**
     * 转换NotificationRecord为NotificationItem
     * @param record 通知记录
     * @param fundOrderMap 订单信息Map
     * @param userInfoMap 用户信息Map
     * @param userId 当前用户ID
     * @return 通知项
     */
    private QueryNotificationRsp.NotificationItem convertToNotificationItem(
            NotificationRecord record, 
            Map<String, BaseOrderInfo> fundOrderMap,
            Map<String, MPayUserInfo> userInfoMap,
            String userId) {
            
        QueryNotificationRsp.NotificationItem item = new QueryNotificationRsp.NotificationItem();
        
        // 映射基础字段
        item.setNotificationId(record.getNotificationId());
        item.setOrderId(record.getResourceId()); // resourceId 对应 fundOrderId
        
        // 根据订单ID获取订单信息
        String fundOrderId = record.getResourceId();
        if (StringUtils.hasText(fundOrderId) && fundOrderMap != null) {
            BaseOrderInfo fundOrder = fundOrderMap.get(fundOrderId);
            if (fundOrder != null) {
                // 设置msg字段：从订单表的memo字段获取
                item.setMemo(fundOrder.getMemo());
                
                // 设置资金金额
                if (fundOrder.getAcceptOrderInfoList() != null && !fundOrder.getAcceptOrderInfoList().isEmpty()) {
                    // 从AcceptOrderInfo获取金额，通过userId找到用户自己的收款记录
                    fundOrder.getAcceptOrderInfoList().stream()
                            .filter(acceptInfo -> userId.equals(acceptInfo.getUserId()))
                            .findFirst()
                            .ifPresent(acceptInfo -> item.setFundAmount(
                                    new FundAmountDTO("mCoin", acceptInfo.getAcceptAmount().getAmount())));
                } 
                
                // 设置支付时间
                if (fundOrder.getCompleteTime() != null) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    item.setPaidTime(sdf.format(fundOrder.getCompleteTime()));
                }
                
                // 从MPayUserInfo获取付款人的昵称、头像、区号和电话
                if (fundOrder.getPayOrderInfoList() != null && !fundOrder.getPayOrderInfoList().isEmpty()) {
                    PayerParticipantInfo payer = fundOrder.getPayOrderInfoList().get(0).getPayer();
                    if (payer != null) {
                        String payerUserId = payer.getUserId();
                        if (StringUtils.hasText(payerUserId) && userInfoMap != null) {
                            MPayUserInfo payerInfo = userInfoMap.get(payerUserId);
                            if (payerInfo != null) {
                                item.setPayerUserNickName(payerInfo.getNickName());
                                item.setPayerUserHeadImg(payerInfo.getHeadLogo());
                                item.setPayerAreaCode(payerInfo.getAreaCode());
                                item.setPayerPhone(payerInfo.getPhone());
                            }
                        }
                        
                        // 如果头像为空，设置默认空字符串
                        if (item.getPayerUserHeadImg() == null) {
                            item.setPayerUserHeadImg("");
                        }
                        // 如果区号为空，设置默认空字符串
                        if (item.getPayerAreaCode() == null) {
                            item.setPayerAreaCode("");
                        }
                        // 如果电话为空，设置默认空字符串
                        if (item.getPayerPhone() == null) {
                            item.setPayerPhone("");
                        }
                    }
                }
            }
        }
        
        return item;
    }
}