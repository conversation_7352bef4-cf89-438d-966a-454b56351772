package com.agtech.pointprod.service.app.processing.step.template;

import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.gateway.TemplateGateway;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.template.DeleteTemplateReq;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/24 19:35
 */
@Component
public class DeleteTemplateStep {

    @Resource
    private TemplateGateway templateGateway;

    public void process(DeleteTemplateReq deleteTemplateReq){
        boolean success = templateGateway.deleteTemplate(deleteTemplateReq.getTemplateId(), deleteTemplateReq.getOperator());
        AssertUtil.assertTrue(success, PointProdBizErrorCodeEnum.SYS_ERROR, "模板刪除失敗[{0}]", "Template deleting failed[{0}]", deleteTemplateReq.getTemplateId());
    }

}
