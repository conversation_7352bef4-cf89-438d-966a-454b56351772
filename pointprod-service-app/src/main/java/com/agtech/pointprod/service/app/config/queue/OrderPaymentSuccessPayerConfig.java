package com.agtech.pointprod.service.app.config.queue;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.infrastructure.config.RabbitMQConfig;

import lombok.AllArgsConstructor;

/**
 * 订单支付成功付款方队列配置
 *
 * <AUTHOR>
 */
@Configuration
@AllArgsConstructor
public class OrderPaymentSuccessPayerConfig {

    private final RabbitMQConfig rabbitMQConfig;

    @Bean
    @Lazy
    public Queue orderPaymentSuccessPayerQueue() {
        return new Queue(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_PAYER.getConfigKey()).getQueue());
    }

    @Bean
    @Lazy
    public Exchange orderPaymentSuccessPayerExchange() {
        return new DirectExchange(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_PAYER.getConfigKey()).getExchange());
    }

    @Bean
    @Lazy
    public Binding orderPaymentSuccessPayerBinding() {
        return BindingBuilder.bind(orderPaymentSuccessPayerQueue()).to(orderPaymentSuccessPayerExchange())
                .with(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_PAYER.getConfigKey()).getRoutingKey()).noargs();
    }
    
    @Bean
    @Lazy
    public Exchange orderPaymentSuccessPayerDelayExchange() {
        DirectExchange exchange = new DirectExchange(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_PAYER.getConfigKey()).getDelayExchange());
        exchange.setDelayed(true);
        return exchange;
    }

    @Bean
    @Lazy
    public Binding orderPaymentSuccessPayerDelayBinding() {
        return BindingBuilder.bind(orderPaymentSuccessPayerQueue()).to(orderPaymentSuccessPayerDelayExchange())
                .with(rabbitMQConfig.getQueueConfig(MessageQueueEnum.ORDER_PAYMENT_SUCCESS_PAYER.getConfigKey()).getDelayRoutingKey()).noargs();
    }
}
