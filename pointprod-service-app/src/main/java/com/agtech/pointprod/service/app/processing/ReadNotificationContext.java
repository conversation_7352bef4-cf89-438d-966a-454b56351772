package com.agtech.pointprod.service.app.processing;

import java.util.List;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.facade.dto.rsp.ReadNotificationRsp;

import lombok.Getter;
import lombok.Setter;

/**
 * 阅读通知上下文
 */
@Getter
@Setter
public class ReadNotificationContext {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 通知ID列表
     */
    private List<String> notificationIds;
    
    /**
     * 该类型通知全部标记已读
     */
    private String readAllByType;
    
    /**
     * 处理是否成功
     */
    private Boolean success;
    
    /**
     * 处理结果
     */
    private GenericResult<ReadNotificationRsp> result;
} 