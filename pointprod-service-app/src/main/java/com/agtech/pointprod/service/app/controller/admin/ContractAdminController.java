package com.agtech.pointprod.service.app.controller.admin;

import com.agtech.pointprod.service.app.service.ContractAdminService;
import com.agtech.pointprod.service.facade.dto.req.ContractAdminReq;
import com.agtech.pointprod.service.facade.dto.rsp.ContractAdminRsp;
import com.alibaba.fastjson2.JSONObject;
import com.zat.gateway.component.result.model.GwResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Contract Admin RPC Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/transfer/contract")
public class ContractAdminController {
    
    @Resource
    private ContractAdminService contractAdminService;
    
    /**
     * Get latest valid contract by type
     */
    @PostMapping("/latest")
    public GwResult<ContractAdminRsp> getLatestContract(@RequestBody ContractAdminReq req) {
        log.info("admin getLatestContract request: {}", JSONObject.toJSONString(req));
        GwResult<ContractAdminRsp> result = contractAdminService.getLatestValidContract(req);
        log.info("admin getLatestContract response: {}", JSONObject.toJSONString(result));
        return result;

    }
    
    /**
     * Update or create new contract
     */
    @PostMapping("/edit")
    public GwResult<Boolean> updateContract(@RequestBody ContractAdminReq req) {
        log.info("admin updateContract request: {}", JSONObject.toJSONString(req));
        GwResult<Boolean> result = contractAdminService.updateContract(req);
        log.info("admin updateContract response: {}", JSONObject.toJSONString(result));
        return result;
    }
} 