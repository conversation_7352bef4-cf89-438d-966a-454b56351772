package com.agtech.pointprod.service.app.processing.step.admin.contract;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.app.util.ContractUtils;
import com.agtech.pointprod.service.app.util.OssUtils;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.ContractGateway;
import com.agtech.pointprod.service.domain.model.Contract;
import com.agtech.pointprod.service.facade.dto.req.ContractAdminReq;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Contract admin processing step
 * <AUTHOR>
 */
@Component
@Slf4j
public class ContractUpdateOrAddStep {

    @Resource
    private ContractGateway contractGateway;
    
    @Resource
    private BizSequenceRepository bizSequenceRepository;
    
    @Resource
    private OssUtils ossUtils;

    private final Object versionLock = new Object();
    /**
     * Create or update contract
     */
    @Transactional(rollbackFor = Exception.class)
    public GenericResult<Boolean> process(ContractAdminReq req) {
        synchronized(versionLock) {
            int currentVersion = 1;
            if (StringUtils.isNotEmpty(req.getContractId())) {
                Contract contract = contractGateway.queryLatestContractByBizType("MCOIN_TRANSFER");
                if (contract != null) {
                    currentVersion = contract.getVersion() + 1;
                }
            }
            return GenericResult.success(createNewVersionContract(req, currentVersion));
        }
    }

    /**
     * Create a new version of a contract
     * @param version Contract version
     */
    private Boolean createNewVersionContract(ContractAdminReq req, Integer version) {
        String contractId = bizSequenceRepository.getBizId(null, SequenceCodeEnum.CONTRACT) ;
        // Generate and upload HTML content to OSS
        String contentUrl = ContractUtils.generateAndUploadHtml(req.getContent(), req.getTitle(), contractId, "zh", ossUtils);
        if (StringUtils.isEmpty(contentUrl)) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
        }
        String contentEnUrl = null;
        if (StringUtils.isNotEmpty(req.getContentEn())) {
            contentEnUrl = ContractUtils.generateAndUploadHtml(req.getContentEn(), req.getTitleEn(), contractId, "en", ossUtils);
            if (StringUtils.isEmpty(contentEnUrl)) {
                throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
            }
        }
        Contract contract = new Contract();
        contract.setContractId(contractId);
        contract.setTitle(req.getTitle());
        contract.setTitleEn(req.getTitleEn());
        contract.setContentUrl(contentUrl);
        contract.setContentEnUrl(contentEnUrl);
        contract.setContractType(req.getContractType());
        contract.setVersion(version);
        contract.setValidTime(req.getValidTime());
        contract.setIsDeleted(0);
        boolean success = contractGateway.saveContract(contract);
        if (!success) {
            log.error("Contract add fail, ruleId: {}", contract.getContractId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.DB_UPDATE_FAIL,PointProdBizErrorCodeEnum.DB_UPDATE_FAIL.getResultMsg());
        }
        log.info("Contract created/updated successfully, contractId: {}", contractId);
        return true;
    }

} 