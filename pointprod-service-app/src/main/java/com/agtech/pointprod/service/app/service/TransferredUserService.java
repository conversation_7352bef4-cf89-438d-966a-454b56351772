package com.agtech.pointprod.service.app.service;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.facade.dto.req.QueryTransferredUserListReq;
import com.agtech.pointprod.service.facade.dto.req.UpdateTransferredUserStatusReq;
import com.agtech.pointprod.service.facade.dto.rsp.QueryTransferredUserListRsp;
import com.zat.gateway.component.result.model.GwResult;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 14:41
 */
public interface TransferredUserService {

    GwResult<PageInfoDTO<QueryTransferredUserListRsp>> queryUserList(QueryTransferredUserListReq request);

    GwResult<Void> updateStatus(UpdateTransferredUserStatusReq updateTransferredUserStatusReq);

}
