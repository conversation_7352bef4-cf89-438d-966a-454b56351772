package com.agtech.pointprod.service.app.service.impl;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.agtech.common.domain.container.ResultContainer;
import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.app.digest.wrapper.SimpleDigestLogWrapper;
import com.agtech.pointprod.service.app.processing.NotificationDetailContext;
import com.agtech.pointprod.service.app.processing.NotificationPushContext;
import com.agtech.pointprod.service.app.processing.QueryNotificationContext;
import com.agtech.pointprod.service.app.processing.ReadNotificationContext;
import com.agtech.pointprod.service.app.processing.step.NotificationDetailStep;
import com.agtech.pointprod.service.app.processing.step.NotificationPushStep;
import com.agtech.pointprod.service.app.processing.step.NotificationStep;
import com.agtech.pointprod.service.app.service.NotificationService;
import com.agtech.pointprod.service.app.template.PointProdServiceCallback;
import com.agtech.pointprod.service.app.template.PointProdServiceTemplate;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.ResourceTypeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.NotificationDetailReq;
import com.agtech.pointprod.service.facade.dto.req.NotificationPushReq;
import com.agtech.pointprod.service.facade.dto.req.QueryNotificationReq;
import com.agtech.pointprod.service.facade.dto.req.ReadNotificationReq;
import com.agtech.pointprod.service.facade.dto.rsp.NotificationDetailRsp;
import com.agtech.pointprod.service.facade.dto.rsp.QueryNotificationRsp;
import com.agtech.pointprod.service.facade.dto.rsp.ReadNotificationRsp;
import com.alibaba.fastjson2.JSONObject;
import com.zat.gateway.component.result.model.GwResult;

import lombok.extern.slf4j.Slf4j;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.*;

/**
 * 通知服务实现
 */
@Slf4j
@Service
public class NotificationServiceImpl implements NotificationService {
    
    @Resource
    private NotificationStep notificationStep;
    
    @Resource
    private NotificationPushStep notificationPushStep;
    
    @Resource
    private NotificationDetailStep notificationDetailStep;
    
    @Override
    public GwResult<QueryNotificationRsp> queryNotificationList(QueryNotificationReq req) {
        log.info("queryNotificationList req={}", JSONObject.toJSONString(req));
        final ResultContainer<GenericResult<QueryNotificationRsp>> container = new ResultContainer<>(new GenericResult<QueryNotificationRsp>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_QUERY_NOTIFICATION_LIST, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能为空", "userId param missing");
            }

            @Override
            public void process() {
                String userId = req.getUserId();
                
                QueryNotificationContext context = new QueryNotificationContext();
                context.setUserId(userId);
                context.setShareCode(req.getShareCode());
                
                // 查询通知列表
                notificationStep.queryNotificationList(context);
                
                // 批量查询订单信息
                notificationStep.queryOrderInfo(context);
                
                // 构建响应（传递语言参数）
                notificationStep.buildResponse(context, req.getLang());
                
                container.setResult(context.getResult());
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
    
    @Override
    public GwResult<ReadNotificationRsp> readNotification(ReadNotificationReq req) {
        log.info("readNotification req={}", JSONObject.toJSONString(req));
        final ResultContainer<GenericResult<ReadNotificationRsp>> container = new ResultContainer<>(new GenericResult<ReadNotificationRsp>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_READ_NOTIFICATION, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能为空", "userId param missing");
                
                // 检查notificationIds和readAllByType参数是互斥的，必须有且只有一个
                boolean hasNotificationIds = !CollectionUtils.isEmpty(req.getNotificationIds());
                boolean hasReadAllByType = !StringUtils.isEmpty(req.getReadAllByType());
                
                // 必须提供其中一个参数
                AssertUtil.assertTrue(hasNotificationIds || hasReadAllByType, 
                        PointProdBizErrorCodeEnum.PARAMS_MISSING,
                        "必须提供notificationIds或readAllByType", 
                        "must provide either notificationIds or readAllByType");
                
                // 不能同时提供两个参数
                AssertUtil.assertFalse(hasNotificationIds && hasReadAllByType, 
                        PointProdBizErrorCodeEnum.PARAMS_ERROR,
                        "notificationIds和readAllByType不能同时提供", 
                        "cannot provide both notificationIds and readAllByType");
                
                // 如果提供了readAllByType，验证其值是否有效
                if (hasReadAllByType) {
                    try {
                        ResourceTypeEnum.fromValue(req.getReadAllByType());
                    } catch (Exception e) {
                        throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
                    }
                }
            }

            @Override
            public void process() {
                String userId = req.getUserId();
                
                ReadNotificationContext context = new ReadNotificationContext();
                context.setUserId(userId);
                
                if (!CollectionUtils.isEmpty(req.getNotificationIds())) {
                    // 按通知ID列表阅读
                    context.setNotificationIds(req.getNotificationIds());
                    notificationStep.readNotification(context);
                } else {
                    // 按类型全部阅读
                    context.setReadAllByType(req.getReadAllByType());
                    notificationStep.readNotificationByType(context);
                }
                
                // 构建响应
                notificationStep.buildReadResponse(context);
                
                container.setResult(context.getResult());
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
    
    @Override
    public GwResult<NotificationDetailRsp> getNotificationDetail(NotificationDetailReq req) {
        log.info("getNotificationDetail req={}", JSONObject.toJSONString(req));
        final ResultContainer<GenericResult<NotificationDetailRsp>> container = new ResultContainer<>(new GenericResult<NotificationDetailRsp>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_GET_NOTIFICATION_DETAIL, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                AssertUtil.assertNotBlank(req.getUserId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "userId不能为空", "userId param missing");
                AssertUtil.assertNotBlank(req.getNotificationId(), PointProdBizErrorCodeEnum.PARAMS_MISSING, "notificationId不能为空", "notificationId param missing");
            }

            @Override
            public void process() {
                String userId = req.getUserId();
                String notificationId = req.getNotificationId();
                
                NotificationDetailContext context = new NotificationDetailContext();
                context.setUserId(userId);
                context.setNotificationId(notificationId);
                
                // 查询通知详情，并检查是否已读
                boolean shouldTerminate = notificationDetailStep.queryNotificationDetail(context);
                
                // 如果通知已读，直接返回空数据，终止后续流程
                if (shouldTerminate) {
                    container.setResult(context.getResult());
                    return;
                }
                
                // 查询订单信息
                notificationDetailStep.queryOrderInfo(context);
                
                // 获取付款人信息
                notificationDetailStep.getPayerUserInfo(context);
                
                // 构建响应
                notificationDetailStep.buildResponse(context);
                
                container.setResult(context.getResult());
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req, container.getResult(), timeCost);
            }
        });
        
        return PointProdServiceTemplate.convertToGwResult(container.getResult());
    }
    
    @Override
    public boolean processNotificationPush(NotificationPushReq req) {
        log.info("processNotificationPush req={}", req.toString());
        final ResultContainer<GenericResult<Boolean>> container = new ResultContainer<>(new GenericResult<Boolean>());
        
        PointProdServiceTemplate.execute(SERVICE_NAME_PUSH_NOTIFICATION, container.getResult(), new PointProdServiceCallback() {
            @Override
            public void checkParameter() {
                // Request is already validated in NotificationPushListener
            }

            @Override
            public void process() {
                // 创建推送上下文
                NotificationPushContext context = new NotificationPushContext();
                context.setMessageBody(req.toString()); // For logging purposes
                
                // 2. 构建通知记录（直接使用请求对象数据）
                notificationPushStep.buildNotificationRecordFromRequest(context, req);
                
                // 3. 创建通知记录
                notificationPushStep.createNotificationRecord(context);
                
                // 4. 推送通知并更新状态
                notificationPushStep.pushNotificationAndUpdateStatus(context);
                
                // 判断整体是否成功（创建和推送都必须成功）
                boolean success = context.getCreateSuccess() != null && context.getCreateSuccess() 
                        && context.getPushSuccess() != null && context.getPushSuccess();
                
                log.info("processNotificationPush completed - success: {}, created: {}/{}, pushed: {}/{}", 
                        success,
                        context.getSuccessNotifications() != null ? context.getSuccessNotifications().size() : 0, 
                        context.getNotifications() != null ? context.getNotifications().size() : 0,
                        context.getSuccessPushedNotifications() != null ? context.getSuccessPushedNotifications().size() : 0,
                        context.getSuccessNotifications() != null ? context.getSuccessNotifications().size() : 0);
                
                container.setResult(GenericResult.success(success));
            }

            @Override
            public BaseDigestLog composeDigestLog(long timeCost) {
                return SimpleDigestLogWrapper.wrap(req.toString(), container.getResult(), timeCost);
            }
        });
        
        return container.getResult().getValue();
    }
} 