package com.agtech.pointprod.service.app.listeners;

import java.util.UUID;

import javax.annotation.Resource;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.model.OrderStatusChangeMessage;
import com.agtech.pointprod.service.app.service.NotificationService;
import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.domain.common.enums.NotificationTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.NumbersEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.MessageSenderGateway;
import com.agtech.pointprod.service.domain.gateway.QueueConfigGateway;
import com.agtech.pointprod.service.facade.dto.req.NotificationPushReq;
import com.agtech.pointprod.service.infrastructure.config.RabbitMQConfig;
import com.agtech.pointprod.service.infrastructure.config.RabbitMQConfig.QueueConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;

import lombok.extern.slf4j.Slf4j;


/**
 * 通知推送监听器
 * 监听订单事件，当订单支付成功时发送通知给付款人和收款人
 */
@Slf4j
@Component
public class NotificationPushListener {

    @Resource
    private NotificationService notificationService;
    
    @Resource
    private QueueConfigGateway queueConfigGateway;
    
    @Resource
    private ObjectMapper objectMapper;
    
    @Resource
    private MessageSenderGateway messageSenderGateway;

    @Resource
    private RabbitMQConfig rabbitMQConfig;
    
    // 阶梯重试配置
    @Value("${notification.push.retry.max-attempts:10}")
    private int defaultMaxRetry;
    
    @Value("${notification.push.retry.initial-delay:2000}")
    private int initialRetryDelay;

    @RabbitListener(queues = "${rabbitmq.queues.config.order-payment-success-payer.queue}")
    public void onPayerOrderPaymentSuccess(Channel channel, Message message) {
        log.info("PayerOrderPaymentSuccess listener triggered");
            String notificationType = NotificationTypeEnum.SEND.getCode();
            process(channel,message,notificationType);
    }

    @RabbitListener(queues = "${rabbitmq.queues.config.order-payment-success-payee.queue}")
    public void onPayeeOrderPaymentSuccess(Channel channel, Message message) {
        log.info("PayeeOrderPaymentSuccess listener triggered");
            String notificationType = NotificationTypeEnum.ACCEPT.getCode();
        process(channel, message,notificationType);
    }

    public void process(Channel channel, Message message, String notificationType) {
        String messageBody = null;
        try {
            messageBody = new String(message.getBody());
            log.info("NotificationPushListener received message: {}", messageBody);

            // 解析订单事件消息
            OrderStatusChangeMessage orderEvent = objectMapper.readValue(messageBody, OrderStatusChangeMessage.class);

            // 根据队列名称确定通知类型

            // 提取订单ID和订单状态
            String orderId = orderEvent.getResourceId();
            FundOrderStatusEnum orderStatus = orderEvent.getOrderStatus();

            if (orderId == null || orderStatus == null) {
                log.error("Missing required fields in order event. orderId: {}, orderStatus: {}", orderId, orderStatus);
                throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
            }

            // 判断是否为支付成功事件
            if (!isPaymentSuccessEvent(orderStatus)) {
                log.info("Order event is not payment success, skip notification. orderId: {}, orderStatus: {}",
                        orderId, orderStatus.getCode());
                return;
            }

            log.info("Processing payment success notification for orderId: {}, orderStatus: {}, notificationType: {}",
                    orderId, orderStatus.getCode(), notificationType);

            // 构建通知推送请求（包含orderId和notificationType字段）
            NotificationPushReq req = new NotificationPushReq();
            req.setOrderId(orderId);
            req.setNotificationType(notificationType);

            log.info("Built notification push request: {}", req.toString());

            // 调用NotificationService处理推送消息（共用逻辑）
            boolean success = notificationService.processNotificationPush(req);

            if (success) {
                log.info("NotificationPushListener processed successfully for orderId: {}, notificationType: {}",
                        orderId, notificationType);
            } else {
                log.error("NotificationPushListener processing failed for orderId: {}, notificationType: {}",
                        orderId, notificationType);
                throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR, "Notification push processing failed for orderId: " + orderId);
            }

        } catch (Exception e) {
            log.error("Error processing notification push message: {}", messageBody, e);
            handleRetry(message, messageBody,notificationType);
        }
    }

    /**
     * 处理消息重试
     * 阶梯重试策略，总共重试10次，第一次延迟2秒，后面每次延迟时间递增
     */
    private void handleRetry(Message message, String messageBody,String notificationType) {
        OrderStatusChangeMessage orderEvent = null;
        try {
            // 解析订单事件消息
            orderEvent = objectMapper.readValue(messageBody, OrderStatusChangeMessage.class);
        } catch (JsonProcessingException e) {
            log.error("Failed to parse message for retry: {}", messageBody, e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR, "Failed to parse message for retry");
        }
        
        // 初始化重试相关属性（如果为null）
        initRetryFields(orderEvent);
        
        // 判断是否应该重试
        if (orderEvent.getDelayCount() < orderEvent.getDelayMax()) {
            // 增加重试次数
            orderEvent.setDelayCount(orderEvent.getDelayCount() + 1);
            
            // 计算阶梯式延迟时间 - 基于当前重试次数递增
            Integer delayTime = calculateSteppedDelay(orderEvent.getDelayCount());
            orderEvent.setDelayTime(delayTime);

            String queueConfigKey;
            if (notificationType.equals(NotificationTypeEnum.SEND.getCode())) {
                queueConfigKey = MessageQueueEnum.ORDER_PAYMENT_SUCCESS_PAYER.getConfigKey();
            } else {
                queueConfigKey = MessageQueueEnum.ORDER_PAYMENT_SUCCESS_PAYEE.getConfigKey();
            }
            QueueConfig queueConfig = rabbitMQConfig.getQueueConfig(queueConfigKey);
            
            // 获取交换机和路由键
            String exchangeName = queueConfig.getDelayExchange();
            String routingKey = queueConfig.getDelayRoutingKey();
            
            log.info("Retrying message, attempt {}/{}, delay: {}ms, exchange: {}, routingKey: {}", 
                    orderEvent.getDelayCount(), orderEvent.getDelayMax(), delayTime,
                    exchangeName, routingKey);
            
            // 生成重试消息ID和任务重试ID
            String messageId = UUID.randomUUID().toString();
            
            // 使用 MessageSenderGateway 发送重试消息
            messageSenderGateway.sendMessage(
                    messageId,
                    orderEvent,
                    exchangeName,
                    routingKey,
                    delayTime
            );
        } else {
            log.error("Max retry attempts ({}) reached for message, rejecting message", orderEvent.getDelayMax());
        }
    }
    
    /**
     * 计算阶梯式延迟时间
     * 首次重试延迟2秒，后续每次递增
     * 实现指数级递增策略: 2s, 4s, 8s, 16s...
     * 注意：为避免超过Integer.MAX_VALUE，对较大的重试次数限制最大延迟时间
     * @param retryCount 当前重试次数
     * @return 延迟时间（毫秒）
     */
    private Integer calculateSteppedDelay(int retryCount) {
        // 使用指数级递增策略: 每次重试延迟时间翻倍
        double calculatedDelay = initialRetryDelay * Math.pow(NumbersEnum.TWO.getIntValue(), (double)retryCount - NumbersEnum.ONE.getIntValue());
        
        // 防止延迟时间超过Integer.MAX_VALUE
        if (calculatedDelay > Integer.MAX_VALUE) {
            return Integer.MAX_VALUE;
        }
        
        return (int) calculatedDelay;
    }
    
    /**
     * 初始化OrderStatusChangeMessage中的重试相关字段
     * 如果字段为空，则设置默认值
     */
    private void initRetryFields(OrderStatusChangeMessage message) {
        if (message.getDelayCount() == null) {
            message.setDelayCount(NumbersEnum.ZERO.getIntValue());
        }
        
        if (message.getDelayMax() == null) {
            message.setDelayMax(defaultMaxRetry);
        }
        
        if (message.getDelayTime() == null) {
            message.setDelayTime(initialRetryDelay);
        }
    }

    /**
     * 判断是否为支付成功事件
     * @param orderStatus 订单状态
     * @return 是否为支付成功事件
     */
    private boolean isPaymentSuccessEvent(FundOrderStatusEnum orderStatus) {
        return FundOrderStatusEnum.SUCCESS.equals(orderStatus);
    }
}