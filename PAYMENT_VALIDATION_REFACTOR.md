# Payment聚合根校验逻辑重构说明

## 重构概述

本次重构将 `PayCheckStep` 中的校验逻辑迁移到 `Payment` 聚合根中，符合DDD（领域驱动设计）原则，将核心业务逻辑集中在领域层的聚合根中。

## 重构前后对比

### 重构前
- 校验逻辑分散在应用层的 `PayCheckStep` 中
- 聚合根 `Payment` 只包含基础的状态和余额校验
- 违反了DDD原则，核心业务逻辑不在领域层

### 重构后
- 所有校验逻辑集中在 `Payment` 聚合根中
- 符合DDD原则，聚合根负责维护业务不变性
- 提高了代码的内聚性和可维护性

## 新增校验功能

### 1. 黑名单校验
- **方法**: `validateBlackList()`
- **功能**: 校验付款人和收款人是否在黑名单中
- **依赖**: `BlackListDomainService`

### 2. 限额校验
- **方法**: `validateLimit()`
- **功能**: 校验付款金额是否超出限额限次
- **依赖**: `LimitService`

### 3. 详细用户状态校验
- **方法**: `validatePayerUserStatus()`, `validatePayeeUserStatus()`
- **功能**: 校验付款人和收款人的MPay用户状态
- **要求**: 用户状态必须为 `NORMAL`

### 4. 详细账户状态校验
- **方法**: `validatePayerAccountStatus()`, `validatePayeeAccountStatus()`
- **功能**: 校验付款人和收款人的积分账户状态
- **要求**: 账户状态必须为 `NORMAL`

### 5. 订单初始状态校验
- **方法**: `validateOrderInitialStatus()`
- **功能**: 校验订单状态是否为待支付状态
- **要求**: 订单状态必须为 `WAIT_PAY`

### 6. 安全码校验
- **方法**: `validateSecurityCode()`
- **功能**: 校验安全码格式和有效性
- **要求**: 安全码长度不少于6位

## 依赖注入

### 新增依赖
```java
/** 黑名单领域服务 */
private BlackListDomainService blackListDomainService;

/** 限额服务 */
private LimitService limitService;
```

### 构造函数更新
```java
public Payment(String orderId, String payerId, String payeeId, BigDecimal amount, String securityCode,
               BlackListDomainService blackListDomainService, LimitService limitService)
```

## 校验流程

在 `validatePaymentConditions()` 方法中，按以下顺序执行校验：

1. 基础状态校验
2. 用户信息校验（包含详细状态校验）
3. 积分账户校验（包含详细状态校验）
4. 订单状态校验（包含初始状态和安全码校验）
5. 用户状态校验
6. 积分余额校验
7. **新增**: 黑名单校验
8. **新增**: 限额校验

## 错误处理

### 新增错误码使用
- `USER_IN_BLACKLIST`: 用户在黑名单中
- `LIMIT_EXCEEDED`: 超出限额限制
- `LIMIT_CHECK_ERROR`: 限额校验异常
- `MPAY_USER_STATUS_INVALID`: MPay用户状态无效
- `MCOIN_ACCOUNT_STATUS_INVALID`: 积分账户状态无效
- `SECURITY_CODE_INVALID`: 安全码无效

## 架构优势

### 1. 符合DDD原则
- 聚合根负责维护业务不变性
- 核心业务逻辑集中在领域层
- 提高了领域模型的完整性

### 2. 提高内聚性
- 相关校验逻辑集中在一个地方
- 减少了跨层的业务逻辑分散
- 便于理解和维护

### 3. 增强可测试性
- 可以直接对聚合根进行单元测试
- 测试覆盖更加全面
- 减少了集成测试的复杂性

### 4. 提高可扩展性
- 新增校验规则只需在聚合根中添加
- 不需要修改应用层代码
- 符合开闭原则

## 使用示例

```java
// 创建Payment聚合根（带依赖注入）
Payment payment = new Payment(orderId, payerId, payeeId, amount, securityCode,
                             blackListDomainService, limitService);

// 设置用户信息和账户信息
payment.setPayerUserInfo(payerUserInfo);
payment.setPayeeUserInfo(payeeUserInfo);
payment.setPayerMcoinAccount(payerMcoinAccount);
payment.setPayeeMcoinAccount(payeeMcoinAccount);

// 执行完整的支付条件校验
payment.validatePaymentConditions();
```

## 注意事项

1. **依赖注入**: 确保在创建Payment聚合根时正确注入所需的领域服务
2. **空值检查**: 所有校验方法都包含了空值检查，确保系统稳定性
3. **日志记录**: 每个校验步骤都有详细的日志记录，便于问题排查
4. **异常处理**: 使用统一的业务异常处理机制
5. **向后兼容**: 如果依赖服务未注入，会记录警告日志但不会中断流程

## 后续优化建议

1. **配置化校验**: 将部分校验规则配置化，提高灵活性
2. **校验链模式**: 考虑使用责任链模式组织校验逻辑
3. **缓存优化**: 对频繁查询的黑名单和限额信息进行缓存
4. **监控告警**: 添加校验失败的监控和告警机制
5. **性能优化**: 对批量校验场景进行性能优化