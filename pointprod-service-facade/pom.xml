<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pointprod-service</artifactId>
        <groupId>com.agtech</groupId>
        <version>0.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>pointprod-service-facade</artifactId>
    <version>0.0.1</version>
    <name>pointprod-service-facade</name>
    <description>pointprod-service-facade</description>
    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.agtech.common</groupId>
            <artifactId>agtech-common-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mpay</groupId>
            <artifactId>mpay-gateway-component-result</artifactId>
            <version>${gateway.result.version}</version>
        </dependency>

    </dependencies>

</project>
