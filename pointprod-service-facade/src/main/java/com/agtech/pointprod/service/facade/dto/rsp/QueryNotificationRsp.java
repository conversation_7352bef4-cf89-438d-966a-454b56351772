package com.agtech.pointprod.service.facade.dto.rsp;

import java.util.List;

import com.agtech.pointprod.order.service.facade.dto.FundAmountDTO;

import lombok.Getter;
import lombok.Setter;

/**
 * 查询通知列表响应参数
 */
@Setter
@Getter
public class QueryNotificationRsp {
    
    /**
     * 通知列表
     */
    private List<NotificationItem> notifications;

    /**
     * 当前用户的角色
     * IS_PAYER - 当前用户是该分享码的付款人
     * IS_PAYEE - 当前用户是该分享码的收款人
     * IS_THIRD_PARTY - 分享码是第三方
     */
    private String shareCodeRole;

    /**
     * 分享码状态
     * VALID - 有效
     * EXPIRED - 过期
     */
    private String shareCodeStatus;

    /**
     * 分享码对应的通知状态
     * READ - 已读
     * UNREAD - 未读
     */
    private String shareCodeNotificationStatus;

    /**
     * 通知项
     */
    @Setter
    @Getter
    public static class NotificationItem {
        
        /**
         * 资金订单ID
         */
        private String orderId;
        
        /**
         * 通知唯一标识
         */
        private String notificationId;
        
        /**
         * 付款人用户昵称
         */
        private String payerUserNickName;
        
        /**
         * 付款人用户头像
         */
        private String payerUserHeadImg;

        /**
         * 付款人电话区号
         */
        private String payerAreaCode;

        /**
         * 付款人电话
         */
        private String payerPhone;
        
        /**
         * 支付时间
         */
        private String paidTime;
        
        /**
         * 资金金额
         */
        private FundAmountDTO fundAmount;
        
        /**
         * 消息内容
         */
        private String memo;

    }

} 