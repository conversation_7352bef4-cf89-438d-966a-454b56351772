package com.agtech.pointprod.service.facade.dto.req;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Accumulate rule admin request DTO
 */
@Getter
@Setter
@ToString
public class AccumulateRuleAdminReq extends BaseReq{
    /** 规则ID */
    private String ruleId;
    
    /** MPay用户等级 */
    private String title;
    
    /** 场景码 */
    private String sceneCode;
    
    /** 转赠档位对应的值 */
    private List<Integer> gearVal;
    
    /** 转赠频次 */
    private Integer transferCount;
    
    /** 转赠频次-自然天 */
    private Integer transferCountDay;
    
    /** 转赠最高金额 */
    private Integer transferAmount;
    
    /** 转赠最高金额-自然天 */
    private Integer transferAmountDay;
    
    /** 状态 VALID:启用 INVALID:未启用 */
    private String status;
    
    /** 系统用户名 */
    private String username;
    
    /** 当前页数 */
    private Integer pageNum = 1;
    
    /** 每页数量 */
    private Integer pageSize = 10;
} 