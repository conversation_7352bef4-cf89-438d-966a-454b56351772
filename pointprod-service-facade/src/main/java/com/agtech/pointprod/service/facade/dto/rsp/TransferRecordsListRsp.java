package com.agtech.pointprod.service.facade.dto.rsp;

import java.util.List;

import com.agtech.pointprod.order.service.facade.dto.FundAmountDTO;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 转赠记录列表响应参数
 */
@Getter
@Setter
@ToString
public class TransferRecordsListRsp {
    
    /**
     * 当前页数
     */
    private Integer currentPage;
    
    /**
     * 最后一页页数
     */
    private Integer lastPage;
    
    /**
     * 每页数量
     */
    private Integer perPage;
    
    /**
     * 总数量
     */
    private Long total;
    
    /**
     * 转赠记录列表
     */
    private List<TransferRecordItem> list;
    
    /**
     * 转赠记录项
     */
    @Getter
    @Setter
    @ToString
    public static class TransferRecordItem {
        
        /**
         * 资金订单ID
         */
        private String orderId;
        
        /**
         * 资金流向类型 (PAY:转出, ACCEPT:收到)
         */
        private String userOrderRole;
        
        /**
         * 付款订单信息列表
         */
        private List<PayOrderInfo> payOrderInfoList;
        
        /**
         * 收款订单信息列表
         */
        private List<PayOrderInfo> acceptOrderInfoList;
        
        /**
         * 支付时间
         */
        private String paidTime;
        
        /**
         * 资金金额
         */
        private FundAmountDTO fundAmount;
        
        /**
         * 资金订单状态
         */
        private String fundOrderStatus;
        
        /**
         * 留言
         */
        private String memo;
    }
    
    /**
     * Pay order information DTO
     */
    @Getter
    @Setter
    @ToString
    public static class PayOrderInfo {
        /**
         * 用户昵称
         */
        private String nickName;
        
        /**
         * 用户头像URL
         */
        private String headImg;
        
        /**
         * 用户电话
         */
        private String phone;
        
        /**
         * 区号
         */
        private String areaCode;
    }
} 