package com.agtech.pointprod.service.facade.dto.rsp;

import com.agtech.pointprod.order.service.facade.dto.FundAmountDTO;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 通知详情响应
 */
@Getter
@Setter
@ToString
public class NotificationDetailRsp {
    
    /**
     * 通知详情
     */
    private NotificationDetail notification;
    
    /**
     * 通知详情内部类
     */
    @Getter
    @Setter
    @ToString
    public static class NotificationDetail {
        /**
         * 订单号
         */
        private String orderId;
        
        /**
         * 通知ID
         */
        private String notificationId;
        
        /**
         * 付款人昵称
         */
        private String payerUserNickName;
        
        /**
         * 付款人头像URL
         */
        private String payerUserHeadImg;
        
        /**
         * 付款人电话区号
         */
        private String payerAreaCode;
        
        /**
         * 付款人电话
         */
        private String payerPhone;
        
        /**
         * 付款时间
         */
        private String paidTime;
        
        /**
         * 转赠金额
         */
        private FundAmountDTO fundAmount;
        
        /**
         * 留言
         */
        private String memo;
    }
} 