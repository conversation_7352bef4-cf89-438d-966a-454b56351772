package com.agtech.pointprod.service.facade.dto.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum TaskRetryStatusEnum {

    INIT("INIT", "待處理"),
    FINISH("FINISH", "已完成"),
    FAILED("FAILED", "已达最大尝试次数"),
    ;

    private final String value;

    private final String desc;

    TaskRetryStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
