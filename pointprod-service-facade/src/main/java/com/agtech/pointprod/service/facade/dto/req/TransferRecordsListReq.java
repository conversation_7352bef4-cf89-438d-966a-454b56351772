package com.agtech.pointprod.service.facade.dto.req;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 转赠记录列表请求参数
 */
@Getter
@Setter
@ToString
public class TransferRecordsListReq extends BaseReq {
    
    /**
     * 资金流向类型 (PAY:转出, ACCEPT:收到)
     */
    private String subOrderType;
    
    /**
     * 当前页数 (默认为1)
     */
    private Integer currentPage = 1;
    
    /**
     * 每页条数 (默认为10，最大不能超过20)
     */
    private Integer perPage;
} 