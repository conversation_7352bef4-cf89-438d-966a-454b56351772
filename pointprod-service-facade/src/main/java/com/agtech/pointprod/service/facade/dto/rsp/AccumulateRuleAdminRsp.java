package com.agtech.pointprod.service.facade.dto.rsp;

import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Accumulate rule admin response DTO
 */
@Getter
@Setter
@ToString
public class AccumulateRuleAdminRsp {
    /** 规则ID */
    private String ruleId;
    
    /** MPay用户等级 */
    private String title;

    /** MPay用户等级描述 */
    private String titleDesc;
    
    /** 场景码 */
    private String sceneCode;
    
    /** 转赠档位对应的值 */
    private List<Integer> gearVal;
    
    /** 转赠频次 */
    private Integer transferCount;
    
    /** 转赠频次-自然天 */
    private Integer transferCountDay;
    
    /** 转赠频次描述 */
    private String transferCountDesc;
    
    /** 转赠最高金额 */
    private Integer transferAmount;
    
    /** 转赠最高金额-自然天 */
    private Integer transferAmountDay;
    
    /** 转赠金额描述 */
    private String transferAmountDesc;
    
    /** 状态 VALID:启用 INVALID:未启用 */
    private String status;
    
    /** 状态描述 */
    private String statusDesc;
    
    /** 创建时间 */
    private String gmtCreate;
    
    /** 修改时间 */
    private String gmtModified;
    
    /** 创建人 */
    private String creator;
    
    /** 修改人 */
    private String modifier;
    
    /** 分页信息 */
    private Map<String, Object> paginationInfo;
} 