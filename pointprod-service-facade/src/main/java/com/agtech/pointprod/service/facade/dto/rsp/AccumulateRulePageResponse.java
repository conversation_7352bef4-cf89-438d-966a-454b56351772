package com.agtech.pointprod.service.facade.dto.rsp;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * Paginated response wrapper for accumulate rules
 */
@Getter
@Setter
public class AccumulateRulePageResponse {

    /**
     * Page data records
     */
    private List<AccumulateRuleAdminRsp> records;
    
    /**
     * Total count of records
     */
    private Integer totalCount;
    
    /**
     * Current page number
     */
    private Integer pageNum;
    
    /**
     * Page size
     */
    private Integer pageSize;
    
    /**
     * Create a new paginated response
     * 
     * @param records List of records
     * @param totalCount Total count of records
     * @param pageNum Current page number
     * @param pageSize Page size
     * @return Paginated response
     */
    public static AccumulateRulePageResponse of(
            List<AccumulateRuleAdminRsp> records, 
            Integer totalCount, 
            Integer pageNum, 
            Integer pageSize) {
        AccumulateRulePageResponse response = new AccumulateRulePageResponse();
        response.setRecords(records);
        response.setTotalCount(totalCount);
        response.setPageNum(pageNum);
        response.setPageSize(pageSize);
        return response;
    }
} 