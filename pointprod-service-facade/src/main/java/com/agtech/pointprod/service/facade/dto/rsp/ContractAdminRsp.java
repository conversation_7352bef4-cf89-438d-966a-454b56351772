package com.agtech.pointprod.service.facade.dto.rsp;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * Contract admin response DTO
 */
@Getter
@Setter
@ToString
public class ContractAdminRsp {
     /** 主键ID */
     private Long id;
    
     /** 条款id */
     private String contractId;
     
     /** 条款标题 */
     private String title;
     
     /** 条款英文标题 */
     private String titleEn;
     
     /** 条款内容 */
     private String contentUrl;
     
     /** 条款英文内容 */
     private String contentEnUrl;
     
     /** 合约类型 */
     private String contractType;
     
     /** 合约类型描述 */
     private String contractTypeDesc;
     
     /** 合约版本 */
     private Integer version;
     
     /** 生效时间 */
     private String validTime;
     
     /** 创建时间 */
     private String gmtCreate;
     
     /** 修改时间 */
     private String gmtModified;
} 