# 重试策略配置使用说明

## 概述

本项目实现了基于 `TaskResouceTypeEnum` 枚举的重试策略配置系统，支持从 Nacos 配置中心动态获取不同任务类型的重试参数，并提供了灵活的重试时间计算服务。

## 核心组件

### 1. RetryStrategyProperties
配置属性类，用于绑定 Nacos 配置参数。

**配置前缀**: `pointprod.retry.strategy`

**主要功能**:
- 定义默认重试策略配置
- 支持针对不同任务类型的个性化配置
- 提供配置获取的便捷方法

### 2. RetryStrategyDomainService
重试策略领域服务接口，提供重试相关的业务逻辑。

**主要方法**:
- `calculateNextRetryTime()`: 计算下次重试时间
- `getMaxRetryCount()`: 获取最大重试次数
- `isMaxRetryCountReached()`: 检查是否达到最大重试次数
- `calculateRetryDelay()`: 计算重试延迟时间
- `canRetry()`: 检查是否可以重试
- `getRetryStrategyDescription()`: 获取策略描述

### 3. RetryStrategyConfiguration
配置启用类，支持 Nacos 配置动态刷新。

## Nacos 配置示例

### 配置文件信息
- **Data ID**: `pointprod-retry-strategy`
- **Group**: `DEFAULT_GROUP`
- **配置格式**: YAML

### 配置结构

```yaml
pointprod:
  retry:
    # 重试批处理大小配置
    batch-size: 100                         # 每批处理的失败任务数量，默认100
    
    strategy:
      # 默认重试策略配置
      default-config:
        max-retry-count: 3                    # 默认最大重试次数
        initial-delay-seconds: 60              # 默认初始重试间隔（秒）
        max-delay-seconds: 3600                # 默认最大重试间隔（秒）
        backoff-multiplier: 2.0                # 默认重试间隔倍数（指数退避）
        enable-exponential-backoff: true       # 默认启用指数退避
      
      # 针对不同任务类型的重试策略配置
      task-configs:
        # MQ订单状态变更消息重试策略
        MQ_ORDER_STATUS_CHANGED_MSG:
          max-retry-count: 5
          initial-delay-seconds: 30
          max-delay-seconds: 1800
          backoff-multiplier: 2.0
          enable-exponential-backoff: true
        
        # MQ订单支付超时消息重试策略
        MQ_ORDER_PAYMENT_TIMEOUT_MSG:
          max-retry-count: 3
          initial-delay-seconds: 120
          max-delay-seconds: 7200
          backoff-multiplier: 3.0
          enable-exponential-backoff: true
```

## 配置参数说明

### 批处理配置

| 参数名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| batch-size | int | 每批处理的失败任务数量，控制重试调度器每次处理的任务数量，避免一次性处理过多任务导致性能问题 | 100 |

### 重试策略配置

| 参数名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| max-retry-count | int | 最大重试次数，达到此次数后任务将被标记为失败 | 3 |
| initial-delay-seconds | long | 初始重试延迟时间（秒） | 60 |
| max-delay-seconds | long | 最大重试延迟时间（秒），防止延迟时间过长 | 3600 |
| backoff-multiplier | double | 指数退避倍数，每次重试延迟时间 = 上次延迟时间 * 倍数 | 2.0 |
| enable-exponential-backoff | boolean | 是否启用指数退避，false时使用固定延迟 | true |

## 重试延迟计算公式

### 指数退避模式（enable-exponential-backoff = true）
```
delay = min(initial_delay * (backoff_multiplier ^ retry_count), max_delay)
```

### 固定延迟模式（enable-exponential-backoff = false）
```
delay = initial_delay
```

## 使用示例

### 1. 在业务代码中使用

```java
@Service
public class MessageService {
    
    @Autowired
    private RetryStrategyDomainService retryStrategyDomainService;
    
    public void processMessage(TaskResouceTypeEnum taskType, int currentRetryCount) {
        // 检查是否可以重试
        if (!retryStrategyDomainService.canRetry(taskType, currentRetryCount)) {
            log.warn("Task cannot retry: {}", taskType);
            return;
        }
        
        // 计算下次重试时间
        LocalDateTime nextRetryTime = retryStrategyDomainService.calculateNextRetryTime(taskType, currentRetryCount);
        
        // 获取最大重试次数
        int maxRetryCount = retryStrategyDomainService.getMaxRetryCount(taskType);
        
        // 获取策略描述
        String description = retryStrategyDomainService.getRetryStrategyDescription(taskType);
        log.info("Retry strategy: {}", description);
    }
}
```

### 2. 重试时间序列示例

以 `MQ_ORDER_STATUS_CHANGED_MSG` 配置为例：
- 初始延迟: 30秒
- 退避倍数: 2.0
- 最大延迟: 1800秒
- 最大重试次数: 5次

重试时间序列：
- 第1次重试: 30秒后 (30 * 2^0)
- 第2次重试: 60秒后 (30 * 2^1)
- 第3次重试: 120秒后 (30 * 2^2)
- 第4次重试: 240秒后 (30 * 2^3)
- 第5次重试: 480秒后 (30 * 2^4)
- 超过5次后标记为失败

## 支持的任务类型

根据 `TaskResouceTypeEnum` 枚举，当前支持的任务类型包括：

- `MQ_ORDER_STATUS_CHANGED_MSG`: MQ订单状态变更消息
- `MQ_ORDER_CREATED_MSG`: MQ订单创建消息
- `MQ_ORDER_PAYMENT_TIMEOUT_MSG`: MQ订单支付超时消息

## 配置动态刷新

配置支持 Nacos 动态刷新，修改配置后无需重启应用即可生效。

## 代码质量改进

### 重构历史

#### 2024-01-XX: 消息投递任务创建重构
- **问题**: `ensureOrderStatusChangedMessageDelivery` 和 `ensurePayTimeoutMessageDelivery` 方法存在大量重复代码
- **解决方案**: 提取公共方法 `createMessageDeliveryTask`
- **改进效果**:
  - 减少代码重复，提高可维护性
  - 统一消息投递任务创建逻辑
  - 增强代码的一致性和可读性
  - 便于后续扩展新的消息类型

```java
// 重构前：两个方法各自实现相似逻辑，约80行重复代码
// 重构后：使用公共方法，减少到约10行调用代码
private <T extends TaskData> TaskRetry createMessageDeliveryTask(
    String taskRetryId, String userId, String orderId,
    TaskResouceTypeEnum resourceType, Date createTime,
    T taskData, MessageQueueEnum messageQueueEnum,
    String operationName)
```

## 注意事项

1. **配置优先级**: 任务特定配置 > 默认配置
2. **最小延迟**: 系统确保延迟时间不小于1秒
3. **最大延迟**: 实际延迟时间不会超过 `max-delay-seconds` 设置
4. **重试次数**: 从0开始计数，达到 `max-retry-count` 时停止重试
5. **时区处理**: 系统使用系统默认时区进行时间计算
6. **代码维护**: 新增消息类型时，优先考虑复用 `createMessageDeliveryTask` 方法

## 监控和调试

可以通过以下方式获取重试策略信息：

```java
// 获取策略描述
String description = retryStrategyDomainService.getRetryStrategyDescription(taskType);

// 获取重试时间序列（调试用）
String sequence = ((RetryStrategyDomainServiceImpl) retryStrategyDomainService)
    .getRetryTimeSequence(taskType);
```

## 扩展说明

如需添加新的任务类型，请：
1. 在 `TaskResouceTypeEnum` 中添加新的枚举值
2. 在 Nacos 配置中添加对应的重试策略配置
3. 重启应用或等待配置刷新生效