-- 初始化DB数据
INSERT INTO fund_biz_sequence (name,min_value,max_value,current_value,step,description,is_deleted,gmt_create,gmt_modified) VALUES
	 ('template',1,99999999,9001,1000,'留言表',0,'2025-06-19 15:37:45','2025-06-25 11:13:13'),
	 ('black_list',1,99999999,401,100,'黑名单表',0,'2025-06-19 15:38:54','2025-06-19 20:35:05'),
	 ('transfer_fund_accumulation',1,99999999,1,1000,'转出获赠累计表',0,'2025-06-19 15:39:34','2025-06-19 15:39:34'),
	 ('contract_confirm',1,99999999,1,100,'确认条款记录表',0,'2025-06-19 15:39:34','2025-06-19 15:39:34'),
	 ('contract',1,99999999,301,100,'條款表',0,'2025-06-19 15:39:34','2025-06-24 17:11:25'),
	 ('transfer_rule',1,99999999,1,100,'轉贈規則',0,'2025-06-19 15:39:34','2025-06-19 15:39:34'),
	 ('limit_cumulate_rule',1,99999999,1,100,'限額規則',0,'2025-06-19 15:39:34','2025-06-19 15:39:34'),
	 ('share_record',1,99999999,101,100,'分享记录表',0,'2025-06-24 11:39:46','2025-06-24 16:21:04'),
	 ('notification_record',1,99999999,1,100,'通知记录表',0,'2025-06-24 11:39:46','2025-06-24 11:39:46'),
	 ('mpay_push_task',1,99999999,1,100,'MPay推送任务表',0,'2025-06-24 11:39:46','2025-06-24 11:39:46');
INSERT INTO fund_biz_sequence (name,min_value,max_value,current_value,step,description,is_deleted,gmt_create,gmt_modified) VALUES
	 ('share_code',10000000,***********,10000020,20,'分享码序列号',0,'2025-06-24 11:48:29','2025-06-24 16:21:04'),
	 ('order_token',1,99999999,201,100,'订单token表',0,'2025-06-19 15:39:34','2025-06-24 17:27:35'),
	 ('order_token_value',1,99999999,701,100,'订单token值',0,'2025-06-19 15:39:34','2025-06-25 09:53:33'),
	 ('fund_unique',1,99999999,601,100,'幂等表',0,'2025-06-19 15:39:34','2025-06-24 17:28:04'),
	 ('fund_order',1,99999999,601,100,'订单表',0,'2025-06-19 15:39:34','2025-06-24 17:28:06'),
	 ('fund_order_env',1,99999999,1,100,'订单环境信息表',0,'2025-06-19 15:39:34','2025-06-19 15:39:34'),
	 ('fund_pay',1,99999999,1,100,'支付表',0,'2025-06-19 15:39:34','2025-06-19 15:39:34'),
	 ('task_retry',1,99999999,501,100,'重试表',0,'2025-06-19 15:39:34','2025-06-24 17:28:08'),
     ('fund_flux',1,99999999,1,100,'资金表',0,'2025-06-19 15:39:34','2025-06-24 17:28:08'),
     ('transfer_relation',1,99999999,1,100,'转赠关系表',0,'2025-06-19 15:39:34','2025-06-24 17:28:08'),
     ('cumulate_task',1,99999999,1,100,'',0,'2025-06-19 15:39:34','2025-06-24 17:28:08');

INSERT INTO point_prod.fund_biz_sequence
(name, min_value, max_value, current_value, step, description, is_deleted, gmt_create, gmt_modified)
VALUES('cumulate_config', 1, ***********, 100, 100, '限额配置表', 0, '2025-07-01 11:33:09', '2025-07-07 09:44:08');


INSERT INTO `point_prod`.`fund_biz_sequence` (`name`, `min_value`, `max_value`, `current_value`, `step`, `description`, `is_deleted`, `gmt_create`, `gmt_modified`) VALUES ('cumulate_account', 1, ***********, 1, 100, '限额累积表', 0, '2025-07-01 10:19:25', '2025-07-01 11:37:25');
INSERT INTO `point_prod`.`fund_biz_sequence` (`name`, `min_value`, `max_value`, `current_value`, `step`, `description`, `is_deleted`, `gmt_create`, `gmt_modified`) VALUES ('cumulate_account_log', 1, ***********, 1, 100, '限额累积日志表', 0, '2025-07-01 11:33:09', '2025-07-01 11:38:49');



INSERT INTO contract (contract_id, title, title_en, content_url, content_en_url, contract_type, version,
                      valid_time, status, gmt_create, gmt_modified, is_deleted)
VALUES ('1', '测试01', 'test01', 'http', 'http', 'MCOIN_TRANSFER', 1, '1970-01-01', 'VALID', '2025-06-23 15:00:03',
        '2025-06-23 15:00:03', 0);

INSERT INTO transfer_rule (transfer_rule_id, level_key, optional_info, limit_rule_ids, status, creator, modifier,
                           gmt_create, gmt_modified, is_deleted)
VALUES ('******************', '3A', '[100,200,300,500,1000]', 'LCR1750320570120234', 'INVALID', 'system', 'system',
        '2025-06-19 16:10:13', '2025-06-19 16:34:51', 0);

INSERT INTO transfer_rule (transfer_rule_id, level_key, optional_info, limit_rule_ids, status, creator, modifier,
                           gmt_create, gmt_modified, is_deleted)
VALUES ('TR1750320613260102', '3A', '[100,200,300,500,1000]', 'LCR1750320570120233', 'VALID', 'system', 'system',
        '2025-06-19 16:10:13', '2025-06-19 16:34:51', 0);

INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (1, 'max_sort', '祝您新年快乐，万事如意！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '新年祝福', 21111, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:01', 0, 'able', 'Happy New Year and best wishes!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (2, 'TEMP_002', '恭喜发财，心想事成！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '新年祝福', 2, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:06', 0, 'able', 'Wish you prosperity and success!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (3, 'TEMP_003', '生日快乐，愿您天天开心！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '生日祝福', 3, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:09', 0, 'able', 'Happy birthday, may you be happy every day!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (4, 'TEMP_004', '春节快乐，阖家幸福！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '春节祝福', 4, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:11', 0, 'able', 'Happy Spring Festival, wish your family happiness!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (5, 'min_sort', '中秋快乐，月圆人圆！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', 'min_sort', 1, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-27 10:28:34', 0, 'able', 'Happy Mid-Autumn Festival, family reunion!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (6, 'TEMP_006', '感谢您的帮助，这是一点心意！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '感谢', 6, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:15', 0, 'able', 'Thank you for your help, this is a small token of appreciation!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (7, 'TEMP_007', '非常感谢您的支持！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '感谢', 7, 'SYSTEM', 'admin', '2025-06-20 16:39:29', '2025-07-02 20:00:12', 0, 'able', 'Thank you very much for your support!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (8, 'TEMP_008', '谢谢您一直以来的照顾！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '感谢', 7, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-27 10:27:14', 0, 'able', 'Thank you for your care all along!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (9, 'TEMP_009', '感恩有您，万分感谢！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', 'same_sort_min_create_time', 7, 'SYSTEM', 'SYSTEM', '2025-06-18 16:39:29', '2025-06-27 10:28:41', 0, 'able', 'Grateful to have you, many thanks!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (10, 'TEMP_010', '谢谢您的友情！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '感谢', 10, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:25', 0, 'able', 'Thank you for your friendship!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (11, 'TEMP_011', '祝您工作顺利，事业有成！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '祝愿', 11, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:28', 0, 'able', 'Wish you success in your work and career!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (12, 'TEMP_012', '愿您平安喜乐，幸福安康！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '祝愿', 12, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:30', 0, 'able', 'May you be safe, happy, and healthy!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (13, 'TEMP_013', '祝您前程似锦，梦想成真！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '祝愿', 13, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:32', 0, 'able', 'Wish you a bright future and your dreams come true!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (14, 'TEMP_014', '愿您快乐每一天！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '祝愿', 14, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:36', 0, 'able', 'May you be happy every day!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (15, 'TEMP_015', '祝您身体健康，万事如意！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '祝愿2222', 15, 'SYSTEM', 'admin', '2025-06-19 16:39:29', '2025-06-27 10:29:04', 1, 'able', 'Wish you good health and all the best!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (16, 'TEMP_016', '您好，这是一点小心意！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '问候', 16, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:42', 0, 'able', 'Hello, this is a small token of appreciation!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (17, 'TEMP_017', '希望您喜欢这份礼物！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '问候是是的', 17, 'SYSTEM', 'admin', '2025-06-19 16:39:29', '2025-07-01 16:44:55', 0, 'able', 'Hope you like this gift!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (18, 'TEMP_018', '请收下这份心意！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '问候', 18, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:48', 0, 'able', 'Please accept this token of appreciation!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (19, 'TEMP_019', '与您分享快乐时光！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '问候', 19, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:50', 0, 'able', 'Sharing happy moments with you!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (20, 'TEMP_020', '愿这份礼物带给您温暖！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2025-12-31 23:59:59', '问候', 20, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:58:52', 0, 'able', 'May this gift bring you warmth!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (21, 'TEMP_021', '加油，你是最棒的！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2024-12-31 23:59:59', '鼓励', 21, 'SYSTEM', 'admin', '2025-06-19 16:39:29', '2025-06-24 17:06:16', 0, 'able', 'Keep going, you are the best!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (22, 'TEMP_022', '相信你一定能成功！', 'MCOIN_TRANSFER', '2024-01-01 00:00:00', '2024-12-31 23:59:59', '鼓励', 22, 'SYSTEM', 'SYSTEM', '2025-06-19 16:39:29', '2025-06-19 19:43:30', 1, 'able', 'Believe in yourself, you can succeed!');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (24, '182250619503000002002003', 'Excepteur', 'MCOIN_TRANSFER', '2024-11-28 11:14:41', '2025-08-13 07:36:37', 'do velit sit et sint', 63, 'admin', 'admin', '2025-06-19 17:57:14', '2025-06-30 19:41:38', 0, 'disabled', 'Exception');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (25, '182250623503000003002006', '1111', 'MCOIN_TRANSFER', '2025-06-23 19:07:32', '2025-06-23 19:07:32', '测试', 111111, 'admin', 'admin', '2025-06-23 19:07:45', '2025-06-23 19:08:12', 1, 'able', '1111');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (26, '182250623503000003003009', '😍🥰😘😗😗😗😗🥰😗😔👏👨‍🎓💯😇🙈🙉😸🫠🫠😊😊', 'MCOIN_TRANSFER', '2025-06-23 19:16:05', '2025-07-03 19:16:05', '11', 0, 'admin', 'admin', '2025-06-23 19:16:14', '2025-07-03 09:49:57', 0, 'able', '😃😃😃😃');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (27, '182250624503000004002008', '🏄‍♀️🚣🚣‍♂️🏌️‍♂️🏊‍♂️🚴🏋️🚵‍♀️👨‍👨‍👧👩‍👩‍👧‍👧🐤🦦🐔🦃🥐🥩🍽️🍴🔪🫙🚂🚄🚞🚢🎳🏑📫☸️3️⃣©️5️⃣☺️☺️☺️🌤️🌤️🌤️🇼🇸🇽🇰🇻🇦🇲🇾🇮🇹🔳🔟😉😊😇🥰😍🤗🤐🫡😶‍🌫️🫥🤪😗🤩🫠☺️', 'MCOIN_TRANSFER', '2025-06-24 16:05:57', '2025-06-24 16:05:57', '', 0, 'admin', 'admin', '2025-06-24 16:10:33', '2025-06-24 16:16:03', 1, 'able', 'Sports and emojis');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (32, '182250625243000008002009', 'sed anim', 'MCOIN_TRANSFER', '2025-04-22 14:38:56', '2025-08-01 08:42:16', 'labore laborum', 1, '123', 'admin', '2025-06-25 11:13:13', '2025-06-30 19:11:22', 1, 'able', 'but animation');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (33, '182250626243000009002007', 'sed anim改啟用', 'MCOIN_TRANSFER', '2025-06-19 16:00:00', '2025-06-30 23:00:00', 'labore laborum', 1, 'admin', 'admin', '2025-06-26 21:04:11', '2025-06-30 19:11:18', 1, 'disabled', 'but animation changed to enabled');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (34, '182250627243000010002007', '测试测试测试', 'MCOIN_TRANSFER', '2025-06-27 11:57:18', '2025-06-27 11:57:18', '123222222', 1, 'admin', 'admin', '2025-06-27 11:57:33', '2025-06-27 16:58:31', 1, 'able', 'Test test test');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (35, '182250627243000010003000', '123232🥰12354是的', 'MCOIN_TRANSFER', '2025-06-27 04:04:24', '2025-06-27 04:04:24', '2434343', 0, 'admin', 'admin', '2025-06-27 12:04:37', '2025-06-30 10:44:55', 1, 'able', '123232🥰12354 Yes');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (36, '182250627243000011002002', 'asd', 'MCOIN_TRANSFER', '2025-06-27 08:43:22', '2025-06-28 08:43:22', '', 0, 'admin', 'admin', '2025-06-27 16:43:31', '2025-06-27 17:45:29', 1, 'able', 'asd');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (37, '182250701243000012002008', '新增测试', 'MCOIN_TRANSFER', '2025-07-01 10:50:41', '2025-07-01 10:50:41', '', 1, 'admin', 'admin', '2025-07-01 10:50:57', '2025-07-01 11:20:29', 1, 'disabled', 'New test');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (38, '182250701243000013002007', '测试超限', 'MCOIN_TRANSFER', '2025-07-01 16:39:54', '2025-08-28 16:39:54', '', 0, 'admin', 'admin', '2025-07-01 16:40:35', '2025-07-01 16:44:50', 0, 'disabled', 'Test overflow');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (39, '182250702243000014002008', '1234123231345134123412312341232313451341', 'MCOIN_TRANSFER', '2025-07-02 17:51:13', '2025-07-02 17:51:13', '123', 999999, 'admin', 'admin', '2025-07-02 17:51:24', '2025-07-02 18:04:22', 1, 'able', '1234123231345134123412312341232313451341');
INSERT INTO point_prod.template (id, template_id, template_content, biz_type, start_time, end_time, template_remark, sort, creator, modifier, gmt_create, gmt_modified, is_deleted, status, template_content_en) VALUES (40, '182250702243000014003003', '非常感谢您的支持！', 'MCOIN_TRANSFER', '2025-07-02 18:08:07', '2025-07-02 18:08:07', 'test', 0, 'admin', 'admin', '2025-07-02 18:08:12', '2025-07-03 10:41:59', 0, 'able', 'Thank you very much for your support! 123123 English content English content ☺️☺️☺️😘😘😘');




INSERT INTO order_token (token_id,user_id,order_type,token,use_status,gmt_create,gmt_modified) VALUES
	 ('182250624201000000002007','**************','TRANSFER_MCOIN','182250624202000000002001','WAITING_USE','2025-06-24 15:25:08','2025-06-24 16:35:59');



-- 訂單創建消息測試數據
INSERT INTO fund_order (fund_order_id,title,title_en,actor_user_id,fund_type,fund_mode,request_id,fund_order_status,fund_amount,fund_amount_currency,charge_amount,charge_amount_currency,tax_amount,tax_amount_currency,paid_total_amount,paid_total_amount_currency,accept_total_amount,accept_total_amount_currency,accept_expiry_time,pay_expiry_time,auto_accept,stage_level,complete_time,memo,extend_info,is_deleted,gmt_create,gmt_modified) VALUES
	 ('182250624203000000503009','mCoin轉贈','mCoin transfer','**************','TRANSFER_MCOIN','OTO','182250624202000000502003','INIT',1000.00,'mcoin',0.00,'',0.00,'',1000.00,'mcoin',1000.00,'mcoin','2025-06-24 17:43:04','2025-06-24 17:43:04','1','ONE_STAGE','2025-06-24 17:28:04','memo_549c17a0fac8','',0,'2025-06-24 17:28:04','2025-06-24 17:30:12');


-- 訂單關單消息測試數據
INSERT INTO fund_order (fund_order_id,title,title_en,actor_user_id,fund_type,fund_mode,request_id,fund_order_status,fund_amount,fund_amount_currency,charge_amount,charge_amount_currency,tax_amount,tax_amount_currency,paid_total_amount,paid_total_amount_currency,accept_total_amount,accept_total_amount_currency,accept_expiry_time,pay_expiry_time,auto_accept,stage_level,complete_time,memo,extend_info,is_deleted,gmt_create,gmt_modified) VALUES
	 ('182250624203000000503000','mCoin轉贈','mCoin transfer','**************','TRANSFER_MCOIN','OTO','182250624202000000502003','INIT',1000.00,'mcoin',0.00,'',0.00,'',1000.00,'mcoin',1000.00,'mcoin','2025-06-24 17:43:04','2025-06-24 17:43:04','1','ONE_STAGE','2025-06-24 17:28:04','memo_549c17a0fac8','',0,'2025-06-24 17:28:04','2025-06-24 17:30:12');
INSERT INTO fund_pay (fund_pay_id,fund_order_id,sub_order_type,user_id,asset_tool,account_no,participant_ext_info,identify_id,identify_id_type,status,amount,currency,occur_time,request_extend_info,system_extend_info,is_deleted,gmt_create,gmt_modified) VALUES
	 ('182250707206000008402593','182250624203000000503000','PAY','**************','MCOIN','mcidRvrZZdGfm2vFOi0fMT','{"userLevel":"2"}','+86-***********','MOBILE_NO','INIT',10,'MCOIN','2025-07-07 19:58:21','','',0,'2025-07-07 19:58:21','2025-07-07 20:13:22'),
	 ('182250707206000008403817','182250624203000000503000','ACCEPT','**************','MCOIN','mcidqb1pg64wrkTpDNrY1A','{"headLogo":"http://************:8881/commimages/userPhoto/Hc580e9e0eccee3bc1cc782b4d4d31dfa20240227140949.jpg","nickName":"harper"}','+86-***********','MOBILE_NO','INIT',10,'MCOIN','2025-07-07 19:58:21','','',0,'2025-07-07 19:58:21','2025-07-07 20:13:22');


-- 收银台测试数据
INSERT INTO fund_order (fund_order_id,title,title_en,actor_user_id,fund_type,fund_mode,request_id,fund_order_status,fund_amount,fund_amount_currency,charge_amount,charge_amount_currency,tax_amount,tax_amount_currency,paid_total_amount,paid_total_amount_currency,accept_total_amount,accept_total_amount_currency,accept_expiry_time,pay_expiry_time,auto_accept,stage_level,complete_time,memo,extend_info,is_deleted,gmt_create,gmt_modified) VALUES
	 ('182250624203000000303982','mCoin轉贈','mCoin transfer','**************','TRANSFER_MCOIN','OTO','182250624202000000202982','INIT',1000.00,'mcoin',0.00,'',0.00,'',1000.00,'mcoin',1000.00,'mcoin','2025-06-24 17:23:18','2025-06-24 17:23:18','1','ONE_STAGE','2025-06-24 17:08:18','memo_549c17a0fac8','',0,'2025-06-24 17:08:18','2025-06-24 17:08:18');

INSERT INTO `transfer_fund_accumulation` (`fund_accumulation_id`, `user_id`, `fund_out_amount`, `fund_out_count`, `fund_in_amount`, `fund_in_count`, `description`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('1', '**************', 0.00, 0, 0.00, 0, '造的数据', '2025-06-18 20:01:15', '2025-06-30 16:55:10', 0);
INSERT INTO `transfer_fund_accumulation` (`fund_accumulation_id`, `user_id`, `fund_out_amount`, `fund_out_count`, `fund_in_amount`, `fund_in_count`, `description`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('2', '**************', 0.00, 0, 0.00, 0, '造的数据', '2025-06-18 20:01:34', '2025-06-30 16:54:05', 0);
INSERT INTO `transfer_fund_accumulation` (`fund_accumulation_id`, `user_id`, `fund_out_amount`, `fund_out_count`, `fund_in_amount`, `fund_in_count`, `description`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('3', '**************', 0.00, 0, 0.00, 0, '造的数据', '2025-06-18 20:02:31', '2025-06-30 16:52:18', 0);
INSERT INTO `transfer_fund_accumulation` (`fund_accumulation_id`, `user_id`, `fund_out_amount`, `fund_out_count`, `fund_in_amount`, `fund_in_count`, `description`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('5', '00000100037831', 0.00, 0, 0.00, 0, '', '2025-06-30 16:20:11', '2025-06-30 16:20:11', 0);
INSERT INTO `transfer_fund_accumulation` (`fund_accumulation_id`, `user_id`, `fund_out_amount`, `fund_out_count`, `fund_in_amount`, `fund_in_count`, `description`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('6', '**************', 0.00, 0, 0.00, 0, '', '2025-06-30 16:20:11', '2025-06-30 16:20:11', 0);
INSERT INTO `transfer_fund_accumulation` (`fund_accumulation_id`, `user_id`, `fund_out_amount`, `fund_out_count`, `fund_in_amount`, `fund_in_count`, `description`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('7', '**************', 0.00, 0, 0.00, 0, '', '2025-06-30 16:20:11', '2025-06-30 16:20:11', 0);

INSERT INTO `black_list` (`black_list_id`, `entity_type`, `entity_id`, `reason`, `account_type`, `gmt_create`, `gmt_modified`, `is_deleted`, `creator`, `modifier`) VALUES ('3', 'PERSON', '**************', '', 'USER_ID', '2025-06-18 20:04:52', '2025-06-30 16:52:39', 0, '', 'admin');
INSERT INTO `black_list` (`black_list_id`, `entity_type`, `entity_id`, `reason`, `account_type`, `gmt_create`, `gmt_modified`, `is_deleted`, `creator`, `modifier`) VALUES ('182250619502000000302005', 'PERSON', '**************', '', 'USER_ID', '2025-06-19 20:35:06', '2025-06-30 16:53:58', 1, 'admin', 'admin');
INSERT INTO `black_list` (`black_list_id`, `entity_type`, `entity_id`, `reason`, `account_type`, `gmt_create`, `gmt_modified`, `is_deleted`, `creator`, `modifier`) VALUES ('182250627242000000402004', 'PERSON', '**************', '', 'USER_ID', '2025-06-27 17:34:02', '2025-07-01 16:48:27', 0, 'admin', 'admin');
INSERT INTO `black_list` (`black_list_id`, `entity_type`, `entity_id`, `reason`, `account_type`, `gmt_create`, `gmt_modified`, `is_deleted`, `creator`, `modifier`) VALUES ('182250627242000000402005', 'PERSON', '**************', '', 'USER_ID', '2025-06-27 17:34:02', '2025-07-01 16:48:27', 1, 'admin', 'admin');

INSERT INTO `contract` (`contract_id`, `title`, `title_en`, `content_url`, `content_en_url`, `contract_type`, `version`, `valid_time`, `status`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('182250630401000000606003', '测试条款协议', 'contract title99123545', 'https://oss-mcoin-uat.fookunion.com/dev/contract0f3f6e53756c8700c540761a59f09c0d.html', 'https://oss-mcoin-uat.fookunion.com/dev/contract62216e2411d0dedcde8fd774cf66eb20.html', 'MCOIN_TRANSFER', 15, '2026-06-18', 'VALID', '2025-06-30 16:21:30', '2025-06-30 16:21:30', 0);

INSERT INTO `contract_confirm` (`contract_confirm_id`, `contract_id`, `user_id`, `status`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('1', '182250624506000000002001', '**************', 'AGREE', '2025-06-23 17:47:26', '2025-06-30 16:58:56', 0);
INSERT INTO `contract_confirm` (`contract_confirm_id`, `contract_id`, `user_id`, `status`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('2', '182250630401000000604003', '**************', 'AGREE', '2025-06-23 17:47:26', '2025-06-30 17:08:52', 0);
INSERT INTO `contract_confirm` (`contract_confirm_id`, `contract_id`, `user_id`, `status`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('182250626402000000002987', '182250624506000000002002', '00000102214985', 'AGREE', '2025-06-26 19:16:48', '2025-06-26 20:27:27', 0);
INSERT INTO `contract_confirm` (`contract_confirm_id`, `contract_id`, `user_id`, `status`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('182250626402000000102812', '182250624506000000002002', '00000100713815', 'AGREE', '2025-06-26 20:09:04', '2025-06-27 09:34:18', 0);


INSERT INTO `point_prod`.`transfer_relation` (`transfer_relation_id`, `actor_user_id`, `participant_user_id`, `participant_user_additional_info`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('182250703245000000201085', '00000101439089', '00000101509508', '{\"headLogo\":\"http://************:8881/commimages/userPhoto/H5332ef4ffcd7b830c566e1e360f2198020241220113400.jpg\",\"nickName\":\"dev 大家\",\"sameNameFlag\":null,\"userLevel\":null}', '2025-07-03 16:38:51', '2025-07-03 16:38:51', 0);
INSERT INTO `point_prod`.`transfer_relation` (`transfer_relation_id`, `actor_user_id`, `participant_user_id`, `participant_user_additional_info`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('182250703245000000301175', '00000101439089', '00000100501144', '{\"headLogo\":null,\"nickName\":\"Hans\",\"sameNameFlag\":null,\"userLevel\":null}', '2025-07-03 17:04:58', '2025-07-03 17:04:58', 0);

-- transfer_rule
INSERT INTO `transfer_rule` (`transfer_rule_id`, `level_key`, `optional_info`, `limit_rule_ids`, `status`, `creator`, `modifier`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('182250702602000001902006', '1', '[100,300]', '182250702601000002302001', 'VALID', 'admin', 'admin', '2025-07-02 16:44:42', '2025-07-02 16:44:42', 0);
INSERT INTO `transfer_rule` (`transfer_rule_id`, `level_key`, `optional_info`, `limit_rule_ids`, `status`, `creator`, `modifier`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('182250702602000001903001', '2', '[100,300]', '182250702601000002303002', 'VALID', 'admin', 'admin', '2025-07-02 16:45:08', '2025-07-02 16:45:08', 0);
INSERT INTO `transfer_rule` (`transfer_rule_id`, `level_key`, `optional_info`, `limit_rule_ids`, `status`, `creator`, `modifier`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('182250702602000001904000', 'JR', '[100,300]', '182250702601000002304003', 'VALID', 'admin', 'admin', '2025-07-02 16:45:32', '2025-07-02 16:45:32', 0);
INSERT INTO `transfer_rule` (`transfer_rule_id`, `level_key`, `optional_info`, `limit_rule_ids`, `status`, `creator`, `modifier`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('182250702602000001905001', '3A', '[100,300,1000]', '182250702601000002305004', 'VALID', 'admin', 'admin', '2025-07-02 16:46:02', '2025-07-02 16:46:02', 0);
INSERT INTO `transfer_rule` (`transfer_rule_id`, `level_key`, `optional_info`, `limit_rule_ids`, `status`, `creator`, `modifier`, `gmt_create`, `gmt_modified`, `is_deleted`) VALUES ('182250702602000001906002', '3B', '[100,300,1000]', '182250702601000002306009', 'VALID', 'admin', 'admin', '2025-07-02 16:46:32', '2025-07-02 16:46:32', 0);

