-- 初始化表结构
SET
GLOBAL max_connections = 1000;

CREATE TABLE fund_biz_sequence
(
    id            bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
    name          varchar(64)  DEFAULT ''                NOT NULL comment '名称',
    min_value     bigint(20) DEFAULT 0 NOT NULL comment '最小值',
    max_value     bigint(20) DEFAULT 0 NOT NULL comment '最大值',
    current_value bigint(20) DEFAULT 0 NOT NULL comment '当前值',
    step          bigint(20) DEFAULT 10 NOT NULL comment '步长',
    description   varchar(256) DEFAULT ''                NOT NULL comment '描述',
    is_deleted    tinyint(3) DEFAULT 0 NOT NULL comment '是否删除',
    gmt_create    timestamp    DEFAULT CURRENT_TIMESTAMP NOT NULL comment '创建时间',
    gmt_modified  timestamp    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL comment '更新时间',
    PRIMARY KEY (id),
    UNIQUE INDEX (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci comment='资金序列表';


CREATE TABLE `contract`
(
    `id`             bigint       NOT NULL AUTO_INCREMENT,
    `contract_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '条款id',
    `title`          varchar(255) NOT NULL DEFAULT '' COMMENT '条款标题',
    `title_en`       varchar(255) NOT NULL DEFAULT '' COMMENT '条款英文标题',
    `content_url`    varchar(255) NOT NULL DEFAULT '' COMMENT '条款内容',
    `content_en_url` varchar(255) NOT NULL DEFAULT '' COMMENT '条款英文内容',
    `contract_type`  varchar(128) NOT NULL DEFAULT '' COMMENT '合约类型:MCOIN_TRANSFER',
    `version`        int(11) NOT NULL DEFAULT '0' COMMENT '合约版本',
    `valid_time`     DATE         NOT NULL DEFAULT '1970-01-01' COMMENT '生效时间',
    `status`         varchar(16)  NOT NULL DEFAULT 'VALID' COMMENT '状态,VALID,INVALID',
    gmt_create       timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted       tinyint      NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`),
    KEY              `idx_contract_id` (`contract_id`),
    UNIQUE KEY `uk_contract_type_version` (`contract_type`, `version`)
) COMMENT='条款管理表';

CREATE TABLE `contract_confirm`
(
    id                  bigint(20) NOT NULL AUTO_INCREMENT,
    contract_confirm_id varchar(64) NOT NULL DEFAULT '' COMMENT '业务id',
    contract_id         varchar(64) NOT NULL DEFAULT '' COMMENT '条款id',
    user_id             VARCHAR(64) NOT NULL DEFAULT '' COMMENT '用户ID',
    `status`            varchar(64) NOT NULL DEFAULT 'AGREE' COMMENT '是否同意：DISAGREEE-不同意，AGREE-同意',
    gmt_create          timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified        timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted          tinyint     NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY uk_user_id_contract_id (user_id,contract_id),
    KEY                 `idx_contract_confirm_id` (`contract_confirm_id`)
) COMMENT='用户确认条款记录表';

CREATE TABLE black_list
(
    id            BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    black_list_id varchar(64)           DEFAULT '' COMMENT '业务id',
    entity_type   VARCHAR(64)  NOT NULL COMMENT '本次固定为：PERSON,实体类型(PERSON-个人/COMPANY-企业)',
    entity_id     VARCHAR(64)  NOT NULL COMMENT '本次固定为： mPay的custId，实体唯一标识',
    reason        VARCHAR(255) NOT NULL COMMENT '加入黑名单原因',
    account_type  VARCHAR(32)  NOT NULL COMMENT '本次固定为： USER_ID，账户类型(IP/EMAIL/ID_CARD等)',
    gmt_create    timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_template_id (entity_type,entity_id,account_type) COMMENT '业务ID唯一索引',
    creator       VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '创建人',
    modifier      VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '修改人',
    is_deleted    tinyint      NOT NULL DEFAULT '0',
    KEY           idx_black_list_id (black_list_id),
    KEY           idx_entity_id (entity_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='黑名单表';

CREATE TABLE `transfer_rule`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `transfer_rule_id` varchar(64)   NOT NULL DEFAULT '' COMMENT '转账规则ID',
    `level_key`        varchar(32)   NOT NULL DEFAULT '' COMMENT '等级ID',
    `optional_info`    varchar(1024) NOT NULL DEFAULT '' COMMENT '可选配置信息,[100,200,300]',
    `limit_rule_ids`   varchar(255)           DEFAULT '' COMMENT '限额规则ID列表,逗号分隔,1,2',
    `status`           varchar(32)            DEFAULT 'VALID/INVALID',
    `creator`          varchar(64)   NOT NULL DEFAULT '' COMMENT '创建人',
    `modifier`         varchar(64)   NOT NULL DEFAULT '' COMMENT '修改人',
    `gmt_create`       timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`     timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除/1-已删除)',
    PRIMARY KEY (`id`),
    KEY                `idx_transfer_rule_id` (`transfer_rule_id`),
    KEY                `idx_level_key` (`level_key`)
) COMMENT='转账规则表';

CREATE TABLE transfer_relation
(
    id                               BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    transfer_relation_id             varchar(64)           DEFAULT '' COMMENT '业务id',
    actor_user_id                    VARCHAR(64)  NOT null DEFAULT '' COMMENT '付款方ID',
    participant_user_id              VARCHAR(64)  NOT null DEFAULT '' COMMENT '收款方用户ID',
    participant_user_additional_info VARCHAR(512) NOT NULL DEFAULT '' COMMENT '接收方附加信息',
    gmt_create                       timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified                     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted                       tinyint      NOT NULL DEFAULT '0',
    -- 索引
    UNIQUE KEY uk_transfer_relation (actor_user_id, participant_user_id) COMMENT '转出接收用户关系唯一索引',
    KEY                              idx_gmt_create (gmt_create),
    KEY                              idx_participant_user_id (participant_user_id),
    KEY                              idx_transfer_relation_id (transfer_relation_id)
) COMMENT='转赠用户关系表';


CREATE TABLE `template` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `template_id` varchar(64) NOT NULL DEFAULT '' COMMENT '业务ID',
    `template_content` varchar(255)   NOT NULL COMMENT '留言内容',
    `template_content_en` varchar(255)   NOT NULL COMMENT '英文留言内容',
    `biz_type` varchar(64)   NOT NULL COMMENT '业务类型，目前固定为MCOIN_TRANSFER',
    `start_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '启用时间',
    `end_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '失效时间',
    `template_remark` varchar(255)   NOT NULL DEFAULT '' COMMENT '留言备注',
    `sort` int(11) NOT NULL COMMENT '显示顺序',
    `creator` varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
    `modifier` varchar(64)  NOT NULL DEFAULT '' COMMENT '修改人',
    `gmt_create` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除/1-已删除)',
    `status` varchar(64) DEFAULT '' COMMENT '是否生效：able ，disabled',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_template_id` (`template_id`) ,
    KEY `idx_sort` (`sort`),
    KEY `idx_start_time` (`start_time`) ,
    KEY `idx_end_time` (`end_time`),
    KEY `idx_gmt_create` (`gmt_create`)
) COMMENT = '留言表';

-- point_prod.notification_records definition
CREATE TABLE `notification_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `notification_id` varchar(64)  NOT NULL COMMENT '通知唯一标识',
  `notification_type` varchar(16)  NOT NULL DEFAULT '' COMMENT 'ACCEPT-接收,SEND-发送',
  `resource_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '关联id，例如订单号',
  `resource_type` varchar(16)  NOT NULL DEFAULT '' COMMENT 'TRANSFER_SUCCESS',
  `task_id` varchar(64)  NOT NULL DEFAULT '' COMMENT 'mpay 任务id',
  `user_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '用户ID',
  `title` varchar(255)  NOT NULL DEFAULT '' COMMENT '通知标题',
  `content` varchar(1024)  NOT NULL DEFAULT '' COMMENT '通知内容',
  `extra_data` varchar(2048)  NOT NULL DEFAULT '' COMMENT '扩展数据',
  `status` varchar(16)  NOT NULL DEFAULT 'UNREAD' COMMENT '状态：UNREAD-未读，READ-已读',
  `push_status` varchar(16)  NOT NULL DEFAULT 'INIT' COMMENT 'Push状态：INIT-未发送，SUCCESS-已发送，FAILED-发送失败',
  `push_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Push发送时间',
  `read_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  `resource_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关联数据时间，主要用于通知列表排序',
  `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_deleted` tinyint(3) NOT NULL DEFAULT '0' COMMENT '1- 已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `notification_records_notification_id_IDX` (`notification_id`),
  UNIQUE KEY `notification_records_resource_id_IDX` (`resource_id`, `resource_type`, `notification_type`, `user_id`),
  KEY `idx_user_status_type_deleted_create` (`user_id`, `status`, `notification_type`,`resource_type`)
)  COMMENT = '通知记录表';

-- point_prod.share_records definition

CREATE TABLE `share_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `share_records_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '业务id',
  `share_records_type` varchar(16)  NOT NULL DEFAULT '' COMMENT 'ACCEPT-接收,SEND-发送',
  `share_code` varchar(64)  NOT NULL DEFAULT '' COMMENT '分享编码',
  `user_id` varchar(32)  NOT NULL DEFAULT '' COMMENT '用户ID',
  `content_type` varchar(32)  NOT NULL DEFAULT '' COMMENT '分享内容类型：\'TRANSFER_CODE\' - 转赠',
  `content_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '分享内容ID（订单ID等）',
  `share_content` varchar(1024)  NOT NULL DEFAULT '' COMMENT '分享内容标题',
  `share_url` varchar(1024)  NOT NULL DEFAULT '' COMMENT '分享链接',
  `channel_type` varchar(32)  NOT NULL DEFAULT '' COMMENT '分享渠道：WECHAT, WHATSAPP',
  `extra_data` varchar(2048)  NOT NULL DEFAULT '' COMMENT '扩展数据（存储活动配置、渠道信息等）',
  `share_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分享时间',
  `expire_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '过期时间',
  `is_deleted` tinyint(3) NOT NULL DEFAULT '0' COMMENT '1- 已删除',
  `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_records_id` (`share_records_id`),
  UNIQUE KEY `share_records_content_id_IDX` (`content_id`, `content_type`, `share_records_type`, `channel_type`, `user_id`),
  KEY `idx_content` (`content_type`, `content_id`)
) COMMENT = '分享记录表';


-- point_prod.fund_flux definition

CREATE TABLE `fund_flux` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `fund_flux_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '交换明细业务id',
  `fund_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '资金订单号',
  `fund_sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单号',
  `actor_role_id` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'actor_role_id',
  `fund_flux_type` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '资金交换类型()',
  `asset_flux_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '资产交换id',
  `flux_status` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '交换明细状态',
  `relation_flux_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关联交换id',
  `flux_accept_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '受理时间',
  `flux_finish_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '完成时间',
  `channel_info` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '渠道信息',
  `system_extend_info` varchar(4096) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '系统扩展信息',
  `flux_error_code` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '错误码',
  `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `fund_flux_id` (`fund_flux_id`),
  KEY `fund_order_id` (`fund_order_id`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '资金单据交换明细表';


-- point_prod.fund_order definition

CREATE TABLE `fund_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `fund_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `title` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '订单名称',
  `title_en` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '英文订单名称',
  `actor_user_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作员用户id',
  `fund_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '资金类型(TRANSFER_MCOIN)',
  `fund_mode` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '资金模式',
  `request_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求编号',
  `fund_order_status` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '订单状态()',
  `fund_amount` decimal(19,2) NOT NULL DEFAULT '0.00' COMMENT '资金金额',
  `fund_amount_currency` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '币种',
  `charge_amount` decimal(19,2) NOT NULL DEFAULT '0.00' COMMENT '收费金额',
  `charge_amount_currency` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '收费金额币种',
  `tax_amount` decimal(19,2) NOT NULL DEFAULT '0.00' COMMENT '税金额',
  `tax_amount_currency` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '税金额币种',
  `paid_total_amount` decimal(19,2) NOT NULL DEFAULT '0.00' COMMENT '订单支付总额',
  `paid_total_amount_currency` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '支付金额币种',
  `accept_total_amount` decimal(19,2) NOT NULL DEFAULT '0.00' COMMENT '收款总金额',
  `accept_total_amount_currency` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '收款总金额币种',
  `accept_expiry_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '受理过期时间(2nd process user accepted expiry time)',
  `pay_expiry_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '支付到期时间',
  `auto_accept` varchar(1) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '是否自动受理',
  `stage_level` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '阶段等级',
  `complete_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '完成时间',
  `memo` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '订单备注',
  `extend_info` varchar(2048) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '系统扩展信息(system extend info for accept fail reason etc)',
  `is_deleted` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `fund_order_id` (`fund_order_id`),
  KEY `actor_user_id` (`actor_user_id`),
  KEY `request_id` (`request_id`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '资金订单表';


-- point_prod.fund_order_env definition

CREATE TABLE `fund_order_env` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `order_env_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '环境业务id',
  `order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `device_model` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '设备型号',
  `device_brand` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '设备品牌',
  `mac` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '设备mac',
  `imei` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'imei',
  `imsi` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '国际移动用户识别码',
  `screen_size` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '屏幕大小',
  `os` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '系统名称',
  `os_version` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '系统版本',
  `app` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'app',
  `app_version` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作系统版本',
  `sdk_version` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'sdk的版本',
  `language` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '语言',
  `browser` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '浏览器',
  `ip` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'ip地址',
  `latlng_alg` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '经纬度算法',
  `lng` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '经度',
  `lat` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `terminal_type` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '终端类型',
  `network_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '网络类型：2G3G4G5G',
  `ext_info` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '扩展信息',
  `is_deleted` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `order_env_id` (`order_env_id`),
  KEY `order_id` (`order_id`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '资金订单环境信息表';


-- point_prod.fund_pay definition

CREATE TABLE `fund_pay` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `fund_pay_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付业务id',
  `fund_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型(ACCEPT:收款,PAY:付款)',
  `user_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户编号',
  `asset_tool` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '资金工具(MCOIN:积分余额)',
  `account_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内部账号',
  `participant_ext_info` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '扩展信息',
  `identify_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'identifyId',
  `identify_id_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份id类型(MOBILE_NO:手机号,)',
  `status` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '状态()',
  `amount` decimal(19,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `currency` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '币种',
  `occur_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间',
  `request_extend_info` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求参数扩展信息',
  `system_extend_info` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '系统扩展信息',
  `complete_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '完成时间',
  `is_deleted` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `fund_pay_id` (`fund_pay_id`),
  KEY `fund_order_id` (`fund_order_id`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '付款和收款子订单数据表';


-- point_prod.fund_unique definition

CREATE TABLE `fund_unique` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `global_unique_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '幂等业务id',
  `request_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求id',
  `biz_id` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '业务id',
  `biz_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '业务类型',
  `is_deleted` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_idempotent_control` (`request_id`, `biz_id`, `biz_type`),
  KEY `global_unique_id` (`global_unique_id`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单幂等表';


-- point_prod.order_token definition

CREATE TABLE `order_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `token_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'token id',
  `user_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户id',
  `order_type` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '订单类型',
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'token',
  `use_status` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '使用状态：WAITING_USE/USED',
  `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id_order_type` (`user_id`, `order_type`),
  KEY `token_id` (`token_id`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单token表';


-- point_prod.task_retry definition

CREATE TABLE `task_retry` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_retry_id` varchar(64)  NOT NULL COMMENT '业务id',
  `resource_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '来源id，可以用于关联某个业务表',
  `resource_type` varchar(64)  NOT NULL DEFAULT '' COMMENT '来源id的类型，用于指明哪种业务表',
  `try_count` int(11) NOT NULL DEFAULT '0' COMMENT '已尝试次数',
  `status` varchar(16)  NOT NULL DEFAULT 'INIT' COMMENT 'INIT-待处理，FINISH-已完成，FAILED-已达最大尝试次数',
  `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1- 已删除',
  `next_retry` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下次尝试时间，注意这个是指在这个时间之后，并且与当前时间差小于一定范围。',
  `max_try_count` int(11) NOT NULL DEFAULT '0' COMMENT '最大尝试次数',
  `task_config` varchar(2048)  NOT NULL DEFAULT '' COMMENT 'mq的其他发送配置信息',
  `task_data` varchar(4096)  NOT NULL DEFAULT '' COMMENT 'MQ的消息体，过长建议只存此表id，消费时查表使用attachment',
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_retry_id_uidx` (`task_retry_id`) ,
  KEY `task_retry_resource_id_type_IDX` (`resource_id`, `resource_type`) ,
  KEY `task_next_retry_time_IDX` (`next_retry`)
) ;

CREATE TABLE transfer_fund_accumulation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    fund_accumulation_id varchar(64) DEFAULT '' COMMENT '业务id',
    user_id varchar(64) DEFAULT '' NOT NULL comment '用户id',
    fund_out_amount decimal(19, 2) DEFAULT 0 NOT NULL COMMENT '转出资金',
    fund_out_count INT default 0  NOT NULL COMMENT '转出次数',
    fund_in_amount decimal(19, 2) DEFAULT 0 NOT NULL COMMENT '转入资金',
    fund_in_count INT default 0  NOT NULL COMMENT '转入次数',
    description VARCHAR(255) default '' NOT NULL COMMENT '描述',
    gmt_create TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除/1-已删除)',
    UNIQUE KEY uk_user_id (user_id) COMMENT '用户ID唯一索引'
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转出获赠累计表';



-- 限额表 begin-------------------------------------

-- 1. 限额累计配置表
CREATE TABLE `limit_cumulate_config` (
                                         `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                         `config_id` VARCHAR(32) NOT NULL COMMENT '业务ID',
                                         `title` VARCHAR(64) NOT NULL COMMENT '规则名称',
                                         `tnt_inst_id` VARCHAR(64) NOT NULL COMMENT '租户ID',
                                         `scene_code` VARCHAR(64) NOT NULL COMMENT '场景码',
                                         `scene_name` VARCHAR(32) NOT NULL COMMENT '场景名称',
                                         `strategy` VARCHAR(64) NOT NULL COMMENT '限额策略',
                                         `status` VARCHAR(16) NOT NULL COMMENT '状态',
                                         `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
                                         PRIMARY KEY (`id`),
                                         UNIQUE KEY `uk_config_id` (`config_id`),
                                         UNIQUE KEY `uk_scene_code` (`scene_code`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='限额规则配置表';

-- 2. 限额规则表
CREATE TABLE `limit_cumulate_rule` (
                                       `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `rule_id` VARCHAR(32) NOT NULL COMMENT '业务ID',
                                       `title` VARCHAR(64) NOT NULL COMMENT '规则名称',
                                       `tnt_inst_id` VARCHAR(64) NOT NULL COMMENT '租户ID',
                                       `scene_code` VARCHAR(32) NOT NULL COMMENT '场景码',
                                       `expression` VARCHAR(512) default '' NOT NULL COMMENT '规则表达式',
                                       `amount_range` VARCHAR(512) default '' NOT NULL COMMENT '金额范围',
                                       `count_range` VARCHAR(512) default '' NOT NULL COMMENT '次数范围',
                                       `status` VARCHAR(16) NOT NULL COMMENT '状态',
                                       `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_rule_id` (`rule_id`),
                                       KEY `idx_scene_code` (`scene_code`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='限额规则表';

-- 3. 累计任务表
CREATE TABLE `limit_cumulate_task` (
                                       `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `task_id` VARCHAR(32) NOT NULL COMMENT '业务ID',
                                       `tnt_inst_id` VARCHAR(64) NOT NULL COMMENT '租户ID',
                                       `scene_code` VARCHAR(32) NOT NULL COMMENT '场景码',
                                       `biz_no` VARCHAR(64) NOT NULL COMMENT '业务号',
                                       `biz_time` TIMESTAMP NOT NULL COMMENT '业务发生时间',
                                       `biz_context` varchar(2048) NOT NULL DEFAULT '' COMMENT '业务参数',
                                       `status` VARCHAR(16) NOT NULL COMMENT '任务状态',
                                       `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_task_id` (`task_id`),
                                       UNIQUE KEY `uk_scene_biz` (`scene_code`, `biz_no`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='累计任务表';

-- 4. 限额累计账户表
CREATE TABLE `limit_cumulate_account` (
                                          `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                          `account_id` VARCHAR(32) NOT NULL COMMENT '账务ID',
                                          `tnt_inst_id` VARCHAR(64) NOT NULL COMMENT '租户ID',
                                          `principal_id` VARCHAR(64) NOT NULL COMMENT '主体ID',
                                          `principal_type` VARCHAR(32) NOT NULL COMMENT '主体类型',
                                          `scene_code` VARCHAR(32) NOT NULL COMMENT '场景码',
                                          `strategy` VARCHAR(64) NOT NULL COMMENT '限制策略',
                                          `count` BIGINT DEFAULT 0 COMMENT '累计次数',
                                          `amount` BIGINT DEFAULT 0 COMMENT '累计额度',
                                          `currency` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '币种',
                                          `biz_time` VARCHAR(32) NOT NULL COMMENT '业务时间',
                                          `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
                                          PRIMARY KEY (`id`),
                                          UNIQUE KEY `uk_account_id` (`account_id`),
                                          UNIQUE KEY `uk_account_unique` (`scene_code`, `principal_id`, `biz_time`, `strategy`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='限额累计账户表';




-- 5. 限额累计日志表
CREATE TABLE `limit_cumulate_log` (
                                      `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `log_id` VARCHAR(64) NOT NULL COMMENT '日志ID',
                                      `tnt_inst_id` VARCHAR(64) NOT NULL COMMENT '租户ID',
                                      `cumulate_account_id` VARCHAR(32) NOT NULL COMMENT '累计账户ID',
                                      `principal_id` VARCHAR(64) NOT NULL COMMENT '主体ID',
                                      `principal_type` VARCHAR(32) NOT NULL COMMENT '主体类型',
                                      `scene_code` VARCHAR(32) NOT NULL COMMENT '场景码',
                                      `strategy` VARCHAR(64) NOT NULL COMMENT '限制策略',
                                      `biz_no` VARCHAR(64) NOT NULL COMMENT '业务单号',
                                      `biz_time` TIMESTAMP NOT NULL COMMENT '业务发生时间',
                                      `currency` VARCHAR(16) NOT NULL DEFAULT '' COMMENT '币种',
                                      `amount` BIGINT DEFAULT 0 COMMENT '操作金额',
                                      `count` BIGINT DEFAULT 0 COMMENT '操作次数',
                                      `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
                                      PRIMARY KEY (`id`),
                                      UNIQUE KEY `uk_log_id` (`log_id`),
                                      KEY `idx_cumulate_account_id` (`cumulate_account_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='限额累计日志表';




-- 限额表 end  -------------------------------------

