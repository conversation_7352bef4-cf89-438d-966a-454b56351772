spring:
  profiles:
    active: test
  main:
    allow-bean-definition-overriding: true
  application:
    name: pointprod-service
  transaction:
    default-timeout: 30
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
  datasource:
#    hikari:
#      maximum-pool-size: 100
#      minimum-idle: 50
#      idle-timeout: 30000  # 30 seconds
#      max-lifetime: 1800000  # 30 minutes
#      connection-timeout: 30000  # 30 seconds
    dynamic:
      primary: pointprod
      strict: false
      datasource:
        pointprod:
          type: com.zaxxer.hikari.HikariDataSource
          driver-class-name: org.mariadb.jdbc.Driver
          url: ********************************************************************************************************************************************
          username: root
          password: ""
          hikari:
            maximum-pool-size: 20
            minimum-idle: 10
  rabbitmq:
    listener:
      simple:
        auto-startup: false
      direct:
        auto-startup: false

  jackson:
    deserialization:
      fail-on-unknown-properties: false
      fail-on-ignored-properties: false
      fail-on-null-for-primitives: false
      accept-single-value-as-array: true
    serialization:
      fail-on-empty-beans: false
    mapper:
      sort-properties-alphabetically: true
    parser:
      allow-single-quotes: true

mybatis-plus:
  mapper-locations: classpath*:com/agtech/**/xml/*Mapper.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    enable-sql-runner: true

logging:
  level:
    com.baomidou.mybatisplus: DEBUG
    com.agtech.**.mapper: DEBUG
    com.agtech.pointprod.service.infrastructure.dal.mapper: DEBUG
    org.springframework.transaction: TRACE
    com.zaxxer.hikari: TRACE
    com.baomidou.mybatisplus.extension.plugins.pagination.optimize: TRACE


    
# mCoin積分系統
mcoin:
  url: https://mcoin-mall-internal-dev.macaupass-devops.com
  appid: mc8g5LiBfIpJcIvUWn
  publickey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxuiAcGf/DZASDToHatxeZmpwIp712DwWeIEwaQCdsmY9hGILb8jJOl3qD9bU4gD8znFGK9SLhvRAQn044wY7+xs6is+R1vGVpg6KYzIz71i8AHcPvKkMuEz9BSQJp6HjVmK48fLaIlwZBqMHq5/G6wfFi6N+2utuoqNgTJQW42eo2wyQoE+L4NKsusm25EIN7ZMfuK1ZXWvBFZ0bIQJVyRvo0gIuneZEL1RXPEGwDKnFpsWWDKc/pPDMqKRD2QNkeI61c/4hFID5q9Tb9CfWk3UZbCWH6O3UnoPqQDJMPnFVaOhpoBHqYH1acowE8FcXM0ZSePswJkUChfrBJpFdXwIDAQAB
  privatekey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDf++HcqMvHnzDRcL3yMTZfhuYF7Xf4Qx3PhoKR6lrlSTtgxfCEfxvcFJQ+q8LP7NicyuE/9FSy07vWEC6mlPtJlq1yiYghDgvJynEiw5fwAk7b7MggcAfsGgTY+OLcxLGsKGt3Q9dOj4nVc3tMI69tci/6rmrzO0PPsonffgooAghFC7s+cGiON7/blfZRvBBQ2NBh8rdRVZMpQZb8nlYAZ29Zx1HkLktsOd6FrCNO7kHY4P1RtroD95vArl3YwxGpAa6CzZs1E4jk16bT1m0zoHkMt8eBuz9mqmcyqF5w85EmZsHrpVvhnIFY/ptrUP6EK9UIBPXh54amHo0lgpqrAgMBAAECggEBAJ8QtFAOl9mGVhcHJ/3tsR2aIZUoFcD7eRo9/lA9zJt0rHSHXc3aryBWhQkU1d7v5s1Cz0Cp9dShxY26JEctGmAiX78tqL1AymJeIIZ9vVM3cGWC/IT8ysODntmvtvztuvf2JIuoZCloioxJ3NAvr4/cPfKbF1zxQ7Emq/9J9VB+/LTWyFmiFGaS4bb11FW6QHY4MaCzBoiAXIKJ860Nr8i7lix3BErF2TlGm/odeKg+0xn6RVjNwQ8RPOU6ZQRP73eMNq2OhDNG8F8LNGXBvktIdo1KXGI4SBFeQADXMRGRMlB+G3HPg7MfrcYnbA4lx8MNyL9hw0zJxlpCbzt3MMECgYEA9xJmc4l1c95DOf616vXFpYgIo/43sCZWnYthxfS/DDg7DKaY26gpra5lKH2OyQtxxp0qX8qtzSckmVC4/jW8H2eCn8G1R3hFOgX7Y3Wj06s4esoWi9hY7lF6WSwudkoa9RPWWsCfzu6WFt739W1RTg/rxCqnDjaBZvLIOcWfV3sCgYEA6BPmsgcOFgb27jTXF/UlTTNdDPoyNw6esl0e9cSYQFdGhLx1qn1OnmJnbt0J2tqNZKqJ/R5gx00hRQAu353OuOwkiqsyY63IlB7rpTcF4ZJQVNO5vqJZ0pB2C6HSulnmQyeVlbDipc7CP/s++LAMQ4IoZKma7DiIknX7OD9BypECgYAVYSM6Zi+iqh35G8BUJ5ZFv6K3xhy9gmPGWDRKs+YAQbFiY9wgTcnlfIzGVy8O2I2s2Ra8mUY21WdGWQTZAn9X3FYiStnL6G1dGv1o2tolS9CkV25iBYOUg7ppkvgmRj1U7bWDvt1VQ7H7IqokM6Rwc9I79FDmWvMRnHqU/TPPlwKBgH8z0UGI7maSYKwFmFOQUWa0HW9sfzOANumKctrAa7bwXz2H0nKlBf937jtsuecT3WXst39eNCtpEjAwvoBgjZr8C7dZyF+sNAFDxWMj+nw95vvnpKphcBwihCEyDD+J4NS0EKAgeMnqvru06ToDvGUQJTWvZLPO9MRaGF0nBteRAoGBAJ61axSDUhK136MCBv2q+wye2AnX80XwH5xp5O70+Tn1+v10TlsF3bRs4RvpXTZ5Zi6EF/TVilZpDbmqCieI0rKpmF0fCNNFMo7xYKke0Lx7wTsp5vnsqYuD2lGS4HWfkmBLGue0PLvoK9C7yFyERGJGih46tnz8+yVtOBkCPrXt
  mall:
    url: https://mcoin-mall-internal-dev.macaupass-devops.com/mcoin-mall

# mPay系统
mPay:
  gateway:
    url: https://gateway-mp-dev.macaupass-devops.com:7443
  push:
    url: http://************:8881
    appId: mpgFoUFGujHK8ZnzvB
    payerCode: mcoinPayerSuc
    payeeCode: mcoinPayeeSuc


point:
  gateway:
    domain: https://gateway-dev.macaupass-devops.com
  share:
    short:
      domain: https://mcoinmall-dev.macaupass-devops.com:7080
    origin:
      domain: https://h5-dev.macaupass.com
  h5:
    domain: https://h5-mcoin-dev.macaupass-devops.com
  gray:
    switch:
      transfer: 1
      feature1: 0
      feature2: 1
      feature3: 2
    white:
      transfer: "1,**************"
      feature1: ""
      feature2: "**************"
      feature3: ""
    rate:
      transfer: 0
      feature1: 0
      feature2: 30
      feature3: 0

rabbitmq:
  queues:
    config:
      order-status-changed:
        exchange: "order.status.changed.exchange"
        routing-key: "order.status.changed.key"
        queue: "order.status.changed.queue"
      
      order-status-created:
        exchange: "order.status.created.exchange"
        routing-key: "order.status.created.key"
        queue: "order.status.created.queue"
      
      order-payment-timeout:
        exchange: "order.payment.timeout.exchange"
        routing-key: "order.payment.timeout.key"
        queue: "order.payment.timeout.queue"
      # 订单支付成功-付款人队列
      order-payment-success-payer:
        exchange: "order.payment.success.payer.exchange"
        routing-key: "order.payment.success.payer.key"
        queue: "order.payment.success.payer.queue"

      # 订单支付成功-收款人队列
      order-payment-success-payee:
        exchange: "order.payment.success.payee.exchange"
        routing-key: "order.payment.success.payee.key"
        queue: "order.payment.success.payee.queue"

      # 订单支付成功-付款人队列
      order-payment-success-payer-delay:
        exchange: "order.payment.success.payer.delay.exchange"
        routing-key: "order.payment.success.payer.delay.key"
        queue: "order.payment.success.payer.queue"

      # 订单支付成功-收款人队列
      order-payment-success-payee-delay:
        exchange: "order.payment.success.payee.delay.exchange"
        routing-key: "order.payment.success.payee.delay.key"
        queue: "order.payment.success.payee.queue"

      # 订单支付成功-转出获赠累计队列
      order-payment-success-fund-accumulation:
        exchange: "order.payment.success.fund.accumulation.exchange"
        routing-key: "order.payment.success.fund.accumulation.key"
        queue: "order.payment.success.fund.accumulation.queue"

      # 订单状态变更绑定到订单支付成功-转赠关系队列
      order-payment-success-transfer-relation:
        exchange: "order.payment.success.transfer.relation.exchange"
        routing-key: "order.payment.success.transfer.relation.key"
        queue: "order.payment.success.transfer.relation.queue"


secretKey:
  config:
    mpay:
      clientId: '********'
      privateKey: 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCJ1uUTAfRW6ddTJyglFCb6zydCwyx00Ku1wWnrKhDpNNijpQNECtCyMJamx6fRKILltjshMDYyX/rMhtlfn7uG41brbmCbuSavVCIHER1iSiMhygn22/ohYKxfhGQyk7rf6XBTFBQ+x0nB8VtOdr2Lu8RqaEleoA4mlll31Pf4GHWnCdjAmdlBJrqcAWpglRWu8TPy5uJEUg3oy06XUVK5IpBKhr1GlrBzihrG13k2tddZYfEIaKutCV9T+Gsf1BY1prcS5AXEbC4LedYnNDwDh7LuB4q4VgKs1L8tySqGKhlb8ZMfvWklEsirseM9Bx87+AFXZNiXo4zuRWP7xzulAgMBAAECggEAdwhHrCFs8r1fgGR5K4P6oW7Q1bucIYiuL/hF0pnyHqHPT4pJhrHcRXTmNEKIbnwQhfTXnTJ4Kwptl7//6a8UD0k1n3wjG3dJq/D4raee+2lQa3aBIlgW6koEsTuwF2kx4PlDUcsuLISG8l/OXLT3vLq6xLUoXvnTHomJ24zjUq/wPlpAgBHmqM2vxTAZHifLa/noUhqNklmArONqQ0F1IE9BfzxUCQNFxGwLcWmSAzJGDKo9loQPLFHa1yfp+9Gp7Cfmnd2UjdNko1MciI0YYNZxINN6e6tAMRpGbWwKms0h3unx9jnwd/RTsf8+vt9OxDsgcsgvoV1yj9gfSHrqZQKBgQC9ban188Uy4sdGilk0tS5FRDhJWoShLygJGfpYe2vn/HkIcENfWwE4aMI/LwaTJ0QickfKZ+qGcUG0mujauzk0yDw3xc0Xt04tQh+bBtoyretTUtbKjAuNfax7dmLcUOdUrbVyjhG2YwpjywLcjJPh7zu93stEMQnHtKG8yoDFiwKBgQC6R+th+3Ew+HI9qZfTGq1Gy2F0qp45/Gcjbw+ITRpFUPIPKZJ4ZgQ3LIMmABfYhLc4fN/DGbGHHOq45ioWo4IwTVwVE06jRgtFC6FGjvaapwXxyowfDfQt/mzIN10T5Lysre2Y/B1y43KlIEl7ciZKICYoNGOh1ALz3mnVt5UJjwKBgQCuvgMTSBSZpGl/wRAZdyl/7DagNEg1CHM+MiCmjvQzxMtB5Y3A4Qp3JuxJrO+7v3Uy8YkYRlQqQUADgwNbA2r6Ldye9nEDd60+Qsk1EVUnexiifMB3iEj/9Pavzb3Uzy0XCEdhrXzZavOiqxuwqF2jBjVuKaAI/9OtzguFRKkVkQKBgQCe7QcD9EqQ1hZZR1yRrbvRn2jI6VvO6ulvkKw2xFk6dCHgD76324mTLPXDMOcnQaszlU4unLgaJvCWyT91SxCidw2tqWg44mRxBsgUc5ovPXpT3FOJlxURPHTAqINPmqHhzQ3drJLPVv+To6UtnajPKviC6pdqFIBkYQf4XwTXPwKBgGx8LM/SEelVCp8Ehpian7u7dSkBGIjpzvHwMvGiWe57238C+sCpuIH18c+J2X0biI2Sdpm6omnexTD1P4p8ni4JkEJ0VWbTXkfQHMDP37twECbQpm3WAc9YQkdkDcJqMm5Vr8ASL43DdDjIx39xR/8aaPVTaGxT1sHiUrTIHpt5'
      publicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApUygS7QAjss+kN47szjHh1MMAMLOgVGXJo1kF7WeQtoJKyLS4l+UEYolKGCfCecTLcxmVubNqvpXPjt5uAgWzCtIFRQvYY4eRq9IaVZQCkSIDzT4+5hD9tNRsLf0rlY3tabdAG0ghqWOjDJDr0gYEo0tYtodwiqhAcMaplOAMFnMNmfoijxSPn+ntRUw2YyXBnFDVZt9hAB+HMA4YlaguPtOy1GGZmX3QpRQDUaf7QgNI0Roh5qeVVhrP+caYkqZn6st1b67ckseE4UnAJJ0ALK8STyIDuB0fKnuotmPNF5cOpwnHCxx8y2YiQ+kJuoPuffP9cbUjPCFg3D/P8XJSwIDAQAB'

mpay:
  signVerify: false