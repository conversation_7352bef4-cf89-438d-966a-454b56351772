<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="ACTIVE_PROFILE" source="spring.profiles.active"/>
    <springProperty scope="context" name="appName" source="spring.application.name"/>
    <springProperty scope="context" name="endpoint" source="sls.endpoint"/>
    <springProperty scope="context" name="accessKeyId" source="sls.accessKeyId"/>
    <springProperty scope="context" name="accessKeySecret" source="sls.accessKeySecret"/>
    <springProperty scope="context" name="project" source="sls.project"/>
    <springProperty scope="context" name="logStore" source="sls.logStore"/>
    <property name="LOGS"  value="${user.dir}/logs"/>


    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %green(%d{ISO8601}) %highlight(%-5level) [%X{traceId},%X{service}] [%blue(%t)] %yellow(%C{1.}:%L): %msg%n%throwable
            </Pattern>
        </layout>
    </appender>

    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 大于配置级别  但是不包含ERROR-->
        <if condition='"${ACTIVE_PROFILE}".equals("prd")'>
            <then>
                <filter class="ch.qos.logback.classic.filter.LevelFilter">
                    <level>ERROR</level>
                    <onMatch>DENY</onMatch>
                    <onMismatch>ACCEPT</onMismatch>
                </filter>
            </then>
        </if>
        <file>${LOGS}/pointprod-service/pointprod-service.log</file>

        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %green(%d{ISO8601}) %highlight(%-5level) [%X{traceId},%X{service}] [%blue(%t)] %yellow(%C{1.}:%L): %msg%n%throwable
            </Pattern>
        </layout>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- rollover daily and when the file reaches 10 MegaBytes -->
            <if condition='"${ACTIVE_PROFILE}".equals("prd")'>
                <then>
                    <fileNamePattern>${LOGS}/pointprod-service/pointprod-service-%d{yyyy-MM-dd_HH}.log</fileNamePattern>
                </then>
                <else>
                    <fileNamePattern>${LOGS}/pointprod-service/pointprod-service-%d{yyyy-MM-dd}.log</fileNamePattern>
                </else>
            </if>
        </rollingPolicy>
    </appender>

    <!-- 只打印error日志 -->
    <appender name="RollingFile-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${LOGS}/pointprod-service/pointprod-service-error.log</file>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{ISO8601} %-5level [%X{traceId},%X{service}] [%t] %C{1.}:%L: %msg%n%throwable
            </Pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGS}/pointprod-service/pointprod-service-error-%d{yyyy-MM-dd_HH}.log</fileNamePattern>
        </rollingPolicy>
    </appender>

    <!--为了防止进程退出时，内存中的数据丢失，请加上此选项-->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <appender name="aliyun" class="com.aliyun.openservices.log.logback.LoghubAppender">
        <!--必选项-->
        <!-- 账号及网络配置 -->
        <endpoint>${endpoint}</endpoint>
        <accessKeyId>${accessKeyId}</accessKeyId>
        <accessKeySecret>${accessKeySecret}</accessKeySecret>
        <project>${project}</project>
        <logStore>${logStore}</logStore>
        <!--必选项 (end)-->

        <!-- 可选项 -->
        <!--指定日志主题，默认为 ""，可选参数-->
        <topic>${appName}</topic>
        <!--指的日志来源，默认为应用程序所在宿主机的 IP，可选参数-->
        <!--<source>your source</source>-->

        <!-- 可选项 详见 '参数说明'-->
        <!--单个 producer 实例能缓存的日志大小上限，默认为 100MB-->
        <totalSizeInBytes>104857600</totalSizeInBytes>
        <!--如果 producer 可用空间不足，调用者在 send 方法上的最大阻塞时间，默认为 60 秒。为了不阻塞打印日志的线程，强烈建议将该值设置成 0-->
        <maxBlockMs>0</maxBlockMs>
        <!--执行日志发送任务的线程池大小，默认为可用处理器个数-->
        <ioThreadCount>8</ioThreadCount>
        <!--当一个 ProducerBatch 中缓存的日志大小大于等于 batchSizeThresholdInBytes 时，该 batch 将被发送，默认为 512 KB，最大可设置成 5MB-->
        <batchSizeThresholdInBytes>524288</batchSizeThresholdInBytes>
        <!--当一个 ProducerBatch 中缓存的日志条数大于等于 batchCountThreshold 时，该 batch 将被发送，默认为 4096，最大可设置成 40960-->
        <batchCountThreshold>4096</batchCountThreshold>
        <!--一个 ProducerBatch 从创建到可发送的逗留时间，默认为 2 秒，最小可设置成 100 毫秒-->
        <lingerMs>2000</lingerMs>
        <!--如果某个 ProducerBatch 首次发送失败，能够对其重试的次数，默认为 10 次，如果 retries 小于等于 0，该 ProducerBatch 首次发送失败后将直接进入失败队列-->
        <retries>10</retries>
        <!--首次重试的退避时间，默认为 100 毫秒，Producer 采样指数退避算法，第 N 次重试的计划等待时间为 baseRetryBackoffMs * 2^(N-1)-->
        <baseRetryBackoffMs>100</baseRetryBackoffMs>
        <!--重试的最大退避时间，默认为 50 秒-->
        <maxRetryBackoffMs>50000</maxRetryBackoffMs>

        <!-- 可选项 通过配置 encoder 的 pattern 自定义 log 的格式 -->
        <encoder>
            <pattern>%d{ISO8601} %-5level [%X{traceId},%X{service}] [%t] %C{1.}:%L: %msg%n%throwable</pattern>
        </encoder>

        <!-- 可选项 设置 time 字段呈现的格式 -->
        <timeFormat>yyyy-MM-dd HH:mm:ss.SSS</timeFormat>
        <!-- 可选项 设置 time 字段呈现的时区 -->
        <timeZone>Asia/Shanghai</timeZone>
        <!-- 可选项 设置 mdc 字段 -->
        <mdcFields>traceId,service,elapsedTime,code,msg,status</mdcFields>
    </appender>

    <if condition='"${ACTIVE_PROFILE}".equals("prd")'>
        <then>
            <logger name="org.springframework.cache" level="INFO"/>
            <logger name="com.agtech.pointprod.service.infrastructure.integration.mpay.translator.MPayClient" level="debug"/>
            <logger name="com.agtech.pointprod.service.infrastructure.integration.mpay.translator.mPayPushClient" level="debug"/>
            <logger name="com.agtech.pointprod.service.infrastructure.integration.mcoinmall.translator.McoinMallClient" level="debug"/>
        </then>
        <else>
            <logger name="com.baomidou.dynamic" level="debug"/>
            <logger name="com.mcoin.mall.dao" level="debug"/>
            <logger name="com.agtech.pointprod.service.infrastructure.integration.mpay.translator.MPayClient" level="debug"/>
            <logger name="org.springframework.jdbc.datasource.DataSourceTransactionManager" level="DEBUG"/>
            <logger name="org.springframework.transaction.interceptor" level="trace"/>
            <logger name="org.springframework.transaction" level="DEBUG"/>
            <logger name="com.agtech.pointprod.service.infrastructure.integration.mpay.translator.mPayPushClient" level="debug"/>
            <logger name="com.agtech.pointprod.service.infrastructure.integration.mcoinmall.translator.McoinMallClient" level="debug"/>
        </else>
    </if>

    <appender  name="sentinel-record-log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS}/csp/pointprod-service-sentinel-record.log</file>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%-5level %logger - %msg%n</Pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <if condition='"${ACTIVE_PROFILE}".equals("prd")'>
                <then>
                    <maxHistory>7</maxHistory>
                </then>
                <else>
                    <maxHistory>1</maxHistory>
                </else>
            </if>
            <fileNamePattern>${LOGS}/csp/pointprod-service-sentinel-record-%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>

    <logger name="sentinelRecordLogger" level="info" additivity="false">
        <appender-ref ref="sentinel-record-log"/>
    </logger>


    <appender  name="sentinel-command-center-log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS}/csp/pointprod-service-sentinel-command-center.log</file>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%-5level %logger - %msg%n</Pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <if condition='"${ACTIVE_PROFILE}".equals("prd")'>
                <then>
                    <maxHistory>7</maxHistory>
                </then>
                <else>
                    <maxHistory>1</maxHistory>
                </else>
            </if>
            <fileNamePattern>${LOGS}/csp/pointprod-service-sentinel-command-center-%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>

    <logger name="sentinelCommandCenterLogger" level="info" additivity="false">
        <appender-ref ref="sentinel-command-center-log"/>
    </logger>

    <!-- LOG everything at INFO level -->
    <root level="info">
        <if condition='"${ACTIVE_PROFILE}".equals("local") || "${ACTIVE_PROFILE}".equals("test")'>
            <then>
                <appender-ref ref="Console"/>
            </then>
            <else>
                <appender-ref ref="aliyun"/>
            </else>
        </if>
        <if condition='!"${ACTIVE_PROFILE}".equals("prd")'>
            <then>
                <appender-ref ref="RollingFile"/>
                <appender-ref ref="RollingFile-ERROR"/>
            </then>
        </if>
    </root>

</configuration>
