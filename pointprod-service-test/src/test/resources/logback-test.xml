<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="LOGS" value="${user.dir}/logs"/>

    <!-- 控制台输出 -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
            </Pattern>
        </layout>
    </appender>

    <!-- 文件输出 -->
    <appender name="TestFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS}/pointprod-service/test.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOGS}/pointprod-service/test-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 设置日志级别 -->
    <logger name="com.agtech.pointprod" level="DEBUG"/>
    <logger name="org.springframework" level="INFO"/>

    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="Console"/>
        <appender-ref ref="TestFile"/>
    </root>
</configuration> 