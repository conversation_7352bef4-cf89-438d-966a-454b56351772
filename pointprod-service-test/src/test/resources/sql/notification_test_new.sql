-- 清理测试数据
DELETE FROM notification_records WHERE notification_id IN ('notify_001', 'notify_002', 'notify_003', 'notify_004', 'notify_005', 'notify_006', 'notify_007', 'notify_008');
DELETE FROM share_records WHERE share_records_id IN ('share_001', 'share_002', 'share_003', 'share_004', 'share_005', 'share_006', 'share_007', 'share_008', 'share_009');

-- 插入通知记录
INSERT INTO notification_records (
    notification_id, notification_type, resource_id, resource_type, task_id, user_id, 
    title, content, extra_data, status, push_status, push_time, read_time, 
    gmt_create, gmt_modified, is_deleted
) VALUES
-- 用户123的未读通知 (付款方)
('notify_001', 'SEND', 'order_001', 'TRANSFER_SUCCESS', 'task_001', '123', 
 'mCoin转账', 'mCoin转账通知', '{"amount": 100}', 'UNREAD', 'SUCCESS', 
 '2024-01-01 10:00:00', '1970-01-01 00:00:00', '2024-01-01 10:00:00', '2024-01-01 10:00:00', 0),

('notify_002', 'SEND', 'order_002', 'TRANSFER_SUCCESS', 'task_002', '123', 
 'mCoin转账', 'mCoin转账通知', '{"amount": 200}', 'UNREAD', 'SUCCESS', 
 '2024-01-01 11:00:00', '1970-01-01 00:00:00', '2024-01-01 11:00:00', '2024-01-01 11:00:00', 0),

-- 用户123的已读通知
('notify_003', 'SEND', 'order_003', 'TRANSFER_SUCCESS', 'task_003', '123', 
 'mCoin转账', 'mCoin转账通知', '{"amount": 300}', 'READ', 'SUCCESS', 
 '2024-01-01 09:00:00', '2024-01-01 12:00:00', '2024-01-01 09:00:00', '2024-01-01 12:00:00', 0),

-- 用户456的通知 (收款方)
('notify_004', 'ACCEPT', 'order_001', 'TRANSFER_SUCCESS', 'task_004', '456', 
 'mCoin收款', 'mCoin收款通知', '{"amount": 100}', 'UNREAD', 'SUCCESS', 
 '2024-01-01 10:30:00', '1970-01-01 00:00:00', '2024-01-01 10:30:00', '2024-01-01 10:30:00', 0),

('notify_005', 'ACCEPT', 'order_002', 'TRANSFER_SUCCESS', 'task_005', '456', 
 'mCoin收款', 'mCoin收款通知', '{"amount": 200}', 'UNREAD', 'SUCCESS', 
 '2024-01-01 10:15:00', '1970-01-01 00:00:00', '2024-01-01 10:15:00', '2024-01-01 10:15:00', 0),

-- 参考真实数据构建的记录 (付款方通知)
('notify_006', 'SEND', '182250707204000006707140', 'TRANSFER_SUCCESS', 'FFFFFFFF182250707503000001402148', '123', 
 'mCoin转账', 'mCoin转账通知', '{"amount": 500}', 'UNREAD', 'SUCCESS', 
 '2025-07-07 09:44:09', '1970-01-01 00:00:00', '2025-07-07 09:44:09', '2025-07-07 09:44:09', 0),

-- 参考真实数据构建的记录 (收款方通知)
('notify_007', 'ACCEPT', '182250707204000006707140', 'TRANSFER_SUCCESS', 'FFFFFFFF182250707503000001403252', '456', 
 'mCoin收款', 'mCoin收款通知', '{"amount": 500}', 'UNREAD', 'SUCCESS', 
 '2025-07-07 09:44:09', '1970-01-01 00:00:00', '2025-07-07 09:44:09', '2025-07-07 09:44:09', 0),

-- 用户789的通知 (用于shareCode测试)
('notify_008', 'ACCEPT', 'order_share', 'TRANSFER_SUCCESS', 'task_008', '789', 
 'mCoin收款', 'mCoin收款通知', '{"amount": 300}', 'UNREAD', 'SUCCESS', 
 '2025-07-10 09:00:00', '1970-01-01 00:00:00', '2025-07-10 09:00:00', '2025-07-10 09:00:00', 0);

-- 插入分享记录
INSERT INTO share_records (
    share_records_id, share_records_type, share_code, user_id, content_type, content_id,
    share_content, share_url, channel_type, extra_data, share_time, expire_at,
    is_deleted, gmt_create, gmt_modified
) VALUES
-- 有效分享码 - 付款方
('share_001', 'SEND', 'CODE001', '123', 'TRANSFER_COIN', 'order_001',
 'mCoin转赠分享', 'https://example.com/share/CODE001', 'WECHAT', '{}', 
 '2024-01-01 09:00:00', '2024-12-31 23:59:59', 0, '2024-01-01 09:00:00', '2024-01-01 09:00:00'),

-- 有效分享码 - 收款方
('share_002', 'ACCEPT', 'CODE001', '456', 'TRANSFER_COIN', 'order_001',
 'mCoin转赠分享', 'https://example.com/share/CODE001', 'WECHAT', '{}', 
 '2024-01-01 09:05:00', '2024-12-31 23:59:59', 0, '2024-01-01 09:05:00', '2024-01-01 09:05:00'),

-- 另一个有效分享码 - 付款方
('share_003', 'SEND', 'CODE002', '123', 'TRANSFER_COIN', 'order_002',
 'mCoin转赠分享', 'https://example.com/share/CODE002', 'WHATSAPP', '{}', 
 '2024-01-01 10:00:00', '2024-12-31 23:59:59', 0, '2024-01-01 10:00:00', '2024-01-01 10:00:00'),

-- 另一个有效分享码 - 收款方
('share_004', 'ACCEPT', 'CODE002', '456', 'TRANSFER_COIN', 'order_002',
 'mCoin转赠分享', 'https://example.com/share/CODE002', 'WHATSAPP', '{}', 
 '2024-01-01 10:05:00', '2024-12-31 23:59:59', 0, '2024-01-01 10:05:00', '2024-01-01 10:05:00'),

-- 过期分享码
('share_005', 'SEND', 'CODE003', '123', 'TRANSFER_COIN', 'order_003',
 'mCoin转赠分享', 'https://example.com/share/CODE003', 'WECHAT', '{}', 
 '2024-01-01 08:00:00', '2024-01-01 08:30:00', 0, '2024-01-01 08:00:00', '2024-01-01 08:00:00'),
 
-- 基于真实数据的分享码 - 付款方
('share_006', 'SEND', 'RRpZsr', '123', 'TRANSFER_COIN', '182250707204000006707140',
 'mCoin转赠分享', 'https://mcoinmall-dev.macaupass-devops.com:7080/s/RRpZsr', 'OTHER', '{}', 
 '2025-07-07 09:44:13', '2025-10-07 09:44:13', 0, '2025-07-07 09:44:13', '2025-07-07 09:44:13'),

-- 基于真实数据的分享码 - 收款方
('share_007', 'ACCEPT', 'RRpZsr', '456', 'TRANSFER_COIN', '182250707204000006707140',
 'mCoin转赠分享', 'https://mcoinmall-dev.macaupass-devops.com:7080/s/RRpZsr', 'OTHER', '{}', 
 '2025-07-07 09:44:13', '2025-10-07 09:44:13', 0, '2025-07-07 09:44:13', '2025-07-07 09:44:13'),

-- 测试用例的分享码 - 付款方 (发送方用户为"123")
('share_008', 'SEND', 'SHARE123456', '123', 'TRANSFER_COIN', 'order_share',
 'mCoin转赠分享', 'https://example.com/share/SHARE123456', 'WECHAT', '{}', 
 '2025-07-10 08:50:00', '2025-12-31 23:59:59', 0, '2025-07-10 08:50:00', '2025-07-10 08:50:00'),

-- 测试用例的分享码 - 收款方 (接收方用户为"789")
('share_009', 'ACCEPT', 'SHARE123456', '789', 'TRANSFER_COIN', 'order_share',
 'mCoin转赠分享', 'https://example.com/share/SHARE123456', 'WECHAT', '{}', 
 '2025-07-10 08:55:00', '2025-12-31 23:59:59', 0, '2025-07-10 08:55:00', '2025-07-10 08:55:00');
