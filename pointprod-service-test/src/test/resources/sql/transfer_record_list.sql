-- Transfer Record List SQL Script
-- Contains test data for user ************** with both payment and receipt transactions
-- across multiple days

-- Begin transaction
START TRANSACTION;

-- ========== OUTGOING TRANSFERS (User is Payer) ==========

-- Transfer 1 - Today
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307010001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307010001', 'SUCCESS', 100.00, 'MCOIN', 0.00, '', 0.00, '', 100.00, 'MCOIN', 100.00, 'MCOIN', 
NOW(), NOW(), '1', 'ONE_STAGE', NOW(), 'Thank you', '', 0, 
NOW(), NOW());

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307010001', 'FO202307010001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 100.00, 'MCOIN', NOW(), '', '', 0, 
NOW(), NOW());

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307010002', 'FO202307010001', 'ACCEPT', '*********35007', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile2.jpg","nickName":"recipient1"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 100.00, 'MCOIN', NOW(), '', '', 0, 
NOW(), NOW());

-- Transfer 2 - Yesterday
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307020001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307020001', 'SUCCESS', 200.00, 'MCOIN', 0.00, '', 0.00, '', 200.00, 'MCOIN', 200.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 1 DAY), 'Happy birthday', '', 0, 
DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307020001', 'FO202307020001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 200.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 1 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307020002', 'FO202307020001', 'ACCEPT', '*********35008', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile3.jpg","nickName":"recipient2"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 200.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 1 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY));

-- Transfer 3 - 3 days ago
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307030001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307030001', 'SUCCESS', 50.00, 'MCOIN', 0.00, '', 0.00, '', 50.00, 'MCOIN', 50.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 3 DAY), 'Small gift', '', 0, 
DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307030001', 'FO202307030001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 50.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 3 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307030002', 'FO202307030001', 'ACCEPT', '*********35009', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile4.jpg","nickName":"recipient3"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 50.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 3 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY));

-- Transfer 4 - 5 days ago
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307040001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307040001', 'SUCCESS', 150.00, 'MCOIN', 0.00, '', 0.00, '', 150.00, 'MCOIN', 150.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 5 DAY), 'For lunch', '', 0, 
DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307040001', 'FO202307040001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 150.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 5 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307040002', 'FO202307040001', 'ACCEPT', '*********35010', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile5.jpg","nickName":"recipient4"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 150.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 5 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY));

-- Transfer 5 - 7 days ago
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307050001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307050001', 'SUCCESS', 300.00, 'MCOIN', 0.00, '', 0.00, '', 300.00, 'MCOIN', 300.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 7 DAY), 'Party gift', '', 0, 
DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307050001', 'FO202307050001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 300.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 7 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307050002', 'FO202307050001', 'ACCEPT', '*********35011', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile6.jpg","nickName":"recipient5"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 300.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 7 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY));

-- ========== INCOMING TRANSFERS (User is Payee) ==========

-- Incoming Transfer 1 - Today
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307060001', 'mCoin Transfer', 'mCoin Transfer', '*********35020', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307060001', 'SUCCESS', 175.00, 'MCOIN', 0.00, '', 0.00, '', 175.00, 'MCOIN', 175.00, 'MCOIN', 
NOW(), NOW(), '1', 'ONE_STAGE', NOW(), 'Enjoy your gift', '', 0, 
NOW(), NOW());

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307060001', 'FO202307060001', 'PAY', '*********35020', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2A', 
'{"headLogo":"http://example.com/profile7.jpg","nickName":"sender1"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 175.00, 'MCOIN', NOW(), '', '', 0, 
NOW(), NOW());

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307060002', 'FO202307060001', 'ACCEPT', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 175.00, 'MCOIN', NOW(), '', '', 0, 
NOW(), NOW());

-- Incoming Transfer 2 - 2 days ago
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307070001', 'mCoin Transfer', 'mCoin Transfer', '*********35021', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307070001', 'SUCCESS', 225.00, 'MCOIN', 0.00, '', 0.00, '', 225.00, 'MCOIN', 225.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 2 DAY), 'Thanks for your help', '', 0, 
DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307070001', 'FO202307070001', 'PAY', '*********35021', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2B', 
'{"headLogo":"http://example.com/profile8.jpg","nickName":"sender2"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 225.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 2 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307070002', 'FO202307070001', 'ACCEPT', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2B', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 225.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 2 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY));

-- Incoming Transfer 3 - 4 days ago
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307080001', 'mCoin Transfer', 'mCoin Transfer', '*********35022', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307080001', 'SUCCESS', 125.00, 'MCOIN', 0.00, '', 0.00, '', 125.00, 'MCOIN', 125.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 4 DAY), 'Happy shopping', '', 0, 
DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307080001', 'FO202307080001', 'PAY', '*********35022', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2C', 
'{"headLogo":"http://example.com/profile9.jpg","nickName":"sender3"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 125.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 4 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, request_extend_info, system_extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307080002', 'FO202307080001', 'ACCEPT', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2C', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 125.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 4 DAY), '', '', 0, 
DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY));

-- Add more outgoing transfers (to reach at least 20 records total)
-- Transfers 6-10 (different days ago)
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307090001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307090001', 'SUCCESS', 80.00, 'MCOIN', 0.00, '', 0.00, '', 80.00, 'MCOIN', 80.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 6 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 6 DAY), 'Coffee money', '', 0, 
DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 6 DAY));

INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307100001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307100001', 'SUCCESS', 120.00, 'MCOIN', 0.00, '', 0.00, '', 120.00, 'MCOIN', 120.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(NOW(), INTERVAL 8 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 8 DAY), 'Movie tickets', '', 0, 
DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(NOW(), INTERVAL 8 DAY));

INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307110001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307110001', 'SUCCESS', 250.00, 'MCOIN', 0.00, '', 0.00, '', 250.00, 'MCOIN', 250.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 10 DAY), 'Conference gift', '', 0, 
DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY));

INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307120001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307120001', 'SUCCESS', 75.00, 'MCOIN', 0.00, '', 0.00, '', 75.00, 'MCOIN', 75.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 12 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 12 DAY), 'Lunch treat', '', 0, 
DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 12 DAY));

INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307130001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307130001', 'SUCCESS', 180.00, 'MCOIN', 0.00, '', 0.00, '', 180.00, 'MCOIN', 180.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 14 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 14 DAY), 'Dinner celebration', '', 0, 
DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 14 DAY));

-- Add more incoming transfers
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307140001', 'mCoin Transfer', 'mCoin Transfer', '*********35023', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307140001', 'SUCCESS', 110.00, 'MCOIN', 0.00, '', 0.00, '', 110.00, 'MCOIN', 110.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 6 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 6 DAY), 'Project bonus', '', 0, 
DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 6 DAY));

INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307150001', 'mCoin Transfer', 'mCoin Transfer', '*********35024', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307150001', 'SUCCESS', 90.00, 'MCOIN', 0.00, '', 0.00, '', 90.00, 'MCOIN', 90.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 9 DAY), DATE_SUB(NOW(), INTERVAL 9 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 9 DAY), 'Team award', '', 0, 
DATE_SUB(NOW(), INTERVAL 9 DAY), DATE_SUB(NOW(), INTERVAL 9 DAY));

INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307160001', 'mCoin Transfer', 'mCoin Transfer', '*********35025', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307160001', 'SUCCESS', 200.00, 'MCOIN', 0.00, '', 0.00, '', 200.00, 'MCOIN', 200.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 11 DAY), DATE_SUB(NOW(), INTERVAL 11 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 11 DAY), 'Birthday gift', '', 0, 
DATE_SUB(NOW(), INTERVAL 11 DAY), DATE_SUB(NOW(), INTERVAL 11 DAY));

INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307170001', 'mCoin Transfer', 'mCoin Transfer', '*********35026', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307170001', 'SUCCESS', 160.00, 'MCOIN', 0.00, '', 0.00, '', 160.00, 'MCOIN', 160.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 13 DAY), DATE_SUB(NOW(), INTERVAL 13 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 13 DAY), 'Game prize', '', 0, 
DATE_SUB(NOW(), INTERVAL 13 DAY), DATE_SUB(NOW(), INTERVAL 13 DAY));

INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307180001', 'mCoin Transfer', 'mCoin Transfer', '*********35027', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307180001', 'SUCCESS', 135.00, 'MCOIN', 0.00, '', 0.00, '', 135.00, 'MCOIN', 135.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 15 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 15 DAY), 'Appreciation gift', '', 0, 
DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 15 DAY));

-- Insert minimum payment info for the added orders
-- For simplicity, we're adding only the basic payment records needed
-- In a real scenario, each order would have both PAY and ACCEPT entries like the ones above

-- For outgoing transfers 6-10
INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307090001', 'FO202307090001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 80.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 6 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 6 DAY)),

('FP202307100001', 'FO202307100001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 120.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 8 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(NOW(), INTERVAL 8 DAY)),

('FP202307110001', 'FO202307110001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 250.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 10 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY)),

('FP202307120001', 'FO202307120001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 75.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 12 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 12 DAY)),

('FP202307130001', 'FO202307130001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 180.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 14 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 14 DAY));

-- For incoming transfers 4-8
INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307140002', 'FO202307140001', 'ACCEPT', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2D', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 110.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 6 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 6 DAY)),

('FP202307150002', 'FO202307150001', 'ACCEPT', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2E', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 90.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 9 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 9 DAY), DATE_SUB(NOW(), INTERVAL 9 DAY)),

('FP202307160002', 'FO202307160001', 'ACCEPT', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2F', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 200.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 11 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 11 DAY), DATE_SUB(NOW(), INTERVAL 11 DAY)),

('FP202307170002', 'FO202307170001', 'ACCEPT', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2G', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 160.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 13 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 13 DAY), DATE_SUB(NOW(), INTERVAL 13 DAY)),

('FP202307180002', 'FO202307180001', 'ACCEPT', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2H', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 135.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 15 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 15 DAY));

-- Add notification records for some transfers
INSERT INTO notification_records 
(notification_id, notification_type, resource_id, resource_type, task_id, user_id, 
title, content, extra_data, status, push_status, push_time, read_time, gmt_create, gmt_modified, is_deleted) 
VALUES 
('NOTIFY2023120500010', 'SEND', 'FO202307010001', 'TRANSFER_SUCCESS', 'TASK010', '**************', 
'Transfer Success', 'You have successfully transferred 100 mCoin', 
'{"transferId":"FO202307010001","amount":"100","currency":"MCOIN"}', 
'UNREAD', 'SUCCESS', NOW(), NOW(), NOW(), NOW(), 0),

('NOTIFY2023120500011', 'ACCEPT', 'FO202307060001', 'TRANSFER_SUCCESS', 'TASK011', '**************', 
'Transfer Received', 'You have received 175 mCoin', 
'{"transferId":"FO202307060001","amount":"175","currency":"MCOIN"}', 
'UNREAD', 'SUCCESS', NOW(), NOW(), NOW(), NOW(), 0);

-- Add share records for some transfers
INSERT INTO share_records 
(share_records_id, share_records_type, share_code, user_id, content_type, content_id, 
share_content, share_url, channel_type, extra_data, share_time, expire_at, is_deleted, gmt_create, gmt_modified) 
VALUES 
('SR2023120500010', 'SEND', 'DEF456', '**************', 'TRANSFER_CODE', 'FO202307010001', 
'Transfer 100 mCoin', 'https://example.com/transfer/details/FO202307010001', 'WHATSAPP', 
'{"transferDetails":{"amount":"100.00","currency":"MCOIN"}}', NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), 0, 
NOW(), NOW()),

('SR2023120500011', 'SEND', 'GHI789', '**************', 'TRANSFER_CODE', 'FO202307020001', 
'Transfer 200 mCoin', 'https://example.com/transfer/details/FO202307020001', 'WECHAT', 
'{"transferDetails":{"amount":"200.00","currency":"MCOIN"}}', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 6 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY));

-- Additional 5 transfers (mixture of outgoing and incoming)

-- Additional outgoing transfer 1 - 16 days ago
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307190001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307190001', 'SUCCESS', 220.00, 'MCOIN', 0.00, '', 0.00, '', 220.00, 'MCOIN', 220.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 16 DAY), DATE_SUB(NOW(), INTERVAL 16 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 16 DAY), 'Weekend trip', '', 0, 
DATE_SUB(NOW(), INTERVAL 16 DAY), DATE_SUB(NOW(), INTERVAL 16 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307190001', 'FO202307190001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 220.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 16 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 16 DAY), DATE_SUB(NOW(), INTERVAL 16 DAY));

-- Additional incoming transfer 1 - 17 days ago
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307200001', 'mCoin Transfer', 'mCoin Transfer', '*********35028', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307200001', 'SUCCESS', 145.00, 'MCOIN', 0.00, '', 0.00, '', 145.00, 'MCOIN', 145.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 17 DAY), DATE_SUB(NOW(), INTERVAL 17 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 17 DAY), 'Bonus points', '', 0, 
DATE_SUB(NOW(), INTERVAL 17 DAY), DATE_SUB(NOW(), INTERVAL 17 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307200002', 'FO202307200001', 'ACCEPT', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2I', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 145.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 17 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 17 DAY), DATE_SUB(NOW(), INTERVAL 17 DAY));

-- Additional outgoing transfer 2 - 19 days ago
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307210001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307210001', 'SUCCESS', 350.00, 'MCOIN', 0.00, '', 0.00, '', 350.00, 'MCOIN', 350.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 19 DAY), DATE_SUB(NOW(), INTERVAL 19 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 19 DAY), 'Anniversary gift', '', 0, 
DATE_SUB(NOW(), INTERVAL 19 DAY), DATE_SUB(NOW(), INTERVAL 19 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307210001', 'FO202307210001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 350.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 19 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 19 DAY), DATE_SUB(NOW(), INTERVAL 19 DAY));

-- Additional incoming transfer 2 - 21 days ago
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307220001', 'mCoin Transfer', 'mCoin Transfer', '*********35029', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307220001', 'SUCCESS', 185.00, 'MCOIN', 0.00, '', 0.00, '', 185.00, 'MCOIN', 185.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 21 DAY), DATE_SUB(NOW(), INTERVAL 21 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 21 DAY), 'Thank you gift', '', 0, 
DATE_SUB(NOW(), INTERVAL 21 DAY), DATE_SUB(NOW(), INTERVAL 21 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307220002', 'FO202307220001', 'ACCEPT', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY2J', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 185.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 21 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 21 DAY), DATE_SUB(NOW(), INTERVAL 21 DAY));

-- Additional outgoing transfer 3 - 23 days ago
INSERT INTO fund_order 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, fund_order_status, 
fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, tax_amount, tax_amount_currency, 
paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, is_deleted, 
gmt_create, gmt_modified) 
VALUES 
('FO202307230001', 'mCoin Transfer', 'mCoin Transfer', '**************', 'MCOIN_TRANSFER', 'OTO', 
'REQ202307230001', 'SUCCESS', 275.00, 'MCOIN', 0.00, '', 0.00, '', 275.00, 'MCOIN', 275.00, 'MCOIN', 
DATE_SUB(NOW(), INTERVAL 23 DAY), DATE_SUB(NOW(), INTERVAL 23 DAY), '1', 'ONE_STAGE', DATE_SUB(NOW(), INTERVAL 23 DAY), 'School supplies', '', 0, 
DATE_SUB(NOW(), INTERVAL 23 DAY), DATE_SUB(NOW(), INTERVAL 23 DAY));

INSERT INTO fund_pay 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, participant_ext_info, 
identify_id, identify_id_type, status, amount, currency, occur_time, is_deleted, gmt_create, gmt_modified) 
VALUES 
('FP202307230001', 'FO202307230001', 'PAY', '**************', 'MCOIN', 'mcidqb1pg64wrkTpDNrY1A', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"harper"}', 
'+86-***********', 'MOBILE_NO', 'SUCCESS', 275.00, 'MCOIN', DATE_SUB(NOW(), INTERVAL 23 DAY), 0, 
DATE_SUB(NOW(), INTERVAL 23 DAY), DATE_SUB(NOW(), INTERVAL 23 DAY));

-- Commit transaction
COMMIT;

-- Rollback statements (run these if you need to clean up the test data)
/*
START TRANSACTION;

-- Delete notification records
DELETE FROM notification_records WHERE notification_id LIKE 'NOTIFY202312050001%';

-- Delete share records
DELETE FROM share_records WHERE share_records_id LIKE 'SR202312050001%';

-- Delete fund flux records
DELETE FROM fund_flux WHERE fund_order_id LIKE 'FO20230%';

-- Delete fund pay records
DELETE FROM fund_pay WHERE fund_order_id LIKE 'FO20230%';

-- Delete fund order records
DELETE FROM fund_order WHERE fund_order_id LIKE 'FO20230%';

COMMIT;
*/ 