-- Clean up existing test data if needed
DELETE FROM `fund_pay` WHERE fund_order_id IN ('72', '73', '74');
DELETE FROM `fund_order` WHERE fund_order_id IN ('72', '73', '74');

-- Insert order data in fund_order table
INSERT INTO `fund_order` 
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, 
fund_order_status, fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, 
tax_amount, tax_amount_currency, paid_total_amount, paid_total_amount_currency, 
accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
(
    '72', -- fund_order_id
    'Transfer Records Test 1', -- title
    'Transfer Records Test 1 EN', -- title_en
    'user1', -- actor_user_id
    'TRANSFER_MCOIN', -- fund_type
    'NORMAL', -- fund_mode
    'req-001', -- request_id
    'SUCCESS', -- fund_order_status
    995.71, -- fund_amount
    'HKD', -- fund_amount_currency
    0.00, -- charge_amount
    'HKD', -- charge_amount_currency
    0.00, -- tax_amount
    'HKD', -- tax_amount_currency
    995.71, -- paid_total_amount
    'HKD', -- paid_total_amount_currency
    995.71, -- accept_total_amount
    'HKD', -- accept_total_amount_currency
    DATE_ADD(NOW(), INTERVAL 7 DAY), -- accept_expiry_time
    DATE_ADD(NOW(), INTERVAL 7 DAY), -- pay_expiry_time
    'Y', -- auto_accept
    'NORMAL', -- stage_level
    DATE_ADD(NOW(), INTERVAL -5 DAY), -- complete_time (recent date)
    'test transfer memo 1', -- memo
    '{}', -- extend_info
    0, -- is_deleted
    NOW(), -- gmt_create
    NOW() -- gmt_modified
),
(
    '73', -- fund_order_id
    'Transfer Records Test 2', -- title
    'Transfer Records Test 2 EN', -- title_en
    'user1', -- actor_user_id
    'TRANSFER_MCOIN', -- fund_type
    'NORMAL', -- fund_mode
    'req-002', -- request_id
    'SUCCESS', -- fund_order_status
    500.00, -- fund_amount
    'HKD', -- fund_amount_currency
    0.00, -- charge_amount
    'HKD', -- charge_amount_currency
    0.00, -- tax_amount
    'HKD', -- tax_amount_currency
    500.00, -- paid_total_amount
    'HKD', -- paid_total_amount_currency
    500.00, -- accept_total_amount
    'HKD', -- accept_total_amount_currency
    DATE_ADD(NOW(), INTERVAL 7 DAY), -- accept_expiry_time
    DATE_ADD(NOW(), INTERVAL 7 DAY), -- pay_expiry_time
    'Y', -- auto_accept
    'NORMAL', -- stage_level
    DATE_ADD(NOW(), INTERVAL -10 DAY), -- complete_time (recent date)
    'test transfer memo 2', -- memo
    '{}', -- extend_info
    0, -- is_deleted
    NOW(), -- gmt_create
    NOW() -- gmt_modified
),
(
    '74', -- fund_order_id
    'Transfer Records Test 3', -- title
    'Transfer Records Test 3 EN', -- title_en
    'user3', -- actor_user_id
    'TRANSFER_MCOIN', -- fund_type
    'NORMAL', -- fund_mode
    'req-003', -- request_id
    'SUCCESS', -- fund_order_status
    300.00, -- fund_amount
    'HKD', -- fund_amount_currency
    0.00, -- charge_amount
    'HKD', -- charge_amount_currency
    0.00, -- tax_amount
    'HKD', -- tax_amount_currency
    300.00, -- paid_total_amount
    'HKD', -- paid_total_amount_currency
    300.00, -- accept_total_amount
    'HKD', -- accept_total_amount_currency
    DATE_ADD(NOW(), INTERVAL 7 DAY), -- accept_expiry_time
    DATE_ADD(NOW(), INTERVAL 7 DAY), -- pay_expiry_time
    'Y', -- auto_accept
    'NORMAL', -- stage_level
    DATE_ADD(NOW(), INTERVAL -15 DAY), -- complete_time (recent date)
    'test transfer memo 3', -- memo
    '{}', -- extend_info
    0, -- is_deleted
    NOW(), -- gmt_create
    NOW() -- gmt_modified
);

-- Insert payer/payee data in fund_pay table for order '72'
-- Payer for order '72'
INSERT INTO `fund_pay` 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no,
participant_ext_info, identify_id, identify_id_type, status, amount, currency,
occur_time, request_extend_info, system_extend_info, is_deleted, gmt_create, gmt_modified)
VALUES 
(
    'pay-72-1', -- fund_pay_id
    '72', -- fund_order_id
    'PAY', -- sub_order_type
    'user1', -- user_id
    'MCOIN', -- asset_tool
    'acct-user1', -- account_no
    '{"nickName":"user1nick","headLogo":"https://example.com/user1.jpg"}', -- participant_ext_info
    '***********', -- identify_id
    'MOBILE_NO', -- identify_id_type
    'SUCCESS', -- status
    995.71, -- amount
    'HKD', -- currency
    DATE_ADD(NOW(), INTERVAL -5 DAY), -- occur_time (recent date)
    '{}', -- request_extend_info
    '{}', -- system_extend_info
    0, -- is_deleted
    NOW(), -- gmt_create
    NOW() -- gmt_modified
);

-- Payee for order '72'
INSERT INTO `fund_pay` 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no,
participant_ext_info, identify_id, identify_id_type, status, amount, currency,
occur_time, request_extend_info, system_extend_info, is_deleted, gmt_create, gmt_modified)
VALUES 
(
    'acc-72-1', -- fund_pay_id
    '72', -- fund_order_id
    'ACCEPT', -- sub_order_type
    'user3', -- user_id
    'MCOIN', -- asset_tool
    'acct-user3', -- account_no
    '{"nickName":"user3nick","headLogo":"https://example.com/user3.jpg"}', -- participant_ext_info
    '***********', -- identify_id
    'MOBILE_NO', -- identify_id_type
    'SUCCESS', -- status
    995.71, -- amount
    'HKD', -- currency
    DATE_ADD(NOW(), INTERVAL -5 DAY), -- occur_time (recent date)
    '{}', -- request_extend_info
    '{}', -- system_extend_info
    0, -- is_deleted
    NOW(), -- gmt_create
    NOW() -- gmt_modified
);

-- Insert payer/payee data for order '73'
-- Payer for order '73'
INSERT INTO `fund_pay` 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no,
participant_ext_info, identify_id, identify_id_type, status, amount, currency,
occur_time, request_extend_info, system_extend_info, is_deleted, gmt_create, gmt_modified)
VALUES 
(
    'pay-73-1', -- fund_pay_id
    '73', -- fund_order_id
    'PAY', -- sub_order_type
    'user1', -- user_id
    'MCOIN', -- asset_tool
    'acct-user1', -- account_no
    '{"nickName":"user1nick","headLogo":"https://example.com/user1.jpg"}', -- participant_ext_info
    '***********', -- identify_id
    'MOBILE_NO', -- identify_id_type
    'SUCCESS', -- status
    500.00, -- amount
    'HKD', -- currency
    DATE_ADD(NOW(), INTERVAL -10 DAY), -- occur_time (recent date)
    '{}', -- request_extend_info
    '{}', -- system_extend_info
    0, -- is_deleted
    NOW(), -- gmt_create
    NOW() -- gmt_modified
);

-- Payee for order '73'
INSERT INTO `fund_pay` 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no,
participant_ext_info, identify_id, identify_id_type, status, amount, currency,
occur_time, request_extend_info, system_extend_info, is_deleted, gmt_create, gmt_modified)
VALUES 
(
    'acc-73-1', -- fund_pay_id
    '73', -- fund_order_id
    'ACCEPT', -- sub_order_type
    'user3', -- user_id
    'MCOIN', -- asset_tool
    'acct-user3', -- account_no
    '{"nickName":"user3nick","headLogo":"https://example.com/user3.jpg"}', -- participant_ext_info
    '***********', -- identify_id
    'MOBILE_NO', -- identify_id_type
    'SUCCESS', -- status
    500.00, -- amount
    'HKD', -- currency
    DATE_ADD(NOW(), INTERVAL -10 DAY), -- occur_time (recent date)
    '{}', -- request_extend_info
    '{}', -- system_extend_info
    0, -- is_deleted
    NOW(), -- gmt_create
    NOW() -- gmt_modified
);

-- Insert payer/payee data for order '74'
-- Payer for order '74'
INSERT INTO `fund_pay` 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no,
participant_ext_info, identify_id, identify_id_type, status, amount, currency,
occur_time, request_extend_info, system_extend_info, is_deleted, gmt_create, gmt_modified)
VALUES 
(
    'pay-74-1', -- fund_pay_id
    '74', -- fund_order_id
    'PAY', -- sub_order_type
    'user3', -- user_id
    'MCOIN', -- asset_tool
    'acct-user3', -- account_no
    '{"nickName":"user3nick","headLogo":"https://example.com/user3.jpg"}', -- participant_ext_info
    '***********', -- identify_id
    'MOBILE_NO', -- identify_id_type
    'SUCCESS', -- status
    300.00, -- amount
    'HKD', -- currency
    DATE_ADD(NOW(), INTERVAL -15 DAY), -- occur_time (recent date)
    '{}', -- request_extend_info
    '{}', -- system_extend_info
    0, -- is_deleted
    NOW(), -- gmt_create
    NOW() -- gmt_modified
);

-- Payee for order '74'
INSERT INTO `fund_pay` 
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no,
participant_ext_info, identify_id, identify_id_type, status, amount, currency,
occur_time, request_extend_info, system_extend_info, is_deleted, gmt_create, gmt_modified)
VALUES 
(
    'acc-74-1', -- fund_pay_id
    '74', -- fund_order_id
    'ACCEPT', -- sub_order_type
    'user1', -- user_id
    'MCOIN', -- asset_tool
    'acct-user1', -- account_no
    '{"nickName":"user1nick","headLogo":"https://example.com/user1.jpg"}', -- participant_ext_info
    '***********', -- identify_id
    'MOBILE_NO', -- identify_id_type
    'SUCCESS', -- status
    300.00, -- amount
    'HKD', -- currency
    DATE_ADD(NOW(), INTERVAL -15 DAY), -- occur_time (recent date)
    '{}', -- request_extend_info
    '{}', -- system_extend_info
    0, -- is_deleted
    NOW(), -- gmt_create
    NOW() -- gmt_modified
); 