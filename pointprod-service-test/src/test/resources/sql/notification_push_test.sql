-- Clean up existing test data for clean environment
DELETE FROM notification_records WHERE resource_id IN ('TEST_ORDER_001', 'TEST_ORDER_002', 'TEST_ORDER_DUPLICATE');
DELETE FROM fund_pay WHERE fund_order_id IN ('TEST_ORDER_001', 'TEST_ORDER_002', 'TEST_ORDER_DUPLICATE');
DELETE FROM fund_order WHERE fund_order_id IN ('TEST_ORDER_001', 'TEST_ORDER_002', 'TEST_ORDER_DUPLICATE');

-- Insert test fund_order data
INSERT INTO fund_order
(fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, request_id, 
fund_order_status, fund_amount, fund_amount_currency, charge_amount, charge_amount_currency, 
tax_amount, tax_amount_currency, paid_total_amount, paid_total_amount_currency, 
accept_total_amount, accept_total_amount_currency, 
accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time, memo, extend_info, 
is_deleted, gmt_create, gmt_modified) 
VALUES 
-- Test order 1 (SUCCESS status for notification)
('TEST_ORDER_001', 
'Test Transfer Order 1', 
'Test Transfer Order 1 EN', 
'**************', 
'TRANSFER_MCOIN', 
'NORMAL', 
'req-test-001', 
'SUCCESS', 
100.00, 
'MCOIN', 
0.00, 
'MCOIN', 
0.00, 
'MCOIN', 
100.00, 
'MCOIN', 
100.00, 
'MCOIN', 
DATE_ADD(NOW(), INTERVAL 7 DAY), 
DATE_ADD(NOW(), INTERVAL 7 DAY), 
'Y', 
'NORMAL', 
NOW(), 
'Test order for notifications', 
'{}', 
0, 
NOW(), 
NOW()),

-- Test order 2 (different status)
('TEST_ORDER_002', 
'Test Transfer Order 2', 
'Test Transfer Order 2 EN', 
'PAYER_002', 
'TRANSFER_MCOIN', 
'NORMAL', 
'req-test-002', 
'PROCESSING', 
200.00, 
'MCOIN', 
0.00, 
'MCOIN', 
0.00, 
'MCOIN', 
200.00, 
'MCOIN', 
200.00, 
'MCOIN', 
DATE_ADD(NOW(), INTERVAL 7 DAY), 
DATE_ADD(NOW(), INTERVAL 7 DAY), 
'Y', 
'NORMAL', 
NOW(), 
'Test order for notifications', 
'{}', 
0, 
NOW(), 
NOW()),

-- Test order 3 (for duplicate notification test)
('TEST_ORDER_DUPLICATE', 
'Test Transfer Order Duplicate', 
'Test Transfer Order Duplicate EN', 
'DUPLICATE_PAYER', 
'TRANSFER_MCOIN', 
'NORMAL', 
'req-test-duplicate', 
'SUCCESS', 
300.00, 
'MCOIN', 
0.00, 
'MCOIN', 
0.00, 
'MCOIN', 
300.00, 
'MCOIN', 
300.00, 
'MCOIN', 
DATE_ADD(NOW(), INTERVAL 7 DAY), 
DATE_ADD(NOW(), INTERVAL 7 DAY), 
'Y', 
'NORMAL', 
NOW(), 
'Test order for duplicate notification', 
'{}', 
0, 
NOW(), 
NOW());

-- Insert pay orders (for payers)
INSERT INTO fund_pay
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, 
participant_ext_info, identify_id, identify_id_type, status, amount, currency, 
occur_time, request_extend_info, system_extend_info, is_deleted, gmt_create, gmt_modified) 
VALUES 
-- Payer for order 1
('FP_TEST_001', 
'TEST_ORDER_001', 
'PAY', 
'**************', 
'MCOIN', 
'ACC001', 
'{"headLogo":"http://example.com/profile1.jpg","nickName":"TestPayer1"}', 
'+86-***********', 
'MOBILE_NO', 
'SUCCESS', 
100.00, 
'MCOIN', 
NOW(), 
'{}', 
'{}', 
0, 
NOW(), 
NOW()),

-- Payer for order 2
('FP_TEST_003', 
'TEST_ORDER_002', 
'PAY', 
'PAYER_002', 
'MCOIN', 
'ACC003', 
'{"headLogo":"http://example.com/profile3.jpg","nickName":"TestPayer2"}', 
'+86-***********', 
'MOBILE_NO', 
'PROCESSING', 
200.00, 
'MCOIN', 
NOW(), 
'{}', 
'{}', 
0, 
NOW(), 
NOW()),

-- Payer for duplicate test order
('FP_TEST_DUPLICATE_PAY', 
'TEST_ORDER_DUPLICATE', 
'PAY', 
'DUPLICATE_PAYER', 
'MCOIN', 
'ACC_DUPLICATE_PAY', 
'{"headLogo":"http://example.com/duplicate.jpg","nickName":"DuplicatePayer"}', 
'+86-***********', 
'MOBILE_NO', 
'SUCCESS', 
300.00, 
'MCOIN', 
NOW(), 
'{}', 
'{}', 
0, 
NOW(), 
NOW());

-- Insert accept orders (for payees)
INSERT INTO fund_pay
(fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool, account_no, 
participant_ext_info, identify_id, identify_id_type, status, amount, currency, 
occur_time, request_extend_info, system_extend_info, is_deleted, gmt_create, gmt_modified) 
VALUES 
-- Payee for order 1
('FP_TEST_002', 
'TEST_ORDER_001', 
'ACCEPT', 
'**************', 
'MCOIN', 
'ACC002', 
'{"headLogo":"http://example.com/profile2.jpg","nickName":"TestPayee1"}', 
'+86-***********', 
'MOBILE_NO', 
'SUCCESS', 
100.00, 
'MCOIN', 
NOW(), 
'{}', 
'{}', 
0, 
NOW(), 
NOW()),

-- Payee for order 2
('FP_TEST_004', 
'TEST_ORDER_002', 
'ACCEPT', 
'PAYEE_002', 
'MCOIN', 
'ACC004', 
'{"headLogo":"http://example.com/profile4.jpg","nickName":"TestPayee2"}', 
'+86-***********', 
'MOBILE_NO', 
'PROCESSING', 
200.00, 
'MCOIN', 
NOW(), 
'{}', 
'{}', 
0, 
NOW(), 
NOW()),

-- Payee for duplicate test order
('FP_TEST_DUPLICATE_ACCEPT', 
'TEST_ORDER_DUPLICATE', 
'ACCEPT', 
'DUPLICATE_PAYEE', 
'MCOIN', 
'ACC_DUPLICATE_ACCEPT', 
'{"headLogo":"http://example.com/duplicate_payee.jpg","nickName":"DuplicatePayee"}', 
'+86-10000000098', 
'MOBILE_NO', 
'SUCCESS', 
300.00, 
'MCOIN', 
NOW(), 
'{}', 
'{}', 
0, 
NOW(), 
NOW());

-- Add a pre-existing notification record to test duplicate handling
INSERT INTO notification_records 
(notification_id, notification_type, resource_id, resource_type, task_id, user_id,
title, content, extra_data, status, push_status, push_time, read_time, resource_time, 
gmt_create, gmt_modified, is_deleted)
VALUES
('PRE_EXISTING_NOTIFICATION_ID',
'SEND', 
'TEST_ORDER_DUPLICATE', 
'TRANSFER_SUCCESS', 
'TASK_ID_123', 
'DUPLICATE_PAYER',
'预先存在的通知', 
'这是一个测试重复记录的预先存在通知', 
'{}', 
'UNREAD', 
'SUCCESS', 
NOW(), 
NOW(), 
NOW(), 
NOW(), 
NOW(), 
0); 