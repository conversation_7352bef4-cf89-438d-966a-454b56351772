-- Clean up existing test data
DELETE FROM notification_records WHERE notification_id IN ('TEST_NOTIFICATION_ID', 'TEST_READ_NOTIFICATION_ID', 'TEST_NOTIFICATION_ID_SYSTEM', 'TEST_NOTIFICATION_ID_OTHER_USER');
DELETE FROM fund_order WHERE fund_order_id IN ('TEST_FUND_ORDER_ID', 'TEST_READ_FUND_ORDER_ID', 'TEST_FUND_ORDER_ID_SYSTEM', 'TEST_FUND_ORDER_ID_OTHER');
DELETE FROM fund_pay WHERE fund_order_id IN ('TEST_FUND_ORDER_ID', 'TEST_READ_FUND_ORDER_ID', 'TEST_FUND_ORDER_ID_SYSTEM', 'TEST_FUND_ORDER_ID_OTHER');

-- Insert test notification record
INSERT INTO notification_records (
    notification_id, notification_type, resource_id, resource_type, task_id, 
    user_id, title, content, extra_data, status, push_status, 
    push_time, read_time, gmt_create, gmt_modified, is_deleted, resource_time
) VALUES (
    'TEST_NOTIFICATION_ID', 'ACCEPT', 'TEST_FUND_ORDER_ID', 'TRANSFER_SUCCESS', 'TEST_TASK_ID',
    '**************', 'Test Notification Title', 'Test Notification Content', 
    '{"payerNickName":"Test Payer", "payerHeadImg":"http://example.com/avatar.jpg", "amount":"100.00"}', 
    'UNREAD', 'SUCCESS', 
    NOW(), NOW(), NOW(), NOW(), 0, NOW()
);

-- Insert a read notification for testing negative case
INSERT INTO notification_records (
    notification_id, notification_type, resource_id, resource_type, task_id, 
    user_id, title, content, extra_data, status, push_status, 
    push_time, read_time, gmt_create, gmt_modified, is_deleted, resource_time
) VALUES (
    'TEST_READ_NOTIFICATION_ID', 'ACCEPT', 'TEST_READ_FUND_ORDER_ID', 'TRANSFER_SUCCESS', 'TEST_READ_TASK_ID',
    '**************', 'Test Read Notification', 'Test Read Notification Content', 
    '{"payerNickName":"Test Payer", "payerHeadImg":"http://example.com/avatar.jpg", "amount":"100.00"}', 
    'READ', 'SUCCESS', 
    NOW(), NOW(), NOW(), NOW(), 0, NOW()
);

-- Insert test fund order record
INSERT INTO fund_order (
    fund_order_id, title, title_en, actor_user_id, fund_type, fund_mode, 
    request_id, fund_order_status, fund_amount, fund_amount_currency,
    charge_amount, charge_amount_currency, tax_amount, tax_amount_currency,
    paid_total_amount, paid_total_amount_currency, accept_total_amount, accept_total_amount_currency,
    accept_expiry_time, pay_expiry_time, auto_accept, stage_level, complete_time,
    memo, extend_info, is_deleted, gmt_create, gmt_modified
) VALUES (
    'TEST_FUND_ORDER_ID', '積分轉贈', 'mCoin Transfer', '**************', 'TRANSFER_MCOIN', 'OTO',
    'TEST_REQUEST_ID', 'SUCCESS', 100.00, 'MCOIN',
    0.00, '', 0.00, '',
    100.00, 'MCOIN', 100.00, 'MCOIN',
    DATE_ADD(NOW(), INTERVAL 1 HOUR), DATE_ADD(NOW(), INTERVAL 1 HOUR), '1', 'ONE_STAGE', NOW(),
    '测试转账留言', '', 0, NOW(), NOW()
);

-- Insert test fund pay records
INSERT INTO fund_pay (
    fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool,
    account_no, participant_ext_info, identify_id, identify_id_type,
    status, amount, currency, occur_time, request_extend_info,
    system_extend_info, is_deleted, gmt_create, gmt_modified, complete_time
) VALUES (
    'TEST_FUND_PAY_ID_PAY', 'TEST_FUND_ORDER_ID', 'PAY', '**************', 'MCOIN',
    '**************', '{"nickName":"Test Payer","userLevel":"1"}', '+86-***********', 'MOBILE_NO',
    'SUCCESS', 100.00, 'MCOIN', NOW(), '',
    '', 0, NOW(), NOW(), NOW()
);

INSERT INTO fund_pay (
    fund_pay_id, fund_order_id, sub_order_type, user_id, asset_tool,
    account_no, participant_ext_info, identify_id, identify_id_type,
    status, amount, currency, occur_time, request_extend_info,
    system_extend_info, is_deleted, gmt_create, gmt_modified, complete_time
) VALUES (
    'TEST_FUND_PAY_ID_ACCEPT', 'TEST_FUND_ORDER_ID', 'ACCEPT', '**************', 'MCOIN',
    '**************', '{"nickName":"Test Receiver"}', '+853-********', 'MOBILE_NO',
    'SUCCESS', 100.00, 'MCOIN', NOW(), '',
    '', 0, NOW(), NOW(), NOW()
);

-- For testing non-ACCEPT notification type
INSERT INTO notification_records (
    notification_id, notification_type, resource_id, resource_type, task_id, 
    user_id, title, content, extra_data, status, push_status, 
    push_time, read_time, gmt_create, gmt_modified, is_deleted, resource_time
) VALUES (
    'TEST_NOTIFICATION_ID_SYSTEM', 'SYSTEM', 'TEST_FUND_ORDER_ID_SYSTEM', 'SYSTEM_MESSAGE', 'TEST_TASK_ID_SYSTEM',
    '**************', 'System Notification', 'System Notification Content', 
    '{}', 'UNREAD', 'SUCCESS', 
    NOW(), NOW(), NOW(), NOW(), 0, NOW()
);

-- For testing notification not found
INSERT INTO notification_records (
    notification_id, notification_type, resource_id, resource_type, task_id, 
    user_id, title, content, extra_data, status, push_status, 
    push_time, read_time, gmt_create, gmt_modified, is_deleted, resource_time
) VALUES (
    'TEST_NOTIFICATION_ID_OTHER_USER', 'ACCEPT', 'TEST_FUND_ORDER_ID_OTHER', 'TRANSFER_SUCCESS', 'TEST_TASK_ID_OTHER',
    '**************', 'Other User Notification', 'Other User Notification Content', 
    '{}', 'UNREAD', 'SUCCESS', 
    NOW(), NOW(), NOW(), NOW(), 0, NOW()
); 