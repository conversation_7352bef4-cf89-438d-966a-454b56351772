<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pointprod-service</artifactId>
        <groupId>com.agtech</groupId>
        <version>0.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>pointprod-service-test</artifactId>
    <version>0.0.1</version>
    <name>pointprod-service-test</name>
    <description>pointprod-service-test</description>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!--project modules, 添加所有模块, 用于jacoco收集覆盖率-->
        <dependency>
            <groupId>com.agtech</groupId>
            <artifactId>pointprod-service-app</artifactId>
        </dependency>
        <dependency>
            <groupId>com.agtech</groupId>
            <artifactId>pointprod-service-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.agtech</groupId>
            <artifactId>pointprod-service-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.agtech</groupId>
            <artifactId>pointprod-service-facade</artifactId>
        </dependency>
        <!--单元测试相关依赖-->
        <!--junit5核心包,版本号5.9.1-->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <!--junit5 api包,版本号5.9.1-->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <!--mockito相关包,版本号4.5.1-->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy-agent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.12.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.12.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.78</version>
        </dependency>

        <!--springboot测试包-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>ch.vorburger.mariaDB4j</groupId>
            <artifactId>mariaDB4j</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.8</version>
                <configuration>
                    <!--重要！⽤追加的模式-->
                    <append>true</append>
                </configuration>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report-aggregate</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                        <configuration>
                            <excludes>
                                <exclude>com/agtech/package/ignoretest/**</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>merge-results</id>
                        <phase>package</phase>
                        <goals>
                            <goal>merge</goal>
                        </goals>
                        <configuration>
                            <fileSets>
                                <fileSet>
                                    <directory>${basedir}/../</directory>
                                    <includes>
                                        <include>**/target/jacoco.exec</include>
                                    </includes>
                                </fileSet>
                            </fileSets>
                            <destFile>${basedir}/target/jacoco.exec</destFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Maven Surefire插件配置 -->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-surefire-plugin</artifactId>-->
<!--                <version>${maven-surefire-plugin.version}</version>-->
<!--                <configuration>-->
<!--                    &lt;!&ndash; 显示标准输出和错误 &ndash;&gt;-->
<!--                    <redirectTestOutputToFile>false</redirectTestOutputToFile>-->
<!--                    &lt;!&ndash; 详细日志 &ndash;&gt;-->
<!--                    <trimStackTrace>false</trimStackTrace>-->
<!--                    <useFile>false</useFile>-->
<!--                    &lt;!&ndash; 传递系统属性 &ndash;&gt;-->
<!--                    <systemPropertyVariables>-->
<!--                        <spring.profiles.active>test</spring.profiles.active>-->
<!--                        <logging.level.com.agtech>DEBUG</logging.level.com.agtech>-->
<!--                    </systemPropertyVariables>-->
<!--                    &lt;!&ndash; 启用字符编码 &ndash;&gt;-->
<!--                    <argLine>-Dfile.encoding=UTF-8</argLine>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>


</project>
