package com.agtech.pointprod.order.service.infrastructure.dal.converter;

import com.agtech.pointprod.order.service.domain.model.FundOrderEnv;
import com.agtech.pointprod.order.service.domain.model.FundUnique;
import com.agtech.pointprod.order.service.domain.model.OrderToken;
import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundOrderEnvDO;
import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundUniqueDO;
import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.OrderTokenDO;

import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface OrderConverter {

    OrderToken toOrderTokenDomainObject(OrderTokenDO orderTokenDO);

    OrderTokenDO toOrderTokenDO(OrderToken orderToken);

    FundUniqueDO toFundUniqueDO(FundUnique fundUnique);


    FundOrderEnvDO orderEnvDomainObjectToDO(FundOrderEnv fundOrderEnv);
}
