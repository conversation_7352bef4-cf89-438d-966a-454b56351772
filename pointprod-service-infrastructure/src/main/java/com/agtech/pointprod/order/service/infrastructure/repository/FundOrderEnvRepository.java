package com.agtech.pointprod.order.service.infrastructure.repository;

import com.agtech.pointprod.order.service.domain.model.FundOrderEnv;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
public interface FundOrderEnvRepository {
    /**
     * 创建订单环境
     *
     * @param fundOrderEnv 订单环境
     * @return 是否成功
     */
    boolean createOrderEnv(FundOrderEnv fundOrderEnv);
}
