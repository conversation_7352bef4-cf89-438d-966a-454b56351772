<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agtech.pointprod.order.service.infrastructure.dal.mapper.OrderTokenMapper">
  <resultMap id="BaseResultMap" type="com.agtech.pointprod.order.service.infrastructure.dal.daoobject.OrderTokenDO">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="token_id" jdbcType="VARCHAR" property="tokenId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="use_status" jdbcType="VARCHAR" property="useStatus" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>

</mapper>