package com.agtech.pointprod.order.service.infrastructure.gateway.impl;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import com.agtech.common.lang.util.StringUtil;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.OrderTokenUseStatusEnum;
import com.agtech.pointprod.order.service.domain.gateway.OrderGateway;
import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.FundOrderEnv;
import com.agtech.pointprod.order.service.domain.model.FundUnique;
import com.agtech.pointprod.order.service.domain.model.OrderExtendInfo;
import com.agtech.pointprod.order.service.domain.model.OrderToken;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;
import com.agtech.pointprod.order.service.infrastructure.common.OrderConfig;
import com.agtech.pointprod.order.service.infrastructure.repository.FundOrderEnvRepository;
import com.agtech.pointprod.order.service.infrastructure.repository.FundOrderRepository;
import com.agtech.pointprod.order.service.infrastructure.repository.FundUniqueRepository;
import com.agtech.pointprod.order.service.infrastructure.repository.OrderTokenRepository;
import com.agtech.pointprod.pay.service.infrastructure.repository.FundPayRepository;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import com.alibaba.fastjson2.JSON;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 20:31
 */
@Component
@Slf4j
public class OrderGatewayImpl implements OrderGateway {
    @Resource
    private FundOrderRepository fundOrderRepository;

    @Resource
    private FundOrderEnvRepository fundOrderEnvRepository;

    @Resource
    private FundPayRepository fundPayRepository;

    @Resource
    private FundUniqueRepository fundUniqueRepository;

    @Resource
    private OrderTokenRepository orderTokenRepository;

    @Resource
    private BizSequenceRepository bizSequenceRepository;

    @Resource
    private OrderConfig orderConfig;

    @Override
    public OrderToken getOrderTokenForUpdate(String custId, String orderType) {
        return orderTokenRepository.getOrderTokenForUpdate(custId, orderType);
    }

    @Override
    public OrderToken createOrderToken(String custId, String orderType) {
        OrderToken orderToken = new OrderToken();
        String tokenId = bizSequenceRepository.getBizId(custId, SequenceCodeEnum.ORDER_TOKEN);
        orderToken.setTokenId(tokenId);
        orderToken.setUserId(custId);
        orderToken.setOrderType(orderType);
        String token = bizSequenceRepository.getBizId(custId, SequenceCodeEnum.ORDER_TOKEN_VALUE);
        orderToken.setToken(token);
        orderToken.setUseStatus(OrderTokenUseStatusEnum.WAITING_USE.getValue());
        Date now = ZonedDateUtil.now();
        orderToken.setGmtCreate(now);
        orderToken.setGmtModified(now);
        try {
            boolean success = orderTokenRepository.saveOrderToken(orderToken);
            if(!success){
                return null;
            }
        } catch (DuplicateKeyException duplicateKeyException) {
            // 正常不會出現重複記錄
            log.info("createOrderToken duplicateKeyException, custId:{}, orderType:{}", custId, orderType);
            return orderTokenRepository.getOrderTokenForUpdate(custId, orderType);
        }

        return orderToken;
    }

    @Override
    public OrderToken updateOrderToken(OrderToken orderToken) {
        if(orderToken ==null || StringUtil.isBlank(orderToken.getUserId())
                || StringUtil.isBlank(orderToken.getOrderType()) || StringUtil.isBlank(orderToken.getTokenId())){
            log.error("updateOrderToken error, orderTokenDomainObject:{}", JSON.toJSONString(orderToken));
            return null;
        }
        orderToken.setUseStatus(OrderTokenUseStatusEnum.WAITING_USE.getValue());
        String token = bizSequenceRepository.getBizId(orderToken.getUserId(), SequenceCodeEnum.ORDER_TOKEN_VALUE);
        orderToken.setToken(token);
        orderToken.setGmtModified(ZonedDateUtil.now());
        boolean success = orderTokenRepository.updateOrderToken(orderToken);
        if(!success){
            throw new PointProdBizException(PointProdBizErrorCodeEnum.DB_UPDATE_FAIL);
        }
        return orderToken;
    }

    @Override
    public OrderToken getOrderToken(String userId, String orderToken) {
        if(StringUtil.isBlank(userId) || StringUtil.isBlank(orderToken)){
            log.error("getOrderToken param error, userId:{}, orderToken:{}", userId, orderToken);
            return null;
        }
        return orderTokenRepository.getOrderToken(userId, orderToken);
    }


    @Override
    public BaseOrderInfo getFundOrderForUpdate(String orderId, String userId) {
    	BaseOrderInfo baseOrderInfo = fundOrderRepository.getFundOrderForUpdate(orderId, userId);
        if (baseOrderInfo == null) {
			return null;
		}
    	List<PayOrderInfo> payOrderInfoList = fundPayRepository.getPayOrderInfo(orderId);
    	baseOrderInfo.setPayOrderInfoList(payOrderInfoList);
    	List<AcceptOrderInfo> acceptOrderInfoList = fundPayRepository.getAcceptOrderInfo(orderId);
    	baseOrderInfo.setAcceptOrderInfoList(acceptOrderInfoList);
        return baseOrderInfo;
    }

    @Override
    public Map<String, BaseOrderInfo> queryFundOrdersByIds(List<String> fundOrderIds) {
        if (CollectionUtils.isEmpty(fundOrderIds)) {
            return Collections.emptyMap();
        }

        // 批量查询订单信息
        List<BaseOrderInfo> fundOrders = fundOrderRepository.queryByFundOrderIds(fundOrderIds);
        if (CollectionUtils.isEmpty(fundOrders)) {
            return Collections.emptyMap();
        }
        
        // 转换为Map，key为订单ID
        Map<String, BaseOrderInfo> result = new HashMap<>(fundOrders.size());
        
        // 批量查询订单的支付和收款信息
        Map<String, List<PayOrderInfo>> payOrderInfoMap = fundPayRepository.batchGetPayOrderInfo(fundOrderIds);
        Map<String, List<AcceptOrderInfo>> acceptOrderInfoMap = fundPayRepository.batchGetAcceptOrderInfo(fundOrderIds);
        
        for (BaseOrderInfo baseOrderInfo : fundOrders) {
            String orderId = baseOrderInfo.getFundOrderId();
            
            // 设置支付和收款信息
            baseOrderInfo.setPayOrderInfoList(payOrderInfoMap.getOrDefault(orderId, Collections.emptyList()));
            baseOrderInfo.setAcceptOrderInfoList(acceptOrderInfoMap.getOrDefault(orderId, Collections.emptyList()));
            
            result.put(orderId, baseOrderInfo);
        }
        
        return result;
    }

    @Override
    public boolean createFundUnique(FundUnique fundUnique) {
        String bizId = bizSequenceRepository.getBizId(fundUnique.getBizId(), SequenceCodeEnum.FUND_UNIQUE);
        fundUnique.setGlobalUniqueId(bizId);
        return fundUniqueRepository.createFundUnique(fundUnique);
    }

    @Override
    public String createNewOrderId(String userId) {
        return bizSequenceRepository.getBizId(userId, SequenceCodeEnum.FUND_UNIQUE);
    }

    @Override
    public boolean createOrder(BaseOrderInfo baseOrderInfo) {
        return fundOrderRepository.createFundOrder(baseOrderInfo);
    }

    @Override
    public Integer getOrderExpireSeconds() {
        return orderConfig.getExpireSeconds();
    }

    @Override
    public boolean setOrderTokenUsed(String orderToken, String userId) {
        return orderTokenRepository.setOrderTokenUsed(orderToken, userId);
    }

    @Override
    public String createNewOrderEnvId(String custId) {
        return bizSequenceRepository.getBizId(custId, SequenceCodeEnum.FUND_ORDER_ENV);
    }

    @Override
    public boolean createOrderEnv(FundOrderEnv fundOrderEnv) {
        if(fundOrderEnv == null){
            log.error("createOrderEnv error, fundOrderEnv empty");
            //环境参数为空时，默认返回成功
            return true;
        }
        return fundOrderEnvRepository.createOrderEnv(fundOrderEnv);
    }

    @Override
    public String createNewTaskRetryId(String userId) {
        return bizSequenceRepository.getBizId(userId, SequenceCodeEnum.TASK_RETRY);
    }

    @Override
    public boolean updateFundOrderStatus(String orderId, String userId, String fromStatus, String toStatus) {
        return fundOrderRepository.updateFundOrderStatus(orderId, userId, fromStatus, toStatus);
    }

    @Override
    public boolean updateStatusAndExtendInfo(String orderId, String userId, FundOrderStatusEnum fromStatus, FundOrderStatusEnum toStatus, OrderExtendInfo extendInfo) {
        return fundOrderRepository.updateStatusAndExtendInfo(orderId, userId, fromStatus, toStatus, extendInfo);
    }

    @Override
    public BaseOrderInfo getFundOrder(String orderId) {
        BaseOrderInfo baseOrderInfo = fundOrderRepository.getFundOrder(orderId);
        if (baseOrderInfo == null) {
            return null;
        }
        List<PayOrderInfo> payOrderInfoList = fundPayRepository.getPayOrderInfo(orderId);
        baseOrderInfo.setPayOrderInfoList(payOrderInfoList);
        List<AcceptOrderInfo> acceptOrderInfoList = fundPayRepository.getAcceptOrderInfo(orderId);
        baseOrderInfo.setAcceptOrderInfoList(acceptOrderInfoList);
        return baseOrderInfo;
    }
}
