package com.agtech.pointprod.order.service.infrastructure.gateway.impl;

import com.agtech.pointprod.service.domain.gateway.TransferUserGateway;
import com.agtech.pointprod.service.domain.model.TransferRelation;
import com.agtech.pointprod.service.infrastructure.repository.TransferRelationRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
public class TransferUserGatewayImpl implements TransferUserGateway {
    @Resource
    private TransferRelationRepository transferRelationRepository;

    @Override
    public List<TransferRelation> queryLatestTransferRalations(String userId, Date startTime) {
        return transferRelationRepository.queryLatestTransferRalations(userId, startTime);
    }
}
