package com.agtech.pointprod.order.service.infrastructure.dal.converter;
import java.math.BigDecimal;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.pointprod.order.service.domain.common.enums.FundModeEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundStageLevelEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundTypeEnum;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.OrderExtendInfo;
import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundOrderDO;
import com.agtech.pointprod.service.infrastructure.common.constant.PointProdConstant;
import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;


/**
 * 订单数据对象转换领域对象
 * 
 * <AUTHOR>
 * @version : FundOrderDataConverter.java, v 0.1 2025年6月25日 17:01:27
 *          zhongqiang Exp $
 */
@Slf4j
public class FundOrderDataConverter {

	/**
	 * 数据对象转换领域对象
	 * 
	 * @param fundOrderDO
	 * @return
	 */
	public static BaseOrderInfo toBaseOrderInfo(FundOrderDO fundOrderDO) {

		if (fundOrderDO == null) {
			return null;
		}

		BaseOrderInfo baseOrderInfo = new BaseOrderInfo();
		baseOrderInfo.setFundOrderId(fundOrderDO.getFundOrderId());
		baseOrderInfo.setTitle(fundOrderDO.getTitle());
		baseOrderInfo.setTitleEn(fundOrderDO.getTitleEn());
		baseOrderInfo.setFundMode(FundModeEnum.getByCode(fundOrderDO.getFundMode()));
		baseOrderInfo.setOrderStatus(FundOrderStatusEnum.getByCode(fundOrderDO.getFundOrderStatus()));
		baseOrderInfo.setFundType(FundTypeEnum.getByCode(fundOrderDO.getFundType()));
		baseOrderInfo.setAcceptExpiryTime(fundOrderDO.getAcceptExpiryTime());
		baseOrderInfo.setActorUserId(fundOrderDO.getActorUserId());
		baseOrderInfo.setAutoAccept(fundOrderDO.getAutoAccept());

		baseOrderInfo.setFundAmount(new MultiCurrencyMoney(fundOrderDO.getFundAmount()));
		baseOrderInfo.setChargeAmount(new MultiCurrencyMoney(fundOrderDO.getChargeAmount()));
		baseOrderInfo.setTaxAmount(new MultiCurrencyMoney(fundOrderDO.getTaxAmount()));
		baseOrderInfo.setPaidTotalAmount(new MultiCurrencyMoney(fundOrderDO.getPaidTotalAmount()));
		baseOrderInfo.setAcceptTotalAmount(new MultiCurrencyMoney(fundOrderDO.getAcceptTotalAmount()));

		baseOrderInfo.setCompleteTime(fundOrderDO.getCompleteTime());
		
		// 反序列化OrderExtendInfo
		if (StringUtils.isNotBlank(fundOrderDO.getExtendInfo())) {
			try {
				OrderExtendInfo orderExtendInfo = JSON.parseObject(fundOrderDO.getExtendInfo(), OrderExtendInfo.class);
				baseOrderInfo.setExtendInfo(orderExtendInfo);
			} catch (Exception e) {
				log.error("反序列化OrderExtendInfo失败，fundOrderId={}", fundOrderDO.getFundOrderId(), e);
				// 解析失败时设置为null，避免影响主流程
				baseOrderInfo.setExtendInfo(null);
			}
		}
		
		baseOrderInfo.setCreatedTime(fundOrderDO.getGmtCreate());
		baseOrderInfo.setModifiedTime(fundOrderDO.getGmtModified());
		baseOrderInfo.setMemo(fundOrderDO.getMemo());
		baseOrderInfo.setPayExpiryTime(fundOrderDO.getPayExpiryTime());
		baseOrderInfo.setRequestId(fundOrderDO.getRequestId());
		baseOrderInfo.setStageLevel(FundStageLevelEnum.getByCode(fundOrderDO.getStageLevel()));
		return baseOrderInfo;
	}

    public static List<BaseOrderInfo> toBaseOrderInfoList(List<FundOrderDO> fundOrderDOList) {
		if (CollectionUtils.isEmpty(fundOrderDOList)) {
			return Collections.emptyList();
		}

		return fundOrderDOList.stream().map(FundOrderDataConverter::toBaseOrderInfo).collect(Collectors.toList());
    }

	public static FundOrderDO toFundOrderDO(BaseOrderInfo baseOrderInfo) {
		FundOrderDO fundOrderDO = new FundOrderDO();
		//fundOrderDO.setId(0L);
		fundOrderDO.setFundOrderId(baseOrderInfo.getFundOrderId());
		fundOrderDO.setTitle(baseOrderInfo.getTitle());
		fundOrderDO.setTitleEn(baseOrderInfo.getTitleEn());
		fundOrderDO.setActorUserId(baseOrderInfo.getActorUserId());
		fundOrderDO.setFundType(baseOrderInfo.getFundType().getCode());
		fundOrderDO.setFundMode(baseOrderInfo.getFundMode().getCode());
		fundOrderDO.setRequestId(baseOrderInfo.getRequestId());
		fundOrderDO.setFundOrderStatus(baseOrderInfo.getOrderStatus().getCode());
		fundOrderDO.setFundAmount(baseOrderInfo.getFundAmount().getAmount());
		fundOrderDO.setFundAmountCurrency(PointProdConstant.MCOIN_CURRENCY);
		fundOrderDO.setChargeAmount(BigDecimal.ZERO);
		fundOrderDO.setChargeAmountCurrency(PointProdConstant.MCOIN_CURRENCY);
		fundOrderDO.setTaxAmount(BigDecimal.ZERO);
		fundOrderDO.setTaxAmountCurrency(PointProdConstant.MCOIN_CURRENCY);
		fundOrderDO.setPaidTotalAmount(baseOrderInfo.getPaidTotalAmount().getAmount());
		fundOrderDO.setPaidTotalAmountCurrency(PointProdConstant.MCOIN_CURRENCY);
		fundOrderDO.setAcceptTotalAmount(baseOrderInfo.getAcceptTotalAmount().getAmount());
		fundOrderDO.setAcceptTotalAmountCurrency(PointProdConstant.MCOIN_CURRENCY);
		fundOrderDO.setAcceptExpiryTime(baseOrderInfo.getAcceptExpiryTime());
		fundOrderDO.setPayExpiryTime(baseOrderInfo.getPayExpiryTime());
		fundOrderDO.setAutoAccept(baseOrderInfo.getAutoAccept());
		fundOrderDO.setStageLevel(baseOrderInfo.getStageLevel().getCode());
		fundOrderDO.setCompleteTime(baseOrderInfo.getCompleteTime());
		fundOrderDO.setMemo(baseOrderInfo.getMemo());
		
		// 序列化OrderExtendInfo
		if (baseOrderInfo.getExtendInfo() != null) {
			try {
				String orderExtendInfoJson = JSON.toJSONString(baseOrderInfo.getExtendInfo());
				fundOrderDO.setExtendInfo(orderExtendInfoJson);
			} catch (Exception e) {
				log.error("序列化OrderExtendInfo失败，fundOrderId={}", fundOrderDO.getFundOrderId(), e);
				// 序列化失败时设置为null
				fundOrderDO.setExtendInfo(null);
			}
		}
		
		fundOrderDO.setIsDeleted(0);
		fundOrderDO.setGmtCreate(baseOrderInfo.getCreatedTime());
		fundOrderDO.setGmtModified(baseOrderInfo.getModifiedTime());
        return fundOrderDO;
	}
}
