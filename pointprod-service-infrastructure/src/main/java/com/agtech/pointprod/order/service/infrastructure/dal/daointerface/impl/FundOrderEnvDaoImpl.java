package com.agtech.pointprod.order.service.infrastructure.dal.daointerface.impl;

import com.agtech.pointprod.order.service.infrastructure.dal.daointerface.FundOrderEnvDao;
import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundOrderEnvDO;
import com.agtech.pointprod.order.service.infrastructure.dal.mapper.FundOrderEnvMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
@Component
public class FundOrderEnvDaoImpl implements FundOrderEnvDao {
    @Resource
    private FundOrderEnvMapper fundOrderEnvMapper;

    @Override
    public boolean createOrderEnv(FundOrderEnvDO fundOrderEnvDO) {
        return fundOrderEnvMapper.insert(fundOrderEnvDO) > 0;
    }
}
