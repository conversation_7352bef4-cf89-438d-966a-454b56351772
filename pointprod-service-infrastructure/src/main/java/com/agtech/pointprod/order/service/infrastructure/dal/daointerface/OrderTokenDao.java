package com.agtech.pointprod.order.service.infrastructure.dal.daointerface;

import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.OrderTokenDO;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
public interface OrderTokenDao {
    OrderTokenDO getOrderTokenForUpdate(String custId, String orderType);

    boolean saveOrderToken(OrderTokenDO orderTokenDO);

    boolean updateOrderToken(OrderTokenDO orderTokenDO);

    OrderTokenDO getOrderToken(String userId, String orderToken);

    boolean setOrderTokenUsed(String orderToken, String userId);
}
