package com.agtech.pointprod.order.service.infrastructure.dal.daointerface.impl;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.model.OrderExtendInfo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.agtech.common.util.CollectionUtil;
import com.agtech.pointprod.order.service.infrastructure.dal.daointerface.FundOrderDao;
import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundOrderDO;
import com.agtech.pointprod.order.service.infrastructure.dal.mapper.FundOrderMapper;
import com.agtech.pointprod.service.domain.common.enums.YesOrNoEnum;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
@Component
public class FundOrderDaoImpl extends ServiceImpl<FundOrderMapper, FundOrderDO> implements FundOrderDao {

    @Resource
    private FundOrderMapper fundOrderMapper;


    @Override
    public FundOrderDO queryFundOrderForUpdate(String orderId, String userId) {
        Wrapper<FundOrderDO> wrapper = Wrappers.lambdaQuery(FundOrderDO.class)
                .eq(FundOrderDO::getFundOrderId, orderId)
                .eq(FundOrderDO::getActorUserId, userId)
                .eq(FundOrderDO::getIsDeleted, YesOrNoEnum.NO.getValue())
                .last("for update");
        List<FundOrderDO> fundOrders = fundOrderMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(fundOrders)){
            return null;
        }
        return fundOrders.get(0);
    }

    @Override
    public List<FundOrderDO> queryFundOrderByIds(List<String> fundOrderIds) {
        if (CollectionUtils.isEmpty(fundOrderIds)) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<FundOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FundOrderDO::getFundOrderId, fundOrderIds)
                   .eq(FundOrderDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        return list(queryWrapper);
    }

    @Override
    public boolean createFundOrder(FundOrderDO fundOrderDO) {
        return fundOrderMapper.insert(fundOrderDO) > 0;
    }

    @Override
    public boolean updateFundOrderStatus(String orderId, String userId, String fromStatus, String toStatus) {
        LambdaUpdateWrapper<FundOrderDO> updateWrapper = Wrappers.<FundOrderDO>lambdaUpdate()
                .eq(FundOrderDO::getFundOrderId, orderId)
                .eq(FundOrderDO::getActorUserId, userId)
                .eq(FundOrderDO::getFundOrderStatus, fromStatus)
                .set(FundOrderDO::getFundOrderStatus, toStatus)
                .set(FundOrderDO::getGmtModified, ZonedDateUtil.now());
        if (FundOrderStatusEnum.SUCCESS.getCode().equals(toStatus)) {
            updateWrapper.set(FundOrderDO::getCompleteTime, ZonedDateUtil.now());
        }
        int update = baseMapper.update(null, updateWrapper);
        return update == 1;
    }

    @Override
    public boolean updateStatusAndExtendInfo(String orderId, String userId, FundOrderStatusEnum fromStatus, FundOrderStatusEnum toStatus, OrderExtendInfo extendInfo) {
        LambdaUpdateWrapper<FundOrderDO> updateWrapper = Wrappers.<FundOrderDO>lambdaUpdate()
                .eq(FundOrderDO::getFundOrderId, orderId)
                .eq(FundOrderDO::getActorUserId, userId)
                .eq(FundOrderDO::getFundOrderStatus, fromStatus.getCode())
                .set(FundOrderDO::getFundOrderStatus, toStatus.getCode())
                .set(FundOrderDO::getGmtModified, ZonedDateUtil.now());
        
        // 序列化扩展信息到JSON字符串
        try {
            String extendInfoJson = extendInfo != null ? JSON.toJSONString(extendInfo) : null;
            updateWrapper.set(FundOrderDO::getExtendInfo, extendInfoJson);
        } catch (Exception e) {
            // 序列化失败时设置为null
            log.error("updateStatusAndExtendInfo 序列化扩展信息到JSON字符串失败", e);
            updateWrapper.set(FundOrderDO::getExtendInfo, null);
        }
        
        if (FundOrderStatusEnum.SUCCESS == toStatus) {
            updateWrapper.set(FundOrderDO::getCompleteTime, ZonedDateUtil.now());
        }
        
        int update = baseMapper.update(null, updateWrapper);
        return update == 1;
    }

    @Override
    public FundOrderDO queryFundOrder(String orderId) {
        Wrapper<FundOrderDO> wrapper = Wrappers.lambdaQuery(FundOrderDO.class)
                .eq(FundOrderDO::getFundOrderId, orderId)
//                .eq(FundOrderDO::getActorUserId, userId)
                .eq(FundOrderDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        List<FundOrderDO> fundOrders = fundOrderMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(fundOrders)){
            return null;
        }
        return fundOrders.get(0);
    }
}
