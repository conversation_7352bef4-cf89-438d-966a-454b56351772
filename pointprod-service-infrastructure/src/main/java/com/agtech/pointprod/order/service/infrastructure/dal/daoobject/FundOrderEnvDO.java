package com.agtech.pointprod.order.service.infrastructure.dal.daoobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Table: fund_order_env
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("fund_order_env")
public class FundOrderEnvDO {
    /**
     * Column: id
     * Type: BIGINT
     * Remark: 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Column: order_env_id
     * Type: VARCHAR(64)
     * Remark: 环境业务id
     */
    private String orderEnvId;

    /**
     * Column: order_id
     * Type: VARCHAR(64)
     * Remark: 订单号
     */
    private String orderId;

    /**
     * Column: device_model
     * Type: VARCHAR(64)
     * Remark: 设备型号
     */
    private String deviceModel;

    /**
     * Column: device_brand
     * Type: VARCHAR(64)
     * Remark: 设备品牌
     */
    private String deviceBrand;

    /**
     * Column: mac
     * Type: VARCHAR(64)
     * Remark: 设备mac
     */
    private String mac;

    /**
     * Column: imei
     * Type: VARCHAR(64)
     * Remark: 设备imei
     */
    private String imei;

    /**
     * Column: imsi
     * Type: VARCHAR(64)
     * Remark: 设备imsi
     */
    private String imsi;

    /**
     * Column: screen_size
     * Type: VARCHAR(64)
     * Remark: 屏幕尺寸
     */
    private String screenSize;

    /**
     * Column: os
     * Type: VARCHAR(64)
     * Remark: 系统名称
     */
    private String os;

    /**
     * Column: os_version
     * Type: VARCHAR(64)
     * Remark: 系统版本
     */
    private String osVersion;

    /**
     * Column: app
     * Type: VARCHAR(64)
     * Remark: 应用
     */
    private String app;

    /**
     * Column: app_version
     * Type: VARCHAR(64)
     * Remark: 应用版本
     */
    private String appVersion;

    /**
     * Column: sdk_version
     * Type: VARCHAR(64)
     * Remark: sdk版本
     */
    private String sdkVersion;

    /**
     * Column: language
     * Type: VARCHAR(64)
     * Remark: 语言
     */
    private String language;

    /**
     * Column: browser
     * Type: VARCHAR(64)
     * Remark: 浏览器
     */
    private String browser;

    /**
     * Column: ip
     * Type: VARCHAR(32)
     * Remark: ip地址
     */
    private String ip;

    /**
     * Column: latlng_alg
     * Type: VARCHAR(16)
     * Remark: 经纬度算法
     */
    private String latlngAlg;

    /**
     * Column: lng
     * Type: VARCHAR(64)
     * Remark: 经度
     */
    private String lng;

    /**
     * Column: lat
     * Type: VARCHAR(64)
     * Remark: 纬度
     */
    private String lat;

    /**
     * Column: terminal_type
     * Type: VARCHAR(32)
     * Remark: 终端类型
     */
    private String terminalType;

    /**
     * Column: network_type
     * Type: VARCHAR(64)
     * Remark: 网络类型：2G3G4G5G
     */
    private String networkType;

    /**
     * Column: ext_info
     * Type: VARCHAR(512)
     * Remark: 扩展信息
     */
    private String extInfo;

    /**
     * Column: is_deleted
     * Type: TINYINT(3)
     * Default value: 0
     * Remark: 是否删除
     */
    private Integer isDeleted;

    /**
     * Column: gmt_create
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date gmtCreate;

    /**
     * Column: gmt_modified
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date gmtModified;
}