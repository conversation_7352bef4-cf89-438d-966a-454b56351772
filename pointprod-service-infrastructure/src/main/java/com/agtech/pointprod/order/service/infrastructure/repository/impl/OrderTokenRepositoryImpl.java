package com.agtech.pointprod.order.service.infrastructure.repository.impl;

import com.agtech.pointprod.order.service.domain.model.OrderToken;
import com.agtech.pointprod.order.service.infrastructure.dal.converter.OrderConverter;
import com.agtech.pointprod.order.service.infrastructure.dal.daointerface.OrderTokenDao;
import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.OrderTokenDO;
import com.agtech.pointprod.order.service.infrastructure.repository.OrderTokenRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
@Repository
public class OrderTokenRepositoryImpl implements OrderTokenRepository {
    @Resource
    private OrderTokenDao orderTokenDao;

    @Resource
    private OrderConverter orderConverter;

    @Override
    public OrderToken getOrderTokenForUpdate(String custId, String orderType) {
        OrderTokenDO orderTokenDO = orderTokenDao.getOrderTokenForUpdate(custId, orderType);
        return orderConverter.toOrderTokenDomainObject(orderTokenDO);
    }

    @Override
    public boolean saveOrderToken(OrderToken orderToken) {
        return orderTokenDao.saveOrderToken(orderConverter.toOrderTokenDO(orderToken));
    }

    @Override
    public boolean updateOrderToken(OrderToken orderToken) {
        return orderTokenDao.updateOrderToken(orderConverter.toOrderTokenDO(orderToken));
    }

    @Override
    public OrderToken getOrderToken(String userId, String orderToken) {
        OrderTokenDO orderTokenDO = orderTokenDao.getOrderToken(userId, orderToken);
        return orderConverter.toOrderTokenDomainObject(orderTokenDO);
    }

    @Override
    public boolean setOrderTokenUsed(String orderToken, String userId) {
        return orderTokenDao.setOrderTokenUsed(orderToken, userId);
    }
}
