package com.agtech.pointprod.order.service.infrastructure.dal.daointerface.impl;

import com.agtech.common.lang.util.StringUtil;
import com.agtech.common.util.CollectionUtil;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.order.service.domain.common.enums.OrderTokenUseStatusEnum;
import com.agtech.pointprod.order.service.infrastructure.dal.daointerface.OrderTokenDao;
import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.OrderTokenDO;
import com.agtech.pointprod.order.service.infrastructure.dal.mapper.OrderTokenMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
@Component
public class OrderTokenDaoImpl implements OrderTokenDao {
    @Resource
    private OrderTokenMapper orderTokenMapper;

    @Override
    public OrderTokenDO getOrderTokenForUpdate(String custId, String orderType) {
        Wrapper<OrderTokenDO> wrapper = Wrappers.lambdaQuery(OrderTokenDO.class)
                .eq(OrderTokenDO::getUserId, custId)
                .eq(StringUtil.isNotBlank(orderType), OrderTokenDO::getOrderType, orderType)
                .last(" for update");
        List<OrderTokenDO> orderTokenList = orderTokenMapper.selectList(wrapper);
        if(CollectionUtil.isEmpty(orderTokenList)){
            return null;
        }
        return orderTokenList.get(0);
    }

    @Override
    public boolean saveOrderToken(OrderTokenDO orderTokenDO) {
        return orderTokenMapper.insert(orderTokenDO) > 0;
    }

    @Override
    public boolean updateOrderToken(OrderTokenDO orderTokenDO) {
        Wrapper<OrderTokenDO> wrapper = Wrappers.lambdaUpdate(OrderTokenDO.class)
                .eq(OrderTokenDO::getUserId, orderTokenDO.getUserId())
                .eq(OrderTokenDO::getTokenId, orderTokenDO.getTokenId())
                .eq(OrderTokenDO::getOrderType, orderTokenDO.getOrderType())
                .set(OrderTokenDO::getToken, orderTokenDO.getToken())
                .set(OrderTokenDO::getUseStatus, orderTokenDO.getUseStatus())
                .set(OrderTokenDO::getGmtModified,orderTokenDO.getGmtModified());
        int i = orderTokenMapper.update(null, wrapper);
        return i == 1;
    }

    @Override
    public OrderTokenDO getOrderToken(String userId, String orderToken) {
        Wrapper<OrderTokenDO> wrapper = Wrappers.lambdaQuery(OrderTokenDO.class)
                .eq(OrderTokenDO::getUserId, userId)
                .eq(OrderTokenDO::getToken, orderToken);
        List<OrderTokenDO> orderTokenList = orderTokenMapper.selectList(wrapper);
        if(CollectionUtil.isEmpty(orderTokenList)){
            return null;
        }
        return orderTokenList.get(0);
    }

    @Override
    public boolean setOrderTokenUsed(String orderToken, String userId) {
        Wrapper<OrderTokenDO> wrapper = Wrappers.lambdaUpdate(OrderTokenDO.class)
                .eq(OrderTokenDO::getUserId, userId)
                .eq(OrderTokenDO::getToken, orderToken)
                .eq(OrderTokenDO::getUseStatus, OrderTokenUseStatusEnum.WAITING_USE.getValue())
                .set(OrderTokenDO::getUseStatus, OrderTokenUseStatusEnum.USED.getValue())
                .set(OrderTokenDO::getGmtModified, ZonedDateUtil.now());
        return orderTokenMapper.update(null, wrapper) > 0;
    }
}
