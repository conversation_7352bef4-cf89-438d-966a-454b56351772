package com.agtech.pointprod.order.service.infrastructure.repository.impl;

import com.agtech.pointprod.order.service.domain.model.FundUnique;
import com.agtech.pointprod.order.service.infrastructure.dal.converter.OrderConverter;
import com.agtech.pointprod.order.service.infrastructure.dal.daointerface.FundUniqueDao;
import com.agtech.pointprod.order.service.infrastructure.repository.FundUniqueRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
@Repository
public class FundUniqueRepositoryImpl implements FundUniqueRepository {
    @Resource
    private FundUniqueDao fundUniqueDao;

    @Resource
    private OrderConverter orderConverter;

    @Override
    public boolean createFundUnique(FundUnique fundUnique) {
        return fundUniqueDao.createFundUnique(orderConverter.toFundUniqueDO(fundUnique));
    }
}
