package com.agtech.pointprod.order.service.infrastructure.dal.daointerface;

import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundOrderEnvDO;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
public interface FundOrderEnvDao {
    /**
     * 创建订单环境
     *
     * @param fundOrderEnvDO 订单环境
     * @return 是否成功
     */
    boolean createOrderEnv(FundOrderEnvDO fundOrderEnvDO);
}
