package com.agtech.pointprod.order.service.infrastructure.dal.daointerface;

import java.util.List;

import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundOrderDO;
import com.agtech.pointprod.order.service.domain.model.OrderExtendInfo;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
public interface FundOrderDao extends IService<FundOrderDO> {

    /**
     * 查询订单DO
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 订单DO
     */
	FundOrderDO queryFundOrderForUpdate(String orderId, String userId);

    /**
     * 根据订单ID列表批量查询订单DO
     * @param fundOrderIds 订单ID列表
     * @return 订单DO列表
     */
    List<FundOrderDO> queryFundOrderByIds(List<String> fundOrderIds);

    /**
     * 創建訂單
     * @param fundOrderDO 订单DO
     * @return 是否成功
     */
    boolean createFundOrder(FundOrderDO fundOrderDO);

    /**
     * 关闭订单
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateFundOrderStatus(String orderId, String userId, String fromStatus, String toStatus);

    /**
     * 关闭订单并更新扩展信息
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @param extendInfo 扩展信息
     * @return 是否成功
     */
    boolean updateStatusAndExtendInfo(String orderId, String userId, FundOrderStatusEnum fromStatus, FundOrderStatusEnum toStatus, OrderExtendInfo extendInfo);

    /**
     * 查询订单信息
     * @param orderId 订单ID
     * @return 订单信息
     */
    FundOrderDO queryFundOrder(String orderId);
}
