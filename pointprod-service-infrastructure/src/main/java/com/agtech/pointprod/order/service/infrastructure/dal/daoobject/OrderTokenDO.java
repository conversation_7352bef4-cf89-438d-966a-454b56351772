package com.agtech.pointprod.order.service.infrastructure.dal.daoobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Table: order_token
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("order_token")
public class OrderTokenDO {
    /**
     * Column: id
     * Type: BIGINT
     * Remark: 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Column: token_id
     * Type: VARCHAR(64)
     * Remark: token id
     */
    private String tokenId;

    /**
     * Column: user_id
     * Type: VARCHAR(64)
     * Remark: 用户id
     */
    private String userId;

    /**
     * Column: order_type
     * Type: VARCHAR(16)
     * Remark: 订单类型
     */
    private String orderType;

    /**
     * Column: token
     * Type: VARCHAR(64)
     * Remark: token
     */
    private String token;

    /**
     * Column: use_status
     * Type: VARCHAR(16)
     * Remark: 使用状态：WAITING_USE/USED
     */
    private String useStatus;

    /**
     * Column: gmt_create
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date gmtCreate;

    /**
     * Column: gmt_modified
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date gmtModified;
}