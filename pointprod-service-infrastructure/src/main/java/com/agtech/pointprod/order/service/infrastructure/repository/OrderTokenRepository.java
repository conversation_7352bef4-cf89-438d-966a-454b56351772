package com.agtech.pointprod.order.service.infrastructure.repository;

import com.agtech.pointprod.order.service.domain.model.OrderToken;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
public interface OrderTokenRepository {
    OrderToken getOrderTokenForUpdate(String custId, String orderType);

    boolean saveOrderToken(OrderToken orderToken);

    boolean updateOrderToken(OrderToken orderToken);

    OrderToken getOrderToken(String userId, String orderToken);

    boolean setOrderTokenUsed(String orderToken, String userId);
}
