package com.agtech.pointprod.order.service.infrastructure.repository.impl;

import com.agtech.pointprod.order.service.domain.model.FundOrderEnv;
import com.agtech.pointprod.order.service.infrastructure.dal.converter.OrderConverter;
import com.agtech.pointprod.order.service.infrastructure.dal.daointerface.FundOrderEnvDao;
import com.agtech.pointprod.order.service.infrastructure.repository.FundOrderEnvRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
@Repository
public class FundOrderEnvRepositoryImpl implements FundOrderEnvRepository {
    @Resource
    private FundOrderEnvDao fundOrderEnvDao;

    @Resource
    private OrderConverter orderConverter;

    @Override
    public boolean createOrderEnv(FundOrderEnv fundOrderEnv) {
        return fundOrderEnvDao.createOrderEnv(orderConverter.orderEnvDomainObjectToDO(fundOrderEnv));
    }
}
