<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agtech.pointprod.order.service.infrastructure.dal.mapper.FundOrderMapper">
  <resultMap id="BaseResultMap" type="com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundOrderDO">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fund_order_id" jdbcType="VARCHAR" property="fundOrderId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="title_en" jdbcType="VARCHAR" property="titleEn" />
    <result column="actor_user_id" jdbcType="VARCHAR" property="actorUserId" />
    <result column="fund_type" jdbcType="VARCHAR" property="fundType" />
    <result column="fund_mode" jdbcType="VARCHAR" property="fundMode" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="fund_order_status" jdbcType="VARCHAR" property="fundOrderStatus" />
    <result column="fund_amount" jdbcType="DECIMAL" property="fundAmount" />
    <result column="fund_amount_currency" jdbcType="VARCHAR" property="fundAmountCurrency" />
    <result column="charge_amount" jdbcType="DECIMAL" property="chargeAmount" />
    <result column="charge_amount_currency" jdbcType="VARCHAR" property="chargeAmountCurrency" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="tax_amount_currency" jdbcType="VARCHAR" property="taxAmountCurrency" />
    <result column="paid_total_amount" jdbcType="DECIMAL" property="paidTotalAmount" />
    <result column="paid_total_amount_currency" jdbcType="VARCHAR" property="paidTotalAmountCurrency" />
    <result column="accept_total_amount" jdbcType="DECIMAL" property="acceptTotalAmount" />
    <result column="accept_total_amount_currency" jdbcType="VARCHAR" property="acceptTotalAmountCurrency" />
    <result column="accept_expiry_time" jdbcType="TIMESTAMP" property="acceptExpiryTime" />
    <result column="pay_expiry_time" jdbcType="TIMESTAMP" property="payExpiryTime" />
    <result column="auto_accept" jdbcType="VARCHAR" property="autoAccept" />
    <result column="stage_level" jdbcType="VARCHAR" property="stageLevel" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="extend_info" jdbcType="VARCHAR" property="extendInfo" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>

</mapper>