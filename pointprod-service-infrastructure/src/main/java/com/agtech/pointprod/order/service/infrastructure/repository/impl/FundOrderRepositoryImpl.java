package com.agtech.pointprod.order.service.infrastructure.repository.impl;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.OrderExtendInfo;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.infrastructure.dal.converter.FundOrderDataConverter;
import com.agtech.pointprod.order.service.infrastructure.dal.converter.OrderConverter;
import com.agtech.pointprod.order.service.infrastructure.dal.daointerface.FundOrderDao;
import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundOrderDO;
import com.agtech.pointprod.order.service.infrastructure.repository.FundOrderRepository;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
@Repository
public class FundOrderRepositoryImpl implements FundOrderRepository {
    @Resource
    private FundOrderDao fundOrderDao;

    @Resource
    private OrderConverter orderConverter;

    @Override
    public BaseOrderInfo getFundOrderForUpdate(String orderId, String userId) {
    	FundOrderDO fundOrderDO = fundOrderDao.queryFundOrderForUpdate(orderId, userId);
        return FundOrderDataConverter.toBaseOrderInfo(fundOrderDO);
    }
    

    @Override
    public List<BaseOrderInfo> queryByFundOrderIds(List<String> fundOrderIds) {
        if (CollectionUtils.isEmpty(fundOrderIds)) {
            return Collections.emptyList();
        }
        
        // DAO层查询DO对象
        List<FundOrderDO> fundOrderDOList = fundOrderDao.queryFundOrderByIds(fundOrderIds);
        
        // Repository层转换为Domain对象
        return FundOrderDataConverter.toBaseOrderInfoList(fundOrderDOList);
    }

    @Override
    public boolean createFundOrder(BaseOrderInfo baseOrderInfo) {
        FundOrderDO fundOrderDO = FundOrderDataConverter.toFundOrderDO(baseOrderInfo);
        return fundOrderDao.createFundOrder(fundOrderDO);
    }

    @Override
    public boolean updateFundOrderStatus(String orderId, String userId, String fromStatus, String toStatus) {
        return fundOrderDao.updateFundOrderStatus(orderId, userId, fromStatus, toStatus);
    }

    @Override
    public boolean updateStatusAndExtendInfo(String orderId, String userId, FundOrderStatusEnum fromStatus, FundOrderStatusEnum toStatus, OrderExtendInfo extendInfo) {
        return fundOrderDao.updateStatusAndExtendInfo(orderId, userId, fromStatus, toStatus, extendInfo);
    }

    @Override
    public BaseOrderInfo getFundOrder(String orderId) {
    	
    	FundOrderDO fundOrderDO = fundOrderDao.queryFundOrder(orderId);
        return FundOrderDataConverter.toBaseOrderInfo(fundOrderDO);
    }
}
