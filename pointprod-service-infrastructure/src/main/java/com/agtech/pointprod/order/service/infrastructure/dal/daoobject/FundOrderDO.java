package com.agtech.pointprod.order.service.infrastructure.dal.daoobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Table: fund_order
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("fund_order")
public class FundOrderDO {
    /**
     * Column: id
     * Type: BIGINT
     * Remark: 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Column: fund_order_id
     * Type: VARCHAR(64)
     * Remark: 订单号
     */
    private String fundOrderId;

    /**
     * Column: title
     * Type: VARCHAR(256)
     * Remark: 订单名称
     */
    private String title;

    /**
     * Column: title_en
     * Type: VARCHAR(256)
     * Remark: 英文订单名称
     */
    private String titleEn;

    /**
     * Column: actor_user_id
     * Type: VARCHAR(64)
     * Remark: 操作员用户id
     */
    private String actorUserId;

    /**
     * Column: fund_type
     * Type: VARCHAR(32)
     * Remark: 资金类型(TRANSFER_MCOIN)
     */
    private String fundType;

    /**
     * Column: fund_mode
     * Type: VARCHAR(32)
     * Remark: 资金模式
     */
    private String fundMode;

    /**
     * Column: request_id
     * Type: VARCHAR(64)
     * Remark: 请求编号
     */
    private String requestId;

    /**
     * Column: fund_order_status
     * Type: VARCHAR(16)
     * Remark: 订单状态()
     */
    private String fundOrderStatus;

    /**
     * Column: fund_amount
     * Type: DECIMAL
     * Default value: 0.00
     * Remark: 资金金额
     */
    private BigDecimal fundAmount;

    /**
     * Column: fund_amount_currency
     * Type: VARCHAR(16)
     * Remark: 币种
     */
    private String fundAmountCurrency;

    /**
     * Column: charge_amount
     * Type: DECIMAL
     * Default value: 0.00
     * Remark: 收费金额
     */
    private BigDecimal chargeAmount;

    /**
     * Column: charge_amount_currency
     * Type: VARCHAR(16)
     * Remark: 收费金额币种
     */
    private String chargeAmountCurrency;

    /**
     * Column: tax_amount
     * Type: DECIMAL
     * Default value: 0.00
     * Remark: 税金额
     */
    private BigDecimal taxAmount;

    /**
     * Column: tax_amount_currency
     * Type: VARCHAR(16)
     * Remark: 税金额币种
     */
    private String taxAmountCurrency;

    /**
     * Column: paid_total_amount
     * Type: DECIMAL
     * Default value: 0.00
     * Remark: 订单支付总额
     */
    private BigDecimal paidTotalAmount;

    /**
     * Column: paid_total_amount_currency
     * Type: VARCHAR(16)
     * Remark: 支付金额币种
     */
    private String paidTotalAmountCurrency;

    /**
     * Column: accept_total_amount
     * Type: DECIMAL
     * Default value: 0.00
     * Remark: 收款总金额
     */
    private BigDecimal acceptTotalAmount;

    /**
     * Column: accept_total_amount_currency
     * Type: VARCHAR(16)
     * Remark: 收款总金额币种
     */
    private String acceptTotalAmountCurrency;

    /**
     * Column: accept_expiry_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 受理过期时间(2nd process user accepted expiry time)
     */
    private Date acceptExpiryTime;

    /**
     * Column: pay_expiry_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 支付到期时间
     */
    private Date payExpiryTime;

    /**
     * Column: auto_accept
     * Type: VARCHAR(1)
     * Remark: 是否自动受理
     */
    private String autoAccept;

    /**
     * Column: stage_level
     * Type: VARCHAR(32)
     * Remark: 阶段等级
     */
    private String stageLevel;

    /**
     * Column: complete_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 完成时间
     */
    private Date completeTime;

    /**
     * Column: memo
     * Type: VARCHAR(256)
     * Remark: 订单备注
     */
    private String memo;

    /**
     * Column: extend_info
     * Type: VARCHAR(2048)
     * Remark: 系统扩展信息(system extend info for accept fail reason etc)
     */
    private String extendInfo;



    /**
     * Column: is_deleted
     * Type: TINYINT(3)
     * Default value: 0
     * Remark: 是否删除
     */
    private Integer isDeleted;

    /**
     * Column: gmt_create
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date gmtCreate;

    /**
     * Column: gmt_modified
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date gmtModified;
}