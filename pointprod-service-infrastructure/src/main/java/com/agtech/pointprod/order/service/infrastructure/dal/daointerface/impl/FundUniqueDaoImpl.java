package com.agtech.pointprod.order.service.infrastructure.dal.daointerface.impl;

import com.agtech.pointprod.order.service.infrastructure.dal.daointerface.FundUniqueDao;
import com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundUniqueDO;
import com.agtech.pointprod.order.service.infrastructure.dal.mapper.FundUniqueMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
@Component
public class FundUniqueDaoImpl implements FundUniqueDao {
    @Resource
    private FundUniqueMapper fundUniqueMapper;

    @Override
    public boolean createFundUnique(FundUniqueDO fundUniqueDO) {
        return fundUniqueMapper.insert(fundUniqueDO) > 0;
    }
}
