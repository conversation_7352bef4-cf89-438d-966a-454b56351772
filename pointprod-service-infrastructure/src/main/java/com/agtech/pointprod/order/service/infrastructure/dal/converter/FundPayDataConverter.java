package com.agtech.pointprod.order.service.infrastructure.dal.converter;

import java.util.ArrayList;
import java.util.List;

import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.common.util.CollectionUtil;
import com.agtech.pointprod.order.service.domain.common.enums.AcceptOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.AssetToolNameEnum;
import com.agtech.pointprod.order.service.domain.common.enums.IdentifyIdTypeEnum;
import com.agtech.pointprod.order.service.domain.common.enums.PayOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.SubOrderTypeEnum;
import com.agtech.pointprod.service.domain.model.ParticipantUser;
import com.agtech.pointprod.service.infrastructure.common.constant.PointProdConstant;
import com.alibaba.fastjson2.JSON;
import com.agtech.common.lang.util.StringUtil;
import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;
import com.agtech.pointprod.order.service.domain.model.PayeeParticipantInfo;
import com.agtech.pointprod.order.service.domain.model.PayerParticipantInfo;
import com.agtech.pointprod.pay.service.infrastructure.dal.daoobject.FundPayDO;


/**
 * 支付&收款单据数据转换器
 * 
 * <AUTHOR>
 * @version $Id: FundPayDataConverter.java, v 0.1 2025年6月25日 18:02:52 zhongqiang
 *          Exp $
 */
public class FundPayDataConverter {

	/**
	 * 将JSON字符串转换为ParticipantUser对象
	 * 
	 * @param jsonString JSON字符串
	 * @return ParticipantUser对象
	 */
	public static ParticipantUser convertToParticipantUser(String jsonString) {
		if (StringUtil.isBlank(jsonString)) {
			return null;
		}
		try {
			return JSON.parseObject(jsonString, ParticipantUser.class);
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 将ParticipantUser对象转换为JSON字符串
	 * 
	 * @param participantUser ParticipantUser对象
	 * @return JSON字符串
	 */
	public static String convertToJsonString(ParticipantUser participantUser) {
		if (participantUser == null) {
			return null;
		}
		try {
			return JSON.toJSONString(participantUser);
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 批量将支付单数据对象转换支付领域模型
	 * 
	 * @param fundPayList
	 * @return
	 */
	public static List<PayOrderInfo> toPayOrderInfoList(List<FundPayDO> fundPayList) {
		if (CollectionUtil.isEmpty(fundPayList)) {
			return null;
		}
		List<PayOrderInfo> payOrderInfoList = new ArrayList<PayOrderInfo>();
		for (FundPayDO fundPayDO : fundPayList) {
			payOrderInfoList.add(toPayOrderInfo(fundPayDO));
		}
		return payOrderInfoList;
	}

	/**
	 * 支付单据数据对象转换领域对象
	 * 
	 * @param fundPayDO
	 * @return
	 */
	private static PayOrderInfo toPayOrderInfo(FundPayDO fundPayDO) {

		if (fundPayDO == null) {
			return null;
		}
		PayOrderInfo payOrderInfo = new PayOrderInfo();
		payOrderInfo.setFundOrderId(fundPayDO.getFundOrderId());
		payOrderInfo.setPayAmount(new MultiCurrencyMoney(fundPayDO.getAmount()));
		ParticipantUser payerExtInfo = convertToParticipantUser(fundPayDO.getParticipantExtInfo());
		PayerParticipantInfo payer = new PayerParticipantInfo(fundPayDO.getUserId(), AssetToolNameEnum.MCOIN,
				fundPayDO.getAccountNo(), IdentifyIdTypeEnum.getByCode(fundPayDO.getIdentifyIdType()),
				fundPayDO.getIdentifyId(), payerExtInfo);

		payOrderInfo.setPayer(payer);
		payOrderInfo.setPayExtendInfo(fundPayDO.getRequestExtendInfo());
		payOrderInfo.setPayOrderId(fundPayDO.getFundPayId());
		payOrderInfo.setPayStatus(PayOrderStatusEnum.getByCode(fundPayDO.getStatus()));
		payOrderInfo.setPayTime(fundPayDO.getCompleteTime());
		payOrderInfo.setCreatedTime(fundPayDO.getGmtCreate());
		payOrderInfo.setFluxDetailInfoList(null);
		return payOrderInfo;
	}

	/**
	 * 批量将收款单数据对象转换收款领域模型
	 * 
	 * @param fundPayList
	 * @return
	 */
	public static List<AcceptOrderInfo> toAcceptOrderInfoList(List<FundPayDO> fundPayList) {

		if (CollectionUtil.isEmpty(fundPayList)) {
			return null;
		}
		List<AcceptOrderInfo> acceptOrderInfoList = new ArrayList<AcceptOrderInfo>();
		for (FundPayDO fundPayDO : fundPayList) {
			acceptOrderInfoList.add(toAcceptOrderInfo(fundPayDO));
		}
		return acceptOrderInfoList;
	}

	/**
	 * 
	 * 
	 * @param fundPayDO
	 * @return
	 */
	private static AcceptOrderInfo toAcceptOrderInfo(FundPayDO fundPayDO) {

		if (fundPayDO == null) {
			return null;
		}
		AcceptOrderInfo acceptOrderInfo = new AcceptOrderInfo();
		acceptOrderInfo.setFundOrderId(fundPayDO.getFundOrderId());
		acceptOrderInfo.setAcceptAmount(new MultiCurrencyMoney(fundPayDO.getAmount()));
		PayeeParticipantInfo payee = new PayeeParticipantInfo(fundPayDO.getUserId(), AssetToolNameEnum.MCOIN,
				fundPayDO.getAccountNo(), IdentifyIdTypeEnum.getByCode(fundPayDO.getIdentifyIdType()),
				fundPayDO.getIdentifyId(), convertToParticipantUser(fundPayDO.getParticipantExtInfo()));

		acceptOrderInfo.setPayee(payee);
		acceptOrderInfo.setAcceptExtendInfo(fundPayDO.getRequestExtendInfo());
		acceptOrderInfo.setAcceptOrderId(fundPayDO.getFundPayId());
		acceptOrderInfo.setAcceptStatus(AcceptOrderStatusEnum.getByCode(fundPayDO.getStatus()));
		acceptOrderInfo.setAcceptTime(fundPayDO.getOccurTime());
		acceptOrderInfo.setCreatedTime(fundPayDO.getGmtCreate());
		acceptOrderInfo.setFluxDetailInfoList(null);
		return acceptOrderInfo;

	}

	/**
	 * 收款领域对象转换数据对象
	 * 
	 * @param acceptOrderInfo
	 * @return
	 */
	private static FundPayDO toFundPayDO(AcceptOrderInfo acceptOrderInfo) {

		if (acceptOrderInfo == null) {
			return null;
		}
		FundPayDO fundPayDO = new FundPayDO();
		fundPayDO.setAmount(acceptOrderInfo.getAcceptAmount().getAmount());
		fundPayDO.setCurrency(null);
		fundPayDO.setFundOrderId(acceptOrderInfo.getFundOrderId());
		fundPayDO.setFundPayId(acceptOrderInfo.getAcceptOrderId());
		fundPayDO.setGmtCreate(acceptOrderInfo.getCreatedTime());
		PayeeParticipantInfo payee = acceptOrderInfo.getPayee();
		if (payee != null) {
			fundPayDO.setUserId(payee.getUserId());
			fundPayDO.setIdentifyId(payee.getIdentifyId());
			fundPayDO.setIdentifyIdType(payee.getIdentifyIdType().getCode());
			fundPayDO.setAssetTool(payee.getAssetTool().getCode());
			fundPayDO.setAccountNo(payee.getAccountNo());
			fundPayDO.setParticipantExtInfo(convertToJsonString(payee.getPayeeExtInfo()));
		}
		fundPayDO.setOccurTime(acceptOrderInfo.getAcceptTime());
		fundPayDO.setRequestExtendInfo(acceptOrderInfo.getAcceptExtendInfo());
		fundPayDO.setStatus(acceptOrderInfo.getAcceptStatus().getCode());
		fundPayDO.setSubOrderType(SubOrderTypeEnum.ACCEPT.getCode());

		return fundPayDO;
	}
	
	/**
	 * 批量收款单领域对象转换到数据对象
	 * 
	 * @param acceptOrderInfoList
	 * @return
	 */
	public static List<FundPayDO> toFundPayDOList(List<AcceptOrderInfo> acceptOrderInfoList){
		
		if (CollectionUtil.isEmpty(acceptOrderInfoList)) {
			return null;
		}
		List<FundPayDO> fundPayDOList = new ArrayList<FundPayDO>();
		for (AcceptOrderInfo acceptOrderInfo : acceptOrderInfoList) {
			fundPayDOList.add(toFundPayDO(acceptOrderInfo));
		}
		return fundPayDOList;
	}

	public static FundPayDO toPayOrderDO(PayOrderInfo payOrderInfo) {
		if(payOrderInfo == null) {
            return null;
        }
		FundPayDO fundPayDO = new FundPayDO();
		//fundPayDO.setId(0L);
		fundPayDO.setFundPayId(payOrderInfo.getPayOrderId());
		fundPayDO.setFundOrderId(payOrderInfo.getFundOrderId());
		fundPayDO.setSubOrderType(SubOrderTypeEnum.PAY.getCode());
		fundPayDO.setUserId(payOrderInfo.getUserId());
		fundPayDO.setAssetTool(payOrderInfo.getPayer().getAssetTool().getCode());
		fundPayDO.setAccountNo(payOrderInfo.getPayer().getAccountNo());
		fundPayDO.setParticipantExtInfo(convertToJsonString(payOrderInfo.getPayer().getPayerExtInfo()));
		fundPayDO.setIdentifyId(payOrderInfo.getPayer().getIdentifyId());
		fundPayDO.setIdentifyIdType(payOrderInfo.getPayer().getIdentifyIdType().getCode());
		fundPayDO.setStatus(payOrderInfo.getPayStatus().getCode());
		fundPayDO.setAmount(payOrderInfo.getPayAmount().getAmount());
		fundPayDO.setCurrency(PointProdConstant.MCOIN_CURRENCY);
		fundPayDO.setOccurTime(payOrderInfo.getPayTime());
		fundPayDO.setCompleteTime(payOrderInfo.getPayTime());
		fundPayDO.setRequestExtendInfo("");
		fundPayDO.setSystemExtendInfo("");
		fundPayDO.setIsDeleted(0);
		fundPayDO.setGmtCreate(payOrderInfo.getCreatedTime());
		fundPayDO.setGmtModified(payOrderInfo.getCreatedTime());
		return fundPayDO;
	}

	public static FundPayDO toAcceptOrderDO(AcceptOrderInfo acceptOrderInfo) {
		if(acceptOrderInfo == null) {
            return null;
        }
        FundPayDO fundPayDO = new FundPayDO();
        //fundPayDO.setId(0L);
        fundPayDO.setFundPayId(acceptOrderInfo.getAcceptOrderId());
        fundPayDO.setFundOrderId(acceptOrderInfo.getFundOrderId());
        fundPayDO.setSubOrderType(SubOrderTypeEnum.ACCEPT.getCode());
        fundPayDO.setUserId(acceptOrderInfo.getUserId());
        fundPayDO.setAssetTool(acceptOrderInfo.getPayee().getAssetTool().getCode());
        fundPayDO.setAccountNo(acceptOrderInfo.getPayee().getAccountNo());
        fundPayDO.setParticipantExtInfo(convertToJsonString(acceptOrderInfo.getPayee().getPayeeExtInfo()));
        fundPayDO.setIdentifyId(acceptOrderInfo.getPayee().getIdentifyId());
        fundPayDO.setIdentifyIdType(acceptOrderInfo.getPayee().getIdentifyIdType().getCode());
        fundPayDO.setStatus(acceptOrderInfo.getAcceptStatus().getCode());
        fundPayDO.setAmount(acceptOrderInfo.getAcceptAmount().getAmount());
        fundPayDO.setCurrency(PointProdConstant.MCOIN_CURRENCY);
        fundPayDO.setOccurTime(acceptOrderInfo.getAcceptTime());
        fundPayDO.setRequestExtendInfo("");
        fundPayDO.setSystemExtendInfo("");
        fundPayDO.setIsDeleted(0);
        fundPayDO.setGmtCreate(acceptOrderInfo.getCreatedTime());
        fundPayDO.setGmtModified(acceptOrderInfo.getCreatedTime());
		return fundPayDO;
	}
}
