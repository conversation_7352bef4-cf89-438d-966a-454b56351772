package com.agtech.pointprod.order.service.infrastructure.repository;

import java.util.List;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.OrderExtendInfo;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;

/**
 * <AUTHOR>
 * @version OrderTokenDao.java, v0.1 2025/6/18 11:45 zhongqigang Exp $
 */
public interface FundOrderRepository {
	BaseOrderInfo getFundOrderForUpdate(String orderId, String userId);

    /**
     * 根据订单ID列表批量查询订单信息
     * @param fundOrderIds 订单ID列表
     * @return 订单信息列表
     */
    List<BaseOrderInfo> queryByFundOrderIds(List<String> fundOrderIds);


    /**
     * 創建訂單
     * @param baseOrderInfo 订单信息
     * @return 是否成功
     */
    boolean createFundOrder(BaseOrderInfo baseOrderInfo);

    /**
     * 更新订单状态
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateFundOrderStatus(String orderId, String userId, String fromStatus, String toStatus);

    /**
     * 更新订单状态和扩展信息
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @param extendInfo 扩展信息
     * @return 是否成功
     */
    boolean updateStatusAndExtendInfo(String orderId, String userId, FundOrderStatusEnum fromStatus, FundOrderStatusEnum toStatus, OrderExtendInfo extendInfo);

    /**
     * 查询订单信息
     * @param orderId 订单ID
     * @return 订单信息
     */
    BaseOrderInfo getFundOrder(String orderId);
}
