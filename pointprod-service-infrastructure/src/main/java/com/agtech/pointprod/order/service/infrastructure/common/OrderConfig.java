package com.agtech.pointprod.order.service.infrastructure.common;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version OrderConfig.java, v0.1 2025/06/16 14:21 zhongqigang Exp $
 */
@Getter
@Setter
@RefreshScope
@Component
public class OrderConfig {
    @Value("${order.expireSeconds:900}")
    private Integer expireSeconds;

    @Value("${order.payViewMcoinChannelIcon:https://mcoin-prd.oss-cn-hongkong.aliyuncs.com/image/show/mCion_cashier_logo.png}")
    private String payViewMcoinChannelIcon;

    @Value("${order.needValidateMemo:false}")
    private boolean needValidateMemo;
}
