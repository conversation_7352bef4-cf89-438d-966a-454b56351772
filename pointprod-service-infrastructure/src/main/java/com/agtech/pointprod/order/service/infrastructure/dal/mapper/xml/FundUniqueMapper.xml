<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agtech.pointprod.order.service.infrastructure.dal.mapper.FundUniqueMapper">
  <resultMap id="BaseResultMap" type="com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundUniqueDO">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="global_unique_id" jdbcType="VARCHAR" property="globalUniqueId" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>

</mapper>