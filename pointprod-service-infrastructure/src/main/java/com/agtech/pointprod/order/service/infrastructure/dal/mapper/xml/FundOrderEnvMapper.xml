<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agtech.pointprod.order.service.infrastructure.dal.mapper.FundOrderEnvMapper">
  <resultMap id="BaseResultMap" type="com.agtech.pointprod.order.service.infrastructure.dal.daoobject.FundOrderEnvDO">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_env_id" jdbcType="VARCHAR" property="orderEnvId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="device_model" jdbcType="VARCHAR" property="deviceModel" />
    <result column="device_brand" jdbcType="VARCHAR" property="deviceBrand" />
    <result column="mac" jdbcType="VARCHAR" property="mac" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="imsi" jdbcType="VARCHAR" property="imsi" />
    <result column="screen_size" jdbcType="VARCHAR" property="screenSize" />
    <result column="os" jdbcType="VARCHAR" property="os" />
    <result column="os_version" jdbcType="VARCHAR" property="osVersion" />
    <result column="app" jdbcType="VARCHAR" property="app" />
    <result column="app_version" jdbcType="VARCHAR" property="appVersion" />
    <result column="sdk_version" jdbcType="VARCHAR" property="sdkVersion" />
    <result column="language" jdbcType="VARCHAR" property="language" />
    <result column="browser" jdbcType="VARCHAR" property="browser" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="latlng_alg" jdbcType="VARCHAR" property="latlngAlg" />
    <result column="lng" jdbcType="VARCHAR" property="lng" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="terminal_type" jdbcType="VARCHAR" property="terminalType" />
    <result column="network_type" jdbcType="VARCHAR" property="networkType" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>

</mapper>