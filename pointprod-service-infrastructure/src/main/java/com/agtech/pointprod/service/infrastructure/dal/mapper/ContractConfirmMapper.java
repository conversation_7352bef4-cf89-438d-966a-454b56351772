package com.agtech.pointprod.service.infrastructure.dal.mapper;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.ContractConfirmDO;
import com.agtech.pointprod.service.infrastructure.dto.response.UserConfirmContractRsp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 广告表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
public interface ContractConfirmMapper extends BaseMapper<ContractConfirmDO> {

    List<UserConfirmContractRsp> queryMultiUserConfirmContractList(@Param("contractType") String contractType, @Param("status") String status, @Param("userIds") List<String> userIds);

}
