package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Getter;
import lombok.Setter;

/**
 * 转赠用户关系数据对象
 * 对应表：transfer_relation
 */
@Getter
@Setter
@TableName("transfer_relation")
public class TransferRelationDO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务id
     */
    private String transferRelationId;

    /**
     * 付款方ID
     */
    private String actorUserId;

    /**
     * 收款方用户ID
     */
    private String participantUserId;

    /**
     * 接收方附加信息，JSON字符串结构：{"nickName":"张三","phone":"111111111","areaCode":"+86","headLogo":"https://example.com/avatar/123.jpg"}
     */
    private String participantUserAdditionalInfo;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer isDeleted;
}
