package com.agtech.pointprod.service.infrastructure.gateway.impl;

import com.agtech.pointprod.service.domain.gateway.MPayUserInfoGateway;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.infrastructure.integration.mpay.acl.MPayAclService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class MPayUserInfoGatewayImpl implements MPayUserInfoGateway {
    @Resource
    private MPayAclService mPayAclService;

    @Override
    public MPayUserInfo getUserMsg(String custId) {
        return mPayAclService.getUserMsg(custId);
    }

    @Override
    public MPayUserInfo getUserMsg(String areaCode, String phone) {
        return mPayAclService.getUserInfo(areaCode, phone);
    }

    @Override
    public boolean checkSecurityId(String custId, String orderId, String securityId) {
        return mPayAclService.checkSecurityId(custId, orderId, securityId);
    }

    @Override
    public List<MPayUserInfo> getUserMsgList(List<String> custIds) {
        return mPayAclService.getUserMsgList(custIds);
    }


}
