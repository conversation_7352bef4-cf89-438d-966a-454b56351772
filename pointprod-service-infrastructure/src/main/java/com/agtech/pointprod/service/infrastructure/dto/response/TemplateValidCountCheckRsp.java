package com.agtech.pointprod.service.infrastructure.dto.response;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/24 20:06
 */
@Setter
@Getter
public class TemplateValidCountCheckRsp {

    private boolean exceedLimit;
    private Integer limitCount;

    public TemplateValidCountCheckRsp() {
    }

    public TemplateValidCountCheckRsp(boolean exceedLimit, Integer limitCount) {
        this.exceedLimit = exceedLimit;
        this.limitCount = limitCount;
    }
}
