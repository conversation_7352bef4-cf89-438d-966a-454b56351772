package com.agtech.pointprod.service.infrastructure.gateway.impl;

import com.agtech.pointprod.service.domain.gateway.McoinAccountGateway;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.McoinTransferInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.acl.McoinAclService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class McoinAccountGatewayImpl implements McoinAccountGateway {
    @Resource
    private McoinAclService mcoinAclService;

    @Override
    public McoinAccount queryMcoinAccount(String userId) {
        return mcoinAclService.queryMcoinAccount(userId);
    }

    @Override
    public McoinTransferResult transferPoint(McoinTransferInfo transferInfo, MPayUserInfo payerUserInfo,
                                             MPayUserInfo payeeUserInfo) {
        return mcoinAclService.transferPoint(transferInfo, payerUserInfo, payeeUserInfo);
    }

    @Override
    public McoinTransferResult queryTransfer(String outOrderId, String payeeCustId) {
        return mcoinAclService.queryTransfer(outOrderId, payeeCustId);
    }

    
}
