package com.agtech.pointprod.service.infrastructure.dal.converter;

import com.agtech.pointprod.service.domain.model.BlackList;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.BlackListDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BlackListConverter {
    BlackList do2Model(BlackListDO blackListDO);

    List<BlackList> doList2ModelList(List<BlackListDO> blackList);

    BlackListDO model2Do(BlackList blackList);
}
