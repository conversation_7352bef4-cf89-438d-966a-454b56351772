package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import com.agtech.pointprod.service.domain.common.enums.BlackListAccountTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.BlackListEntityTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.YesOrNoEnum;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.BlackListDao;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.BlackListDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.BlackListMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @version TaskRetryDao.java, v0.1 2025/6/18 11:21 zhongqigang Exp $
 */
@Component
public class BlackListDaoImpl extends ServiceImpl<BlackListMapper, BlackListDO> implements BlackListDao {
    @Resource
    private BlackListMapper blackListMapper;


    @Override
    public BlackListDO queryTransferBlackListByUserId(String userId) {
        LambdaQueryWrapper<BlackListDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BlackListDO::getEntityType, BlackListEntityTypeEnum.PERSON.getValue());
        lambdaQueryWrapper.eq(BlackListDO::getEntityId, userId);
        lambdaQueryWrapper.eq(BlackListDO::getAccountType, BlackListAccountTypeEnum.USER_ID.getValue());
        lambdaQueryWrapper.eq(BlackListDO::getIsDeleted, 0);
        List<BlackListDO> blackListDOList = this.list(lambdaQueryWrapper);
        return CollectionUtils.isNotEmpty(blackListDOList) ? blackListDOList.get(0) : null;
    }

    @Override
    public long countBlackList(String userId) {
        return blackListMapper.selectCount(new LambdaQueryWrapper<BlackListDO>()
                .eq(BlackListDO::getEntityId, userId)
                .eq(BlackListDO::getAccountType, BlackListAccountTypeEnum.USER_ID.getValue())
                .eq(BlackListDO::getIsDeleted, YesOrNoEnum.NO.getValue())
                .eq(BlackListDO::getEntityType, BlackListEntityTypeEnum.PERSON.getValue())
        );
    }

    @Override
    public BlackListDO selectBlackList(String entityType, String accountType, String entityId) {
        LambdaQueryWrapper<BlackListDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BlackListDO::getEntityType, entityType);
        lambdaQueryWrapper.eq(BlackListDO::getAccountType, accountType);
        lambdaQueryWrapper.eq(BlackListDO::getEntityId, entityId);
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public List<BlackListDO> selectBlackLists(String entityType, String accountType, List<String> entityIds) {
        LambdaQueryWrapper<BlackListDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BlackListDO::getEntityType, entityType);
        lambdaQueryWrapper.eq(BlackListDO::getAccountType, accountType);
        lambdaQueryWrapper.in(BlackListDO::getEntityId, entityIds);
        return list(lambdaQueryWrapper);
    }

    @Override
    public void updateByEntity(BlackListDO blackList, String entityType, String accountType, String entityId) {
        blackList.setEntityId(null);
        blackList.setEntityType(null);
        blackList.setAccountType(null);

        LambdaUpdateWrapper<BlackListDO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(BlackListDO::getEntityId, entityId);
        lambdaUpdateWrapper.eq(BlackListDO::getAccountType, accountType);
        lambdaUpdateWrapper.eq(BlackListDO::getEntityType, entityType);
        update(blackList, lambdaUpdateWrapper);
    }

    @Override
    public List<String> selectAllEntityIdsInBlackList(String entityType, String accountType, List<String> entityIds) {
        LambdaQueryWrapper<BlackListDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BlackListDO::getEntityType, entityType);
        lambdaQueryWrapper.eq(BlackListDO::getAccountType, accountType);
        lambdaQueryWrapper.eq(BlackListDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(entityIds), BlackListDO::getEntityId, entityIds);
        lambdaQueryWrapper.select(BlackListDO::getEntityId);
        return blackListMapper.selectObjs(lambdaQueryWrapper);
    }


}
