package com.agtech.pointprod.service.infrastructure.repository.impl;

import com.agtech.pointprod.service.domain.model.Contract;
import com.agtech.pointprod.service.infrastructure.dal.converter.ContractConverter;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.ContractDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.ContractDO;
import com.agtech.pointprod.service.infrastructure.repository.ContractRepository;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Component
@Slf4j
public class ContractRepositoryImpl implements ContractRepository {
    @Resource
    private ContractDAO contractDAO;
    @Resource
    private ContractConverter contractConverter;

    @Override
    public Contract queryLatestContractByBizType(String bizType) {
        ContractDO contractDO = contractDAO.queryLatestContractByBizType(bizType);
        return contractConverter.do2Model(contractDO);
    }

    @Override
    public Contract queryContractByContractId(String contractId) {
        ContractDO contractDO = contractDAO.queryContractByContractId(contractId);
        return contractConverter.do2Model(contractDO);
    }
    
    @Override
    public boolean saveContract(Contract contract) {
        try {
            ContractDO contractDO = contractConverter.model2Do(contract);
            return contractDAO.saveContract(contractDO);
        } catch (Exception e) {
            log.error("Failed to save contract: {}", contract.getContractId(), e);
            return false;
        }
    }
}
