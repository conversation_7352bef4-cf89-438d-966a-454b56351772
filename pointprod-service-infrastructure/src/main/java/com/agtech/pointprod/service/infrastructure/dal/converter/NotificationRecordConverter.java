package com.agtech.pointprod.service.infrastructure.dal.converter;

import java.util.List;

import org.mapstruct.Mapper;

import com.agtech.pointprod.service.domain.model.NotificationRecord;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.NotificationRecordDO;

/**
 * 通知记录转换器
 */
@Mapper(componentModel = "spring")
public interface NotificationRecordConverter {
    
    /**
     * DO转换为领域模型
     */
    NotificationRecord doToDomain(NotificationRecordDO notificationRecordDO);
    
    /**
     * 领域模型转换为DO
     */
    NotificationRecordDO domainToDo(NotificationRecord notificationRecord);
    
    /**
     * DO列表转换为领域模型列表
     */
    List<NotificationRecord> doListToDomainList(List<NotificationRecordDO> notificationRecordDOList);
    
    /**
     * 领域模型列表转换为DO列表
     */
    List<NotificationRecordDO> domainListToDoList(List<NotificationRecord> notificationRecordList);
} 