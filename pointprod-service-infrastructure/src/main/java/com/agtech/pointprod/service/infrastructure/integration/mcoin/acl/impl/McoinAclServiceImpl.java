package com.agtech.pointprod.service.infrastructure.integration.mcoin.acl.impl;

import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.McoinTransferInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.acl.McoinAclService;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.adapter.McoinAdapterService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version McoinAclService.java, v0.1 2025/6/17 17:33 zhongqigang Exp $
 */
@Service
public class McoinAclServiceImpl implements McoinAclService {
    @Resource
    private McoinAdapterService mcoinAdapterService;

    @Override
    public McoinAccount queryMcoinAccount(String userId) {
        return mcoinAdapterService.queryMcoinAccount(userId);
    }

    @Override
    public McoinTransferResult transferPoint(McoinTransferInfo transferInfo,
                                             MPayUserInfo payerUserInfo, MPayUserInfo payeeUserInfo) {
        return mcoinAdapterService.transferPoint(transferInfo, payerUserInfo, payeeUserInfo);
    }

    @Override
    public McoinTransferResult queryTransfer(String outOrderId, String payeeCustId) {
        return mcoinAdapterService.queryTransfer(outOrderId, payeeCustId);
    }
}
