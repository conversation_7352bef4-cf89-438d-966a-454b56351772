package com.agtech.pointprod.service.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 线程池配置属性
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "thread-pool")
@RefreshScope
public class ThreadPoolProperties {

    /**
     * 后端执行器线程池配置
     */
    private ThreadPoolConfig backend = new ThreadPoolConfig();

    /**
     * 事务后消息处理线程池配置
     */
    private ThreadPoolConfig postTransactionMessage = new ThreadPoolConfig();

    /**
     * 支付异步处理线程池配置
     */
    private ThreadPoolConfig payment = new ThreadPoolConfig();

    /**
     * 订单服务线程池配置
     */
    private ThreadPoolConfig orderService = new ThreadPoolConfig();

    /**
     * 通用线程池配置
     */
    @Data
    public static class ThreadPoolConfig {
        /**
         * 核心线程数
         */
        private int corePoolSize = 2;

        /**
         * 最大线程数
         */
        private int maxPoolSize = 8;

        /**
         * 队列容量
         */
        private int queueCapacity = 100;

        /**
         * 等待时间（秒）
         */
        private int awaitTerminationSeconds = 60;

        /**
         * 线程保活时间（秒）
         */
        private int keepAliveSeconds = 300;
    }
}