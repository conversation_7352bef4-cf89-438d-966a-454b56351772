package com.agtech.pointprod.service.infrastructure.repository.impl;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.domain.model.TransferFundAccumulation;
import com.agtech.pointprod.service.infrastructure.dal.converter.TransferFundAccumulationConverter;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.TransferFundAccumulationDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferFundAccumulationDO;
import com.agtech.pointprod.service.infrastructure.repository.TransferFundAccumulationRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 15:11
 */
@Component
public class TransferFundAccumulationRepositoryImpl implements TransferFundAccumulationRepository {
    
    @Resource
    private TransferFundAccumulationDAO transferFundAccumulationDAO;
    
    @Resource
    private TransferFundAccumulationConverter transferFundAccumulationConverter;

    @Override
    public PageInfoDTO<TransferFundAccumulation> queryTemplateListByPage(List<String> includeUserIds, List<String> excludeUserIds, Integer pageSize, Integer pageNo) {
        IPage<TransferFundAccumulationDO> pageResult = transferFundAccumulationDAO.listByPage(includeUserIds, excludeUserIds, pageSize, pageNo);
        PageInfoDTO<TransferFundAccumulation> pageInfoDTO = new PageInfoDTO<>();
        pageInfoDTO.setPageSize(pageResult.getSize());
        pageInfoDTO.setCurrentPage(pageResult.getCurrent());
        pageInfoDTO.setTotalPage(pageResult.getPages());
        pageInfoDTO.setTotalCount(pageResult.getTotal());
        List<TransferFundAccumulation> transferFundAccumulations = transferFundAccumulationConverter.convert2TransferFundAccumulationList(pageResult.getRecords());
        pageInfoDTO.setRecords(transferFundAccumulations);
        return pageInfoDTO;
    }

    @Override
    public TransferFundAccumulation findByUserId(String userId) {
        TransferFundAccumulationDO transferFundAccumulationDO = transferFundAccumulationDAO.selectByUserId(userId);
        return transferFundAccumulationDO != null ? transferFundAccumulationConverter.doToDomain(transferFundAccumulationDO) : null;
    }
    
    @Override
    public TransferFundAccumulation findByUserIdForUpdate(String userId) {
        TransferFundAccumulationDO transferFundAccumulationDO = transferFundAccumulationDAO.selectByUserIdForUpdate(userId);
        return transferFundAccumulationDO != null ? transferFundAccumulationConverter.doToDomain(transferFundAccumulationDO) : null;
    }
    
    @Override
    public TransferFundAccumulation findByFundAccumulationId(String fundAccumulationId) {
        TransferFundAccumulationDO transferFundAccumulationDO = transferFundAccumulationDAO.selectByFundAccumulationId(fundAccumulationId);
        return transferFundAccumulationDO != null ? transferFundAccumulationConverter.doToDomain(transferFundAccumulationDO) : null;
    }
    
    @Override
    public boolean save(TransferFundAccumulation transferFundAccumulation) {
        TransferFundAccumulationDO transferFundAccumulationDO = transferFundAccumulationConverter.domainToDO(transferFundAccumulation);
        return transferFundAccumulationDAO.saveTransferFundAccumulation(transferFundAccumulationDO);
    }
    
    @Override
    public boolean update(TransferFundAccumulation transferFundAccumulation) {
        TransferFundAccumulationDO transferFundAccumulationDO = transferFundAccumulationConverter.domainToDO(transferFundAccumulation);
        return transferFundAccumulationDAO.updateTransferFundAccumulation(transferFundAccumulationDO);
    }
    
    @Override
    public boolean accumulateFundOut(String userId, BigDecimal amount, Integer count) {
        return transferFundAccumulationDAO.accumulateFundOut(userId, amount, count);
    }
    
    @Override
    public boolean accumulateFundIn(String userId, BigDecimal amount, Integer count) {
        return transferFundAccumulationDAO.accumulateFundIn(userId, amount, count);
    }
}
