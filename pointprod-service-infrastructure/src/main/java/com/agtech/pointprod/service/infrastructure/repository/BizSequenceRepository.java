package com.agtech.pointprod.service.infrastructure.repository;

//import com.agtech.applet.service.facade.dto.BizSequenceDTO;

import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;

/**
 * <AUTHOR>
 * @version DemoRepository.java, v0.1 2024/6/24 20:40 zhongqigang Exp $
 */
public interface BizSequenceRepository {
//    BizSequenceDTO queryByTableName(String tableName);

    long nextValue(String tableName);

    String getBizId(String userId, SequenceCodeEnum sequenceCodeEnum);


}
