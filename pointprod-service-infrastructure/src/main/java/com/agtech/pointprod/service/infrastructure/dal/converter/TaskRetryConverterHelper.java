package com.agtech.pointprod.service.infrastructure.dal.converter;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import com.agtech.pointprod.service.domain.model.TaskConfig;
import com.agtech.pointprod.service.domain.model.TaskConfigFactory;
import com.agtech.pointprod.service.domain.model.TaskData;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * TaskRetryConverter辅助类
 * 用于处理TaskConfigFactory的依赖注入
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class TaskRetryConverterHelper {
    
    @Resource
    private TaskConfigFactory taskConfigFactory;

    @Resource
    private ObjectMapper objectMapper;
    
    /**
     * 将TaskConfig对象转换为JSON字符串
     */
    public String taskConfigToJson(TaskConfig taskConfig) {
        if (taskConfig == null) {
            return null;
        }
        try {
            return taskConfigFactory.toJson(taskConfig);
        } catch (Exception e) {
            // 记录日志或处理异常
            return null;
        }
    }
    
    /**
     * 将JSON字符串转换为TaskConfig对象
     */
    public <T extends TaskConfig> T jsonToTaskConfig(String json, Class<T> taskConfigClass) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return taskConfigFactory.fromJson(json, taskConfigClass);
        } catch (Exception e) {
            // 记录日志或处理异常
            return null;
        }
    }
    
    /**
     * 将TaskData对象转换为JSON字符串
     */
    public String taskDataToJson(TaskData taskData) {
        if (taskData == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(taskData);
        } catch (Exception e) {
            // 记录日志或处理异常
            return null;
        }
    }
    
    /**
     * 将JSON字符串转换为TaskData对象
     */
    public <T extends TaskData> T jsonToTaskData(String json, Class<T> taskDataClass) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, taskDataClass);
        } catch (Exception e) {
            // 记录日志或处理异常
            log.error("Failed to deserialize JSON to TaskData: {}", json, e);
            return null;
        }
    }

}