package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import com.agtech.pointprod.service.domain.common.enums.ContractStatusEnum;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.ContractDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.ContractDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.ContractMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ContractDAOImpl extends ServiceImpl<ContractMapper, ContractDO> implements ContractDAO {
    @Override
    public ContractDO queryLatestContractByBizType(String bizType) {
        LambdaQueryWrapper<ContractDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ContractDO::getContractType, bizType);
        lambdaQueryWrapper.eq(ContractDO::getStatus, ContractStatusEnum.VALID.getValue());
        lambdaQueryWrapper.eq(ContractDO::getIsDeleted, 0);
        lambdaQueryWrapper.orderByDesc(ContractDO::getVersion);
        lambdaQueryWrapper.last(" limit 1");
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public ContractDO queryContractByContractId(String contractId) {
        LambdaQueryWrapper<ContractDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ContractDO::getContractId, contractId);
        lambdaQueryWrapper.eq(ContractDO::getIsDeleted, 0);
        return this.getOne(lambdaQueryWrapper);
    }
    
    @Override
    public boolean saveContract(ContractDO contractDO) {
        try {
            // Set default status if not provided
            if (contractDO.getStatus() == null) {
                contractDO.setStatus(ContractStatusEnum.VALID.getValue());
            }
            
            return this.save(contractDO);
        } catch (Exception e) {
            log.error("Failed to save contract: {}", contractDO.getContractId(), e);
            return false;
        }
    }
}
