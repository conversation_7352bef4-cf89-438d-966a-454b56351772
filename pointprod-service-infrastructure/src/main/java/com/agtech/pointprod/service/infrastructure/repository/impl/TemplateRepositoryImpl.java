package com.agtech.pointprod.service.infrastructure.repository.impl;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.domain.model.Template;
import com.agtech.pointprod.service.facade.dto.req.template.QueryTemplateInfoListReq;
import com.agtech.pointprod.service.infrastructure.dal.converter.TemplateConvertor;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.TemplateDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TemplateDO;
import com.agtech.pointprod.service.infrastructure.repository.TemplateRepository;
import com.agtech.pointprod.service.infrastructure.dto.response.TemplateValidCountQueryRsp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Repository
public class TemplateRepositoryImpl implements TemplateRepository {

    @Resource
    private TemplateDAO templateDAO;

    @Resource
    private TemplateConvertor templateConvertor;

    @Override
    public PageInfoDTO<Template> queryValidTemplateListByPage(String bizType, Integer pageNo, Integer pageSize) {
        IPage<TemplateDO> pageResult = templateDAO.selectValidTemplateByBizType(bizType,pageNo,  pageSize);
        PageInfoDTO<Template> pageInfoDTO = new PageInfoDTO<>();
        pageInfoDTO.setPageSize(pageResult.getSize());
        pageInfoDTO.setCurrentPage(pageResult.getCurrent());
        pageInfoDTO.setTotalPage(pageResult.getPages());
        pageInfoDTO.setTotalCount(pageResult.getTotal());
        List<Template> templates = templateConvertor.convertFromDoToDomainModels(pageResult.getRecords());
        pageInfoDTO.setRecords(templates);
        return pageInfoDTO;
    }

    @Override
    public PageInfoDTO<Template> queryTemplateListByPage(QueryTemplateInfoListReq templateListQueryRequest) {
        IPage<TemplateDO> pageResult = templateDAO.queryTemplateListByPage(templateListQueryRequest);
        PageInfoDTO<Template> pageInfoDTO = new PageInfoDTO<>();
        pageInfoDTO.setPageSize(pageResult.getSize());
        pageInfoDTO.setCurrentPage(pageResult.getCurrent());
        pageInfoDTO.setTotalPage(pageResult.getPages());
        pageInfoDTO.setTotalCount(pageResult.getTotal());
        List<Template> templates = templateConvertor.convertFromDoToDomainModels(pageResult.getRecords());
        pageInfoDTO.setRecords(templates);
        return pageInfoDTO;
    }

    @Override
    public boolean createTemplate(Template template) {
        return templateDAO.createTemplate(templateConvertor.domainToDo(template));
    }

    @Override
    public boolean updateTemplate(Template template) {
        return templateDAO.updateTemplate(templateConvertor.domainToDo(template));
    }

    @Override
    public boolean deleteTemplate(String templateId, String operator) {
        return templateDAO.deleteTemplate(templateId, operator);
    }

    @Override
    public TemplateValidCountQueryRsp selectValidCount(String bizType, String templateId, Date time) {
        return templateDAO.selectValidCount(bizType, templateId, time);
    }

}
