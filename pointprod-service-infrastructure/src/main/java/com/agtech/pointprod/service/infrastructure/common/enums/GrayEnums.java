package com.agtech.pointprod.service.infrastructure.common.enums;

/**
 * 数字常量枚举
 * 
 */
public enum GrayEnums {
    
    /**
     * 灰度开关 - 关闭
     */
    GRAY_SWITCH_OFF(0, "0"),
    
    /**
     * 灰度开关 - 灰度中
     */
    GRAY_SWITCH_ON(1, "1"),
    
    /**
     * 灰度开关 - 全量开放
     */
    GRAY_SWITCH_FULL(2, "2"),
    
    /**
     * Hash取余基数
     */
    HASH_MOD_BASE(100, "100"),
    
    /**
     * 比例最小值
     */
    RATE_MIN(0, "0"),
    
    /**
     * 比例最大值
     */
    RATE_MAX(100, "100"),
    
    /**
     * 默认灰度比例
     */
    DEFAULT_RATE(0, "0");
    
    private final int value;
    private final String stringValue;
    
    GrayEnums(int value, String stringValue) {
        this.value = value;
        this.stringValue = stringValue;
    }
    
    public int getValue() {
        return value;
    }
    
    public String getStringValue() {
        return stringValue;
    }
} 