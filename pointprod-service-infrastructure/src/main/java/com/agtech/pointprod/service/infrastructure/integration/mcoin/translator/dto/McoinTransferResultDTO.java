package com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
public class McoinTransferResultDTO {

    private String outTradeNo;

    private String payerOrderId;

    private String payeeOrderId;

    private Date transferTime;

    private String orderStatus;

    private String integral;

}
