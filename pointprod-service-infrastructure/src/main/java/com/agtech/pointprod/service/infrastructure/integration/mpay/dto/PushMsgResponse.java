package com.agtech.pointprod.service.infrastructure.integration.mpay.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 推送消息响应DTO
 */
@Getter
@Setter
@ToString
public class PushMsgResponse {
    
    /**
     * 返回码
     * "00"代表调用成功
     * "21"代表app_token失效，需要重新获取
     */
    @JsonProperty("errcode")
    private String errcode;
    
    /**
     * 接口调用结果信息
     */
    @JsonProperty("errmsg")
    private String errmsg;
} 