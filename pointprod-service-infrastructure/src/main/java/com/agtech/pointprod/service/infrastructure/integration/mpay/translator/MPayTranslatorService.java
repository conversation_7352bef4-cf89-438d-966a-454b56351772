package com.agtech.pointprod.service.infrastructure.integration.mpay.translator;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.CheckSecurityIdDTO;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.MPayUserInfoListRequest;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.MPayUserInfoRequest;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.UserMsgDTO;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.PushMsgRequest;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.PushMsgResponse;

import java.util.List;

public interface MPayTranslatorService {
	GenericResult<UserMsgDTO> getUserInfo(MPayUserInfoRequest mPayUserInfoRequest);

	GenericResult<CheckSecurityIdDTO> checkSecurityId(String custId, String orderId, String securityId);

	/**
	 * 推送消息给用户
	 * 
	 * @param request 推送请求
	 * @return 推送响应
	 */
	GenericResult<PushMsgResponse> pushMessage(PushMsgRequest request);


	GenericResult<List<UserMsgDTO>> getUserInfoList(MPayUserInfoListRequest mPayUserInfoListRequest);
}
