package com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@Accessors(chain = true)
public class McoinTransferRequestBody {

    private Integer integral;
    private String outTradeNo;
    private String description;
    private String tradeTime;
    private TransUserInfo payerUserInfo;
    private TransUserInfo payeeUserInfo;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class TransUserInfo{
        private @NotNull String custId;
        private String nickName;
        private String phone;
        private String areaCode;
        private String headLogo;
    }
}
