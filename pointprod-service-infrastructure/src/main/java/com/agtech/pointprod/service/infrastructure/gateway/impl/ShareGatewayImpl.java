package com.agtech.pointprod.service.infrastructure.gateway.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.agtech.pointprod.service.domain.gateway.ShareGateway;
import com.agtech.pointprod.service.domain.model.share.ShareRecord;
import com.agtech.pointprod.service.domain.service.ShareDomainService;
import com.agtech.pointprod.service.infrastructure.repository.ShareRecordRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 分享领域网关实现
 */
@Component
@Slf4j
public class ShareGatewayImpl implements ShareGateway {
    
    @Resource
    private ShareRecordRepository shareRecordRepository;
    
    @Resource
    private ShareDomainService shareDomainService;
    
    @Override
    public ShareRecord findByUniqueBusinessKey(String contentId, String contentType, String recordType, String channelType, String userId) {
        return shareRecordRepository.findByUniqueBusinessKey(contentId, contentType, recordType, channelType, userId);
    }
    
    @Override
    public ShareRecord findByUniqueBusinessKeyForUpdate(String contentId, String contentType, String recordType, String channelType, String userId) {
        return shareRecordRepository.findByUniqueBusinessKeyForUpdate(contentId, contentType, recordType, channelType, userId);
    }
    
    @Override
    public void saveShareRecord(ShareRecord shareRecord) {
        shareRecordRepository.saveShareRecord(shareRecord);
    }
    
    @Override
    public void updateShareRecord(ShareRecord shareRecord) {
        shareRecordRepository.updateShareRecord(shareRecord);
    }
} 