package com.agtech.pointprod.service.infrastructure.integration.mpay.translator;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.PushMsgRequest;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.PushMsgResponse;

@FeignClient(name = "mPayPushClient", url = "${mPay.push.url:}")
public interface MPayPushClient {

    /**
     * 商户向用户推送消息接口
     * 
     * @param request 推送消息请求参数
     * @return 推送消息响应
     */
    @PostMapping(value = "/data/pushMsg/push")
    PushMsgResponse pushMessage(@RequestBody PushMsgRequest request);
}
