package com.agtech.pointprod.service.infrastructure.gateway.impl;

import com.agtech.pointprod.service.domain.gateway.BlackListGateway;
import com.agtech.pointprod.service.domain.model.BlackList;
import com.agtech.pointprod.service.infrastructure.repository.BlackListRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 20:31
 */
@Component
public class BlackListGatewayImpl implements BlackListGateway {
    @Resource
    private BlackListRepository blackListRepository;

    @Override
    public BlackList queryTransferBlackListByUserId(String userId) {
        return blackListRepository.queryTransferBlackListByUserId(userId);
    }

    @Override
    public long countBlackList(String userId) {
        return blackListRepository.countBlackList(userId);
    }


    @Override
    public BlackList selectBlackList(String entityType, String accountType, String entityId) {
        return blackListRepository.selectBlackList(entityType, accountType, entityId);
    }

    @Override
    public List<BlackList> selectBlackLists(String entityType, String accountType, List<String> entityIds) {
        return blackListRepository.selectBlackLists(entityType, accountType, entityIds);
    }

    @Override
    public void save(BlackList blackList) {
        blackListRepository.save(blackList);
    }

    @Override
    public void updateByEntity(BlackList blackList, String entityType, String accountType, String entityId) {
        blackListRepository.updateByEntity(blackList, entityType, accountType, entityId);
    }

    @Override
    public List<String> selectAllEntityIdsInBlackList(String entityType, String accountType, List<String> entityIds) {
        return blackListRepository.selectAllEntityIdsInBlackList(entityType, accountType, entityIds);
    }
}
