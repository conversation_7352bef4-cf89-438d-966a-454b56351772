package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferRelationDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * 转赠用户关系DAO接口
 */
public interface TransferRelationDAO extends IService<TransferRelationDO> {
    
    /**
     * 根据付款方用户ID查询转赠关系列表
     * 
     * @param actorUserId 付款方用户ID
     * @return 转赠关系列表
     */
    List<TransferRelationDO> selectByActorUserId(String actorUserId);
    
    /**
     * 根据收款方用户ID查询转赠关系列表
     * 
     * @param participantUserId 收款方用户ID
     * @return 转赠关系列表
     */
    List<TransferRelationDO> selectByParticipantUserId(String participantUserId);
    
    /**
     * 根据付款方和收款方用户ID查询转赠关系
     * 
     * @param actorUserId 付款方用户ID
     * @param participantUserId 收款方用户ID
     * @return 转赠关系，如果不存在则返回null
     */
    TransferRelationDO selectByActorAndParticipant(String actorUserId, String participantUserId);
    
    /**
     * 根据业务ID查询转赠关系
     * 
     * @param transferRelationId 业务ID
     * @return 转赠关系，如果不存在则返回null
     */
    TransferRelationDO selectByTransferRelationId(String transferRelationId);

    /**
     * 根据用户ID查询所有相关的转赠关系（作为付款方或收款方）
     *
     * @param userId 用户ID
     * @return 转赠关系列表
     */
    List<TransferRelationDO> selectByUserId(String userId);

    List<TransferRelationDO> queryLatestTransferRalations(String userId, Date startTime);
}