<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agtech.pointprod.service.infrastructure.dal.mapper.TemplateMapper">

    <select id="selectValidCount" resultType="com.agtech.pointprod.service.infrastructure.dto.response.TemplateValidCountQueryRsp">
        SELECT COUNT(*) AS valid_count,
        <if test='templateId != null and templateId != ""'>
            IFNULL(SUM(template_id = #{templateId}),0) AS spec_temp_valid_count
        </if>
        <if test='templateId == null or templateId == ""'>
            0 AS spec_temp_valid_count
        </if>
        FROM `template`
        WHERE `status` = 'able' AND #{time} &lt;= end_time AND is_deleted = 0 AND biz_type = #{bizType}
    </select>

</mapper>
