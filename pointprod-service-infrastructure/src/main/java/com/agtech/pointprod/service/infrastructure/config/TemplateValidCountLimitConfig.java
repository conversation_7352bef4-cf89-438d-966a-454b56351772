package com.agtech.pointprod.service.infrastructure.config;

import com.agtech.pointprod.service.domain.common.enums.TemplateBizTypeEnum;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/24 19:28
 */
@Configuration
@ConfigurationProperties(prefix = "template.limit")
@RefreshScope
@Getter
public class TemplateValidCountLimitConfig {

    private Map<String, Integer> count = null;

    public TemplateValidCountLimitConfig() {
        count = new HashMap<>();
        count.put(TemplateBizTypeEnum.MCOIN_TRANSFER.getCode(), 20);
    }

    public void setCount(Map<String, Integer> count) {
        this.count.putAll(count);
    }

    public Integer getTemplateValidCountLimit(String bizType){
        return count.getOrDefault(bizType, -1);
    }
}
