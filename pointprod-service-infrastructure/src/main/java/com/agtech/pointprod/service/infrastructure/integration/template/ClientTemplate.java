package com.agtech.pointprod.service.infrastructure.integration.template;

import com.agtech.common.result.BaseResult;

/**
 * rpc调用下游服务模版
 *
 * <AUTHOR>
 * @version $Id: ClientTemplate.java, v 0.1 2024年8月6日 20:53:27 zhongqiang Exp $
 */
public class ClientTemplate {

	/**
	 * execute
	 *
	 * @param <T>
	 * @param callback
	 * @return
	 */
	public static <T extends BaseResult> T execute(ClientCallback<T> callback) {
		long begin = System.currentTimeMillis();
		T result = null;
		try {
			result = callback.execute();
		}
//		catch (AppletBizException ex) {
//			log.error("call rpc service process, occur biz exception:{}", ex.getMessage(), ex);
//			ResultFillUtil.mappingAndfillFailureResult(SystemCodeEnum.APPLET_SERVICE.getCode(), ex.getResultCode(), ex.getMessage(), result);
//		} catch(BizCommonException ex) {
//			log.error("call rpc service process, occur biz CommonException:{}", ex.getMessage(), ex);
//			ResultFillUtil.mappingAndfillFailureResult(SystemCodeEnum.APPLET_SERVICE.getCode(), ex.getResultCode(), ex.getMessage(), result);
//		}catch (Throwable throwable) {
//			log.error("call rpc service process, occur unknown exception:{}", throwable.getMessage(), throwable);
//			ResultFillUtil.fillFailureResult(SystemCodeEnum.APPLET_SERVICE.getCode(), AqcProdResultCode.SYSTEM_ERROR,
//					AqcProdResultCode.SYSTEM_ERROR.getResultMsg(), result);
//		} 
		finally {
			long timeCost = System.currentTimeMillis() - begin;
			callback.composeDigestLog(timeCost, result);
		}
		return result;
	}

}
