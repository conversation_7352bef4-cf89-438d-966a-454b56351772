package com.agtech.pointprod.service.infrastructure.common.enums;

/**
 * 用户等级枚举
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public enum UserLevelEnum {
    
    /**
     * 一级账户
     */
    LEVEL_1("1", "一級账户"),
    
    /**
     * 二级账户
     */
    LEVEL_2("2", "二級账户"),
    
    /**
     * 3A级账户
     */
    LEVEL_3A("3A", "3A級账户"),
    
    /**
     * 3B级账户
     */
    LEVEL_3B("3B", "3B級账户"),
    
    /**
     * JR级账户
     */
    LEVEL_JR("JR", "JR級账户");
    
    private final String levelKey;
    private final String levelValue;
    
    UserLevelEnum(String levelKey, String levelValue) {
        this.levelKey = levelKey;
        this.levelValue = levelValue;
    }
    
    public String getLevelKey() {
        return levelKey;
    }
    
    public String getLevelValue() {
        return levelValue;
    }
    
    /**
     * 根据levelKey获取枚举
     * @param levelKey 等级键
     * @return 对应的枚举值
     */
    public static UserLevelEnum getByLevelKey(String levelKey) {
        for (UserLevelEnum level : values()) {
            if (level.getLevelKey().equals(levelKey)) {
                return level;
            }
        }
        return null;
    }
    
    /**
     * 根据levelValue获取枚举
     * @param levelValue 等级值
     * @return 对应的枚举值
     */
    public static UserLevelEnum getByLevelValue(String levelValue) {
        for (UserLevelEnum level : values()) {
            if (level.getLevelValue().equals(levelValue)) {
                return level;
            }
        }
        return null;
    }
    
    /**
     * 根据levelKey获取levelValue
     * @param levelKey 等级键
     * @return 对应的等级值
     */
    public static String getLevelValueByKey(String levelKey) {
        UserLevelEnum level = getByLevelKey(levelKey);
        return level != null ? level.getLevelValue() : null;
    }
    
    /**
     * 根据levelValue获取levelKey
     * @param levelValue 等级值
     * @return 对应的等级键
     */
    public static String getLevelKeyByValue(String levelValue) {
        UserLevelEnum level = getByLevelValue(levelValue);
        return level != null ? level.getLevelKey() : null;
    }
    
    /**
     * 验证levelValue是否有效
     * @param levelValue 等级值
     * @return 是否有效
     */
    public static boolean isValidLevelValue(String levelValue) {
        return getByLevelValue(levelValue) != null;
    }
    
    /**
     * 验证levelKey是否有效
     * @param levelKey 等级键
     * @return 是否有效
     */
    public static boolean isValidLevelKey(String levelKey) {
        return getByLevelKey(levelKey) != null;
    }
    
    /**
     * 获取所有有效的用户等级值列表
     * @return 用户等级值列表
     */
    public static String[] getAllLevelValues() {
        UserLevelEnum[] values = values();
        String[] levelValues = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            levelValues[i] = values[i].getLevelValue();
        }
        return levelValues;
    }
    
    /**
     * 获取所有有效的用户等级键列表
     * @return 用户等级键列表
     */
    public static String[] getAllLevelKeys() {
        UserLevelEnum[] values = values();
        String[] levelKeys = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            levelKeys[i] = values[i].getLevelKey();
        }
        return levelKeys;
    }
    
    /**
     * 获取格式化的有效等级值字符串（用于错误提示）
     * @return 格式化字符串
     */
    public static String getValidLevelKeyssString() {
        return String.join("、", getAllLevelKeys());
    }
} 
