package com.agtech.pointprod.service.infrastructure.integration.risk.acl.impl;

import com.agtech.pointprod.service.infrastructure.integration.risk.acl.RiskAclService;
import com.agtech.pointprod.service.infrastructure.integration.risk.adapter.RiskAdapterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version RiskAclServiceImpl.java, v0.1 2025/7/3 17:33 zhongqigang Exp $
 */
@Slf4j
@Service
public class RiskAclServiceImpl implements RiskAclService {
    @Resource
    private RiskAdapterService riskAdapterService;

    @Override
    public String validateText(String text) {
        return riskAdapterService.validateText(text);
    }
}
