package com.agtech.pointprod.service.infrastructure.repository;

import com.agtech.pointprod.service.domain.model.ContractConfirm;
import com.agtech.pointprod.service.infrastructure.dto.response.UserConfirmContractRsp;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/12 10:46
 */
public interface ContractConfirmRepository {
    ContractConfirm queryContractConfirmByUserIdAndContractId(String userId, String contractId);

//    ContractConfirm queryContractConfirmByUserIdAndContractIdForLock(String userId, String contractId);

    boolean updateContractConfirmForUser(String userId, String contractId, String status);

    boolean saveContractConfirm(ContractConfirm contractConfirm);

    List<UserConfirmContractRsp> queryMultiUserConfirmContract(String contractType, String status, List<String> userIds);
}
