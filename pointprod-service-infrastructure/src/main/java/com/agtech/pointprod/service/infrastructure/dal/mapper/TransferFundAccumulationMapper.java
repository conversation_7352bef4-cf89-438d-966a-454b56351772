package com.agtech.pointprod.service.infrastructure.dal.mapper;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferFundAccumulationDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 16:58
 */
public interface TransferFundAccumulationMapper extends BaseMapper<TransferFundAccumulationDO> {

    IPage<TransferFundAccumulationDO> listByConditionAndPage(IPage<TransferFundAccumulationDO> page, @Param("includeUserIds") List<String> includeUserIds, @Param("excludeUserIds") List<String> excludeUserIds);

}
