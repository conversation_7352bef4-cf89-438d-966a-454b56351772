package com.agtech.pointprod.service.infrastructure.config;

import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;

import static com.agtech.pointprod.service.infrastructure.common.constant.MDCConstants.MDC_KEY_ELAPSED_TIME;

public class MdcTaskDecorator implements TaskDecorator {
    @Override
    public @NotNull Runnable decorate(@NotNull Runnable runnable) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        long currentTimeMillis = System.currentTimeMillis();
        return () -> {
            try {
                if (ObjectUtils.isNotEmpty(contextMap)) {
                    MDC.setContextMap(contextMap);
                }
                runnable.run();
                MDC.put(MDC_KEY_ELAPSED_TIME, String.valueOf(System.currentTimeMillis() - currentTimeMillis));
            } finally {
                MDC.clear();
            }
        };
    }
}
