package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.domain.common.enums.DeletedEnum;
import com.agtech.pointprod.service.facade.dto.enums.TaskRetryStatusEnum;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.TaskRetryDao;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TaskRetryDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.TaskRetryMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

/**
 * <AUTHOR>
 * @version TaskRetryDao.java, v0.1 2025/6/18 11:21 zhongqigang Exp $
 */
@Component
public class TaskRetryDaoImpl implements TaskRetryDao {
    @Resource
    private TaskRetryMapper taskRetryMapper;

    @Override
    public boolean createTaskRetry(TaskRetryDO taskRetryDO) {
        return taskRetryMapper.insert(taskRetryDO) > 0;
    }

    @Override
    public boolean finishTaskRetry(String taskRetryId) {
        Wrapper<TaskRetryDO> wrapper = Wrappers.lambdaUpdate(TaskRetryDO.class)
                .eq(TaskRetryDO::getTaskRetryId, taskRetryId)
                .eq(TaskRetryDO::getStatus, TaskRetryStatusEnum.INIT.getValue())
                .eq(TaskRetryDO::getIsDeleted, DeletedEnum.NO.getCode())
                .set(TaskRetryDO::getStatus, TaskRetryStatusEnum.FINISH.getValue())
                .set(TaskRetryDO::getGmtModified, ZonedDateUtil.now());
        int i = taskRetryMapper.update(null, wrapper);
        return i == 1;
    }


    @Override
    public TaskRetryDO getTaskRetryByIdForUpdate(String taskRetryId) {
        LambdaQueryWrapper<TaskRetryDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TaskRetryDO::getTaskRetryId, taskRetryId);
        lambdaQueryWrapper.eq(TaskRetryDO::getIsDeleted, DeletedEnum.NO.getCode());
        lambdaQueryWrapper.last("for update");
        return taskRetryMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public TaskRetryDO getTaskRetryById(String taskRetryId) {
        LambdaQueryWrapper<TaskRetryDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TaskRetryDO::getTaskRetryId, taskRetryId);
        lambdaQueryWrapper.eq(TaskRetryDO::getIsDeleted, DeletedEnum.NO.getCode());
        return taskRetryMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public TaskRetryDO getTaskRetryByResourceIdForUpdate(String resourceId, String resourceType) {
        LambdaQueryWrapper<TaskRetryDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TaskRetryDO::getResourceId, resourceId);
        lambdaQueryWrapper.eq(TaskRetryDO::getResourceType, resourceType);
        lambdaQueryWrapper.eq(TaskRetryDO::getIsDeleted, DeletedEnum.NO.getCode());
        lambdaQueryWrapper.orderByAsc(TaskRetryDO::getId);
        lambdaQueryWrapper.last("limit 1 for update");
        return taskRetryMapper.selectOne(lambdaQueryWrapper);
    }



    @Override
    public List<TaskRetryDO> listFailedTasksForRetry(int limit, Date startTime) {
        Date now = ZonedDateUtil.now();
        LambdaQueryWrapper<TaskRetryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskRetryDO::getStatus, TaskRetryStatusEnum.INIT.getValue())
                .eq(TaskRetryDO::getIsDeleted, DeletedEnum.NO.getCode())
                .le(TaskRetryDO::getNextRetry, now)
                .gt(TaskRetryDO::getNextRetry, startTime) // 添加开始时间过滤
                .apply("try_count < max_try_count")
                .orderByAsc(TaskRetryDO::getNextRetry)
                .last("LIMIT " + limit);
        return taskRetryMapper.selectList(queryWrapper);
    }

    @Override
    public boolean incrTaskRetryCount(TaskRetryDO taskRetryDO) {
        LambdaUpdateWrapper<TaskRetryDO> wrapper = Wrappers.lambdaUpdate(TaskRetryDO.class)
                .eq(TaskRetryDO::getTaskRetryId, taskRetryDO.getTaskRetryId())
                .eq(TaskRetryDO::getIsDeleted, DeletedEnum.NO.getCode())
                .setSql("try_count = try_count + 1")
                .set(TaskRetryDO::getGmtModified, ZonedDateUtil.now());


        if (taskRetryDO.getNextRetry() != null){
            wrapper.set(TaskRetryDO::getNextRetry, taskRetryDO.getNextRetry());
        }

        int i = taskRetryMapper.update(null, wrapper);
        return i == 1;
    }

    @Override
    public boolean updateTaskRetryStatus(String taskRetryId, String fromStatus, String toStatus) {
        LambdaUpdateWrapper<TaskRetryDO> wrapper = Wrappers.lambdaUpdate(TaskRetryDO.class)
                .eq(TaskRetryDO::getTaskRetryId, taskRetryId)
                .eq(TaskRetryDO::getStatus, fromStatus)
                .eq(TaskRetryDO::getIsDeleted, DeletedEnum.NO.getCode())
                .set(TaskRetryDO::getStatus, toStatus)
                .set(TaskRetryDO::getGmtModified, ZonedDateUtil.now());

        int i = taskRetryMapper.update(null, wrapper);
        return i == 1;
    }
}
