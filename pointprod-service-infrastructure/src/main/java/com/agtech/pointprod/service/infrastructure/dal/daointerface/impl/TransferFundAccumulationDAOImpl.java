package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import com.agtech.pointprod.service.infrastructure.dal.daointerface.TransferFundAccumulationDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferFundAccumulationDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.TransferFundAccumulationMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 17:04
 */
@Component
public class TransferFundAccumulationDAOImpl extends ServiceImpl<TransferFundAccumulationMapper, TransferFundAccumulationDO> implements TransferFundAccumulationDAO {
    @Resource
    private TransferFundAccumulationMapper transferFundAccumulationMapper;

    @Override
    public IPage<TransferFundAccumulationDO> listByPage(List<String> includeUserIds, List<String> excludeUserIds, Integer pageSize, Integer pageNo) {
        IPage<TransferFundAccumulationDO> page = new Page<>(pageNo, pageSize);
        return transferFundAccumulationMapper.listByConditionAndPage(page, includeUserIds, excludeUserIds);
    }

    @Override
    public TransferFundAccumulationDO selectByUserId(String userId) {
        LambdaQueryWrapper<TransferFundAccumulationDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TransferFundAccumulationDO::getUserId, userId)
               .eq(TransferFundAccumulationDO::getIsDeleted, 0);
        return this.getOne(wrapper);
    }
    
    @Override
    public TransferFundAccumulationDO selectByUserIdForUpdate(String userId) {
        LambdaQueryWrapper<TransferFundAccumulationDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TransferFundAccumulationDO::getUserId, userId)
               .eq(TransferFundAccumulationDO::getIsDeleted, 0)
               .last("for update");
        return this.getOne(wrapper);
    }
    
    @Override
    public TransferFundAccumulationDO selectByFundAccumulationId(String fundAccumulationId) {
        LambdaQueryWrapper<TransferFundAccumulationDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TransferFundAccumulationDO::getFundAccumulationId, fundAccumulationId)
               .eq(TransferFundAccumulationDO::getIsDeleted, 0);
        return this.getOne(wrapper);
    }
    
    @Override
    public boolean saveTransferFundAccumulation(TransferFundAccumulationDO transferFundAccumulationDO) {
        return this.save(transferFundAccumulationDO);
    }
    
    @Override
    public boolean updateTransferFundAccumulation(TransferFundAccumulationDO transferFundAccumulationDO) {
        LambdaUpdateWrapper<TransferFundAccumulationDO> wrapper = Wrappers.lambdaUpdate();
        return this.update(transferFundAccumulationDO, wrapper);
    }
    
    @Override
    public boolean accumulateFundOut(String userId, BigDecimal amount, Integer count) {
        LambdaUpdateWrapper<TransferFundAccumulationDO> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(TransferFundAccumulationDO::getUserId, userId)
               .eq(TransferFundAccumulationDO::getIsDeleted, 0)
               .setSql("fund_out_amount = fund_out_amount + " + amount)
               .setSql("fund_out_count = fund_out_count + " + count);
        return this.update(wrapper);
    }
    
    @Override
    public boolean accumulateFundIn(String userId, BigDecimal amount, Integer count) {
        LambdaUpdateWrapper<TransferFundAccumulationDO> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(TransferFundAccumulationDO::getUserId, userId)
               .eq(TransferFundAccumulationDO::getIsDeleted, 0)
               .setSql("fund_in_amount = fund_in_amount + " + amount)
               .setSql("fund_in_count = fund_in_count + " + count);
        return this.update(wrapper);
    }
}
