package com.agtech.pointprod.service.infrastructure.repository;

import com.agtech.pointprod.service.domain.model.Contract;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/12 10:46
 */
public interface ContractRepository {
    Contract queryLatestContractByBizType(String bizType);

    Contract queryContractByContractId(String contractId);
    
    /**
     * Save contract
     * 
     * @param contract Contract to save
     * @return true if successful, false otherwise
     */
    boolean saveContract(Contract contract);
}
