package com.agtech.pointprod.service.infrastructure.common.constant;

/**
 * 通知相关常量类
 */
public class NotificationConstants {
    
    /**
     * 查询限制常量
     */
    public static final class QueryLimits {
        /** 最终返回结果的限制数量 */
        public static final int FINAL_RESULT_LIMIT = 30;
        
        /** DAO层查询的限制数量（供Gateway层过滤使用） */
        public static final int DAO_QUERY_LIMIT = 100;
        
        private QueryLimits() {}
    }
    
    /**
     * 通知状态常量
     */
    public static final class Status {
        /** 未读状态 */
        public static final String UNREAD = "UNREAD";
        
        /** 已读状态 */
        public static final String READ = "READ";
        
        private Status() {}
    }
    
    /**
     * 通知类型常量
     */
    public static final class Type {
        /** 接受类型 */
        public static final String ACCEPT = "ACCEPT";
        
        /** 拒绝类型 */
        public static final String REJECT = "REJECT";
        
        private Type() {}
    }
    
    /**
     * Push状态常量
     */
    public static final class PushStatus {
        /** 未发送 */
        public static final String INIT = "INIT";
        
        /** 已发送 */
        public static final String SUCCESS = "SUCCESS";
        
        /** 发送失败 */
        public static final String FAILED = "FAILED";
        
        private PushStatus() {}
    }
    
    /**
     * 数据库相关常量
     */
    public static final class Database {
        /** 未删除标识 */
        public static final int NOT_DELETED = 0;
        
        /** 已删除标识 */
        public static final int DELETED = 1;
        
        private Database() {}
    }
    
    /**
     * SQL相关常量
     */
    public static final class Sql {
        /** 限制查询数量的SQL片段 */
        public static final String LIMIT_100 = "LIMIT 100";
        
        /** LIMIT关键字 */
        public static final String LIMIT = "LIMIT ";
        
        private Sql() {}
    }
    
    private NotificationConstants() {}
} 