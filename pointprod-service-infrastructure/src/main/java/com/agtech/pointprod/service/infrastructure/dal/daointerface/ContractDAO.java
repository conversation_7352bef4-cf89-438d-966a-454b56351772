package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.ContractDO;

/**
 * <AUTHOR>
 */
public interface ContractDAO {
    ContractDO queryLatestContractByBizType(String bizType);

    ContractDO queryContractByContractId(String contractId);
    
    /**
     * Save contract
     * 
     * @param contractDO Contract DO to save
     * @return true if successful, false otherwise
     */
    boolean saveContract(ContractDO contractDO);
}
