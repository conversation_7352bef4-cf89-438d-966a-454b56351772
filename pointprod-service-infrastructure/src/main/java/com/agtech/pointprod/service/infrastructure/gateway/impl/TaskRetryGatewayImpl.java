package com.agtech.pointprod.service.infrastructure.gateway.impl;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.agtech.common.lang.util.StringUtil;
import com.agtech.pointprod.service.domain.model.TaskConfig;
import com.agtech.pointprod.service.domain.model.TaskData;
import com.agtech.pointprod.service.domain.model.TaskRetry;

import com.agtech.pointprod.service.domain.gateway.TaskRetryGateway;
import com.agtech.pointprod.service.facade.dto.enums.TaskRetryStatusEnum;
import com.agtech.pointprod.service.infrastructure.repository.TaskRetryRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 20:31
 */
@Component
@Slf4j
public class TaskRetryGatewayImpl implements TaskRetryGateway {
    @Resource
    private TaskRetryRepository taskRetryRepository;

    @Override
    public boolean createTaskRetry(TaskRetry taskRetry) {
        if(taskRetry==null || StringUtil.isBlank(taskRetry.getTaskRetryId())){
            log.error("createTaskRetry fail, taskRetry is null");
            return false;
        }
        return taskRetryRepository.createTaskRetry(taskRetry);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean finishTaskRetry(String taskRetryId) {
        if(StringUtil.isBlank(taskRetryId)){
            log.error("finishTaskRetry fail, taskRetryId is null");
            return false;
        }
        return taskRetryRepository.finishTaskRetry(taskRetryId);
    }


    @Override
    public <T extends TaskConfig, D extends TaskData> TaskRetry
    getTaskRetryByIdForUpdate(String taskRetryId, Class<T> taskConfigClass, Class<D> taskDataClass) {
        if(StringUtil.isBlank(taskRetryId)){
            log.error("getTaskRetryById fail, taskRetryId is null");
            return null;
        }
        return taskRetryRepository.getTaskRetryByIdForUpdate(taskRetryId, taskConfigClass, taskDataClass);
    }

    @Override
    public <T extends TaskConfig, D extends TaskData> TaskRetry
    getTaskRetryById(String taskRetryId, Class<T> taskConfigClass, Class<D> taskDataClass) {
        if(StringUtil.isBlank(taskRetryId)){
            log.error("getTaskRetryById fail, taskRetryId is null");
            return null;
        }
        return taskRetryRepository.getTaskRetryById(taskRetryId, taskConfigClass, taskDataClass);
    }

    @Override
    public <T extends TaskConfig, D extends TaskData> TaskRetry getTaskRetryByResourceIdForUpdate(String resourceId,
                                                                                                  TaskResouceTypeEnum resourceType, Class<T> taskConfigClass, Class<D> taskDataClass) {
        if(StringUtil.isBlank(resourceId) || resourceType == null){
            log.error("getTaskRetryByResourceId fail, resourceId is blank or resourceType is null");
            return null;
        }
        return taskRetryRepository.getTaskRetryByResourceIdForUpdate(resourceId, resourceType, taskConfigClass, taskDataClass);
    }

    @Override
    public List<TaskRetry> listFailedTasksForRetry(int limit, java.util.Date startTime) {
        if(limit <= 0){
            log.error("listFailedTasksForRetry fail, limit must be positive");
            return Collections.emptyList();
        }
        if(startTime == null){
            log.error("listFailedTasksForRetry fail, startTime cannot be null");
            return Collections.emptyList();
        }
        return taskRetryRepository.listFailedTasksForRetry(limit, startTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrTaskRetryCount(TaskRetry taskRetry) {
        if(taskRetry == null || StringUtil.isBlank(taskRetry.getTaskRetryId())){
            log.error("incrTaskRetryCount fail, taskRetry is null or taskRetryId is blank");
            return false;
        }
        return taskRetryRepository.incrTaskRetryCount(taskRetry);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskRetryStatus(String taskRetryId, TaskRetryStatusEnum fromStatus, TaskRetryStatusEnum toStatus) {
        if(StringUtil.isBlank(taskRetryId) || fromStatus == null || toStatus == null){
            log.error("updateTaskRetryStatus fail, taskRetryId is blank or status is null");
            return false;
        }
        return taskRetryRepository.updateTaskRetryStatus(taskRetryId, fromStatus, toStatus);
    }
}
