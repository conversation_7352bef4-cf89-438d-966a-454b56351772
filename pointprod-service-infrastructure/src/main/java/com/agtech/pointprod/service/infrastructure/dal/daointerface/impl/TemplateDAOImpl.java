package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import com.agtech.pointprod.service.domain.common.enums.DeletedEnum;
import com.agtech.pointprod.service.domain.common.enums.TemplateStatusEnum;
import com.agtech.pointprod.service.facade.dto.SortItemDTO;
import com.agtech.pointprod.service.facade.dto.req.template.QueryTemplateInfoListReq;
import com.agtech.pointprod.service.infrastructure.common.enums.sort.ItemSortTypeEnum;
import com.agtech.pointprod.service.infrastructure.common.enums.sort.TemplateSortKeyEnum;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.TemplateDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TemplateDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.TemplateMapper;
import com.agtech.pointprod.service.facade.dto.req.BaseListQueryReq;
import com.agtech.pointprod.service.infrastructure.dto.response.TemplateValidCountQueryRsp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class TemplateDAOImpl extends ServiceImpl<TemplateMapper, TemplateDO> implements TemplateDAO {

    @Resource
    private TemplateMapper templateMapper;

    @Override
    public IPage<TemplateDO> selectValidTemplateByBizType(String bizType, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<TemplateDO> queryWrapper = new LambdaQueryWrapper<>();
        // 业务类型匹配
        queryWrapper.eq(TemplateDO::getBizType, bizType);

        // 未删除
        queryWrapper.eq(TemplateDO::getIsDeleted, DeletedEnum.NO.getCode());

        // 状态为生效
        queryWrapper.eq(TemplateDO::getStatus, TemplateStatusEnum.ABLE.getCode());

        // 当前时间在有效期内
        Date now = new Date();
        queryWrapper.le(TemplateDO::getStartTime, now)
                .ge(TemplateDO::getEndTime, now);

        // 按排序字段排序
        queryWrapper.orderByAsc(TemplateDO::getSort);
        queryWrapper.orderByDesc(TemplateDO::getId);

        // 分页查询
        IPage<TemplateDO> page = new Page<>(pageNo, pageSize);
        return this.page(page, queryWrapper);
    }

    @Override
    public IPage<TemplateDO> queryTemplateListByPage(QueryTemplateInfoListReq templateListQueryRequest) {
        LambdaQueryWrapper<TemplateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TemplateDO::getIsDeleted, DeletedEnum.NO.getCode());

        String status = TemplateStatusEnum.EXPIRED.getCode().equals(templateListQueryRequest.getStatus())?TemplateStatusEnum.ABLE.getCode() : templateListQueryRequest.getStatus();
        queryWrapper.eq(TemplateStatusEnum.containRealCode(status), TemplateDO::getStatus, status);
        queryWrapper.ge(TemplateStatusEnum.ABLE.getCode().equals(templateListQueryRequest.getStatus()), TemplateDO::getEndTime, new Date());
        queryWrapper.lt(TemplateStatusEnum.EXPIRED.getCode().equals(templateListQueryRequest.getStatus()), TemplateDO::getEndTime, new Date());
        queryWrapper.eq(StringUtils.isNotEmpty(templateListQueryRequest.getBizType()), TemplateDO::getBizType, templateListQueryRequest.getBizType());

        String templateContent = templateListQueryRequest.getTemplateContent();
        queryWrapper.like(StringUtils.isNotEmpty(templateContent), TemplateDO::getTemplateContent, templateContent);

        // 按排序字段排序
        setSort(templateListQueryRequest, queryWrapper);

        // 分页查询
        IPage<TemplateDO> page = new Page<>(templateListQueryRequest.getPageNo(), templateListQueryRequest.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    public boolean createTemplate(TemplateDO template) {
        return save(template);
    }

    @Override
    public boolean updateTemplate(TemplateDO template) {
        LambdaUpdateWrapper<TemplateDO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(TemplateDO::getTemplateId, template.getTemplateId());
        return update(template, lambdaUpdateWrapper);
    }

    @Override
    public boolean deleteTemplate(String templateId, String operator) {
        LambdaUpdateWrapper<TemplateDO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(TemplateDO::getTemplateId, templateId);
        lambdaUpdateWrapper.set(TemplateDO::getIsDeleted, DeletedEnum.YES.getCode());
        lambdaUpdateWrapper.set(TemplateDO::getModifier, operator);
        return update(lambdaUpdateWrapper);
    }

    @Override
    public TemplateValidCountQueryRsp selectValidCount(String bizType, String templateId, Date time) {
        return templateMapper.selectValidCount(bizType, templateId, time);
    }

    private void setSort(BaseListQueryReq baseListQueryReq, LambdaQueryWrapper<TemplateDO> queryWrapper){
        if (CollectionUtils.isEmpty(baseListQueryReq.getSortItems())){
            return;
        }
        for (SortItemDTO sortItem : baseListQueryReq.getSortItems()){
            String sortKey = sortItem.getSortKey();

            SFunction<TemplateDO, Object> column = null;
            boolean isAsc = ItemSortTypeEnum.ASC.getCode().equals(sortItem.getSortType());
            if (TemplateSortKeyEnum.GMT_CREATE.getCode().equals(sortKey)){
                column = TemplateDO::getGmtCreate;
            }else if (TemplateSortKeyEnum.TEMPLATE_ID.getCode().equals(sortKey)){
                column = TemplateDO::getTemplateId;
            } else if (TemplateSortKeyEnum.SORT.getCode().equals(sortKey)){
                column = TemplateDO::getSort;
            } else if (TemplateSortKeyEnum.ID.getCode().equals(sortKey)){
                column = TemplateDO::getId;
            }
            if (column != null){
                queryWrapper.orderBy(true, isAsc, column);
            }
        }
    }

} 