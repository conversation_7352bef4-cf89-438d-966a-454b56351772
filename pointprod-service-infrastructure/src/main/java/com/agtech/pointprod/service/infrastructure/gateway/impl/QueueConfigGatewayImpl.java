package com.agtech.pointprod.service.infrastructure.gateway.impl;

import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.domain.gateway.QueueConfigGateway;
import com.agtech.pointprod.service.infrastructure.config.RabbitMQConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 队列配置网关实现类
 * 根据MessageQueueEnum枚举类型获取RabbitMQ队列配置信息
 * <AUTHOR>
 */
@Component
public class QueueConfigGatewayImpl implements QueueConfigGateway {
    
    @Resource
    private RabbitMQConfig rabbitMQConfig;
    
    @Override
    public String getQueue(MessageQueueEnum messageQueueEnum) {
        RabbitMQConfig.QueueConfig queueConfig = rabbitMQConfig.getQueueConfig(messageQueueEnum.getConfigKey());
        return queueConfig != null ? queueConfig.getQueue() : null;
    }
    
    @Override
    public String getExchange(MessageQueueEnum messageQueueEnum) {
        RabbitMQConfig.QueueConfig queueConfig = rabbitMQConfig.getQueueConfig(messageQueueEnum.getConfigKey());
        return queueConfig != null ? queueConfig.getExchange() : null;
    }
    
    @Override
    public String getRoutingKey(MessageQueueEnum messageQueueEnum) {
        RabbitMQConfig.QueueConfig queueConfig = rabbitMQConfig.getQueueConfig(messageQueueEnum.getConfigKey());
        return queueConfig != null ? queueConfig.getRoutingKey() : null;
    }
    
    @Override
    public String getDelayExchange(MessageQueueEnum messageQueueEnum) {
        RabbitMQConfig.QueueConfig queueConfig = rabbitMQConfig.getQueueConfig(messageQueueEnum.getConfigKey());
        return queueConfig != null ? queueConfig.getDelayExchange() : null;
    }

    @Override
    public String getDelayRoutingKey(MessageQueueEnum messageQueueEnum) {
        RabbitMQConfig.QueueConfig queueConfig = rabbitMQConfig.getQueueConfig(messageQueueEnum.getConfigKey());
        return queueConfig != null ? queueConfig.getDelayRoutingKey() : null;
    }

}