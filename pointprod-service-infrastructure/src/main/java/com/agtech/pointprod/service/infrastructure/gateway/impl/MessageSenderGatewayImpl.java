package com.agtech.pointprod.service.infrastructure.gateway.impl;

import javax.annotation.Resource;

import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.MessageSenderGateway;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 消息发送网关实现类
 * 基于 RabbitMQ 实现消息发送功能
 * <AUTHOR>
 */
@Component
@Slf4j
public class MessageSenderGatewayImpl implements MessageSenderGateway {

    @Resource
    private RabbitTemplate rabbitTemplate;
    
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public void sendMessage(String messageId, Object message, String exchange, String routingKey, Integer delay) {
        MessagePostProcessor messagePostProcessor = msg -> {
            msg.getMessageProperties().setMessageId(messageId);
            if(delay != null) {
                msg.getMessageProperties().setDelay(delay);
            }
            return msg;
        };

        String messageBody;
        if (message instanceof String) {
            messageBody = (String) message;
        } else {
            try {
                messageBody = objectMapper.writeValueAsString(message);
            } catch (Exception e) {
                log.error("序列化消息失败, messageId={}, message={}", messageId, message, e);
                throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
            }
        }
        log.info("開始發送MQ消息 exchange={}, routingKey={}, messageId={}, message={}, delay={}", 
                exchange, routingKey, messageId, messageBody, delay);
        long start = System.currentTimeMillis();
        rabbitTemplate.convertAndSend(exchange, routingKey, messageBody, messagePostProcessor);
        long end = System.currentTimeMillis();
        log.info("發送MQ消息成功, cost:{}ms", end - start);
    }
}