package com.agtech.pointprod.service.infrastructure.integration.mpay.util;

import java.util.List;
import java.util.UUID;

import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.PushMsgRequest;

/**
 * 推送消息请求构建器
 */
public class PushMsgRequestBuilder {
    
    private final PushMsgRequest request;
    
    private PushMsgRequestBuilder() {
        this.request = new PushMsgRequest();
        // 设置默认值
        this.request.setPushAll(0); // 默认不推送给所有用户
        this.request.setOpenType("proto"); // 默认原生页面
    }
    
    /**
     * 创建构建器实例
     */
    public static PushMsgRequestBuilder builder() {
        return new PushMsgRequestBuilder();
    }
    
    /**
     * 设置app_token（必填）
     */
    public PushMsgRequestBuilder appToken(String appToken) {
        request.setAppToken(appToken);
        return this;
    }
    
    /**
     * 设置appid（必填）
     */
    public PushMsgRequestBuilder appid(String appid) {
        request.setAppid(appid);
        return this;
    }
    
    /**
     * 设置目标用户openid列表
     */
    public PushMsgRequestBuilder openidList(List<String> openidList) {
        request.setOpenidList(openidList);
        return this;
    }
    
    /**
     * 设置目标用户手机号列表
     */
    public PushMsgRequestBuilder phoneList(List<String> phoneList) {
        request.setPhoneList(phoneList);
        return this;
    }
    
    /**
     * 设置目标用户custid列表
     */
    public PushMsgRequestBuilder custidList(List<String> custidList) {
        request.setCustidList(custidList);
        return this;
    }
    
    /**
     * 设置是否推送给所有用户
     */
    public PushMsgRequestBuilder pushAll(boolean pushAll) {
        request.setPushAll(pushAll ? 1 : 0);
        return this;
    }
    
    /**
     * 设置任务ID，如果不设置将自动生成UUID
     */
    public PushMsgRequestBuilder taskId(String taskId) {
        request.setTaskId(taskId);
        return this;
    }
    
    /**
     * 设置消息内容（必填，最大长度4000）
     */
    public PushMsgRequestBuilder msgContext(String msgContext) {
        request.setMsgContext(msgContext);
        return this;
    }
    
    /**
     * 设置消息标题（必填，最大长度500）
     */
    public PushMsgRequestBuilder msgTitle(String msgTitle) {
        request.setMsgTitle(msgTitle);
        return this;
    }
    
    /**
     * 设置消息模板code
     */
    public PushMsgRequestBuilder msgCode(String msgCode) {
        request.setMsgCode(msgCode);
        return this;
    }
    
    /**
     * 设置消息提示类型
     * proto：原生页面（默认值）
     * h5：h5全屏
     * h5Alter：h5弹窗
     * redpackage：红包弹窗
     */
    public PushMsgRequestBuilder openType(String openType) {
        request.setOpenType(openType);
        return this;
    }
    
    /**
     * 设置路由
     */
    public PushMsgRequestBuilder route(String route) {
        request.setRoute(route);
        return this;
    }
    
    /**
     * 设置模板参数
     */
    public PushMsgRequestBuilder templateParams(PushMsgRequest.TemplateParams templateParams) {
        request.setTemplateParams(templateParams);
        return this;
    }
    
    /**
     * 快速设置mCoin模板参数
     */
    public PushMsgRequestBuilder mCoinParam(String mCoinValue) {
        PushMsgRequest.TemplateParams params = new PushMsgRequest.TemplateParams();
        params.setMCoin(mCoinValue);
        request.setTemplateParams(params);
        return this;
    }
    
    /**
     * 设置扩展信息
     */
    public PushMsgRequestBuilder extMap(PushMsgRequest.ExtMap extMap) {
        request.setExtMap(extMap);
        return this;
    }
    
    /**
     * 快速设置内容扩展信息
     */
    public PushMsgRequestBuilder contentExt(String amount, String tradeType, String otherCustId, String currency) {
        PushMsgRequest.ExtMap extMap = new PushMsgRequest.ExtMap();
        PushMsgRequest.ExtMap.ContentExt contentExt = new PushMsgRequest.ExtMap.ContentExt();
        
        contentExt.setAmount(amount);
        contentExt.setTradeType(tradeType);
        contentExt.setOtherCustId(otherCustId);
        contentExt.setCurrency(currency);
        
        extMap.setContentExt(contentExt);
        request.setExtMap(extMap);
        
        return this;
    }
    
    /**
     * 设置logo
     */
    public PushMsgRequestBuilder logo(String logo) {
        request.setLogo(logo);
        return this;
    }
    
    /**
     * 构建请求对象
     */
    public PushMsgRequest build() {
        // 如果没有设置taskId，自动生成一个32位的UUID
        if (request.getTaskId() == null || request.getTaskId().isEmpty()) {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            request.setTaskId(uuid);
        }
        
        return request;
    }
} 