package com.agtech.pointprod.service.infrastructure.gateway.impl;

import java.net.URLEncoder;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;
import com.agtech.pointprod.service.domain.common.enums.ContentTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.McoinTradeTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.NotificationTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.NumbersEnum;
import com.agtech.pointprod.service.domain.common.enums.ResourceTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.ShareCodeRoleEnum;
import com.agtech.pointprod.service.domain.common.enums.ShareRecordsTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.YesOrNoEnum;
import com.agtech.pointprod.service.domain.gateway.NotificationGateway;
import com.agtech.pointprod.service.domain.model.NotificationRecord;
import com.agtech.pointprod.service.domain.model.NotificationRecordList;
import com.agtech.pointprod.service.domain.model.share.ShareRecord;
import com.agtech.pointprod.service.infrastructure.common.constant.NotificationConstants;
import com.agtech.pointprod.service.infrastructure.common.utils.NumberUtil;
import com.agtech.pointprod.service.infrastructure.dto.request.PushMessageRequestDTO;
import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.acl.McoinMallAclService;
import com.agtech.pointprod.service.infrastructure.integration.mpay.acl.MPayPushAclService;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import com.agtech.pointprod.service.infrastructure.repository.NotificationRecordRepository;
import com.agtech.pointprod.service.infrastructure.repository.ShareRecordRepository;
import com.google.common.collect.Lists;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 通知网关实现 - 在内存中聚合多表数据
 */
@Slf4j
@Component
public class NotificationGatewayImpl implements NotificationGateway {
    
    @Resource
    private NotificationRecordRepository notificationRecordRepository;
    
    @Resource
    private ShareRecordRepository shareRecordRepository;
    
    @Resource
    private BizSequenceRepository bizSequenceRepository;
    
    @Resource
    private McoinMallAclService mcoinMallAclService;
    
    @Resource
    private MPayPushAclService mPayPushAclService;

    @Value("${mPay.push.appId:}")
    private String mPayPushAppId;
    
    @Value("${mPay.push.payerCode:}")
    private String payerMsgCode;
    
    @Value("${mPay.push.payeeCode:}")
    private String payeeMsgCode;

    private static final String OPEN_TYPE = "h5";

      
    @Value("${point.h5.domain:}")
    private String shareTransferLinkConfig;

    @Value("${point.gateway.domain:}")
    private String gatewayUrl;

    /**
     * 分享码参数名
     */
    private static final String ORDER_DETAIL_PARAM = "/transfer/index.html#/detail?notificationId=";
    private static final String ORDER_DETAIL_PARAM_TYPE = "&orderId=";
    
    /**
     * 页面ID参数名
     */

    private static final String GATEWAY_PATH = "/gateway/gateway-auth/mpay/auth/authorize?appId=";
    private static final String GATEWAY_REDIRECT = "&redirectUrl=";
    
    /**
     * 通知类型常量
     */

    /**
     * 分享码查询结果
     */
    @Getter
    @Setter
    private static class ShareCodeQueryResult {
        private List<NotificationRecord> notifications;
        private boolean isExpired;
        private String orderId;
        private String userRole;
        
        public ShareCodeQueryResult(List<NotificationRecord> notifications, boolean isExpired, String orderId, String userRole) {
            this.notifications = notifications;
            this.isExpired = isExpired;
            this.orderId = orderId;
            this.userRole = userRole;
        }
    }

    @Override
    public NotificationRecordList queryNotificationList(String userId, String shareCode, String resourceType) {
        List<NotificationRecord> finalResult = Lists.newArrayList();
        boolean isShareCodeExpired = false;
        String shareCodeNotificationStatus = null;
        String shareCodeOrderId = null;
        String shareCodeRole = null;
        
        // 1. 如果shareCode不为空白，查询相关的未读通知并排到前面
        if (StringUtils.hasText(shareCode)) {
            ShareCodeQueryResult shareCodeResult = queryNotificationsByShareCode(userId, shareCode, resourceType);
            finalResult.addAll(shareCodeResult.getNotifications());
            isShareCodeExpired = shareCodeResult.isExpired(); // 获取分享码是否过期标志
            shareCodeOrderId = shareCodeResult.getOrderId(); // 获取分享码对应的订单ID
            shareCodeRole = shareCodeResult.getUserRole(); // 获取用户角色
            
            // 确定分享码通知状态 - 对于share code应该只有一条通知记录
            List<NotificationRecord> shareCodeNotifications = shareCodeResult.getNotifications();
            if (!shareCodeNotifications.isEmpty()) {
                // 取第一条记录（应该只有一条）
                NotificationRecord notification = shareCodeNotifications.get(0);
                shareCodeNotificationStatus = notification.getStatus();
            } 
        }
        
        // 2. 查询剩余的通知列表（排除已添加的通知）
        List<NotificationRecord> remainingNotifications = queryRemainingNotifications(userId, finalResult, resourceType);
        finalResult.addAll(remainingNotifications);
        
        // 3. 创建结果对象
        NotificationRecordList result = new NotificationRecordList(finalResult, isShareCodeExpired, shareCodeNotificationStatus);
        result.setShareCodeOrderId(shareCodeOrderId);
        result.setShareCodeRole(shareCodeRole);
        return result;
    }
    
    /**
     * 根据shareCode查询相关的未读通知
     * @param userId 用户ID
     * @param shareCode 分享码
     * @param notificationType 通知类型
     * @return 分享码查询结果，包含通知列表、过期标志、订单ID和用户角色
     */
    private ShareCodeQueryResult queryNotificationsByShareCode(String userId, String shareCode, String notificationType) {
        // 1. 根据shareCode + shareRecordsType=ACCEPT + contentType=TRANSFER_CODE查询分享记录
        List<ShareRecord> shareRecords = shareRecordRepository.findByShareCodeAndTypeAndContentType(
                shareCode, 
                ShareRecordsTypeEnum.ACCEPT.getValue(), 
                ContentTypeEnum.TRANSFER_COIN.getValue()
        );
        
        // 若没有找到分享记录，视为过期
        if (shareRecords == null || shareRecords.isEmpty()) {
            log.info("No share records found for shareCode: {}", shareCode);
            return new ShareCodeQueryResult(Collections.emptyList(), true, null, null);
        }
        
        // 取第0个记录
        ShareRecord shareRecord = shareRecords.get(0);
        
        // ShareRecord为空，视为过期
        if (shareRecord == null) {
            log.info("Share record is null for shareCode: {}", shareCode);
            return new ShareCodeQueryResult(Collections.emptyList(), true, null, null);
        }
        
        // 检查记录是否已过期
        Date now = new Date();
        Date expireAt = shareRecord.getExpireAt();
        boolean isExpired = expireAt == null || expireAt.before(now);
        
        if (isExpired) {
            log.info("Share record is expired for shareCode: {}, expireAt: {}", shareCode, expireAt);
            return new ShareCodeQueryResult(Collections.emptyList(), true, null, null);
        }
        
        // 获取订单ID，若为空，视为过期
        String orderId = shareRecord.getContentId();
        if (!StringUtils.hasText(orderId)) {
            log.info("Content ID is empty for shareCode: {}", shareCode);
            return new ShareCodeQueryResult(Collections.emptyList(), true, null, null);
        }
        
        // 根据订单ID在通知表中查找对应的通知，不过滤用户ID
        List<NotificationRecord> allNotifications = notificationRecordRepository.queryByResourceIds(
                Collections.singletonList(orderId), ResourceTypeEnum.TRANSFER_SUCCESS.getValue());
        
        // 若没有找到通知记录，视为过期
        if (allNotifications == null || allNotifications.isEmpty()) {
            log.info("No notification records found for orderId: {}, shareCode: {}", orderId, shareCode);
            return new ShareCodeQueryResult(Collections.emptyList(), true, null, null);
        }
        
        // 确定用户角色
        String userRole = ShareCodeRoleEnum.IS_THIRD_PARTY.getValue();
        List<NotificationRecord> resultNotifications = Collections.emptyList();
        
        // 检查用户是否是付款人
        boolean isPayer = false;
        for (NotificationRecord notification : allNotifications) {
            if (NotificationTypeEnum.SEND.getCode().equals(notification.getNotificationType()) && 
                userId.equals(notification.getUserId())) {
                isPayer = true;
                userRole = ShareCodeRoleEnum.IS_PAYER.getValue();
                break;
            }
        }
        
        // 如果不是付款人，检查是否是收款人
        if (!isPayer) {
            for (NotificationRecord notification : allNotifications) {
                if (NotificationTypeEnum.ACCEPT.getCode().equals(notification.getNotificationType()) && 
                    userId.equals(notification.getUserId())) {
                    userRole = ShareCodeRoleEnum.IS_PAYEE.getValue();
                    // 收款人才会在结果中包含通知
                    resultNotifications = Collections.singletonList(notification);
                    break;
                }
            }
        }
        
        // 返回结果，只有收款人才包含通知记录
        return new ShareCodeQueryResult(resultNotifications, false, orderId, userRole);
    }
    
    /**
     * 查询剩余的通知列表（排除已添加的通知）
     * @param userId 用户ID
     * @param excludeNotifications 需要排除的通知列表
     * @param notificationType 通知类型
     * @return 剩余的通知列表
     */
    private List<NotificationRecord> queryRemainingNotifications(String userId, List<NotificationRecord> excludeNotifications, String notificationType) {
        // 提取已经添加的通知ID集合
        List<String> excludeNotificationIds = excludeNotifications.stream()
                .map(NotificationRecord::getNotificationId)
                .collect(Collectors.toList());
        
        // 计算还需要查询的数量：最终限制数量 - 已添加的数量
        int remainingLimit = NotificationConstants.QueryLimits.FINAL_RESULT_LIMIT - excludeNotifications.size();
        
        // 如果已经达到最终限制数量，则不需要再查询
        if (remainingLimit <= NumbersEnum.ZERO.getIntValue()) {
            return Collections.emptyList();
        }
        
        // 使用SQL NOT IN查询剩余通知，传入计算出的数量限制
        return notificationRecordRepository.queryNotificationListExcludeIds(userId, excludeNotificationIds, remainingLimit, notificationType);
    }
    
    
    @Override
    public NotificationRecord queryByNotificationId(String notificationId, String userId) {
        return notificationRecordRepository.queryByNotificationId(notificationId, userId);
    }

    @Override
    public boolean batchUpdateNotificationToRead(List<String> notificationIds, String userId) {
        return notificationRecordRepository.batchUpdateNotificationToRead(notificationIds, userId);
    }
    
    @Override
    public boolean batchUpdateNotificationByTypeToRead(String resourceType, String userId) {
        return notificationRecordRepository.batchUpdateNotificationByTypeToRead(resourceType, userId);
    }
    
    @Override
    public boolean createNotification(NotificationRecord notification, String userId) {
        String notificationId = bizSequenceRepository.getBizId(userId, SequenceCodeEnum.NOTIFICATION_RECORD);
        notification.setNotificationId(notificationId);
        
        // 生成32位taskId（通过sequence表生成）
        String taskId = bizSequenceRepository.getBizId(userId, SequenceCodeEnum.MPAY_PUSH_TASK);
        
        // 确保taskId长度为32位，不足的在前面补'F'
        if (taskId != null && taskId.length() < 32) {
            int paddingLength = 32 - taskId.length();
            StringBuilder paddedTaskId = new StringBuilder();
            for (int i = 0; i < paddingLength; i++) {
                paddedTaskId.append('F');
            }
            paddedTaskId.append(taskId);
            taskId = paddedTaskId.toString();
        }
        
        notification.setTaskId(taskId);
        notification.setIsDeleted(YesOrNoEnum.NO.getValue());
        
        return notificationRecordRepository.saveNotification(notification);
    }
    
    @Override
    public boolean updateNotificationPushStatus(String notificationId, String pushStatus) {
        return notificationRecordRepository.updateNotificationPushStatus(notificationId, pushStatus);
    }
    
    /**
     * 统一的推送通知方法，支持付款人和收款人场景
     * @param notification 通知记录
     * @param orderInfo 订单信息（基类）
     * @return 推送结果
     */
    @Override
    public boolean pushNotificationToUser(NotificationRecord notification, BaseOrderInfo orderInfo) {
        try {
            if (orderInfo == null) {
                log.error("OrderInfo is null for notification push - notificationId: {}", notification.getNotificationId());
                return false;
            }
            
            // 从通知记录中获取用户ID
            String userId = notification.getUserId();
            
            // 获取 AppToken
            String appToken = mcoinMallAclService.getAppToken();
            if (appToken == null) {
                log.error("Failed to get app token for notification push - notificationId: {}", notification.getNotificationId());
                return false;
            }
            
            log.info("Successfully got app token for notification push - notificationId: {}", notification.getNotificationType());
            
            // 根据通知类型确定消息代码和交易类型
            String msgCode;
            String tradeType;
            String amount;
            String otherCustId;
            AcceptOrderInfo acceptOrderInfo = orderInfo.getAcceptOrderInfoList().get(0);
            PayOrderInfo payOrderInfo = orderInfo.getPayOrderInfoList().get(0);
            
            if (NotificationTypeEnum.ACCEPT.getCode().equals(notification.getNotificationType())) {
                // 收款人
                msgCode = payeeMsgCode;
                tradeType = McoinTradeTypeEnum.MCOIN_TRANSFER_IN.getCode();
                
                // 从 AcceptOrderInfo 获取金额
                amount = acceptOrderInfo.getAcceptAmount() != null ?
                        NumberUtil.formatBigDecimal(acceptOrderInfo.getAcceptAmount().getAmount()) : "0";
                
                // 对方ID (付款人)
                otherCustId = payOrderInfo.getUserId();
            } else {
                // 付款人
                msgCode = payerMsgCode;
                tradeType = McoinTradeTypeEnum.MCOIN_TRANSFER_OUT.getCode();
                
                // 从 PayOrderInfo 获取金额
                amount = payOrderInfo.getPayAmount() != null ?
                        NumberUtil.formatBigDecimal(payOrderInfo.getPayAmount().getAmount()) : "0";
                
                // 对方ID (收款人)
                otherCustId = acceptOrderInfo.getUserId();
            }
            
            // 构建H5链接
            String fundOrderId = orderInfo.getFundOrderId();
            String h5 = shareTransferLinkConfig + ORDER_DETAIL_PARAM + notification.getNotificationId() + ORDER_DETAIL_PARAM_TYPE + fundOrderId;
            
            String route = gatewayUrl + GATEWAY_PATH + mPayPushAppId + GATEWAY_REDIRECT + URLEncoder.encode(h5, "UTF-8");
            
            // 构建templateParams
            PushMessageRequestDTO.TemplateParams templateParams = new PushMessageRequestDTO.TemplateParams();
            templateParams.setMCoin(amount);
            
            // 构建extMap
            PushMessageRequestDTO.ExtMap extMap = new PushMessageRequestDTO.ExtMap();
            PushMessageRequestDTO.ExtMap.ContentExt contentExt = new PushMessageRequestDTO.ExtMap.ContentExt();
            
            contentExt.setAmount(amount);
            contentExt.setTradeType(tradeType);
            contentExt.setCurrency("mCoin");
            contentExt.setOtherCustId(otherCustId);
            
            extMap.setContentExt(contentExt);
            
            // 构建请求DTO并推送消息
            PushMessageRequestDTO requestDTO = PushMessageRequestDTO.builder()
                    .appToken(appToken)
                    .mPayPushAppId(mPayPushAppId)
                    .taskId(notification.getTaskId())
                    .msgCode(msgCode)
                    .userId(userId)
                    .openType(OPEN_TYPE)
                    .route(route)
                    .templateParams(templateParams)
                    .extMap(extMap)
                    .build();
            
            boolean pushResult = mPayPushAclService.pushMessage(requestDTO);
            
            if (pushResult) {
                log.info("Successfully pushed notification - notificationId: {}, userId: {}, type: {}", 
                        notification.getNotificationId(), userId, notification.getNotificationType());
                return true;
            } else {
                log.error("Push notification failed - notificationId: {}, userId: {}, type: {}", 
                        notification.getNotificationId(), userId, notification.getNotificationType());
                return false;
            }
            
        } catch (Exception e) {
            log.error("Exception occurred while pushing notification - notificationId: {}", 
                    notification.getNotificationId(), e);
            return false;
        }
    }

    
    @Override
    public List<NotificationRecord> queryNotificationForUpdate(String resourceId, String resourceType, String userId, String notificationType) {
        return notificationRecordRepository.queryNotificationForUpdate(resourceId, resourceType, userId, notificationType);
    }
    
    @Override
    public List<NotificationRecord> queryNotification(String resourceId, String resourceType, String userId, String notificationType) {
        return notificationRecordRepository.queryNotification(resourceId, resourceType, userId, notificationType);
    }
} 