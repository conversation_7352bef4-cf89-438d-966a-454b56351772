package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Getter;
import lombok.Setter;

/**
 * 通知记录数据对象
 * Table: notification_records
 */
@Getter
@Setter
@TableName("notification_records")
public class NotificationRecordDO {
    
    /**
     * Column: id
     * Type: BIGINT
     * Remark: id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * Column: notification_id
     * Type: VARCHAR(64)
     * Remark: 通知唯一标识
     */
    private String notificationId;
    
    /**
     * Column: notification_type
     * Type: VARCHAR(16)
     * Remark: ACCEPT-接收,SEND-发送
     */
    private String notificationType;
    
    /**
     * Column: resource_id
     * Type: VARCHAR(64)
     * Remark: 关联id，例如订单号
     */
    private String resourceId;
    
    /**
     * Column: resource_type
     * Type: VARCHAR(16)
     * Remark: TRANSFER_SUCCESS
     */
    private String resourceType;
    
    /**
     * Column: resource_time
     * Type: TIMESTAMP
     * Remark: 资源时间，例如订单完成时间
     */
    private Date resourceTime;
    
    /**
     * Column: task_id
     * Type: VARCHAR(64)
     * Remark: mpay 任务id
     */
    private String taskId;
    
    /**
     * Column: user_id
     * Type: VARCHAR(64)
     * Remark: 用户ID
     */
    private String userId;
    
    /**
     * Column: title
     * Type: VARCHAR(255)
     * Remark: 通知标题
     */
    private String title;
    
    /**
     * Column: content
     * Type: VARCHAR(1024)
     * Remark: 通知内容
     */
    private String content;
    
    /**
     * Column: extra_data
     * Type: VARCHAR(2048)
     * Remark: 扩展数据
     */
    private String extraData;
    
    /**
     * Column: status
     * Type: VARCHAR(16)
     * Remark: 状态：UNREAD-未读，READ-已读
     */
    private String status;
    
    /**
     * Column: push_status
     * Type: VARCHAR(16)
     * Remark: Push状态：INIT-未发送，SUCCESS-已发送，FAILED-发送失败
     */
    private String pushStatus;
    
    /**
     * Column: push_time
     * Type: TIMESTAMP
     * Remark: Push发送时间
     */
    private Date pushTime;
    
    /**
     * Column: read_time
     * Type: TIMESTAMP
     * Remark: 阅读时间
     */
    private Date readTime;
    
    /**
     * Column: gmt_create
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date gmtCreate;
    
    /**
     * Column: gmt_modified
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date gmtModified;
    
    /**
     * Column: is_deleted
     * Type: TINYINT(3)
     * Default value: 0
     * Remark: 1- 已删除
     */
    private Integer isDeleted;
} 