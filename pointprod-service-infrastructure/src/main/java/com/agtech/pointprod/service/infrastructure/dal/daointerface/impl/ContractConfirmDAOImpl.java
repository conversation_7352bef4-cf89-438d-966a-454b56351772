package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.ContractConfirmDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.ContractConfirmDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.ContractConfirmMapper;
import com.agtech.pointprod.service.infrastructure.dto.response.UserConfirmContractRsp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ContractConfirmDAOImpl extends ServiceImpl<ContractConfirmMapper, ContractConfirmDO> implements ContractConfirmDAO {

    @Resource
    private ContractConfirmMapper contractConfirmMapper;

    @Override
    public ContractConfirmDO queryContractConfirmByUserIdAndContractId(String userId, String contractId) {
        LambdaQueryWrapper<ContractConfirmDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ContractConfirmDO::getUserId, userId);
        lambdaQueryWrapper.eq(ContractConfirmDO::getContractId, contractId);
        lambdaQueryWrapper.eq(ContractConfirmDO::getIsDeleted, 0);
        return this.getOne(lambdaQueryWrapper);
    }


    @Override
    public boolean updateContractConfirmForUser(String userId, String contractId, String status) {
        LambdaUpdateWrapper<ContractConfirmDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ContractConfirmDO::getUserId, userId);
        lambdaUpdateWrapper.eq(ContractConfirmDO::getContractId, contractId);
        ContractConfirmDO contractConfirmDO = new ContractConfirmDO();
        contractConfirmDO.setStatus(status);
        contractConfirmDO.setGmtModified(ZonedDateUtil.now());
        return this.update(contractConfirmDO, lambdaUpdateWrapper);
    }

    @Override
    public boolean saveContractConfirm(ContractConfirmDO contractConfirmDO) {
        return this.save(contractConfirmDO);
    }

    @Override
    public List<UserConfirmContractRsp> queryMultiUserConfirmContractList(String contractType, String status, List<String> userIds) {
        return contractConfirmMapper.queryMultiUserConfirmContractList(contractType, status, userIds);
    }
}
