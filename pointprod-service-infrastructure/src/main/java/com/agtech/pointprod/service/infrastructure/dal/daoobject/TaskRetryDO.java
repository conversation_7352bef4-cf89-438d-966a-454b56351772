package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Table: task_retry
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("task_retry")
public class TaskRetryDO {
    /**
     * Column: id
     * Type: BIGINT
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Column: task_retry_id
     * Type: VARCHAR(64)
     * Remark: 业务id
     */
    private String taskRetryId;

    /**
     * Column: resource_id
     * Type: VARCHAR(64)
     * Remark: 来源id，可以用于关联某个业务表
     */
    private String resourceId;

    /**
     * Column: resource_type
     * Type: VARCHAR(16)
     * Remark: 来源id的类型，用于指明哪种业务表
     */
    private String resourceType;

    /**
     * Column: try_count
     * Type: INT
     * Default value: 0
     * Remark: 已尝试次数
     */
    private Integer tryCount;

    /**
     * Column: status
     * Type: VARCHAR(16)
     * Default value: INIT
     * Remark: INIT-待处理，FINISH-已完成，FAILED-已达最大尝试次数
     */
    private String status;

    /**
     * Column: gmt_create
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     */
    private Date gmtCreate;

    /**
     * Column: gmt_modified
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     */
    private Date gmtModified;

    /**
     * Column: is_deleted
     * Type: TINYINT(3)
     * Default value: 0
     * Remark: 1- 已删除
     */
    private Integer isDeleted;

    /**
     * Column: next_retry
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 下次尝试时间，注意这个是指在这个时间之后，并且与当前时间差小于一定范围。
     */
    private Date nextRetry;

    /**
     * Column: max_try_count
     * Type: INT
     * Default value: 0
     * Remark: 最大尝试次数
     */
    private Integer maxTryCount;

    /**
     * Column: task_config
     * Type: VARCHAR(512)
     * Remark: mq的其他发送配置信息
     */
    private String taskConfig;

    /**
     * Column: task_data
     * Type: VARCHAR(2048)
     * Remark: MQ的消息体，过长建议只存此表id，消费时查表使用attachment
     */
    private String taskData;
}