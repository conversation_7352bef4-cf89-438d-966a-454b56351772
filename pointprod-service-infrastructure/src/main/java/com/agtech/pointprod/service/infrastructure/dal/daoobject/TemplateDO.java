package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("template")
public class TemplateDO {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String templateId;
    private String templateContent;
    private String templateContentEn;
    private String bizType;
    private Date startTime;
    private Date endTime;
    private String templateRemark;
    private Integer sort;
    private String creator;
    private String modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private Integer isDeleted;
    private String status;
} 