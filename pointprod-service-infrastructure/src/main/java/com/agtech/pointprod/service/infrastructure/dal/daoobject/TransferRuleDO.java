package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Getter;
import lombok.Setter;

/**
 * Transfer rule data object
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("transfer_rule")
public class TransferRuleDO {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String transferRuleId;
    private String levelKey;
    private String optionalInfo;
    private String limitRuleIds;
    private String status;
    private String creator;
    private String modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private Integer isDeleted;
}
