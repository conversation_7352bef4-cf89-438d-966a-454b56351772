package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Getter;
import lombok.Setter;

/**
 * 分享记录数据对象
 * Table: share_records
 */
@Getter
@Setter
@TableName("share_records")
public class ShareRecordDO {
    
    /**
     * Column: id
     * Type: BIGINT
     * Remark: id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * Column: share_records_id
     * Type: VARCHAR(64)
     * Remark: 业务id
     */
    private String shareRecordsId;
    
    /**
     * Column: share_records_type
     * Type: VARCHAR(16)
     * Remark: ACCEPT-接收,SEND-发送
     */
    private String shareRecordsType;
    
    /**
     * Column: share_code
     * Type: VARCHAR(64)
     * Remark: 分享编码
     */
    private String shareCode;
    
    /**
     * Column: user_id
     * Type: VARCHAR(32)
     * Remark: 用户ID
     */
    private String userId;
    
    /**
     * Column: content_type
     * Type: VARCHAR(32)
     * Remark: 分享内容类型：'TRANSFER_COIN' - 转赠
     */
    private String contentType;
    
    /**
     * Column: content_id
     * Type: VARCHAR(64)
     * Remark: 分享内容ID（订单ID等）
     */
    private String contentId;
    
    /**
     * Column: share_content
     * Type: VARCHAR(1024)
     * Remark: 分享内容标题
     */
    private String shareContent;
    
    /**
     * Column: share_url
     * Type: VARCHAR(1024)
     * Remark: 分享链接
     */
    private String shareUrl;
    
    /**
     * Column: channel_type
     * Type: VARCHAR(32)
     * Remark: 分享渠道：WECHAT, WHATSAPP
     */
    private String channelType;
    
    /**
     * Column: extra_data
     * Type: VARCHAR(2048)
     * Remark: 扩展数据（存储活动配置、渠道信息等）
     */
    private String extraData;
    
    /**
     * Column: share_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 分享时间
     */
    private Date shareTime;
    
    /**
     * Column: expire_at
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 过期时间
     */
    private Date expireAt;
    
    /**
     * Column: is_deleted
     * Type: TINYINT(3)
     * Default value: 0
     * Remark: 1- 已删除
     */
    private Integer isDeleted;
    
    /**
     * Column: gmt_create
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date gmtCreate;
    
    /**
     * Column: gmt_modified
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date gmtModified;
} 