package com.agtech.pointprod.service.infrastructure.integration.mcoinmall.dto;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MPayAppTokenResponse implements Serializable {

	private static final long serialVersionUID = 1L;

    
    @JsonProperty("app_token")
    private String appToken;

    @JsonProperty("expires_in")
    private Integer expiresIn;

    @JsonProperty("errcode")
    private String errcode ;

    @JsonProperty("errmsg")
    private String errmsg ;
}