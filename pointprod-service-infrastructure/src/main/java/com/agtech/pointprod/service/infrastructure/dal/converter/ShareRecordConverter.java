package com.agtech.pointprod.service.infrastructure.dal.converter;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.agtech.pointprod.service.domain.model.UserId;
import com.agtech.pointprod.service.domain.model.share.ContentReference;
import com.agtech.pointprod.service.domain.model.share.ShareCode;
import com.agtech.pointprod.service.domain.model.share.ShareRecord;
import com.agtech.pointprod.service.domain.model.share.ShareRecordId;
import com.agtech.pointprod.service.domain.model.share.ShareUrl;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.ShareRecordDO;

/**
 * 分享记录转换器
 */
@Component
public class ShareRecordConverter {
    
    /**
     * DO转换为Domain模型
     * @param shareRecordDO 数据对象
     * @return Domain模型
     */
    public ShareRecord doToDomain(ShareRecordDO shareRecordDO) {
        if (shareRecordDO == null) {
            return null;
        }
        
        ShareRecord shareRecord = new ShareRecord();
        
        // 设置ID
        if (StringUtils.isNotBlank(shareRecordDO.getShareRecordsId())) {
            shareRecord.setShareRecordsId(ShareRecordId.of(shareRecordDO.getShareRecordsId()));
        }
        
        // 设置内容引用
        if (StringUtils.isNotBlank(shareRecordDO.getContentId()) && 
            StringUtils.isNotBlank(shareRecordDO.getContentType())) {
            shareRecord.setContentReference(ContentReference.of(
                shareRecordDO.getContentId(), 
                shareRecordDO.getContentType()
            ));
        }
        
        // 设置分享码
        if (StringUtils.isNotBlank(shareRecordDO.getShareCode())) {
            shareRecord.setShareCode(ShareCode.of(shareRecordDO.getShareCode()));
        } else {
            shareRecord.setShareCode(ShareCode.empty());
        }
        
        // 设置分享链接
        if (StringUtils.isNotBlank(shareRecordDO.getShareUrl())) {
            shareRecord.setShareUrl(ShareUrl.of(shareRecordDO.getShareUrl()));
        } else {
            shareRecord.setShareUrl(ShareUrl.empty());
        }
        
        // 设置用户ID
        if (StringUtils.isNotBlank(shareRecordDO.getUserId())) {
            shareRecord.setUserId(UserId.of(shareRecordDO.getUserId()));
        }
        
        // 设置其他基本属性
        shareRecord.setShareRecordsType(shareRecordDO.getShareRecordsType());
        shareRecord.setChannelType(shareRecordDO.getChannelType());
        shareRecord.setShareContent(shareRecordDO.getShareContent());
        shareRecord.setExtraData(shareRecordDO.getExtraData());
        shareRecord.setShareTime(shareRecordDO.getShareTime());
        shareRecord.setExpireAt(shareRecordDO.getExpireAt());
        shareRecord.setIsDeleted(shareRecordDO.getIsDeleted());
        shareRecord.setGmtCreate(shareRecordDO.getGmtCreate());
        shareRecord.setGmtModified(shareRecordDO.getGmtModified());
        
        return shareRecord;
    }
    
    /**
     * DO列表转换为Domain模型列表
     * @param shareRecordDOList 数据对象列表
     * @return Domain模型列表
     */
    public List<ShareRecord> doListToDomainList(List<ShareRecordDO> shareRecordDOList) {
        if (shareRecordDOList == null) {
            return null;
        }
        
        List<ShareRecord> list = new ArrayList<>(shareRecordDOList.size());
        for (ShareRecordDO shareRecordDO : shareRecordDOList) {
            list.add(doToDomain(shareRecordDO));
        }
        
        return list;
    }
    
    /**
     * Domain模型转换为DO
     * @param shareRecord Domain模型
     * @return 数据对象
     */
    public ShareRecordDO domainToDO(ShareRecord shareRecord) {
        if (shareRecord == null) {
            return null;
        }
        
        ShareRecordDO shareRecordDO = new ShareRecordDO();
        
        // 设置ID
        shareRecordDO.setShareRecordsId(shareRecord.getShareRecordsId());
        
        // 设置内容ID和类型
        shareRecordDO.setContentId(shareRecord.getContentId());
        shareRecordDO.setContentType(shareRecord.getContentType());
        
        // 设置分享码
        shareRecordDO.setShareCode(shareRecord.getShareCode());
        
        // 设置分享链接
        shareRecordDO.setShareUrl(shareRecord.getShareUrl());
        
        // 设置用户ID
        shareRecordDO.setUserId(shareRecord.getUserId());
        
        // 设置其他基本属性
        shareRecordDO.setShareRecordsType(shareRecord.getShareRecordsType());
        shareRecordDO.setChannelType(shareRecord.getChannelType());
        shareRecordDO.setShareContent(shareRecord.getShareContent());
        shareRecordDO.setExtraData(shareRecord.getExtraData());
        shareRecordDO.setShareTime(shareRecord.getShareTime());
        shareRecordDO.setExpireAt(shareRecord.getExpireAt());
        shareRecordDO.setIsDeleted(shareRecord.getIsDeleted());
        shareRecordDO.setGmtCreate(shareRecord.getGmtCreate());
        shareRecordDO.setGmtModified(shareRecord.getGmtModified());
        
        return shareRecordDO;
    }
} 