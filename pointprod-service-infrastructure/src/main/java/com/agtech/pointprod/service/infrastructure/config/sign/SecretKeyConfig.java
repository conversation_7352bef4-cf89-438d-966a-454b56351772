package com.agtech.pointprod.service.infrastructure.config.sign;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@ConfigurationProperties(prefix = "secret-key")
@Slf4j
@Getter
@Setter
@RefreshScope
/**
 * secret-key:
 *   config:
 *     mpay:
 *       clientId:""
 *       privateKey: "xxx"
 *       publicKey: "yyy"
 */
public class SecretKeyConfig {
    private Map<String, KeyInfo> config;

    public KeyInfo findKeyInfo(String partnerId) {
        return config.get(partnerId);
    }


    @Getter
@Setter
    public static class KeyInfo {
        private String clientId;
        private String privateKey;
        private String publicKey;
    }
}
