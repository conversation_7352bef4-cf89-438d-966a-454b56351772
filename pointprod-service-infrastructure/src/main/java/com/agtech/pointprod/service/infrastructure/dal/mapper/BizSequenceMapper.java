package com.agtech.pointprod.service.infrastructure.dal.mapper;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.BizSequenceDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

public interface BizSequenceMapper extends BaseMapper<BizSequenceDO> {

    @MapKey("bizName")
    Map<String, BizSequenceDO> queryBizSequenceEntitys();

    BizSequenceDO selectByBizNameForUpdate(@Param("bizName") String bizName);

    int updateByBizName(@Param("bizName") String bizName, @Param("value") int newValue);
}
