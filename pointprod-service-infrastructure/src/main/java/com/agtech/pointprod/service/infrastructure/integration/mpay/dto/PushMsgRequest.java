package com.agtech.pointprod.service.infrastructure.integration.mpay.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 推送消息请求DTO
 */
@Getter
@Setter
@ToString
public class PushMsgRequest {
    
    /**
     * 商戶通过app_token接口获取到的访问凭证
     */
    @JsonProperty("app_token")
    private String appToken;
    
    /**
     * 商戶appid
     */
    @JsonProperty("appid")
    private String appid;
    
    /**
     * 目标推送用户的openid列表（选填）
     */
    @JsonProperty("openid_list")
    private List<String> openidList;
    
    /**
     * 目标推送用户的手机号列表（选填）
     * 含区号，示例值："+86-13636363636"
     */
    @JsonProperty("phone_list")
    private List<String> phoneList;
    
    /**
     * 目标推送用户的custid列表（选填）
     */
    @JsonProperty("custid_list")
    private List<String> custidList;
    
    /**
     * 是否推送所有与该商戶绑定用户
     * 1：是；0：否。默认值为0
     */
    @JsonProperty("push_all")
    private Integer pushAll;
    
    /**
     * 本次推送任务的id，需要确保唯一
     * 长度必须为32位字符串
     */
    @JsonProperty("task_id")
    private String taskId;
    
    /**
     * 推送消息内容（最大长度4000）
     */
    @JsonProperty("msg_context")
    private String msgContext;
    
    /**
     * 推送消息标题（最大长度500）
     */
    @JsonProperty("msg_title")
    private String msgTitle;
    
    /**
     * 消息模板code（选填）
     * 若此值不为空，msg_context和msg_title则会被忽略
     */
    @JsonProperty("msg_code")
    private String msgCode;
    
    /**
     * 消息提示类型（选填）
     * proto：原生页面（默认值）
     * h5：h5全屏
     * h5Alter：h5弹窗
     * redpackage：红包弹窗
     */
    @JsonProperty("open_type")
    private String openType;
    
    /**
     * 路由（选填）
     * 当open_type = h5，route应是一条http url地址
     * 否则，route应是一个页面标识值
     */
    @JsonProperty("route")
    private String route;
    
    /**
     * 模板参数（选填）
     * msg_code所属的模板需要替换的占位符参数
     * msg_code 所属的模板需要替换的占位符参数，json对象，示例值：
     * "template_params":{
     * "mCoin":"1,000"//需要格式化
     * }
     * 所属模板中的占位符#{mCoin}会被替换为10
     */
    @JsonProperty("template_params")
    private TemplateParams templateParams;
    
    /**
     * 消息左侧logo（选填）
     * 通常是商户logo之类的
     */
    @JsonProperty("logo")
    private String logo;
    
    /**
     * 扩展信息（选填）
     * contentExt包含：
     * - amount: 格式化金额，如"1,000"
     * - tradeType: 交易类型，16=mCoin转出，17=mCoin转入
     * - otherCustId: 对方用户ID
     * - currency: 货币类型，如"mCoin"
     */
    @JsonProperty("ext_map")
    private ExtMap extMap;
    
    /**
     * 模板参数类
     */
    @Getter
    @Setter
    @ToString
    public static class TemplateParams {
        /**
         * mCoin值，格式化后的金额
         */
        @JsonProperty("mCoin")
        private String mCoin;
        
        // 可以添加其他模板参数字段
    }
    
    /**
     * 扩展信息类
     */
    @Getter
    @Setter
    @ToString
    public static class ExtMap {
        /**
         * 内容扩展信息
         */
        @JsonProperty("contentExt")
        private ContentExt contentExt;
        
        /**
         * 内容扩展信息类
         */
        @Getter
        @Setter
        @ToString
        public static class ContentExt {
            /**
             * 格式化金额，如"1,000"
             */
            private String amount;
            
            /**
             * 交易类型
             * 16=mCoin转出
             * 17=mCoin转入
             */
            private String tradeType;
            
            /**
             * 对方用户ID
             */
            private String otherCustId;
            
            /**
             * 货币类型，如"mCoin"
             */
            private String currency;
        }
    }
} 