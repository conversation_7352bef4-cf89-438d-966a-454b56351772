package com.agtech.pointprod.service.infrastructure.repository;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.domain.model.TransferFundAccumulation;

import java.util.List;

import java.math.BigDecimal;

/**
 * 转出获赠累计仓储接口
 */
public interface TransferFundAccumulationRepository {

    PageInfoDTO<TransferFundAccumulation> queryTemplateListByPage(List<String> includeUserIds, List<String> excludeUserIds, Integer pageSize, Integer pageNo);

    /**
     * 根据用户ID查询累计记录
     * @param userId 用户ID
     * @return 累计记录
     */
    TransferFundAccumulation findByUserId(String userId);
    
    /**
     * 根据用户ID查询累计记录并加锁
     * @param userId 用户ID
     * @return 累计记录
     */
    TransferFundAccumulation findByUserIdForUpdate(String userId);
    
    /**
     * 根据业务ID查询累计记录
     * @param fundAccumulationId 业务ID
     * @return 累计记录
     */
    TransferFundAccumulation findByFundAccumulationId(String fundAccumulationId);
    
    /**
     * 保存累计记录
     * @param transferFundAccumulation 累计记录
     * @return 保存成功与否
     */
    boolean save(TransferFundAccumulation transferFundAccumulation);
    
    /**
     * 更新累计记录
     * @param transferFundAccumulation 累计记录
     * @return 更新成功与否
     */
    boolean update(TransferFundAccumulation transferFundAccumulation);
    
    /**
     * 累加转出金额和次数
     * @param userId 用户ID
     * @param amount 转出金额
     * @param count 转出次数
     * @return 更新成功与否
     */
    boolean accumulateFundOut(String userId, BigDecimal amount, Integer count);
    
    /**
     * 累加转入金额和次数
     * @param userId 用户ID
     * @param amount 转入金额
     * @param count 转入次数
     * @return 更新成功与否
     */
    boolean accumulateFundIn(String userId, BigDecimal amount, Integer count);
}
