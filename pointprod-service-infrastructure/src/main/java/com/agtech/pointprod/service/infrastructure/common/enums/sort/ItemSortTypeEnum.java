package com.agtech.pointprod.service.infrastructure.common.enums.sort;

import com.agtech.common.lang.util.StringUtil;

/**
 * ItemSortTypeEnum
 * <AUTHOR>
 * @version v1.0, 2025/6/24 16:39
 */
public enum ItemSortTypeEnum {
	/** 升序 */
    ASC("asc", "升序"),
    DESC("desc", "降序"),
    ;
    /** code */
    private String code;

    /** description */
    private String description;

    ItemSortTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get AcceptOrderStatus enum by code
     */
    public static ItemSortTypeEnum getByCode(String code) {
        for (ItemSortTypeEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }


    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }
    
    public static boolean containCode(String code){
        for (ItemSortTypeEnum itemSortTypeEnum : values()){
            if (itemSortTypeEnum.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }
    
}
