package com.agtech.pointprod.service.infrastructure.integration.mpay.translator;

/**
 * mPay密钥管理服务接口
 */
public interface KeyService {
    /**
     * 获取指定版本的私钥
     */
    String getPrivateKey(String keyVersion);

    /**
     * 获取指定版本的公钥
     */
    String getPublicKey(String keyVersion);

    /**
     * 获取当前密钥版本
     */
    String getCurrentKeyVersion();
    /**
     * 获取当前clientId
     */
    String getClientId();
} 