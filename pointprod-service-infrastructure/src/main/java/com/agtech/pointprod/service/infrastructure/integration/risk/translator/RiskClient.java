package com.agtech.pointprod.service.infrastructure.integration.risk.translator;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.infrastructure.integration.risk.dto.TextSecurityRequest;
import com.agtech.pointprod.service.infrastructure.integration.risk.dto.TextSecurityResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * mPass风控系统
 *
 * <AUTHOR>
 */
@FeignClient(name = "riskClient", url = "${risk.url:}")
public interface RiskClient {

    @PostMapping(value = "/api/risk/security/textSecurityV2", consumes = MediaType.APPLICATION_JSON_VALUE)
    GenericResult<TextSecurityResponse> textSecurityV2(@RequestBody TextSecurityRequest request);
}
