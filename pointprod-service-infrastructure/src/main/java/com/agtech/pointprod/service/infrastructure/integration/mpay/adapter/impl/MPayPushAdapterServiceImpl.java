package com.agtech.pointprod.service.infrastructure.integration.mpay.adapter.impl;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.infrastructure.dto.request.PushMessageRequestDTO;
import com.agtech.pointprod.service.infrastructure.integration.mpay.adapter.MPayPushAdapterService;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.PushMsgRequest;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.PushMsgResponse;
import com.agtech.pointprod.service.infrastructure.integration.mpay.translator.MPayTranslatorService;

import lombok.extern.slf4j.Slf4j;

/**
 * MPay Push Adapter Service Implementation
 */
@Slf4j
@Service
public class MPayPushAdapterServiceImpl implements MPayPushAdapterService {
    
    @Resource
    private MPayTranslatorService mPayTranslatorService;
    
    @Override
    public boolean pushMessage(PushMessageRequestDTO requestDTO) {
        // 构建推送请求
        PushMsgRequest request = buildPushMsgRequest(requestDTO);
        
        // 调用翻译层推送消息
        GenericResult<PushMsgResponse> result = mPayTranslatorService.pushMessage(request);
        
        if (result.isSuccess() && result.getValue() != null) {
            PushMsgResponse response = result.getValue();
            if ("00".equals(response.getErrcode())) {
                log.info("Successfully pushed message via MPay adapter - taskId: {}, userId: {}", 
                        requestDTO.getTaskId(), requestDTO.getUserId());
                return true;
            } else if ("37".equals(response.getErrcode())) {
                log.info("Message already pushed - taskId: {}, errcode: {}, errmsg: {}",
                        requestDTO.getTaskId(), response.getErrcode(), response.getErrmsg());
                return true;
            } else if ("48".equals(response.getErrcode())) {
                // 48 错误码表示用户从未登录过 mcoin mall，这种情况不算失败，直接返回 true
                log.info("Message can not be pushed, may be the user never login mcoin mall before. - taskId: {}, custId: {}, errcode: {}, errmsg: {}",
                        requestDTO.getTaskId(), requestDTO.getUserId(), response.getErrcode(), response.getErrmsg());
                return true;
            } else {
                log.error("MPay push failed - taskId: {}, errcode: {}, errmsg: {}", 
                        requestDTO.getTaskId(), response.getErrcode(), response.getErrmsg());
                return false;
            }
        } else {
            log.error("MPay push failed - taskId: {}, userId: {}, result: {}", 
                    requestDTO.getTaskId(), requestDTO.getUserId(), result);
            return false;
        }
    }
    
    /**
     * 构建推送消息请求
     */
    private PushMsgRequest buildPushMsgRequest(PushMessageRequestDTO requestDTO) {
        PushMsgRequest request = new PushMsgRequest();
        
        // 设置基础信息
        request.setAppToken(requestDTO.getAppToken());
        request.setAppid(requestDTO.getMPayPushAppId());
        request.setTaskId(requestDTO.getTaskId());
        request.setMsgCode(requestDTO.getMsgCode());
        
        // 设置目标用户 - 这里使用 custid_list，实际使用时需要根据具体情况调整
        request.setCustidList(Collections.singletonList(requestDTO.getUserId()));
        
        // 设置推送参数
        request.setOpenType(requestDTO.getOpenType()); 
        request.setRoute(requestDTO.getRoute());
        
        // 设置模板参数
        if (requestDTO.getTemplateParams() != null) {
            PushMsgRequest.TemplateParams templateParams = new PushMsgRequest.TemplateParams();
            templateParams.setMCoin(requestDTO.getTemplateParams().getMCoin());
            request.setTemplateParams(templateParams);
        }
        
        // 设置扩展信息
        if (requestDTO.getExtMap() != null && requestDTO.getExtMap().getContentExt() != null) {
            PushMsgRequest.ExtMap extMap = new PushMsgRequest.ExtMap();
            PushMsgRequest.ExtMap.ContentExt contentExt = new PushMsgRequest.ExtMap.ContentExt();
            
            contentExt.setAmount(requestDTO.getExtMap().getContentExt().getAmount());
            contentExt.setTradeType(requestDTO.getExtMap().getContentExt().getTradeType());
            contentExt.setOtherCustId(requestDTO.getExtMap().getContentExt().getOtherCustId());
            contentExt.setCurrency(requestDTO.getExtMap().getContentExt().getCurrency());
            
            extMap.setContentExt(contentExt);
            request.setExtMap(extMap);
        }
        
        return request;
    }
} 