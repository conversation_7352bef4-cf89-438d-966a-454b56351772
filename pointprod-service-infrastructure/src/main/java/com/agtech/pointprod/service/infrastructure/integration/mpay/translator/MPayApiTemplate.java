package com.agtech.pointprod.service.infrastructure.integration.mpay.translator;

import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.infrastructure.common.utils.RsaUtils;
import com.alipay.api.internal.util.file.IOUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.function.Supplier;

@Component
@Slf4j
public class MPayApiTemplate {

    @Resource
    private KeyService keyService;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${mpay.signVerify:true}")
    private boolean signVerify;


    /**
     * 响应正常示例：{"result":{"code":"000000","subCode":"0000","subMsg":"成功"},"data":{"nickName":"信息中心","enName":null,"grade":"1","phone":"13282031228","areaCode":"+86","status":"normal","blackList":false,"headLogo":null,"custId":"00000100763910","isCertified":"0"}}
     * 响应异常示例：{"result":{"code":"200003","subCode":"APP-UNKNOWN-ERROR","subMsg":"業務系統服務繁忙"}}
     * @param apiCall
     * @return
     */
    public String execute(Supplier<Response> apiCall) {
        try {
            Response response = apiCall.get();
            String responseBody = verifyAndGetBody(response);
            return responseBody;
        } catch (Exception e) {
            log.error("Error during mPay API call, {}", e.getMessage(), e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
    }

    private String verifyAndGetBody(Response response) throws Exception {
        String respClientId = getHeader(response, "Client-Id");
        String responseTime = getHeader(response, "Response-Time");
        String nonce = getHeader(response, "Nonce");
        String signatureJson = getHeader(response, "Signature");

        if (response.body() == null) {
            log.error("Response body is null");
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
        String bodyString;
        try (InputStream is = response.body().asInputStream()) {
            bodyString = IOUtils.toString(is, "UTF-8");
        }catch (IOException e) {
            log.error("Error reading response body", e);
            // sys error
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }

        SignatureInfo signatureInfo = objectMapper.readValue(signatureJson, SignatureInfo.class);
        String toVerify = String.join(".", respClientId, responseTime, nonce, bodyString);
        String publicKey = keyService.getPublicKey(signatureInfo.getKeyVersion());
        boolean valid = RsaUtils.rsa2Verify(publicKey, toVerify, signatureInfo.getSignature());
        if (!valid && signVerify) {
            log.error("MPay response signature verification failed!  signatureJson: {}, toVerify:{}",
                    signatureJson, toVerify);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
        return bodyString;
    }

    private String getHeader(Response response, String name) {
        Collection<String> values = response.headers().get(name);
        return (values != null && !values.isEmpty()) ? values.iterator().next() : null;
    }
} 