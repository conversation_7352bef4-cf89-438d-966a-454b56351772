package com.agtech.pointprod.service.infrastructure.dal.converter;

import com.agtech.pointprod.service.domain.model.TransferRelation;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 转赠用户关系转换器
 */
@Mapper(componentModel = "spring", uses = {JsonConverter.class})
public interface TransferRelationConverter {
    /**
     * DO转换为领域模型
     */
    @Mapping(source = "participantUserAdditionalInfo", target = "participantUser", qualifiedByName = "jsonToParticipantUser")
    TransferRelation doToDomain(TransferRelationDO transferRelationDO);

    /**
     * 领域模型转换为DO
     */
    @Mapping(source = "participantUser", target = "participantUserAdditionalInfo", qualifiedByName = "participantUserToJson")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "isDeleted", constant = "0")
    TransferRelationDO domainToDo(TransferRelation transferRelation);

    /**
     * DO列表转换为领域模型列表
     */
    List<TransferRelation> doListToDomainList(List<TransferRelationDO> transferRelationList);

    /**
     * 领域模型列表转换为DO列表
     */
    List<TransferRelationDO> domainListToDoList(List<TransferRelation> transferRelationList);
}