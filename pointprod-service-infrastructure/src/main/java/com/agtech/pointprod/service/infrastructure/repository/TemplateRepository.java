package com.agtech.pointprod.service.infrastructure.repository;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.domain.model.Template;
import com.agtech.pointprod.service.facade.dto.req.template.QueryTemplateInfoListReq;
import com.agtech.pointprod.service.infrastructure.dto.response.TemplateValidCountQueryRsp;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
public interface TemplateRepository {

    PageInfoDTO<Template> queryValidTemplateListByPage(String bizType, Integer pageNo, Integer pageSize) ;

    PageInfoDTO<Template> queryTemplateListByPage(QueryTemplateInfoListReq templateListQueryRequest);

    boolean createTemplate(Template template);

    boolean updateTemplate(Template template);

    boolean deleteTemplate(String templateId, String operator);

    TemplateValidCountQueryRsp selectValidCount(String bizType, String templateId, Date time);

}
