<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agtech.pointprod.service.infrastructure.dal.mapper.ContractConfirmMapper">

    <select id="queryMultiUserConfirmContractList" resultType="com.agtech.pointprod.service.infrastructure.dto.response.UserConfirmContractRsp">
        SELECT cc.user_id, cc.gmt_modified, c.version
        FROM contract_confirm cc
        INNER JOIN contract c ON c.contract_id = cc.contract_id AND c.contract_type = #{contractType} AND c.is_deleted = 0
        WHERE cc.`status` = #{status} AND cc.is_deleted = 0
        AND cc.user_id IN
        <foreach item="item" index="index" collection="userIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
