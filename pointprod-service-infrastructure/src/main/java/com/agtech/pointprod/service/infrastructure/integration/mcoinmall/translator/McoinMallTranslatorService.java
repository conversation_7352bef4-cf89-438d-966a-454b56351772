package com.agtech.pointprod.service.infrastructure.integration.mcoinmall.translator;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.dto.MPayAppTokenResponse;

/**
 * MCoin Mall 翻译服务接口
 */
public interface McoinMallTranslatorService {
    
    /**
     * 获取应用令牌
     * 
     * @return 应用令牌响应
     */
    GenericResult<MPayAppTokenResponse> getAppToken();
} 