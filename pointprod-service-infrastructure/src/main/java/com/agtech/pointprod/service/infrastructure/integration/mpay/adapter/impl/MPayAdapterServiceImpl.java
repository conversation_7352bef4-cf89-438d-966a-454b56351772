package com.agtech.pointprod.service.infrastructure.integration.mpay.adapter.impl;


import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.CheckSecurityIdDTO;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.MPayUserInfoListRequest;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.MPayUserInfoRequest;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.UserMsgDTO;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.infrastructure.dal.converter.MPayUserInfoConverter;
import com.agtech.pointprod.service.infrastructure.integration.mpay.adapter.MPayAdapterService;
import com.agtech.pointprod.service.infrastructure.integration.mpay.translator.MPayTranslatorService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MPayAdapterServiceImpl implements MPayAdapterService {
	@Resource
	private MPayTranslatorService mPayTranslatorService;
	@Resource
	private MPayUserInfoConverter mPayUserInfoConverter;

	@Override
	public MPayUserInfo getUserMsg(String custId) {
		MPayUserInfoRequest mPayUserInfoRequest = new MPayUserInfoRequest();
		mPayUserInfoRequest.setCustId(custId);
		GenericResult<UserMsgDTO> result = mPayTranslatorService.getUserInfo(mPayUserInfoRequest);
		if (result.isSuccess()) {
			UserMsgDTO userMsgDTO = result.getValue();
			return mPayUserInfoConverter.dto2Model(userMsgDTO);
		}
		return null;
	}

	@Override
	public boolean checkSecurityId(String custId, String orderId, String securityId) {
		GenericResult<CheckSecurityIdDTO> result = mPayTranslatorService.checkSecurityId(custId, orderId, securityId);
		if (result.isSuccess()) {
			CheckSecurityIdDTO checkSecurityIdDTO = result.getValue();
			return checkSecurityIdDTO.isVerify();
		}
		return result.isSuccess();
	}

	@Override
	public MPayUserInfo getUserInfo(String areaCode, String phone) {
		MPayUserInfoRequest mPayUserInfoRequest = new MPayUserInfoRequest();
		mPayUserInfoRequest.setAreaCode(areaCode);
		mPayUserInfoRequest.setPhone(phone);
		GenericResult<UserMsgDTO> result = mPayTranslatorService.getUserInfo(mPayUserInfoRequest);
		if (result.isSuccess()) {
			UserMsgDTO userMsgDTO = result.getValue();
			return mPayUserInfoConverter.dto2Model(userMsgDTO);
		}
		return null;
	}

	@Override
	public List<MPayUserInfo> getUserMsgList(List<String> custIds) {
		if (CollectionUtils.isEmpty(custIds)){
			return Collections.emptyList();
		}
		MPayUserInfoListRequest mPayUserInfoListRequest = new MPayUserInfoListRequest();
		mPayUserInfoListRequest.setCustIds(String.join(",", custIds));
		GenericResult<List<UserMsgDTO>> result = mPayTranslatorService.getUserInfoList(mPayUserInfoListRequest);
		if (result.isSuccess()){
			return mPayUserInfoConverter.dtoListToModelList(result.getValue());
		}
		return Collections.emptyList();
	}
}
