package com.agtech.pointprod.service.infrastructure.dal.converter;

import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinAccountDTO;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTransferResultDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface McoinAccountConverter {
    McoinAccount toMcoinAccount(McoinAccountDTO mcoinAccountDTO);

    McoinTransferResult toTransferResult(McoinTransferResultDTO transferResultDTO);
}
