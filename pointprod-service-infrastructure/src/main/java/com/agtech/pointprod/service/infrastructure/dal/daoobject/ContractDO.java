package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@TableName("contract")
public class ContractDO {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String contractId;
    private String title;
    private String titleEn;
    private String contentUrl;
    private String contentEnUrl;
    private String contractType;
    private Integer version;
    private Date validTime;
    private String status;
    private Date gmtCreate;
    private Date gmtModified;
    private Integer isDeleted;
}
