package com.agtech.pointprod.service.infrastructure.integration.risk.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class TextValueDTO implements Serializable {

    private static final long serialVersionUID = 3204965036274972213L;


    /**
     * 文本内容标识
     */
    private String tag;

    /**
     * 文本内容
     */
    private String value;

    public TextValueDTO() {
    }

    public TextValueDTO(String tag, String value) {
        this.tag = tag;
        this.value = value;
    }
}
