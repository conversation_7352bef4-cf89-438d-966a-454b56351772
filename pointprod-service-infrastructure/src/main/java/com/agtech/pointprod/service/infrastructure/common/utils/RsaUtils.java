package com.agtech.pointprod.service.infrastructure.common.utils;

import java.security.KeyFactory;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

import com.zat.gateway.component.result.utils.AssertUtil;
import org.apache.commons.codec.binary.Base64;
/**
 * Rsa算法工具类
 * 
 * <AUTHOR>
 * @version $Id: RsaUtils.java, v 0.1 2025年7月15日 15:52:12 张志民 Exp $
 */
public final class RsaUtils {
    public static final String ENCODING = "UTF-8";
    public static final String ALGORITHM_RSA = "SHA1withRSA";
    public static final String ALGORITHM_RSA2 = "SHA256withRSA";

    private RsaUtils() {
    }


    public static String rsa2Sign(String privateKey, String originContent) throws Exception {
        return sign(private<PERSON>ey, originContent, ALG<PERSON>ITHM_RSA2);
    }

    public static boolean rsa2Verify(String publicKey, String originContent, String signContent) throws Exception {
        return verifySign(publicKey, originContent, signContent, ALGORITHM_RSA2);
    }

    private static String sign(String privateKey, String originContent, String algorithm) throws Exception {
        AssertUtil.notEmpty(privateKey, "签名密钥不能为空");
        AssertUtil.notEmpty(originContent, "待签名内容不能为空");
        Signature signature = Signature.getInstance(algorithm);
        signature.initSign(loadPrivateKey(privateKey));
        signature.update(originContent.getBytes("UTF-8"));
        byte[] signByte = signature.sign();
        return Base64.encodeBase64String(signByte);
    }

    private static boolean verifySign(String publicKey, String originContent, String signContent, String algorithm) throws Exception {
        AssertUtil.notEmpty(publicKey, "验签密钥不能为空");
        AssertUtil.notEmpty(originContent, "待签名内容不能为空");
        AssertUtil.notEmpty(signContent, "签名内容不能为空");
        byte[] signContentByte = Base64.decodeBase64(signContent);
        Signature signature = Signature.getInstance(algorithm);
        signature.initVerify(loadPublicKey(publicKey));
        signature.update(originContent.getBytes("UTF-8"));
        return signature.verify(signContentByte);
    }

    private static RSAPublicKey loadPublicKey(String publicKey) throws Exception {
        byte[] buffer = Base64.decodeBase64(publicKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(buffer);
        return (RSAPublicKey)keyFactory.generatePublic(keySpec);
    }

    private static RSAPrivateKey loadPrivateKey(String privateKeyBase64) throws Exception {
        byte[] buffer = Base64.decodeBase64(privateKeyBase64);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(buffer);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return (RSAPrivateKey)keyFactory.generatePrivate(keySpec);
    }
} 