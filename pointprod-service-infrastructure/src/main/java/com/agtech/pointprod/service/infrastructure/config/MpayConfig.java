package com.agtech.pointprod.service.infrastructure.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version v1.0, 2025/7/3 17:04
 */
@Configuration
@ConfigurationProperties(prefix = "mpay")
@Slf4j
@Setter
@Getter
@RefreshScope
public class MpayConfig {

    private int userBatchQueryCountLimit = 40;


}
