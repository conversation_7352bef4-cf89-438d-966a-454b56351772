package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import com.agtech.pointprod.service.facade.dto.req.template.QueryTemplateInfoListReq;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TemplateDO;
import com.agtech.pointprod.service.infrastructure.dto.response.TemplateValidCountQueryRsp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;

public interface TemplateDAO extends IService<TemplateDO> {
    IPage<TemplateDO>  selectValidTemplateByBizType(String bizType, Integer pageNo, Integer pageSize) ;

    IPage<TemplateDO> queryTemplateListByPage(QueryTemplateInfoListReq templateListQueryRequest);

    boolean createTemplate(TemplateDO template);

    boolean updateTemplate(TemplateDO template);

    boolean deleteTemplate(String templateId, String operator);

    TemplateValidCountQueryRsp selectValidCount(String bizType, String templateId, Date time);
} 