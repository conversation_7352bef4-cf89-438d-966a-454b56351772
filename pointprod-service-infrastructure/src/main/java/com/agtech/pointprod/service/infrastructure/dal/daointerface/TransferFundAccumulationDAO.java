package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferFundAccumulationDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 17:04
 */
public interface TransferFundAccumulationDAO extends IService<TransferFundAccumulationDO> {

    IPage<TransferFundAccumulationDO> listByPage(List<String> includeUserIds, List<String> excludeUserIds, Integer pageSize, Integer pageNo);

    /**
     * 根据用户ID查询累计记录
     * @param userId 用户ID
     * @return 累计记录
     */
    TransferFundAccumulationDO selectByUserId(String userId);

    /**
     * 根据用户ID查询累计记录并加锁
     * @param userId 用户ID
     * @return 累计记录
     */
    TransferFundAccumulationDO selectByUserIdForUpdate(String userId);

    /**
     * 根据业务ID查询累计记录
     * @param fundAccumulationId 业务ID
     * @return 累计记录
     */
    TransferFundAccumulationDO selectByFundAccumulationId(String fundAccumulationId);

    /**
     * 保存累计记录
     * @param transferFundAccumulationDO 累计记录
     * @return 保存成功与否
     */
    boolean saveTransferFundAccumulation(TransferFundAccumulationDO transferFundAccumulationDO);

    /**
     * 更新累计记录
     * @param transferFundAccumulationDO 累计记录
     * @return 更新成功与否
     */
    boolean updateTransferFundAccumulation(TransferFundAccumulationDO transferFundAccumulationDO);

    /**
     * 累加转出金额和次数
     * @param userId 用户ID
     * @param amount 转出金额
     * @param count 转出次数
     * @return 更新成功与否
     */
    boolean accumulateFundOut(String userId, BigDecimal amount, Integer count);

    /**
     * 累加转入金额和次数
     * @param userId 用户ID
     * @param amount 转入金额
     * @param count 转入次数
     * @return 更新成功与否
     */
    boolean accumulateFundIn(String userId, BigDecimal amount, Integer count);
}
