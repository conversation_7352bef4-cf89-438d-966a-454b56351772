package com.agtech.pointprod.service.infrastructure.repository.impl;

import com.agtech.pointprod.service.domain.model.TransferRelation;
import com.agtech.pointprod.service.infrastructure.dal.converter.TransferRelationConverter;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.TransferRelationDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferRelationDO;
import com.agtech.pointprod.service.infrastructure.repository.TransferRelationRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
public class TransferRelationRepositoryImpl implements TransferRelationRepository {
    @Resource
    private TransferRelationDAO transferRelationDAO;
    @Resource
    private TransferRelationConverter transferRelationConverter;

    @Override
    public List<TransferRelation> queryLatestTransferRalations(String userId, Date startTime) {
        List<TransferRelationDO> transferRelationList = transferRelationDAO.queryLatestTransferRalations(userId, startTime);
        return transferRelationConverter.doListToDomainList(transferRelationList);
    }
    
    @Override
    public boolean save(TransferRelation transferRelation) {
        TransferRelationDO transferRelationDO = transferRelationConverter.domainToDo(transferRelation);
        return transferRelationDAO.save(transferRelationDO);
    }
    
    @Override
    public TransferRelation queryByActorAndParticipant(String actorUserId, String participantUserId) {
        TransferRelationDO transferRelationDO = transferRelationDAO.selectByActorAndParticipant(actorUserId, participantUserId);
        return transferRelationConverter.doToDomain(transferRelationDO);
    }
    
    @Override
    public boolean update(TransferRelation transferRelation) {
        TransferRelationDO transferRelationDO = transferRelationConverter.domainToDo(transferRelation);
        return transferRelationDAO.update(transferRelationDO, new LambdaQueryWrapper<TransferRelationDO>()
                .eq(TransferRelationDO::getActorUserId, transferRelationDO.getActorUserId())
                .eq(TransferRelationDO::getParticipantUserId, transferRelationDO.getParticipantUserId())
        );
    }

    @Override
    public TransferRelation queryByTransferRelationId(String transferRelationId) {
        TransferRelationDO transferRelationDO = transferRelationDAO.selectByTransferRelationId(transferRelationId);
        return transferRelationConverter.doToDomain(transferRelationDO);
    }
}
