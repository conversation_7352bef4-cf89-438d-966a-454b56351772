package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import java.util.List;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.ShareRecordDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 分享记录DAO接口
 */
public interface ShareRecordDAO extends IService<ShareRecordDO> {
    
    /**
     * 根据分享码查询分享记录
     * @param shareCode 分享码
     * @return 分享记录列表
     */
    List<ShareRecordDO> selectByShareCode(String shareCode);
    
    /**
     * 根据内容ID列表查询分享记录
     * @param contentIds 内容ID列表
     * @return 分享记录列表
     */
    List<ShareRecordDO> selectByContentIds(List<String> contentIds);
    
    /**
     * 根据分享码、分享记录类型和内容类型查询分享记录
     * @param shareCode 分享码
     * @param shareRecordsType 分享记录类型
     * @param contentType 内容类型
     * @return 分享记录列表
     */
    List<ShareRecordDO> selectByShareCodeAndTypeAndContentType(String shareCode, String shareRecordsType, String contentType);
    
    /**
     * 根据业务唯一键查询分享记录
     * @param contentId 分享内容ID
     * @param contentType 分享内容类型
     * @param shareRecordsType 分享记录类型
     * @param channelType 分享渠道类型
     * @param userId 用户ID
     * @return 分享记录，如果不存在则返回null
     */
    ShareRecordDO selectByUniqueBusinessKey(String contentId, String contentType, String shareRecordsType, String channelType, String userId);
    
    /**
     * 根据业务唯一键查询分享记录，并加排他锁（FOR UPDATE）
     * @param contentId 分享内容ID
     * @param contentType 分享内容类型
     * @param shareRecordsType 分享记录类型
     * @param channelType 分享渠道类型
     * @param userId 用户ID
     * @return 分享记录，如果不存在则返回null
     */
    ShareRecordDO selectByUniqueBusinessKeyForUpdate(String contentId, String contentType, String shareRecordsType, String channelType, String userId);
    
    /**
     * 根据分享记录ID（业务ID）更新分享记录
     * @param record 分享记录DO对象
     * @return 更新是否成功
     */
    boolean updateByShareId(ShareRecordDO record);
} 