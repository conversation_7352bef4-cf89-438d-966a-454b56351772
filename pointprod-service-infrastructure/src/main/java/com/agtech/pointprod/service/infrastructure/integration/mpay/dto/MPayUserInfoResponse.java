package com.agtech.pointprod.service.infrastructure.integration.mpay.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class MPayUserInfoResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	@JsonProperty("openid")
	private String openid;

	@JsonProperty("phone")
	private String phone;
	
	@JsonProperty("area")
	private String area;
	
	@JsonProperty("point")
	private Integer point;
	
	@JsonProperty("userId")
	private String userId;

	private String nickname;

	private String headimgurl;
}