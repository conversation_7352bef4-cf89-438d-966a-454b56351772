package com.agtech.pointprod.service.infrastructure.repository.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.agtech.pointprod.service.domain.model.NotificationRecord;
import com.agtech.pointprod.service.infrastructure.dal.converter.NotificationRecordConverter;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.NotificationRecordDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.NotificationRecordDO;
import com.agtech.pointprod.service.infrastructure.repository.NotificationRecordRepository;

/**
 * 通知记录仓储实现
 */
@Repository
public class NotificationRecordRepositoryImpl implements NotificationRecordRepository {
    
    @Resource
    private NotificationRecordDAO notificationRecordDAO;
    
    @Resource
    private NotificationRecordConverter notificationRecordConverter;
    
    @Override
    public List<NotificationRecord> queryNotificationList(String userId, String notificationType) {
        List<NotificationRecordDO> notificationRecordDOList = notificationRecordDAO.selectNotificationList(userId, notificationType);
        return notificationRecordConverter.doListToDomainList(notificationRecordDOList);
    }
    
    @Override
    public NotificationRecord queryByNotificationId(String notificationId, String userId) {
        NotificationRecordDO notificationRecordDO = notificationRecordDAO.selectByNotificationId(notificationId, userId);
        return notificationRecordDO != null ? notificationRecordConverter.doToDomain(notificationRecordDO) : null;
    }
    
    @Override
    public List<NotificationRecord> queryNotificationListExcludeIds(String userId, List<String> excludeNotificationIds, int limit, String notificationType) {
        List<NotificationRecordDO> notificationRecordDOList = notificationRecordDAO.selectNotificationListExcludeIds(userId, excludeNotificationIds, limit, notificationType);
        return notificationRecordConverter.doListToDomainList(notificationRecordDOList);
    }
    
    @Override
    public List<NotificationRecord> queryByUserIdAndResourceIds(String userId, List<String> resourceIds, String resourceType) {
        List<NotificationRecordDO> notificationRecordDOList = notificationRecordDAO.selectByUserIdAndResourceIds(userId, resourceIds, resourceType);
        return notificationRecordConverter.doListToDomainList(notificationRecordDOList);
    }
    
    @Override
    public List<NotificationRecord> queryByResourceIds(List<String> resourceIds, String resourceType) {
        List<NotificationRecordDO> notificationRecordDOList = notificationRecordDAO.selectByResourceIds(resourceIds, resourceType);
        return notificationRecordConverter.doListToDomainList(notificationRecordDOList);
    }
    
    @Override
    public boolean batchUpdateNotificationToRead(List<String> notificationIds, String userId) {
        return notificationRecordDAO.batchUpdateNotificationToRead(notificationIds, userId);
    }
    
    @Override
    public boolean batchUpdateNotificationByTypeToRead(String resourceType, String userId) {
        return notificationRecordDAO.batchUpdateNotificationByTypeToRead(resourceType, userId);
    }
    
    @Override
    public boolean saveNotification(NotificationRecord notification) {
        NotificationRecordDO notificationRecordDO = notificationRecordConverter.domainToDo(notification);
        return notificationRecordDAO.save(notificationRecordDO);
    }
    
    @Override
    public boolean updateNotificationPushStatus(String notificationId, String pushStatus) {
        return notificationRecordDAO.updateNotificationPushStatus(notificationId, pushStatus);
    }
    
    @Override
    public List<NotificationRecord> queryNotificationForUpdate(String resourceId, String resourceType, String userId, String notificationType) {
        List<NotificationRecordDO> notificationRecordDOList = notificationRecordDAO.selectNotificationForUpdate(resourceId, resourceType, userId, notificationType);
        return notificationRecordConverter.doListToDomainList(notificationRecordDOList);
    }
    
    @Override
    public List<NotificationRecord> queryNotification(String resourceId, String resourceType, String userId, String notificationType) {
        List<NotificationRecordDO> notificationRecordDOList = notificationRecordDAO.selectNotification(resourceId, resourceType, userId, notificationType);
        return notificationRecordConverter.doListToDomainList(notificationRecordDOList);
    }
} 