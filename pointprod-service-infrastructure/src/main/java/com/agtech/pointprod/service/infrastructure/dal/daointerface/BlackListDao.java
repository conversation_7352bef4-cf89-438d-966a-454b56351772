package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.BlackListDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @version TaskRetryDao.java, v0.1 2025/6/18 11:21 zhongqigang Exp $
 */
public interface BlackListDao extends IService<BlackListDO> {
    BlackListDO queryTransferBlackListByUserId(String userId);
    long countBlackList(String userId);

    BlackListDO selectBlackList(String entityType, String accountType, String entityId);

    List<BlackListDO> selectBlackLists(String entityType, String accountType, List<String> entityIds);

    void updateByEntity(BlackListDO blackList, String entityType, String accountType, String entityId);

    List<String> selectAllEntityIdsInBlackList(String entityType, String accountType, List<String> entityIds);
}
