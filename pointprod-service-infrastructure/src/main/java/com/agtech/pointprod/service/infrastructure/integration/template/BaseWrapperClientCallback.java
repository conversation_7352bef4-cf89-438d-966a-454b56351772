package com.agtech.pointprod.service.infrastructure.integration.template;

import com.agtech.common.result.BaseResult;

/**
 * rpc调用下游接口callback
 *
 * <AUTHOR>
 * @version $Id: BaseWrapperClientCallback.java, v 0.1 2024年8月6日 20:27:14
 * zhongqiang Exp $
 */
public abstract class BaseWrapperClientCallback<T extends BaseResult> implements ClientCallback<T> {

	/**
	 * @see com.agtech.applet.service.infrastructure.integration.template.ClientCallback#execute()
	 */
	@Override
	public T execute() {
		T result = doExecute();
		if (null == result) {
			return null;
		}
		isSuccess(result);
		return result;
	}

	protected abstract T doExecute();

	protected abstract boolean isSuccess(T result);
//
//	protected abstract ErrorContext getErrorContext(T paramT);

}
