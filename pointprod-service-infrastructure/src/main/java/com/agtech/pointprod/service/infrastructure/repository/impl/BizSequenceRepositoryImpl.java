package com.agtech.pointprod.service.infrastructure.repository.impl;

import com.agtech.common.enums.MPassTenantEnum;
import com.agtech.common.util.DBPrimaryKeyUtil;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.BizSequenceDao;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @version DemoRepositoryImpl.java, v0.1 2024/6/24 20:40 zhongqigang Exp $
 */
@Component
public class BizSequenceRepositoryImpl implements BizSequenceRepository {
    @Resource
    private BizSequenceDao bizSequenceDao;

    @Override
    public long nextValue(String tableName) {
        return bizSequenceDao.nextValue(tableName);
    }

    @Override
    public String getBizId(String userId, SequenceCodeEnum sequenceCodeEnum) {
        return DBPrimaryKeyUtil.generateGeneralIdForMPass(MPassTenantEnum.MPASS_MO.getSiteId(), ZonedDateUtil.now(),
                sequenceCodeEnum.getBizCode(), null, userId, bizSequenceDao.nextValue(sequenceCodeEnum.getBizName()));
    }

}
