package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.mpass.common.exception.BizException;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.BizSequenceDao;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.BizSequenceDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.BizSequenceMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 业务序列表：一旦从数据成功获取并更新可分配的序列区间后，事务需要单独提交，不能和上游业务代码共用一个事务
 * 否则并发场景下，当上游因业务等失败回滚事务后，会导致多个服务拥有相同的序列号区间，即存在同时生成重复序列号的可能。<br/>
 * 需要注意：涉及调用<code>IService</code>中写方法的地方，没有新开事务，就不要直接调用
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@EnableTransactionManagement
public class BizSequenceDaoImpl extends ServiceImpl<BizSequenceMapper, BizSequenceDO> implements BizSequenceDao {

    @Autowired
    private TransactionTemplate transactionTemplate;
    @Resource
    private BizSequenceMapper bizSequenceMapper;
    @Resource
    private Executor backendExecutor;


    private final Lock lock = new ReentrantLock();
    private final ConcurrentHashMap<String, SequenceRange> sequenceRangeMap = new ConcurrentHashMap<>();
    private static final int RETRY_TIMES = 10;

    @Override
    public BizSequenceDO queryBizSequenceEntity(String bizName) {

        LambdaQueryWrapper<BizSequenceDO> queryWrapper = Wrappers.<BizSequenceDO>lambdaQuery()
                .eq(BizSequenceDO::getName, bizName);
        return this.getOne(queryWrapper);
    }

    private BizSequenceDO queryBizSequenceForUpdate(String bizName) {
        LambdaQueryWrapper<BizSequenceDO> queryWrapper = Wrappers.<BizSequenceDO>lambdaQuery()
                .eq(BizSequenceDO::getName, bizName)
                .last(" for update");
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 5, rollbackFor = Exception.class)
    public boolean updateCurrentValue(Long currentValue, String tableName) {
        LambdaUpdateWrapper<BizSequenceDO> queryWrapper = Wrappers.<BizSequenceDO>lambdaUpdate()
                .eq(BizSequenceDO::getName, tableName)
                .set(BizSequenceDO::getCurrentValue, currentValue)
                .set(BizSequenceDO::getGmtModified, ZonedDateUtil.now());
        return this.update(queryWrapper);
    }

    @Override
    public ConcurrentHashMap<String, BizSequenceDO> queryBizSequenceEntitys() {
        ConcurrentHashMap<String, BizSequenceDO> res = new ConcurrentHashMap<>();
        Map<String, BizSequenceDO> stringBizSequenceEntityMap = baseMapper.queryBizSequenceEntitys();
        res.putAll(stringBizSequenceEntityMap);
        return res;
    }


    @Override
    public int nextValue(String bizName) {
        if (!this.sequenceRangeMap.containsKey(bizName)) {
            this.lock.lock();
            try {
                if (!this.sequenceRangeMap.containsKey(bizName)) {
                    this.sequenceRangeMap.put(bizName, asyncNextRange(bizName));
                }
            } finally {
                this.lock.unlock();
            }
        }

        int value = this.sequenceRangeMap.get(bizName).getAndIncrement();
        if (value == -1) {
            this.lock.lock();
            try {
                do {
                    if (this.sequenceRangeMap.get(bizName).isOver()) {
                        this.sequenceRangeMap.put(bizName, asyncNextRange(bizName));
                    }

                    value = this.sequenceRangeMap.get(bizName).getAndIncrement();
                } while (value == -1);
            } finally {
                this.lock.unlock();
            }
        }

        if (value < 0) {
            throw new RuntimeException("Sequence value<0, value = " + value);
        } else {
            return value;
        }
    }

    // @Override
    // public int syncNextValue(String bizName) {
    // if (!this.sequenceRangeMap.containsKey(bizName)) {
    // this.lock.lock();
    // try {
    // if (!this.sequenceRangeMap.containsKey(bizName)) {
    // this.sequenceRangeMap.put(bizName, nextRange(bizName));
    // }
    // } finally {
    // this.lock.unlock();
    // }
    // }
    //
    // int value = this.sequenceRangeMap.get(bizName).getAndIncrement();
    // if (value == -1) {
    // this.lock.lock();
    // try {
    // do {
    // if (this.sequenceRangeMap.get(bizName).isOver()) {
    // this.sequenceRangeMap.put(bizName, nextRange(bizName));
    // }
    //
    // value = this.sequenceRangeMap.get(bizName).getAndIncrement();
    // } while (value == -1);
    // } finally {
    // this.lock.unlock();
    // }
    // }
    //
    // if (value < 0) {
    // throw new RuntimeException("Sequence value<0, value = " + value);
    // } else {
    // return value;
    // }
    // }

    public SequenceRange asyncNextRange(String bizName) {
        log.info("异步获取seq bizName:{}", bizName);
        try {
            CompletableFuture<SequenceRange> future = CompletableFuture.supplyAsync(() ->
                    nextRange(bizName), backendExecutor);
            return future.get(2, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("异步获取Sequence interrupt异常：" + bizName, e);
            Thread.currentThread().interrupt();
            throw new BizException("9999", "异步获取Sequence interrupt异常");
        } catch (Exception e) {
            log.error("异步获取Sequence异常：" + bizName, e.getMessage(), e);
            throw new BizException("9999", "异步获取Sequence异常");
        }
    }

    /**
     * 此方法使用了Propagation.REQUIRES的事务模板，调用此方法的地方，需要防止和上游业务代码共用一个事务
     * 比如方法：SequenceRange asyncNextRange(String)，进行了异步调用
     *
     * @param bizName
     * @return
     */
    public SequenceRange nextRange(String bizName) {
        return transactionTemplate.execute(status -> {
            if (StringUtils.isBlank(bizName)) {
                throw new RuntimeException("please input sequence name");
            }

            for (int i = 0; i < RETRY_TIMES + 1; ++i) {
                int oldValue;
                int newValue;
                BizSequenceDO sequenceInfo = queryBizSequenceForUpdate(bizName);
                if (sequenceInfo == null) {
                    throw new RuntimeException("sequence not initialized, name=" + bizName);
                }
                if (sequenceInfo.getMinValue() <= 0 || sequenceInfo.getMaxValue() <= 0 || sequenceInfo.getStep() <= 0
                        || sequenceInfo.getStep() >= sequenceInfo.getMaxValue()) {
                    throw new RuntimeException("sequence step or minValue or maxValue error, name=" + bizName);
                }
                oldValue = sequenceInfo.getCurrentValue().intValue();
                if (oldValue < 0) {
                    throw new RuntimeException("sequence value<0, name=" + bizName);
                }

                newValue = oldValue + sequenceInfo.getStep();

                if (newValue <= sequenceInfo.getMaxValue()) {
                    int affectedRows = bizSequenceMapper.updateByBizName(bizName, newValue);
                    if (affectedRows == 0) {
                        continue;
                    }
                    return new SequenceRange(oldValue + 1, newValue);
                } else {
                    // 大于最大值则从头开始循环利用序列号
                    newValue = sequenceInfo.getStep() + sequenceInfo.getMinValue().intValue();
                    int affectedRows = bizSequenceMapper.updateByBizName(bizName, newValue);
                    if (affectedRows == 0) {
                        continue;
                    }
                    return new SequenceRange(sequenceInfo.getMinValue().intValue(), newValue);
                }
            }
            throw new RuntimeException("Retried too many times, retryTimes = " + RETRY_TIMES);
        });
    }

    public static class SequenceRange {
        private final int min;
        private final int max;
        private final AtomicInteger value;
        private volatile boolean over = false;

        public SequenceRange(int min, int max) {
            this.min = min;
            this.max = max;
            this.value = new AtomicInteger(min);
        }

        public int getAndIncrement() {
            int currentValue = this.value.getAndIncrement();
            if (currentValue > this.max) {
                this.over = true;
                return -1;
            } else {
                return currentValue;
            }
        }

        public long getMin() {
            return this.min;
        }

        public long getMax() {
            return this.max;
        }

        public boolean isOver() {
            return this.over;
        }
    }
}
