package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 17:00
 */
@Getter
@Setter
@TableName("transfer_fund_accumulation")
public class TransferFundAccumulationDO {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String fundAccumulationId;
    private String userId;
    private BigDecimal fundOutAmount;
    private Integer fundOutCount;
    private BigDecimal fundInAmount;
    private Integer fundInCount;
    private String description;
    private Date gmtCreate;
    private Date gmtModified;
    private Integer isDeleted;

}
