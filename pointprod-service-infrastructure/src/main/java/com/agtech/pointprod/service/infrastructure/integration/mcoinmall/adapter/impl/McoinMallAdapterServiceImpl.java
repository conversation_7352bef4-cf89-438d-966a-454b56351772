package com.agtech.pointprod.service.infrastructure.integration.mcoinmall.adapter.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.adapter.McoinMallAdapterService;
import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.dto.MPayAppTokenResponse;
import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.translator.McoinMallTranslatorService;

import lombok.extern.slf4j.Slf4j;

/**
 * MCoin Mall Adapter Service Implementation
 */
@Slf4j
@Service
public class McoinMallAdapterServiceImpl implements McoinMallAdapterService {
    
    @Resource
    private McoinMallTranslatorService mcoinMallTranslatorService;
    
    @Override
    public String getAppToken() {
        GenericResult<MPayAppTokenResponse> result = mcoinMallTranslatorService.getAppToken();
        if (result.isSuccess() && result.getValue() != null) {
            String appToken = result.getValue().getAppToken();
            log.info("Successfully got app token from McoinMall adapter");
            return appToken;
        } else {
            log.error("Failed to get app token from McoinMall - result: {}", result);
            return null;
        }
    }
} 