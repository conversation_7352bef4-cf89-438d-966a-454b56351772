package com.agtech.pointprod.service.infrastructure.integration.mcoin.translator;

import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinApiRequest;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinApiResponse;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinBalanceRequestBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinBalanceResponseBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTokenRequestBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTokenResponseBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTransferQueryRequestBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTransferRequestBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTransferResponseBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * mCoin積分系統
 *
 * <AUTHOR>
 */
@FeignClient(name = "mCoinClient", url = "${mcoin.url:}")
public interface McoinClient {

    @PostMapping(value = "/mcoin/service/balance", consumes = MediaType.APPLICATION_JSON_VALUE)
    McoinApiResponse<McoinBalanceResponseBody> getBalance(@RequestBody McoinApiRequest<McoinBalanceRequestBody> request);

    @PostMapping(value = "/mcoin/service/oauth", consumes = MediaType.APPLICATION_JSON_VALUE)
    McoinApiResponse<McoinTokenResponseBody> getToken(@RequestBody McoinApiRequest<McoinTokenRequestBody> request);

    @PostMapping(value = "/mcoin/service/transfer", consumes = MediaType.APPLICATION_JSON_VALUE)
    McoinApiResponse<McoinTransferResponseBody> transfer(@RequestBody McoinApiRequest<McoinTransferRequestBody> request);

    @PostMapping(value = "/mcoin/service/transfer/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    McoinApiResponse<McoinTransferResponseBody> queryTransfer(@RequestBody McoinApiRequest<McoinTransferQueryRequestBody> request);

}
