package com.agtech.pointprod.service.infrastructure.dal.converter;

import com.agtech.pointprod.service.domain.model.ParticipantUser;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * JSON转换器
 */
@Component
public class JsonConverter {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * JSON字符串转换为ParticipantUser对象
     */
    @Named("jsonToParticipantUser")
    public ParticipantUser jsonToParticipantUser(String participantUserAdditionalInfo) {
        if (StringUtils.isBlank(participantUserAdditionalInfo)) {
            return null;
        }
        try {
            return objectMapper.readValue(participantUserAdditionalInfo, ParticipantUser.class);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * ParticipantUser对象转换为JSON字符串
     */
    @Named("participantUserToJson")
    public String participantUserToJson(ParticipantUser participantUser) {
        if (participantUser == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(participantUser);
        } catch (Exception e) {
            return null;
        }
    }
}