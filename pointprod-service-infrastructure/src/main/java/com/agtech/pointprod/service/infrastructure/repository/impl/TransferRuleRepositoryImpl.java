package com.agtech.pointprod.service.infrastructure.repository.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.agtech.pointprod.service.domain.model.TransferRule;
import com.agtech.pointprod.service.infrastructure.dal.converter.TransferRuleConverter;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.TransferRuleDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferRuleDO;
import com.agtech.pointprod.service.infrastructure.repository.TransferRuleRepository;


/**
 * Transfer rule repository implementation
 */
@Repository
public class TransferRuleRepositoryImpl implements TransferRuleRepository {
    
    @Resource
    private TransferRuleDAO transferRuleDAO;
    
    @Resource
    private TransferRuleConverter transferRuleConverter;
    
    /**
     * Query transfer rule list
     */
    @Override
    public TransferRule queryTransferRuleByGrade(String grade) {
        TransferRuleDO transferRuleDO = transferRuleDAO.queryTransferRuleByGrade(grade);
        return transferRuleConverter.do2Model(transferRuleDO);
    }

    /**
     * Query transfer rule list
     */
    @Override
    public List<TransferRule> selectTransferRuleList() {
        List<TransferRuleDO> transferRuleDOList = transferRuleDAO.selectTransferRuleList();
        return transferRuleConverter.doListToDomainList(transferRuleDOList);
    }

    /**
     * Add transfer rule
     */
    @Override
    public boolean addTransferRule(TransferRule rule) {
        TransferRuleDO transferRuleDO = transferRuleConverter.domainToDo(rule);
        boolean success = transferRuleDAO.save(transferRuleDO);
        return success;
    }

    /**
     * Update transfer rule
     */
    @Override
    public boolean updateTransferRule(TransferRule rule) {
        TransferRuleDO transferRuleDO = transferRuleConverter.domainToDo(rule);
        boolean success = transferRuleDAO.updateById(transferRuleDO);
        return success;
    }

    /**
     * Get transfer rule detail
     */
    @Override
    public TransferRule getTransferRuleDetail(String ruleId) {
        TransferRuleDO transferRuleDO = transferRuleDAO.getTransferRuleDetail(ruleId);
        return transferRuleConverter.do2Model(transferRuleDO);
    }

    /**
     * Delete transfer rule
     */
    @Override
    public boolean deleteTransferRule(String ruleId, String username) {
        TransferRuleDO transferRuleDO = transferRuleDAO.getTransferRuleDetail(ruleId);
        if (transferRuleDO == null) {
            return false;
        }

        transferRuleDO.setIsDeleted(1);
        transferRuleDO.setModifier(username);
        boolean success = transferRuleDAO.updateById(transferRuleDO);
        return success;
    }

    /**
     * Disable other rules with the same level
     */
    @Override
    public boolean disableOtherRules(String userLevel,String modify) {
        return transferRuleDAO.disableOtherRules(userLevel,modify);
    }

    /**
     * Check if it's the last enabled rule for this level
     */
    @Override
    public boolean isLastEnabledRule(String userLevel, String ruleId) {
        return transferRuleDAO.isLastEnabledRule(userLevel, ruleId);
    }
}
