package com.agtech.pointprod.service.infrastructure.integration.mpay.acl;


import com.agtech.pointprod.service.domain.model.MPayUserInfo;

import java.util.List;

public interface MPayAclService {

    MPayUserInfo getUserMsg(String custId);

    boolean checkSecurityId(String custId, String orderId, String securityId);

    MPayUserInfo getUserInfo(String areaCode, String phone);

    List<MPayUserInfo> getUserMsgList(List<String> custIds);
}
