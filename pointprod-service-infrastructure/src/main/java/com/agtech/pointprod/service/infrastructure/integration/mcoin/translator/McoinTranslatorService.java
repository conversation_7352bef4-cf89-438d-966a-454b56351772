package com.agtech.pointprod.service.infrastructure.integration.mcoin.translator;


import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinAccountDTO;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferInfo;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTransferResultDTO;

public interface McoinTranslatorService {


    McoinAccountDTO getMcoinAccount(String custId);

    McoinTransferResultDTO transferPoint(McoinTransferInfo transferInfo,
                                         MPayUserInfo payerUserInfo, MPayUserInfo payeeUserInfo);
    
    McoinTransferResultDTO queryTransfer(String outOrderId, String payeeCustId);
}
