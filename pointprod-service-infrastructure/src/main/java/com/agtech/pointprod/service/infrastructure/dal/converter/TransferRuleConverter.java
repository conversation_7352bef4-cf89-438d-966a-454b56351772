package com.agtech.pointprod.service.infrastructure.dal.converter;

import java.util.List;

import org.mapstruct.Mapper;

import com.agtech.pointprod.service.domain.model.TransferRule;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferRuleDO;

/**
 * Transfer rule converter
 */
@Mapper(componentModel = "spring")
public interface TransferRuleConverter {
    TransferRule do2Model(TransferRuleDO transferRuleDO);

    /**
     * Convert domain to DO
     *
     * @param transferRule Transfer rule domain
     * @return Transfer rule DO
     */
    TransferRuleDO domainToDo(TransferRule transferRule);

    /**
     * Convert DO list to domain list
     *
     * @param transferRuleDOList Transfer rule DO list
     * @return Transfer rule domain list
     */
    List<TransferRule> doListToDomainList(List<TransferRuleDO> transferRuleDOList);
}
