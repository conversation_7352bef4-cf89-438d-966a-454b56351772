package com.agtech.pointprod.service.infrastructure.repository.impl;

import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.model.BlackList;
import com.agtech.pointprod.service.infrastructure.dal.converter.BlackListConverter;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.BlackListDao;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.BlackListDO;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import com.agtech.pointprod.service.infrastructure.repository.BlackListRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/18 10:46
 */
@Repository
public class BlackListRepositoryImpl implements BlackListRepository {
    @Resource
    private BlackListDao blackListDao;
    @Resource
    private BlackListConverter blackListConverter;

    @Resource
    private BizSequenceRepository bizSequenceRepository;

    @Override
    public BlackList queryTransferBlackListByUserId(String userId) {
        BlackListDO blackListDO = blackListDao.queryTransferBlackListByUserId(userId);
        return blackListConverter.do2Model(blackListDO);
    }

    @Override
    public long countBlackList(String userId) {
        return blackListDao.countBlackList(userId);
    }

    @Override
    public BlackList selectBlackList(String entityType, String accountType, String entityId) {
        return blackListConverter.do2Model(blackListDao.selectBlackList(entityType, accountType, entityId));
    }

    @Override
    public List<BlackList> selectBlackLists(String entityType, String accountType, List<String> entityIds) {
        return blackListConverter.doList2ModelList(blackListDao.selectBlackLists(entityType, accountType, entityIds));
    }

    @Override
    public void save(BlackList blackList) {
        blackList.setBlackListId(bizSequenceRepository.getBizId(null, SequenceCodeEnum.BLACK_LIST));
        blackListDao.save(blackListConverter.model2Do(blackList));
    }

    @Override
    public void updateByEntity(BlackList blackList, String entityType, String accountType, String entityId) {
        blackListDao.updateByEntity(blackListConverter.model2Do(blackList), entityType, accountType, entityId);
    }

    @Override
    public List<String> selectAllEntityIdsInBlackList(String entityType, String accountType, List<String> entityIds) {
        return blackListDao.selectAllEntityIdsInBlackList(entityType, accountType, entityIds);
    }
}
