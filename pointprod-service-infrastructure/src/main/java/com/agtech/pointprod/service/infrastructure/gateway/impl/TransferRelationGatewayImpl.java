package com.agtech.pointprod.service.infrastructure.gateway.impl;

import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.gateway.TransferRelationGateway;
import com.agtech.pointprod.service.domain.model.TransferRelation;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import com.agtech.pointprod.service.infrastructure.repository.TransferRelationRepository;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 转赠关系领域网关实现
 */
@Component
@Slf4j
public class TransferRelationGatewayImpl implements TransferRelationGateway {
    
    @Resource
    private TransferRelationRepository transferRelationRepository;

    @Resource
    private BizSequenceRepository bizSequenceRepository;
    
    @Override
    public boolean saveTransferRelation(TransferRelation transferRelation) {
        try {
            // 设置业务ID
            if (StringUtils.isBlank(transferRelation.getTransferRelationId())) {
                String userId = transferRelation.getActorUserId();
                String transferRelationId = bizSequenceRepository.getBizId(userId, SequenceCodeEnum.TRANSFER_RELATION);
                transferRelation.setTransferRelationId(transferRelationId);
            }
            return transferRelationRepository.save(transferRelation);
        } catch (Exception e) {
            log.error("保存转赠关系失败", e);
            return false;
        }
    }
    
    @Override
    public TransferRelation queryByActorAndParticipant(String actorUserId, String participantUserId) {
        try {
            return transferRelationRepository.queryByActorAndParticipant(actorUserId, participantUserId);
        } catch (Exception e) {
            log.error("根据付款方和收款方查询转赠关系失败, actorUserId={}, participantUserId={}", 
                     actorUserId, participantUserId, e);
            return null;
        }
    }
    
    @Override
    public List<TransferRelation> queryLatestTransferRelations(String userId, Date startTime) {
        try {
            return transferRelationRepository.queryLatestTransferRalations(userId, startTime);
        } catch (Exception e) {
            log.error("查询用户最近转赠关系失败, userId={}, recentDay={}", userId, startTime, e);
            return null;
        }
    }
    
    @Override
    public boolean updateTransferRelation(TransferRelation transferRelation) {
        try {
            return transferRelationRepository.update(transferRelation);
        } catch (Exception e) {
            log.error("更新转赠关系失败, transferRelationId={}", 
                     transferRelation != null ? transferRelation.getTransferRelationId() : null, e);
            return false;
        }
    }
    
    @Override
    public TransferRelation queryByTransferRelationId(String transferRelationId) {
        try {
            return transferRelationRepository.queryByTransferRelationId(transferRelationId);
        } catch (Exception e) {
            log.error("根据业务ID查询转赠关系失败, transferRelationId={}", transferRelationId, e);
            return null;
        }
    }
}