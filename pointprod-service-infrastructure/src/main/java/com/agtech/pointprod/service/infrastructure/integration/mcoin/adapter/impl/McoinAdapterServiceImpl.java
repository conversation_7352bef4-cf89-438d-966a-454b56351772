package com.agtech.pointprod.service.infrastructure.integration.mcoin.adapter.impl;

import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinAccountDTO;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.McoinTransferInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;
import com.agtech.pointprod.service.infrastructure.dal.converter.McoinAccountConverter;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.adapter.McoinAdapterService;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.McoinTranslatorService;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTransferResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version McoinAdapterService.java, v0.1 2025/6/17 17:32 zhongqigang Exp $
 */
@Service
@Slf4j
public class McoinAdapterServiceImpl implements McoinAdapterService {
    @Resource
    private McoinTranslatorService mcoinTranslatorService;
    @Resource
    private McoinAccountConverter mcoinAccountConverter;

    @Override
    public McoinAccount queryMcoinAccount(String userId) {
        McoinAccountDTO mcoinAccountDTO = null;
        try {
            mcoinAccountDTO = mcoinTranslatorService.getMcoinAccount(userId);
        } catch (Exception e) {
            log.error("queryMcoinAccount error: {}", e.getMessage(), e);
        }
        return mcoinAccountConverter.toMcoinAccount(mcoinAccountDTO);
    }

    @Override
    public McoinTransferResult transferPoint(McoinTransferInfo transferInfo,
                                             MPayUserInfo payerUserInfo, MPayUserInfo payeeUserInfo) {
        try {
            McoinTransferResultDTO resultDTO = mcoinTranslatorService.transferPoint(transferInfo, payerUserInfo, payeeUserInfo);
            if (resultDTO != null) {
                return mcoinAccountConverter.toTransferResult(resultDTO);
            }
        } catch (PointProdBizException e) {
            log.info("transferPoint error: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("transferPoint error: {}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public McoinTransferResult queryTransfer(String outOrderId, String payeeCustId) {
        try {
            McoinTransferResultDTO resultDTO = mcoinTranslatorService.queryTransfer(outOrderId, payeeCustId);
            if (resultDTO != null) {
                return mcoinAccountConverter.toTransferResult(resultDTO);
            }
        } catch (PointProdBizException e) {
            log.info("queryTransfer error: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("queryTransfer error: {}", e.getMessage(), e);
        }
        return null;
    }
}
