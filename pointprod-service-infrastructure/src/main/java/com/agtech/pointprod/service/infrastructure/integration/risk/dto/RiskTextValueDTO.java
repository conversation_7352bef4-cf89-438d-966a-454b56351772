package com.agtech.pointprod.service.infrastructure.integration.risk.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class RiskTextValueDTO implements Serializable {
    private static final long serialVersionUID = 6221288963413591806L;
    private String tag;
    private String value;

    public RiskTextValueDTO(String tag, String value) {
        this.tag = tag;
        this.value = value;
    }

    public RiskTextValueDTO() {
    }
}
