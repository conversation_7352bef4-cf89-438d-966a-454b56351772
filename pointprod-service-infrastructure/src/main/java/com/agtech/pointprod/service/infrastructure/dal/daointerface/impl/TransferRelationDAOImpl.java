package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import com.agtech.pointprod.service.domain.common.enums.YesOrNoEnum;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.TransferRelationDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferRelationDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.TransferRelationMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 转赠用户关系DAO实现
 */
@Component
public class TransferRelationDAOImpl extends ServiceImpl<TransferRelationMapper, TransferRelationDO> implements TransferRelationDAO {
    
    @Override
    public List<TransferRelationDO> selectByActorUserId(String actorUserId) {
        LambdaQueryWrapper<TransferRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据付款方用户ID查询，只查询transfer_relation表
        queryWrapper.eq(TransferRelationDO::getActorUserId, actorUserId)
                   .eq(TransferRelationDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        // 排序：按创建时间倒序
        queryWrapper.orderByDesc(TransferRelationDO::getGmtCreate);
        
        return list(queryWrapper);
    }
    
    @Override
    public List<TransferRelationDO> selectByParticipantUserId(String participantUserId) {
        LambdaQueryWrapper<TransferRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据收款方用户ID查询，只查询transfer_relation表
        queryWrapper.eq(TransferRelationDO::getParticipantUserId, participantUserId)
                   .eq(TransferRelationDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        // 排序：按创建时间倒序
        queryWrapper.orderByDesc(TransferRelationDO::getGmtCreate);
        
        return list(queryWrapper);
    }
    
    @Override
    public TransferRelationDO selectByActorAndParticipant(String actorUserId, String participantUserId) {
        LambdaQueryWrapper<TransferRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据付款方和收款方用户ID查询唯一关系
        queryWrapper.eq(TransferRelationDO::getActorUserId, actorUserId)
                   .eq(TransferRelationDO::getParticipantUserId, participantUserId)
                   .eq(TransferRelationDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        return getOne(queryWrapper);
    }
    
    @Override
    public TransferRelationDO selectByTransferRelationId(String transferRelationId) {
        LambdaQueryWrapper<TransferRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据业务ID查询
        queryWrapper.eq(TransferRelationDO::getTransferRelationId, transferRelationId)
                   .eq(TransferRelationDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        return getOne(queryWrapper);
    }
    
    @Override
    public List<TransferRelationDO> selectByUserId(String userId) {
        LambdaQueryWrapper<TransferRelationDO> queryWrapper = new LambdaQueryWrapper<>();

        // 查询用户作为付款方或收款方的所有关系
        queryWrapper.and(wrapper -> wrapper
                        .eq(TransferRelationDO::getActorUserId, userId)
                        .or()
                        .eq(TransferRelationDO::getParticipantUserId, userId))
                .eq(TransferRelationDO::getIsDeleted, YesOrNoEnum.NO.getValue());

        // 排序：按创建时间倒序
        queryWrapper.orderByDesc(TransferRelationDO::getGmtCreate);

        return list(queryWrapper);
    }

    @Override
    public List<TransferRelationDO> queryLatestTransferRalations(String userId, Date startTime) {
        LambdaQueryWrapper<TransferRelationDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TransferRelationDO::getActorUserId, userId);
        lambdaQueryWrapper.ge(TransferRelationDO::getGmtCreate, startTime);
        lambdaQueryWrapper.orderByDesc(TransferRelationDO::getGmtModified);
        return this.list(lambdaQueryWrapper);
    }
} 