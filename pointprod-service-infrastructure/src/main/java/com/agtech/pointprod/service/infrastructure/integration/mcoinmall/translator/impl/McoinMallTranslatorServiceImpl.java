package com.agtech.pointprod.service.infrastructure.integration.mcoinmall.translator.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.agtech.common.result.GenericResult;
import com.agtech.common.util.log.BaseDigestLog;
import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.dto.MPayAppTokenResponse;
import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.translator.McoinMallClient;
import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.translator.McoinMallTranslatorService;
import com.agtech.pointprod.service.infrastructure.integration.template.BaseWrapperClientCallback;
import com.agtech.pointprod.service.infrastructure.integration.template.ClientTemplate;

import lombok.extern.slf4j.Slf4j;

/**
 * MCoin Mall 翻译服务实现
 */
@Slf4j
@Service
public class McoinMallTranslatorServiceImpl implements McoinMallTranslatorService {
    
    @Resource
    private McoinMallClient mcoinMallClient;
    
    @Override
    public GenericResult<MPayAppTokenResponse> getAppToken() {
        return ClientTemplate.execute(new BaseWrapperClientCallback<GenericResult<MPayAppTokenResponse>>() {
            
            @Override
            public BaseDigestLog composeDigestLog(long timeCost, GenericResult<MPayAppTokenResponse> result) {
                log.info("McoinMall getAppToken - timeCost: {}ms, success: {}", 
                        timeCost, result != null && result.isSuccess());
                return null;
            }
            
            @Override
            protected GenericResult<MPayAppTokenResponse> doExecute() {
                try {
                    MPayAppTokenResponse response = mcoinMallClient.getAppToken();
                    log.info("Successfully got app token from McoinMall");
                    
                    GenericResult<MPayAppTokenResponse> genericResult = new GenericResult<>();
                    genericResult.setSuccess(true);
                    genericResult.setValue(response);
                    return genericResult;
                } catch (Exception e) {
                    log.error("Failed to get app token from McoinMall", e);
                    return new GenericResult<>(); // Return empty result for failure
                }
            }
            
            @Override
            protected boolean isSuccess(GenericResult<MPayAppTokenResponse> result) {
                if (result == null || !result.isSuccess()) {
                    return false;
                }
                MPayAppTokenResponse data = result.getValue();
                return data != null && data.getAppToken() != null;
            }
        });
    }
} 