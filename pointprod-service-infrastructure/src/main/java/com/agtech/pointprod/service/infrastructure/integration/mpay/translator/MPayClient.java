package com.agtech.pointprod.service.infrastructure.integration.mpay.translator;

import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.CheckSecurityIdRequest;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.MPayUserInfoListRequest;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.MPayUserInfoRequest;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "mPayClient",
        url = "${mPay.gateway.url:}",
        configuration = MPayFeignConfig.class)
public interface MPayClient {


    @PostMapping(value ="/mpay/umc/merGateway/security/getUserMsg")
    Response getUserMsg(@RequestBody MPayUserInfoRequest mPayUserInfoRequest);


    @PostMapping(value = "/mpay/umc/merGateway/security/checkSecurityId")
    Response checkSecurityId(@RequestBody CheckSecurityIdRequest request);

    @PostMapping(value ="/mpay/umc/merGateway/security/getUserMsgList")
    Response getUserMsgList(@RequestBody MPayUserInfoListRequest mPayUserInfoListRequest);

}
