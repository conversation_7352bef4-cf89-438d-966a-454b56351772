package com.agtech.pointprod.service.infrastructure.dal.converter;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.agtech.pointprod.service.domain.common.enums.DeletedEnum;
import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.model.TaskConfig;
import com.agtech.pointprod.service.domain.model.TaskData;
import com.agtech.pointprod.service.domain.model.TaskRetry;
import com.agtech.pointprod.service.domain.model.TaskRetry.TaskRetryBuilder;
import com.agtech.pointprod.service.facade.dto.enums.TaskRetryStatusEnum;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TaskRetryDO;

@Mapper(componentModel = "spring", uses = {TaskRetryConverterHelper.class})
public interface TaskRetryConverter {

    @Mapping(target = "resourceType", expression = "java(resourceTypeToString(taskRetry.getResourceType()))")
    @Mapping(target = "status", expression = "java(statusToString(taskRetry.getStatus()))")
    @Mapping(target = "isDeleted", expression = "java(deletedEnumToInteger(taskRetry.getIsDeleted()))")
    @Mapping(target = "taskConfig", expression = "java(taskConfigToJson(taskRetry.getTaskConfig(), converterHelper))")
    @Mapping(target = "taskData", expression = "java(taskDataToJson(taskRetry.getTaskData(), converterHelper))")
    TaskRetryDO toTaskRetryDO(TaskRetry taskRetry, TaskRetryConverterHelper converterHelper);
    
    default <T extends TaskConfig, D extends TaskData> TaskRetry toTaskRetry(TaskRetryDO taskRetryDO, Class<T> taskConfigClass, Class<D> taskDataClass, TaskRetryConverterHelper converterHelper) {
        if ( taskRetryDO == null ) {
            return null;
        }
        TaskRetryBuilder taskRetry = TaskRetry.builder();

        taskRetry.gmtCreate( taskRetryDO.getGmtCreate() );
        taskRetry.gmtModified( taskRetryDO.getGmtModified() );
        taskRetry.isDeleted( integerToDeletedEnum(taskRetryDO.getIsDeleted()) );
        taskRetry.maxTryCount( taskRetryDO.getMaxTryCount() );
        taskRetry.nextRetry( taskRetryDO.getNextRetry() );
        taskRetry.resourceId( taskRetryDO.getResourceId() );
        taskRetry.taskRetryId( taskRetryDO.getTaskRetryId() );
        taskRetry.tryCount( taskRetryDO.getTryCount() );

        taskRetry.resourceType( stringToResourceType(taskRetryDO.getResourceType()) );
        taskRetry.status( stringToStatus(taskRetryDO.getStatus()) );
        taskRetry.taskConfig( jsonToTaskConfig(taskRetryDO.getTaskConfig(), taskConfigClass, converterHelper) );
        taskRetry.taskData( jsonToTaskData(taskRetryDO.getTaskData(), taskDataClass, converterHelper) );
        taskRetry.taskDataString( taskRetryDO.getTaskData() );

        return taskRetry.build();
    }
    
    default String resourceTypeToString(TaskResouceTypeEnum resourceType) {
        return resourceType != null ? resourceType.getCode() : null;
    }
    
    default TaskResouceTypeEnum stringToResourceType(String resourceType) {
        if (resourceType == null) {
            return null;
        }
        for (TaskResouceTypeEnum type : TaskResouceTypeEnum.values()) {
            if (type.getCode().equals(resourceType)) {
                return type;
            }
        }
        return null;
    }
    
    default String statusToString(TaskRetryStatusEnum status) {
        return status != null ? status.getValue() : null;
    }
    
    default TaskRetryStatusEnum stringToStatus(String status) {
        if (status == null) {
            return null;
        }
        for (TaskRetryStatusEnum statusEnum : TaskRetryStatusEnum.values()) {
            if (statusEnum.getValue().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }
    
    default Integer deletedEnumToInteger(DeletedEnum deletedEnum) {
        return deletedEnum != null ? deletedEnum.getCode() : null;
    }
    
    default DeletedEnum integerToDeletedEnum(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeletedEnum deletedEnum : DeletedEnum.values()) {
            if (deletedEnum.getCode().equals(code)) {
                return deletedEnum;
            }
        }
        return null;
    }
    
    /**
     * 将TaskConfig对象转换为JSON字符串
     * MapStruct会自动注入TaskRetryConverterHelper实例
     */
    default String taskConfigToJson(TaskConfig taskConfig, TaskRetryConverterHelper converterHelper) {
        return converterHelper.taskConfigToJson(taskConfig);
    }
    
    /**
     * 将JSON字符串转换为TaskConfig对象
     * MapStruct会自动注入TaskRetryConverterHelper实例
     */
    default <T extends TaskConfig> T jsonToTaskConfig(String json, Class<T> taskConfigClass, TaskRetryConverterHelper converterHelper) {
        return converterHelper.jsonToTaskConfig(json, taskConfigClass);
    }
    
    /**
     * 将TaskData对象转换为JSON字符串
     * MapStruct会自动注入TaskRetryConverterHelper实例
     */
    default String taskDataToJson(TaskData taskData, TaskRetryConverterHelper converterHelper) {
        return converterHelper.taskDataToJson(taskData);
    }
    
    /**
     * 将JSON字符串转换为TaskData对象
     * MapStruct会自动注入TaskRetryConverterHelper实例
     */
    default <D extends TaskData> D jsonToTaskData(String json, Class<D> taskDataClass, TaskRetryConverterHelper converterHelper) {
        return converterHelper.jsonToTaskData(json, taskDataClass);
    }
}