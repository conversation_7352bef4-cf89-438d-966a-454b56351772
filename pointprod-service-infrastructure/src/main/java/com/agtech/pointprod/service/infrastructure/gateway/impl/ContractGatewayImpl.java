package com.agtech.pointprod.service.infrastructure.gateway.impl;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.gateway.ContractGateway;
import com.agtech.pointprod.service.domain.model.Contract;
import com.agtech.pointprod.service.domain.model.ContractConfirm;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import com.agtech.pointprod.service.infrastructure.repository.ContractConfirmRepository;
import com.agtech.pointprod.service.infrastructure.repository.ContractRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class ContractGatewayImpl implements ContractGateway {
    @Resource
    private ContractRepository contractRepository;
    @Resource
    private ContractConfirmRepository contractConfirmRepository;
    @Resource
    private BizSequenceRepository bizSequenceRepository;

    @Override
    public Contract queryLatestContractByBizType(String bizType) {
        return contractRepository.queryLatestContractByBizType(bizType);
    }

    @Override
    public ContractConfirm queryContractConfirmByUserIdAndContractId(String userId, String contractId) {
        return contractConfirmRepository.queryContractConfirmByUserIdAndContractId(userId, contractId);
    }


    @Override
    public boolean updateContractConfirmForUser(String userId, String contractId, String status) {
        return contractConfirmRepository.updateContractConfirmForUser(userId, contractId, status);
    }

    @Override
    public boolean saveContractConfirm(String userId, String contractId, String status) {
        ContractConfirm contractConfirm = new ContractConfirm();
        contractConfirm.setContractConfirmId(bizSequenceRepository.getBizId(userId, SequenceCodeEnum.CONTRACT_CONFIRM));
        contractConfirm.setContractId(contractId);
        contractConfirm.setStatus(status);
        contractConfirm.setUserId(userId);
        Date date = ZonedDateUtil.now();
        contractConfirm.setGmtCreate(date);
        contractConfirm.setGmtModified(date);
        return contractConfirmRepository.saveContractConfirm(contractConfirm);
    }

    @Override
    public Contract queryContractByContractId(String contractId) {
        return contractRepository.queryContractByContractId(contractId);
    }
    
    @Override
    public boolean saveContract(Contract contract) {
        return contractRepository.saveContract(contract);
    }
}
