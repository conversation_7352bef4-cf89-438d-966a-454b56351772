package com.agtech.pointprod.service.infrastructure.integration.mcoin.adapter;

import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.McoinTransferInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;

/**
 * <AUTHOR>
 * @version McoinAdapterService.java, v0.1 2025/6/17 17:32 zhongqigang Exp $
 */
public interface McoinAdapterService {
    McoinAccount queryMcoinAccount(String userId);

    McoinTransferResult transferPoint(McoinTransferInfo transferInfo,
                                      MPayUserInfo payerUserInfo, MPayUserInfo payeeUserInfo);
    
    McoinTransferResult queryTransfer(String outOrderId, String payeeCustId);
}
