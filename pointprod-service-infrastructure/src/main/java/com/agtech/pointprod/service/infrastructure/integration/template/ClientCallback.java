package com.agtech.pointprod.service.infrastructure.integration.template;

import com.agtech.common.result.BaseResult;
import com.agtech.common.util.log.BaseDigestLog;

/**
 * integration调用下游接口
 *
 * <AUTHOR>
 * @version $Id: ClientCallback.java, v 0.1 2024年7月23日 14:39:29 zhongqiang Exp $
 */
public abstract interface ClientCallback<T extends BaseResult> {
    /**
     * 调用下游方法
     *
     * @return
     */
    public abstract T execute();

    /**
     * 调用外部方法打印摘要日志
     *
     * @param timeCost
     * @param result
     * @return
     */
    public abstract BaseDigestLog composeDigestLog(long timeCost, T result);
}