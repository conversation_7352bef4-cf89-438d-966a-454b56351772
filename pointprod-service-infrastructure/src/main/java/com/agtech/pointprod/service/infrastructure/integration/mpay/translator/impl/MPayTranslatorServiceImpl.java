package com.agtech.pointprod.service.infrastructure.integration.mpay.translator.impl;

import com.agtech.common.lang.util.StringUtil;
import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.*;
import com.agtech.pointprod.service.infrastructure.integration.mpay.enums.MpayErrorEnum;
import com.agtech.pointprod.service.infrastructure.integration.mpay.translator.MPayApiTemplate;
import com.agtech.pointprod.service.infrastructure.integration.mpay.translator.MPayClient;
import com.agtech.pointprod.service.infrastructure.integration.mpay.translator.MPayPushClient;
import com.agtech.pointprod.service.infrastructure.integration.mpay.translator.MPayTranslatorService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MPayTranslatorServiceImpl implements MPayTranslatorService {

    @Resource
    private MPayClient mPayClient;

    @Resource
    private MPayApiTemplate mPayApiTemplate;

    @Value("${mPay.gateway.url:}")
    private String mPayGatewayUrl;

    @Resource
    private MPayPushClient mPayPushClient;

    @Override
    public GenericResult<UserMsgDTO> getUserInfo(MPayUserInfoRequest mPayUserInfoRequest) {
        String mpayResponse = null;
        long startTime = System.currentTimeMillis();
        try {
            mpayResponse = mPayApiTemplate.execute(
                    () -> mPayClient.getUserMsg(mPayUserInfoRequest)
            );
            long endTime = System.currentTimeMillis();
            log.info("[請求mPay獲取用戶信息] url:{}{}, request: {}, response:{}, cost:{}ms",
                    mPayGatewayUrl, "/mpay/umc/merGateway/security/getUserMsg", JSON.toJSONString(mPayUserInfoRequest), mpayResponse, endTime - startTime);
        } catch (Exception e){
            long endTime = System.currentTimeMillis();
            log.error("[請求mPay獲取用戶信息] 發生異常, url:{}{}, request: {}, errMsg:{}, cost:{}ms",
                    mPayGatewayUrl, "/mpay/umc/merGateway/security/getUserMsg", JSON.toJSONString(mPayUserInfoRequest), e.getMessage(), endTime - startTime, e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MPAY_SERVICE_FAIL);
        }

        if(StringUtil.isBlank(mpayResponse)) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MPAY_SERVICE_FAIL);
        }

        MPayBaseResponse<UserMsgDTO> mPayBaseResponse = JSON.parseObject(mpayResponse,
                new TypeReference<MPayBaseResponse<UserMsgDTO>>() {
                });

        if (mPayBaseResponse == null || mPayBaseResponse.getResult() == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MPAY_SERVICE_FAIL);
        }
        baseResponseProcess(mPayBaseResponse);
        return GenericResult.success(mPayBaseResponse.getData());
    }

    @Override
    public GenericResult<CheckSecurityIdDTO> checkSecurityId(String custId, String orderId, String securityId) {
        CheckSecurityIdRequest request = new CheckSecurityIdRequest();
        request.setCustId(custId);
        request.setBusinessId(orderId);
        request.setSecurityId(securityId);
        String mpayResponse = mPayApiTemplate.execute(
                () -> mPayClient.checkSecurityId(request)
        );
        if(StringUtil.isBlank(mpayResponse)) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MPAY_SERVICE_FAIL, "mpay checkSecurityId service response empty");
        }
        MPayBaseResponse<CheckSecurityIdDTO> mPayBaseResponse = JSON.parseObject(mpayResponse,
                new TypeReference<MPayBaseResponse<CheckSecurityIdDTO>>() {
                });
        if (mPayBaseResponse == null || mPayBaseResponse.getResult() == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MPAY_SERVICE_FAIL, "mpay checkSecurityId service returns null");
        }
        baseResponseProcess(mPayBaseResponse);
        return GenericResult.success(mPayBaseResponse.getData());
    }

    @Override
    public GenericResult<PushMsgResponse> pushMessage(PushMsgRequest request) {
        PushMsgResponse response = mPayPushClient.pushMessage(request);
        return GenericResult.success(response);
    }

    @Override
    public GenericResult<List<UserMsgDTO>> getUserInfoList(MPayUserInfoListRequest mPayUserInfoListRequest) {
        String mpayResponse = mPayApiTemplate.execute(
                () -> mPayClient.getUserMsgList(mPayUserInfoListRequest)
        );
        if(StringUtil.isBlank(mpayResponse)) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MPAY_SERVICE_FAIL);
        }
        MPayBaseResponse<List<UserMsgDTO>> mPayBaseResponse = JSON.parseObject(mpayResponse,
                new TypeReference<MPayBaseResponse<List<UserMsgDTO>>>() {});
        if (mPayBaseResponse == null || mPayBaseResponse.getResult() == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MPAY_SERVICE_FAIL);
        }
        baseResponseProcess(mPayBaseResponse);
        return GenericResult.success(mPayBaseResponse.getData());
    }

    private void baseResponseProcess(MPayBaseResponse<?> response){
        MPayBaseResult mPayBaseResult = null;
        if (response == null || (mPayBaseResult = response.getResult()) == null){
            return;
        }
        AssertUtil.assertFalse(MpayErrorEnum.REQUEST_TRAFFIC_EXCEED_LIMIT.equalsError(mPayBaseResult.getCode(), mPayBaseResult.getSubCode()), PointProdBizErrorCodeEnum.REQUEST_TRAFFIC_EXCEED_LIMIT);
    }
}
