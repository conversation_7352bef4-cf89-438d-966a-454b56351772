package com.agtech.pointprod.service.infrastructure.config;

import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

/**
 * RabbitMQ队列配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "rabbitmq.queues")
@Getter
@Setter
@RefreshScope
public class RabbitMQConfig {
    
    private Map<String, QueueConfig> config;

    
    /**
     * 根据队列名称获取配置
     */
    public QueueConfig getQueueConfig(String queueName) {
        return config.get(queueName);
    }

    
    @Getter
    @Setter
    public static class QueueConfig {
        private String exchange;
        private String routingKey;
        private String delayExchange;
        private String delayRoutingKey;
        private String queue;
    }
}