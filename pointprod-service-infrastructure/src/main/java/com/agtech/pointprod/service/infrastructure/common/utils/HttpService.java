//package com.agtech.pointprod.service.infrastructure.common.utils;
//
//
//import cn.hutool.core.map.MapUtil;
//import cn.hutool.crypto.SecureUtil;
//import cn.hutool.http.ContentType;
//import cn.hutool.http.HttpRequest;
//import cn.hutool.http.HttpResponse;
//import cn.hutool.http.HttpUtil;
//import com.agtech.common.lang.util.StringUtil;
//import com.alibaba.fastjson2.JSON;
//import com.alibaba.fastjson2.TypeReference;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StopWatch;
//
//import java.net.SocketTimeoutException;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @date 2023-11-29
// */
//@Component
//@Slf4j
//public class HttpService {
//    public static String sendPostRequest(String url, Object param) {
//        try {
//            return sendPostRequest(url, param, false);
//        } catch (Exception e) {
//            return null;
//        }
//    }
//
//    public static String sendPostRequest(String url, Object param, boolean canThrowException) throws Exception {
//        StopWatch stopWatch  = new StopWatch();
//        stopWatch.start();
//        String requestJsonBody = "";
//        String response = null;
//        try {
//            if(param instanceof String) {
//                requestJsonBody = String.valueOf(param);
//            } else {
//                requestJsonBody = JSON.toJSONString(param);
//            }
//
//            Map<String,String> headerMap = new HashMap<>();
//            HttpRequest request = HttpUtil.createPost(url)
//                    .timeout(6000)
//                    .body(requestJsonBody, ContentType.JSON.getValue())
//                    .addHeaders(headerMap);
//            try (HttpResponse httpResponse = request.execute()) {
//                response = httpResponse.body();
//            }
//            stopWatch.stop();
//            log.info("http post url: {}, request: {}, response: {}, costTime:{}ms", url, requestJsonBody,  response, stopWatch.getTotalTimeMillis());
//        } catch (Exception e) {
//            stopWatch.stop();
//            log.error("http post error url: {}, request: {}, exception message:{}, costTime:{}ms", url, requestJsonBody, e.getMessage(), stopWatch.getTotalTimeMillis(), e);
//            if (canThrowException){
//                throw e;
//            }
//        }
//        return response;
//    }
//
//    public static String sendGetRequest(String url) {
//        try {
//            return sendGetRequest(url, false);
//        } catch (Exception e) {
//            return null;
//        }
//    }
//
//    public static String sendGetRequest(String url, boolean canThrowException) throws Exception {
//        StopWatch stopWatch  = new StopWatch();
//        stopWatch.start();
//        String response = null;
//        try {
//            Map<String,String> headerMap = new HashMap<>();
//            headerMap.put("Content-Type", "application/json");
//            HttpRequest request = HttpUtil.createGet(url)
//                    .timeout(6000)
//                    .addHeaders(headerMap);
//            try (HttpResponse httpResponse = request.execute()) {
//                response = httpResponse.body();
//            }
//            stopWatch.stop();
//            log.info("http get url: {}, response: {}, costTime:{}ms", url,  response, stopWatch.getTotalTimeMillis());
//        } catch (Exception e) {
//            stopWatch.stop();
//            log.error("http get error url: {}, exception message:{}, costTime:{}ms", url, e.getMessage(), stopWatch.getTotalTimeMillis(), e);
//            if (canThrowException){
//                throw e;
//            }
//        }
//        return response;
//    }
//
//
//    public static String getCustIdByOpenId(String url,String appId,String md5Key,String openId) {
//        //调用范辉文提供的后门接口进行查询
//        if(StringUtils.isAnyBlank(url,appId,md5Key)){
//            return null;
//        }
//        Map<String, String> signMap = new HashMap<>();
//        signMap.put("openid",openId);
//        signMap.put("appid",appId);
//        String signSource = MapUtil.sortJoin(signMap, "&", "=", true);
//        String sign = SecureUtil.md5(signSource+md5Key);
//        log.info("getuserid req,url:{},openid:{},appid:{},sign:{}",url,openId,appId,sign);
//        HttpResponse execute = HttpRequest.post(url)
//                .header("Content-Type", "application/x-www-form-urlencoded")
//                .form("openid", openId)
//                .form("appid", appId)
//                .form("sign", sign)
//                .execute();
//        String dataListRst = execute.body();
//        log.info("getuserid rsp,dataListRst:{}",dataListRst);
//        if(StringUtils.isNotBlank(dataListRst)){
//            return JSON.parseObject(dataListRst).getString("userId");
//        }else{
//            return null;
//        }
//    }
//
//
//    public static <T> T get(String url, TypeReference<T> typeReference) {
//        try {
//            if(StringUtil.isBlank(url)){
//                log.error("url is empty");
//                throw new AppletBizException(AppletErrorCodeEnum.HTTP_REQUEST_ERROR);
//            }
//
//            String responseBodyString = sendGetRequest(url, true);
//
//            if(StringUtil.isBlank(responseBodyString)) {
//                log.error("response body is empty");
//                throw new AppletBizException(AppletErrorCodeEnum.HTTP_REQUEST_ERROR);
//            }
//
//            return JSON.parseObject(responseBodyString, typeReference);
//        }catch (Exception e){
//            log.error("http get exception, url: {}, exception message: {}", url, e.getMessage(), e);
//            throw formatException(e);
//        }
//    }
//
//    public static <T> T post(String url, Object param, TypeReference<T> typeReference) {
//        try {
//            if(StringUtil.isBlank(url)){
//                log.error("url is empty");
//                throw new AppletBizException(AppletErrorCodeEnum.HTTP_REQUEST_ERROR);
//            }
//
//            String responseBodyString = sendPostRequest(url, param, true);
//
//            if(StringUtil.isBlank(responseBodyString)) {
//                log.error("response body is empty");
//                throw new AppletBizException(AppletErrorCodeEnum.HTTP_REQUEST_ERROR);
//            }
//
//            return JSON.parseObject(responseBodyString, typeReference);
//        }catch (Exception e){
//            log.error("http post exception, url: {}, exception message: {}", url, e.getMessage(), e);
//            throw formatException(e);
//        }
//    }
//
//    private static AppletBizException formatException(Exception e){
//        if (e instanceof SocketTimeoutException){
//            return new AppletBizException(AppletErrorCodeEnum.REQUEST_TIMEOUT_ERROR);
//        }
//        // 組件/框架有時會對SocketTimeoutException做一層RuntimeException的包裝
//        if (e.getCause() instanceof SocketTimeoutException){
//            return new AppletBizException(AppletErrorCodeEnum.REQUEST_TIMEOUT_ERROR);
//        }
//        return new AppletBizException(AppletErrorCodeEnum.HTTP_REQUEST_ERROR);
//    }
//}