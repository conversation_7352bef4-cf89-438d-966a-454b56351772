package com.agtech.pointprod.service.infrastructure.common.constant;

public class MDCConstants {

    public static final String MDC_KEY_SERVICE = "service";
    public static final String MDC_KEY_SERVICE_NAME = "serviceName";
    public static final String MDC_KEY_REQUEST_TIME = "requestTime";
    public static final String MDC_KEY_ELAPSED_TIME = "elapsedTime";
    public static final String MDC_KEY_HTTP_RESPONSE_PAYLOAD = "http.response.payload";
    public static final String MDC_KEY_CODE = "code";
    public static final String MDC_KEY_SUB_MSG = "subMsg";
    public static final String MDC_KEY_SUB_CODE = "subCode";





    public static final String SERVICE_NAME_LIMIT_CHECK = "limitCheck";
    public static final String SERVICE_NAME_LIMIT_CONSULT = "limitConsult";
    public static final String SERVICE_NAME_LIMIT_ACCUMULATE = "limitAccumulate";
    public static final String SERVICE_NAME_LIMIT_SELECT_CUMULATE_RULE_LIST = "limitSelectCumulateRuleList";
    public static final String SERVICE_NAME_LIMIT_ADD_CUMULATE_RULE = "limitAddCumulateRule";
    public static final String SERVICE_NAME_LIMIT_UPDATE_CUMULATE_RULE = "limitUpdateCumulateRule";
    public static final String SERVICE_NAME_LIMIT_GET_CUMULATE_RULE_DETAIL = "limitGetCumulateRuleDetail";
    public static final String SERVICE_NAME_LIMIT_DELETE_CUMULATE_RULE = "limitDeleteCumulateRule";
    public static final String SERVICE_NAME_GET_ORDER_TOKEN = "getOrderToken";
    public static final String SERVICE_NAME_CREATE_ORDER = "createOrder";
    public static final String SERVICE_NAME_CONSULT_PAY_VIEW = "consultPayView";
    public static final String SERVICE_NAME_ORDER_DETAIL = "orderDetail";
    public static final String SERVICE_NAME_ORDER_CREATED = "orderCreated";
    public static final String SERVICE_NAME_PAY_TIMEOUT = "payTimeout";
    public static final String SERVICE_NAME_TRANSFER_PAYMENT = "transferPayment";
    public static final String SERVICE_NAME_TRANSFER_PAYMENT_QUERY = "transferPaymentQuery";
    public static final String SERVICE_NAME_GET_LATEST_VALID_CONTRACT = "getLatestValidContract";
    public static final String SERVICE_NAME_UPDATE_CONTRACT = "updateContract";
    public static final String SERVICE_NAME_QUERY_CONTRACT = "queryContract";
    public static final String SERVICE_NAME_USER_UPDATE_CONTRACT = "userUpdateContract";
    public static final String SERVICE_NAME_TRANSFER_ONE_STAGE_ACCUMULATION = "transferOneStageAccumulation";
    public static final String SERVICE_NAME_CHECK_GRAY_ADMIN = "checkGrayAdmin";
    public static final String SERVICE_NAME_CHECK_GRAY_USER = "checkGrayUser";
    public static final String SERVICE_NAME_QUERY_NOTIFICATION_LIST = "queryNotificationList";
    public static final String SERVICE_NAME_READ_NOTIFICATION = "readNotification";
    public static final String SERVICE_NAME_GET_NOTIFICATION_DETAIL = "getNotificationDetail";
    public static final String SERVICE_NAME_PUSH_NOTIFICATION = "pushNotification";
    public static final String SERVICE_NAME_CREATE_SHARE_LINK= "createShareLink";
    public static final String SERVICE_NAME_TRANSFER_SHORT_TO_LONG_SHARE_LINK = "transferShortToLongShareLink";
    public static final String SERVICE_NAME_QUERY_VALID_TEMPLATE_LIST_BY_PAGE = "queryValidTemplateListByPage";
    public static final String SERVICE_NAME_QUERY_TEMPLATE_FULL_INFO_LIST = "queryTemplateFullInfoList";
    public static final String SERVICE_NAME_ADD_TEMPLATE = "addTemplate";
    public static final String SERVICE_NAME_UPDATE_TEMPLATE = "updateTemplate";
    public static final String SERVICE_NAME_DELETE_TEMPLATE = "deleteTemplate";
    public static final String SERVICE_NAME_QUERY_TRANSFER_RECORDS_LIST = "queryTransferRecordsList";
    public static final String SERVICE_NAME_QUERY_TRANSFERRED_USER_LIST = "queryTransferredUserList";
    public static final String SERVICE_NAME_UPDATE_USER_BLACK_LIST_STATUS = "updateUserBlackListStatus";
    public static final String SERVICE_NAME_SAVE_TRANSFER_RELATION = "saveTransferRelation";
    public static final String SERVICE_NAME_QUERY_TRANSFER_USER_INFO = "queryTransferUserInfo";
    public static final String SERVICE_NAME_PAYEE_INFO_VALIDATE = "payeeInfoValidate";
    public static final String SERVICE_NAME_PAYER_INFO_VALIDATE = "payerInfoValidate";
    public static final String SERVICE_NAME_QUERY_HISTORY_PAYEE_LIST = "queryHistoryPayeeList";


}
