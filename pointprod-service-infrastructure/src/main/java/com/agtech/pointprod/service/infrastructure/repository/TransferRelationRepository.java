package com.agtech.pointprod.service.infrastructure.repository;

import com.agtech.pointprod.service.domain.model.TransferRelation;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/12 10:46
 */
public interface TransferRelationRepository {

    /**
     * 查询用户最近的转赠关系
     *
     * @param userId    用户ID
     * @param startTime
     * @return 转赠关系列表
     */
    List<TransferRelation> queryLatestTransferRalations(String userId, Date startTime);
    
    /**
     * 保存转赠关系
     * @param transferRelation 转赠关系
     * @return 保存成功与否
     */
    boolean save(TransferRelation transferRelation);
    
    /**
     * 根据付款方和收款方查询转赠关系
     * @param actorUserId 付款方ID
     * @param participantUserId 收款方ID
     * @return 转赠关系
     */
    TransferRelation queryByActorAndParticipant(String actorUserId, String participantUserId);
    
    /**
     * 更新转赠关系
     * @param transferRelation 转赠关系
     * @return 更新成功与否
     */
    boolean update(TransferRelation transferRelation);
    
    /**
     * 根据业务ID查询转赠关系
     * @param transferRelationId 业务ID
     * @return 转赠关系
     */
    TransferRelation queryByTransferRelationId(String transferRelationId);
}
