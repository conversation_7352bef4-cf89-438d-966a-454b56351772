package com.agtech.pointprod.service.infrastructure.common.utils;

/**
 * <AUTHOR>
 * @version NumberUtil.java, v0.1 2025/6/23 18:49 zhongqigang Exp $
 */
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Locale;

public class NumberUtil {

    /**
     * 格式化 BigDecimal 为字符串，要求：
     * - 有小数则保留两位，超过两位使用银行家舍入法
     * - 整数不显示小数位
     * - 使用千分位分隔符
     *
     * @param value 需要格式化的 BigDecimal 值
     * @return 格式化后的字符串
     */
    public static String formatBigDecimal(BigDecimal value) {
        if (value == null) {
            return "0";
        }

        // 判断是否为整数
        BigDecimal stripped = value.stripTrailingZeros();
        boolean isInteger = stripped.scale() <= 0;

        // 处理舍入和补零
        BigDecimal roundedValue;
        if (isInteger) {
            roundedValue = stripped;
        } else {
            roundedValue = value.setScale(2, RoundingMode.HALF_EVEN);
        }

        // 创建 NumberFormat 实例
        NumberFormat format = NumberFormat.getNumberInstance(Locale.US);
        format.setGroupingUsed(true); // 启用千分位分隔符

        // 设置小数位数
        if (isInteger) {
            format.setMaximumFractionDigits(0);
            format.setMinimumFractionDigits(0);
        } else {
            format.setMaximumFractionDigits(2);
            format.setMinimumFractionDigits(2);
        }

        return format.format(roundedValue);
    }

    // 示例测试
    public static void main(String[] args) {
        System.out.println(formatBigDecimal(new BigDecimal("1000")));         // 输出: 1,000
        System.out.println(formatBigDecimal(new BigDecimal("1000.0")));       // 输出: 1,000
        System.out.println(formatBigDecimal(new BigDecimal("1000.00")));      // 输出: 1,000
        System.out.println(formatBigDecimal(new BigDecimal("1000.123")));     // 输出: 1,000.12
        System.out.println(formatBigDecimal(new BigDecimal("1000.125")));     // 输出: 1,000.12
        System.out.println(formatBigDecimal(new BigDecimal("1000.135")));     // 输出: 1,000.14
        System.out.println(formatBigDecimal(new BigDecimal("1234.5")));       // 输出: 1,234.50
        System.out.println(formatBigDecimal(new BigDecimal("1234.5678")));    // 输出: 1,234.57
    }
}

