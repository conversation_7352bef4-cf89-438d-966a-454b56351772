package com.agtech.pointprod.service.infrastructure.gateway.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.agtech.pointprod.service.domain.gateway.TransferRuleGateway;
import com.agtech.pointprod.service.domain.model.TransferRule;
import com.agtech.pointprod.service.infrastructure.repository.TransferRuleRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * Transfer rule gateway implementation
 */
@Component
@Slf4j
public class TransferRuleGatewayImpl implements TransferRuleGateway {
    
    @Resource
    private TransferRuleRepository transferRuleRepository;
    
    /**
     * Query transfer rule list
     */
    @Override
    public TransferRule queryTransferRuleByGrade(String grade) {
        return transferRuleRepository.queryTransferRuleByGrade(grade);
    }


    /**
     * Query transfer rule list
     */
    @Override
    public List<TransferRule> selectTransferRuleList() {
        return transferRuleRepository.selectTransferRuleList();
    }

    /**
     * Add transfer rule
     */
    @Override
    public boolean addTransferRule(TransferRule rule) {
        return transferRuleRepository.addTransferRule(rule);
    }

    /**
     * Update transfer rule
     */
    @Override
    public boolean updateTransferRule(TransferRule rule) {
        return transferRuleRepository.updateTransferRule(rule);
    }

    /**
     * Get transfer rule detail
     */
    @Override
    public TransferRule getTransferRuleDetail(String ruleId) {
        return transferRuleRepository.getTransferRuleDetail(ruleId);
    }

    /**
     * Delete transfer rule
     */
    @Override
    public boolean deleteTransferRule(String ruleId, String username) {
        return transferRuleRepository.deleteTransferRule(ruleId, username);
    }

    /**
     * Disable other rules with the same level
     */
    @Override
    public boolean disableOtherRules(String userLevel,String modify) {
        return transferRuleRepository.disableOtherRules(userLevel,modify);
    }

    /**
     * Check if it's the last enabled rule for this level
     */
    @Override
    public boolean isLastEnabledRule(String userLevel, String ruleId) {
        return transferRuleRepository.isLastEnabledRule(userLevel, ruleId);
    }

}
