<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.agtech.pointprod.service.infrastructure.dal.mapper.BizSequenceMapper">
    <resultMap id="BaseResultMap" type="com.agtech.pointprod.service.infrastructure.dal.daoobject.BizSequenceDO">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="min_value" jdbcType="BIGINT" property="minValue"/>
        <result column="max_value" jdbcType="BIGINT" property="maxValue"/>
        <result column="current_value" jdbcType="BIGINT" property="currentValue"/>
        <result column="step" jdbcType="BIGINT" property="step"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <sql id="baseColumn">
        id
        ,`name`,min_value,max_value,current_value,step,description,is_deleted,gmt_create,gmt_modified
    </sql>

    <select id="queryBizSequenceEntitys"
            resultType="com.agtech.pointprod.service.infrastructure.dal.daoobject.BizSequenceDO">
        select
        <include refid="baseColumn"></include>
        from fund_biz_sequence;
    </select>

    <select id="selectByBizNameForUpdate"
            resultType="com.agtech.pointprod.service.infrastructure.dal.daoobject.BizSequenceDO">
        select
        <include refid="baseColumn"></include>
        from fund_biz_sequence
        where name = #{bizName} for update
    </select>

    <update id="updateByBizName">
        update fund_biz_sequence
        set current_value=#{value}
        where name = #{bizName}
    </update>
</mapper>
