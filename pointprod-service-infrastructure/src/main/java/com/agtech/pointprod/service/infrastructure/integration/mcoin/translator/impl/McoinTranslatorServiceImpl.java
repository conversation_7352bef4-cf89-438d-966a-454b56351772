package com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinAccountDTO;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferInfo;
import com.agtech.pointprod.service.infrastructure.common.config.McoinConfig;
import com.agtech.pointprod.service.infrastructure.common.utils.RedisUtils;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.McoinClient;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.McoinTranslatorService;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinApiRequest;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinApiResponse;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinBalanceRequestBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinBalanceResponseBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTokenRequestBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTokenResponseBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTransferQueryRequestBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTransferRequestBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTransferResponseBody;
import com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto.McoinTransferResultDTO;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.TypeReference;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class McoinTranslatorServiceImpl implements McoinTranslatorService {

    @Resource
    private McoinClient mcoinClient;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private McoinConfig mcoinConfig;

    @Value("${mcoin.url:}")
    private String mcoinUrl;

    @Value("${spring.application.name:pointprod-service}")
    private String applicationName;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    private static final String MCOIN_SCORE_TOKEN = "mcoin.score.token";

    private static final String DICTIONARY = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    @Override
    public McoinAccountDTO getMcoinAccount(String custId) {
        String tokenCacheKey = buildTokenCacheKey(custId);
        String token = ObjectUtil.defaultIfNull(redisUtils.get(tokenCacheKey), "").toString();
        String appid = mcoinConfig.getAppid();
        String publicKey = mcoinConfig.getPublicKey();
        if (StringUtils.isBlank(token)) {
            token = getToken(custId);
        }
        McoinApiRequest<McoinBalanceRequestBody> balanceRequest = new McoinApiRequest<McoinBalanceRequestBody>()
                .setAppId(appid)
                .setTimestamp(ZonedDateUtil.nowString())
                .setNonceStr(getNonceStr(10))
                .setToken(token)
                .setBody(new McoinBalanceRequestBody());
        // 加签
        signRequest(balanceRequest);

        // 查余额
        McoinApiResponse<McoinBalanceResponseBody> result = null;
        long startTime = System.currentTimeMillis();
        try {
            result = mcoinClient.getBalance(balanceRequest);
            long endTime = System.currentTimeMillis();
            log.info("[請求mcoin獲取餘額信息] url:{}{}, request:{}, response:{}, cost:{}ms",
                    mcoinUrl, "/mcoin/service/balance", JSON.toJSONString(balanceRequest), JSON.toJSONString(result), endTime - startTime);
        }catch (Exception e){
            long endTime = System.currentTimeMillis();
            log.error("[請求mcoin獲取餘額信息] 發生異常, url:{}{}, request:{}, errMsg:{}, cost:{}ms",
                    mcoinUrl, "/mcoin/service/balance", JSON.toJSONString(balanceRequest), e.getMessage(), endTime - startTime, e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MCOIN_SERVICE_FAIL);
        }
        if (result != null) {
            //token無效,請重新獲取
            if (StringUtils.equals(result.getCode(), "9996")) {
                redisUtils.del(tokenCacheKey);
                log.error("mcoin token invalid");
                throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MCOIN_SERVICE_FAIL, "mcoin token invalid");
            }
            // 驗證簽名
            verifySign(result, publicKey);
            if (StringUtils.equals(result.getCode(), "0000")) {
                String userStatus = result.getData().getUserStatus();
                String integral = result.getData().getIntegral();
                String accountNo = result.getData().getAccountNo();
                McoinAccountDTO mcoinAccountDTO = new McoinAccountDTO();
                mcoinAccountDTO.setBalance(Long.valueOf(integral));
                mcoinAccountDTO.setUserStatus(userStatus);
                mcoinAccountDTO.setAccountNo(accountNo);
                return mcoinAccountDTO;
            }
        }
        return null;
    }

    private String getToken(String custId) {
        String tokenCacheKey = buildTokenCacheKey(custId);
        String appid = mcoinConfig.getAppid();
        String publicKey = mcoinConfig.getPublicKey();
        McoinApiRequest<McoinTokenRequestBody> tokenRequest = new McoinApiRequest<McoinTokenRequestBody>()
                .setAppId(appid)
                .setTimestamp(ZonedDateUtil.nowString())
                .setNonceStr(getNonceStr(10))
                .setBody(new McoinTokenRequestBody().setUserId(custId));
        // 加签
        signRequest(tokenRequest);

        McoinApiResponse<McoinTokenResponseBody> tokenResponse = null;
        long startTime = System.currentTimeMillis();
        try {
            tokenResponse = mcoinClient.getToken(tokenRequest);
            long endTime = System.currentTimeMillis();
            log.info("[請求mcoin獲取token] url:{}{}, request:{}, response:{}, cost:{}ms",
                    mcoinUrl, "/mcoin/service/balance", JSON.toJSONString(tokenRequest), JSON.toJSONString(tokenResponse), endTime - startTime);
        }catch (Exception e){
            long endTime = System.currentTimeMillis();
            log.error("[請求mcoin獲取token] 發生異常, url:{}{}, request:{}, errMsg:{}, cost:{}ms",
                    mcoinUrl, "/mcoin/service/balance", JSON.toJSONString(tokenRequest), e.getMessage(), endTime - startTime, e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MCOIN_SERVICE_FAIL);
        }

        if (tokenResponse == null) {
            log.error("請求mcoin獲取token信息返回空");
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MCOIN_SERVICE_FAIL, "mcoin token service response empty");
        }

        if (!StringUtils.equals(tokenResponse.getCode(), "9996")
                && !StringUtils.equals(tokenResponse.getCode(), "0000")) {
            log.error("請求mcoin獲取token信息返回失敗，response:{}", JSON.toJSONString(tokenResponse));
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MCOIN_SERVICE_FAIL, tokenResponse.getMsg());
        }
        verifySign(tokenResponse, publicKey);
        String token = tokenResponse.getData().getToken();
        String expires = tokenResponse.getData().getExpires();
        long surviveTime = 7200L;
        if (expires != null) {
            surviveTime = Long.parseLong(expires);
        }
        //token放到redis 保存2小時
        redisUtils.set(tokenCacheKey, token, surviveTime, TimeUnit.SECONDS);
        return token;
    }

    /**
     * 构建token缓存key
     * @param custId 客户ID
     * @return token缓存key
     */
    private String buildTokenCacheKey(String custId) {
        return applicationName + ":" + activeProfile + ":" + MCOIN_SCORE_TOKEN + "." + custId;
    }

    /**
     * 生成随机数，随机串取值范围0~9,a~z,A~Z
     * @param len 随机串长度
     * @return
     */
    public String getNonceStr(int len) {
        return RandomUtil.randomString(DICTIONARY, len);
    }

    /**
     * 加签
     * @param request 请求对象
     */
    private void signRequest(McoinApiRequest<?> request){
        String privateKey = mcoinConfig.getPrivateKey();
        StringBuilder balanceSignSb = new StringBuilder();
        balanceSignSb.append("appId=").append(request.getAppId()).append("&");
        String body = JSON.toJSONString(request.getBody());
        balanceSignSb.append("body=").append(body).append("&");
        balanceSignSb.append("nonceStr=").append(request.getNonceStr()).append("&");
        balanceSignSb.append("timestamp=").append(request.getTimestamp());
        if(StringUtils.isNotBlank(request.getToken())) {
            balanceSignSb.append("&").append("token=").append(request.getToken());
        }
        log.info("加簽前：{}", balanceSignSb);
        String balanceRequestSign = null;
        try {
            balanceRequestSign = AlipaySignature.rsaSign(balanceSignSb.toString(), privateKey, "UTF-8", "RSA2");
        } catch (AlipayApiException e) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SIGN_FAIL);
        }
        request.setSign(balanceRequestSign);
        log.info("加簽后：{}", JSON.toJSONString(request));
    }
    /**
     * 驗簽
     * @param apiResponse
     * @return
     */
    private void verifySign(McoinApiResponse<?> apiResponse, String publicKey) {
        try {
            if (StringUtils.isBlank(publicKey)) {
                log.error("publicKey is null");
                throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MCOIN_SERVICE_FAIL, "publicKey is null");
            }
            String signStr = apiResponse.getSign();
            Map<String, Object> signSourceMap = JSON.parseObject(JSON.toJSONString(apiResponse),
                    new TypeReference<Map<String, Object>>() {});
            //這個key不參與驗簽
            signSourceMap.remove("sign");
            if (apiResponse.getData() != null) {
                String data = JSON.toJSONString(apiResponse.getData(), JSONWriter.Feature.MapSortField);
                signSourceMap.put("data",data);
            }
            String signSource = MapUtil.sortJoin(signSourceMap, "&", "=", true);
            log.info("sign source: {}", signSource);
            Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA, null, publicKey);
            boolean verify = sign.verify(signSource.getBytes(StandardCharsets.UTF_8), Base64.decode(signStr));
            if (!verify) {
                log.error("驗簽失敗");
                throw new PointProdBizException(PointProdBizErrorCodeEnum.VERIFY_SIGN_FAIL);
            }
        } catch (Exception e) {
            log.error("驗簽失敗, msg: {}", e.getMessage(), e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.VERIFY_SIGN_FAIL);
        }
    }


    @Override
    public McoinTransferResultDTO transferPoint(McoinTransferInfo transferInfo,
                                                MPayUserInfo payerUserInfo, MPayUserInfo payeeUserInfo) {
        String tokenCacheKey = buildTokenCacheKey(payerUserInfo.getCustId());
        String token = ObjectUtil.defaultIfNull(redisUtils.get(tokenCacheKey), "").toString();
        String appid = mcoinConfig.getAppid();
        String publicKey = mcoinConfig.getPublicKey();
        if (StringUtils.isBlank(token)) {
            token = getToken(payerUserInfo.getCustId());
        }
        McoinApiRequest<McoinTransferRequestBody> transferRequest = new McoinApiRequest<McoinTransferRequestBody>()
                .setAppId(appid)
                .setTimestamp(ZonedDateUtil.nowString())
                .setNonceStr(getNonceStr(10))
                .setToken(token)
                .setBody(
                        new McoinTransferRequestBody()
                                .setOutTradeNo(transferInfo.getOrderId())
                                .setIntegral(transferInfo.getPoint().intValue())
                                .setTradeTime(DateUtil.format(transferInfo.getTradeTime(), NORM_DATETIME_PATTERN))
                                .setDescription(transferInfo.getDescription())
                                .setPayerUserInfo(new McoinTransferRequestBody.TransUserInfo()
                                        .setCustId(payerUserInfo.getCustId())
                                        .setNickName(payerUserInfo.getNickName())
                                        .setHeadLogo(payerUserInfo.getHeadLogo())
                                        .setAreaCode(payerUserInfo.getAreaCode())
                                        .setPhone(payerUserInfo.getPhone())
                                )
                                .setPayeeUserInfo(new McoinTransferRequestBody.TransUserInfo()
                                        .setCustId(payeeUserInfo.getCustId())
                                        .setNickName(payeeUserInfo.getNickName())
                                        .setHeadLogo(payeeUserInfo.getHeadLogo())
                                        .setAreaCode(payeeUserInfo.getAreaCode())
                                        .setPhone(payeeUserInfo.getPhone()))
                );
        // 加签
        signRequest(transferRequest);

        // 转账
        McoinApiResponse<McoinTransferResponseBody> transferResponse = mcoinClient.transfer(transferRequest);
        log.info("mcoin_score转账返回：{}", JSON.toJSONString(transferResponse));
        if (transferResponse == null) {
            log.info("mcoin transfer response is null");
            return null;
        }
        // 驗證簽名
        verifySign(transferResponse, publicKey);
        //读超時
        if (StringUtils.equals(transferResponse.getCode(), "9994")) {
            log.info("mcoin read timeout");
            return null;
        }
        //token無效,請重新獲取
        if (StringUtils.equals(transferResponse.getCode(), "9996")) {
            redisUtils.del(tokenCacheKey);
            log.error("mcoin token invalid");
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MCOIN_SERVICE_FAIL);
        }
        //余额不足
        if (StringUtils.equals(transferResponse.getCode(), "1008")) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.MCOIN_USER_BALANCE_INSUFFICIENT);
        }
        //不能轉贈給自己
        if (StringUtils.equals(transferResponse.getCode(), "1022")) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_AND_PAYEE_SAME);
        }

        //不成功
        if (!StringUtils.equals(transferResponse.getCode(), "0000")) {
            log.info("mcoin_score转账失败：{}", transferResponse.getMsg());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MCOIN_SERVICE_FAIL);
        }

        return new McoinTransferResultDTO()
                .setOutTradeNo(transferResponse.getData().getOutTradeNo())
                .setIntegral(transferResponse.getData().getIntegral())
                .setOrderStatus(transferResponse.getData().getOrderStatus())
                .setPayerOrderId(transferResponse.getData().getOrderId())
                .setPayeeOrderId(transferResponse.getData().getCounterpartyOrderId())
                .setTransferTime(DateUtil.parse(transferResponse.getData().getOrderTime(), NORM_DATETIME_PATTERN));
    }

    /**
     * 返回null，则代表查询结果未知
     */
    @Override
    public McoinTransferResultDTO queryTransfer(String outOrderId, String payeeCustId) {
        String tokenCacheKey = buildTokenCacheKey(payeeCustId);
        String token = ObjectUtil.defaultIfNull(redisUtils.get(tokenCacheKey), "").toString();
        String appid = mcoinConfig.getAppid();
        String publicKey = mcoinConfig.getPublicKey();
        if (StringUtils.isBlank(token)) {
            token = getToken(payeeCustId);
        }
        McoinApiRequest<McoinTransferQueryRequestBody> queryRequest = new McoinApiRequest<McoinTransferQueryRequestBody>()
                .setAppId(appid)
                .setTimestamp(ZonedDateUtil.nowString())
                .setNonceStr(getNonceStr(10))
                .setToken(token)
                .setBody(
                        new McoinTransferQueryRequestBody()
                                .setOutTradeNo(outOrderId)
                                .setPayeeCustId(payeeCustId)
                );
        // 加签
        signRequest(queryRequest);
        
        // 查询
        McoinApiResponse<McoinTransferResponseBody> queryResponse = mcoinClient.queryTransfer(queryRequest);
        log.info("mcoin_score查询转账返回：{}", JSON.toJSONString(queryResponse));
        if (queryResponse == null) {
            log.info("mcoin query transfer response is null");
            return null;
        }
        // 驗證簽名
        verifySign(queryResponse, publicKey);
        //交易不存在
        if (StringUtils.equals(queryResponse.getCode(), "1003")) {
            log.info("mcoin_score查询转账不存在：{}", queryResponse.getMsg());
            return null;
        }
        //處理中
        if (StringUtils.equals(queryResponse.getCode(), "9992")) {
            log.info("mcoin transfer processing:{}", queryResponse.getMsg());
            return null;
        }
        //token無效,請重新獲取
        if (StringUtils.equals(queryResponse.getCode(), "9996")) {
            redisUtils.del(tokenCacheKey);
            log.error("mcoin token invalid,{}", queryResponse.getMsg());
           return null;
        }
         //不成功
        if (!StringUtils.equals(queryResponse.getCode(), "0000")) {
            log.info("mcoin_score查询转账失败：{}", queryResponse.getMsg());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MCOIN_SERVICE_FAIL);
        }
        
        return new McoinTransferResultDTO()
                .setOutTradeNo(queryResponse.getData().getOutTradeNo())
                .setIntegral(queryResponse.getData().getIntegral())
                .setOrderStatus(queryResponse.getData().getOrderStatus())
                .setPayerOrderId(queryResponse.getData().getOrderId())
                .setPayeeOrderId(queryResponse.getData().getCounterpartyOrderId())
                .setTransferTime(DateUtil.parse(queryResponse.getData().getOrderTime(), NORM_DATETIME_PATTERN));
    }

}
