package com.agtech.pointprod.service.infrastructure.repository.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.model.MqTaskConfig;
import com.agtech.pointprod.service.domain.model.TaskConfig;
import com.agtech.pointprod.service.domain.model.TaskData;
import com.agtech.pointprod.service.domain.model.TaskRetry;
import com.agtech.pointprod.service.facade.dto.enums.TaskRetryStatusEnum;
import com.agtech.pointprod.service.infrastructure.dal.converter.TaskRetryConverter;
import com.agtech.pointprod.service.infrastructure.dal.converter.TaskRetryConverterHelper;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.TaskRetryDao;
import com.agtech.pointprod.service.infrastructure.repository.TaskRetryRepository;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/12 10:46
 */
@Repository
public class TaskRetryRepositoryImpl implements TaskRetryRepository {
    @Resource
    private TaskRetryDao taskRetryDao;

    @Resource
    private TaskRetryConverter taskRetryConverter;

    @Resource 
    private TaskRetryConverterHelper taskRetryConverterHelper;

    @Override
    public boolean createTaskRetry(TaskRetry taskRetry) {
        return taskRetryDao.createTaskRetry(taskRetryConverter.toTaskRetryDO(taskRetry, taskRetryConverterHelper));
    }

    @Override
    public boolean finishTaskRetry(String taskRetryId) {
        return taskRetryDao.finishTaskRetry(taskRetryId);
    }


    @Override
    public <T extends TaskConfig, D extends TaskData> TaskRetry getTaskRetryByIdForUpdate(String taskRetryId, Class<T> taskConfigClass, Class<D> taskDataClass) {
        return taskRetryConverter.toTaskRetry(taskRetryDao.getTaskRetryByIdForUpdate(taskRetryId), taskConfigClass, taskDataClass, taskRetryConverterHelper);
    }

    @Override
    public <T extends TaskConfig, D extends TaskData> TaskRetry getTaskRetryById(String taskRetryId, Class<T> taskConfigClass, Class<D> taskDataClass) {
        return taskRetryConverter.toTaskRetry(taskRetryDao.getTaskRetryById(taskRetryId), taskConfigClass, taskDataClass, taskRetryConverterHelper);
    }

    @Override
    public <T extends TaskConfig, D extends TaskData> TaskRetry getTaskRetryByResourceIdForUpdate(String resourceId, TaskResouceTypeEnum resourceType, Class<T> taskConfigClass, Class<D> taskDataClass) {
        return taskRetryConverter.toTaskRetry(taskRetryDao.getTaskRetryByResourceIdForUpdate(resourceId, resourceType.getCode()), taskConfigClass, taskDataClass, taskRetryConverterHelper);
    }


    @Override
    public List<TaskRetry> listFailedTasksForRetry(int limit, Date startTime) {
        return taskRetryDao.listFailedTasksForRetry(limit, startTime).stream()
                .map(taskRetryDO -> taskRetryConverter.toTaskRetry(taskRetryDO, MqTaskConfig.class, TaskData.class, taskRetryConverterHelper))
                .collect(Collectors.toList());
    }

    @Override
    public boolean incrTaskRetryCount(TaskRetry taskRetry) {
        return taskRetryDao.incrTaskRetryCount(taskRetryConverter.toTaskRetryDO(taskRetry, taskRetryConverterHelper));
    }

    @Override
    public boolean updateTaskRetryStatus(String taskRetryId, TaskRetryStatusEnum fromStatus, TaskRetryStatusEnum toStatus) {
        return taskRetryDao.updateTaskRetryStatus(taskRetryId, fromStatus.getValue(), toStatus.getValue());
    }
}
