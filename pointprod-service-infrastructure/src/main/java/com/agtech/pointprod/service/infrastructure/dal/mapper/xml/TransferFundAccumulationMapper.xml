<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agtech.pointprod.service.infrastructure.dal.mapper.TransferFundAccumulationMapper">

    <select id="listByConditionAndPage" resultType="com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferFundAccumulationDO">
        SELECT
        t.user_id AS userId, t.fund_out_amount AS fund_out_amount, t.fund_in_amount AS fund_in_amount
        FROM transfer_fund_accumulation t
        WHERE
        t.is_deleted = 0
        <if test='includeUserIds != null and includeUserIds.size() > 0'>
            <foreach item="item" index="index" collection="includeUserIds"
                     open="AND t.user_id IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test='excludeUserIds != null and excludeUserIds.size() > 0'>
            <foreach item="item" index="index" collection="excludeUserIds"
                     open="AND t.user_id NOT IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY id DESC
    </select>

</mapper>