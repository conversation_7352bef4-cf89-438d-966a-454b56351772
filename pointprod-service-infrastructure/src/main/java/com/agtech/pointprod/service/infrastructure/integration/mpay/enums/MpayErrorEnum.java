package com.agtech.pointprod.service.infrastructure.integration.mpay.enums;

/**
 * <AUTHOR>
 * @version v1.0, 2025/7/22 19:16
 */
public enum MpayErrorEnum {
    REQUEST_TRAFFIC_EXCEED_LIMIT("340001", "REQUEST_TRAFFIC_EXCEED_LIMIT", "活動太Hit啦! 您可以嘗試重新加載或稍等再試。"),
    ;

    public boolean equalsError(String code, String subCode){
        return this.code.equals(code) && this.subCode.equals(subCode);
    }

    MpayErrorEnum(String code, String subCode, String message) {
        this.code = code;
        this.subCode = subCode;
        this.message = message;
    }

    private final String code;
    private final String subCode;
    private final String message;

    public String getCode() {
        return code;
    }

    public String getSubCode() {
        return subCode;
    }

    public String getMessage() {
        return message;
    }
}
