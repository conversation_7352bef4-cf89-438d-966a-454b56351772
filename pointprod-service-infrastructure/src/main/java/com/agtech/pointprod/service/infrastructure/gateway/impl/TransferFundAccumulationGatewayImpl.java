package com.agtech.pointprod.service.infrastructure.gateway.impl;

import java.math.BigDecimal;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.gateway.TransferFundAccumulationGateway;
import com.agtech.pointprod.service.domain.model.TransferFundAccumulation;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import com.agtech.pointprod.service.infrastructure.repository.TransferFundAccumulationRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 转出获赠累计领域网关实现
 */
@Component
@Slf4j
public class TransferFundAccumulationGatewayImpl implements TransferFundAccumulationGateway {
    
    @Resource
    private TransferFundAccumulationRepository transferFundAccumulationRepository;

    @Resource
    private BizSequenceRepository bizSequenceRepository;
    
    @Override
    public TransferFundAccumulation queryByUserId(String userId) {
        return transferFundAccumulationRepository.findByUserId(userId);
    }
    
    @Override
    public TransferFundAccumulation queryByUserIdForUpdate(String userId) {
        return transferFundAccumulationRepository.findByUserIdForUpdate(userId);
    }
    
    @Override
    public TransferFundAccumulation queryByFundAccumulationId(String fundAccumulationId) {
        return transferFundAccumulationRepository.findByFundAccumulationId(fundAccumulationId);
    }
    
    @Override
    public boolean saveTransferFundAccumulation(TransferFundAccumulation transferFundAccumulation) {
        String fundAccumulationId = bizSequenceRepository.getBizId(transferFundAccumulation.getUserId(), SequenceCodeEnum.TRANSFER_FUND_ACCUMULATION);
        transferFundAccumulation.setFundAccumulationId(fundAccumulationId);
        return transferFundAccumulationRepository.save(transferFundAccumulation);
    }
    

    @Override
    public boolean accumulateFundOut(String userId, BigDecimal amount, Integer count) {
        return transferFundAccumulationRepository.accumulateFundOut(userId, amount, count);
    }
    
    @Override
    public boolean accumulateFundIn(String userId, BigDecimal amount, Integer count) {
        return transferFundAccumulationRepository.accumulateFundIn(userId, amount, count);
    }
}