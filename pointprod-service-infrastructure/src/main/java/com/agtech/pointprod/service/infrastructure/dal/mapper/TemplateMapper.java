package com.agtech.pointprod.service.infrastructure.dal.mapper;

import com.agtech.pointprod.service.infrastructure.dto.response.TemplateValidCountQueryRsp;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TemplateDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface TemplateMapper extends BaseMapper<TemplateDO> {

    TemplateValidCountQueryRsp selectValidCount(@Param("bizType") String bizType, @Param("templateId") String templateId, @Param("time") Date time);

} 