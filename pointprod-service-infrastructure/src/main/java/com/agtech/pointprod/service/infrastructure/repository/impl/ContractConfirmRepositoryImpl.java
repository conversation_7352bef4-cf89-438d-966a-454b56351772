package com.agtech.pointprod.service.infrastructure.repository.impl;

import com.agtech.pointprod.service.domain.model.ContractConfirm;
import com.agtech.pointprod.service.infrastructure.dal.converter.ContractConfirmConverter;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.ContractConfirmDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.ContractConfirmDO;
import com.agtech.pointprod.service.infrastructure.repository.ContractConfirmRepository;
import com.agtech.pointprod.service.infrastructure.dto.response.UserConfirmContractRsp;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ContractConfirmRepositoryImpl implements ContractConfirmRepository {
    @Resource
    private ContractConfirmDAO contractConfirmDAO;
    @Resource
    private ContractConfirmConverter contractConfirmConverter;

    @Override
    public ContractConfirm queryContractConfirmByUserIdAndContractId(String userId, String contractId) {
        ContractConfirmDO contractConfirmDO = contractConfirmDAO.queryContractConfirmByUserIdAndContractId(userId, contractId);
        return contractConfirmConverter.do2Model(contractConfirmDO);
    }

//    @Override
//    public ContractConfirm queryContractConfirmByUserIdAndContractIdForLock(String userId, String contractId) {
//        ContractConfirmDO contractConfirmDO = contractConfirmDAO.queryContractConfirmByUserIdAndContractIdForLock(userId, contractId);
//        return contractConfirmConverter.do2Model(contractConfirmDO);
//    }

    @Override
    public boolean updateContractConfirmForUser(String userId, String contractId, String status) {
        return contractConfirmDAO.updateContractConfirmForUser(userId, contractId, status);
    }

    @Override
    public boolean saveContractConfirm(ContractConfirm contractConfirm) {
        ContractConfirmDO contractConfirmDO = contractConfirmConverter.model2Do(contractConfirm);
        return contractConfirmDAO.saveContractConfirm(contractConfirmDO);
    }

    @Override
    public List<UserConfirmContractRsp> queryMultiUserConfirmContract(String contractType, String status, List<String> userIds) {
        return contractConfirmDAO.queryMultiUserConfirmContractList(contractType, status, userIds);
    }
}
