package com.agtech.pointprod.service.infrastructure.integration.mpay.translator;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.infrastructure.common.utils.RsaUtils;
import com.alibaba.fastjson.JSONObject;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;

import java.nio.charset.StandardCharsets;

@Slf4j
public class MPayFeignSignatureInterceptor implements RequestInterceptor {

    private final KeyService keyService;
    private static final String CONTENT_TYPE = "application/json; charset=utf-8";


    public MPayFeignSignatureInterceptor(KeyService keyService) {
        this.keyService = keyService;
    }

    @Override
    public void apply(RequestTemplate template) {
        try {
            // 设置 Content-Type
            template.header("Content-Type", CONTENT_TYPE);
            // 设置 签名
            String clientId = keyService.getClientId();
            String requestTime = ZonedDateUtil.formatDate(ZonedDateUtil.now());
            String nonce = RandomStringUtils.randomAlphanumeric(10);
            String keyVersion = keyService.getCurrentKeyVersion();
            String bodyString = template.body() == null ? "" : new String(template.body(), StandardCharsets.UTF_8);

            String toSign = String.join(".", clientId, requestTime, nonce, bodyString);
            String privateKey = keyService.getPrivateKey(keyVersion);
            String signatureValue = RsaUtils.rsa2Sign(privateKey, toSign);

            SignatureInfo signatureInfo = new SignatureInfo();
            signatureInfo.setAlgorithm("RSA2");
            signatureInfo.setKeyVersion(keyVersion);
            signatureInfo.setSignature(signatureValue);
            template.header("Client-Id", clientId);
            template.header("Request-Time", requestTime);
            template.header("Nonce", nonce);
            template.header("Signature", JSONObject.toJSONString(signatureInfo));
        } catch (Exception e) {
            log.error("mPay API call request sign failed", e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR, "mPay API call request sign failed: " + e.getMessage());
        }
    }
} 