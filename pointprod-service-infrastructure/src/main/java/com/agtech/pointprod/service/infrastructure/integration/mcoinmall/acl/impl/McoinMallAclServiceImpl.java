package com.agtech.pointprod.service.infrastructure.integration.mcoinmall.acl.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.acl.McoinMallAclService;
import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.adapter.McoinMallAdapterService;


/**
 * MCoin Mall Anti-Corruption Layer Service Implementation
 */
@Service
public class McoinMallAclServiceImpl implements McoinMallAclService {
    
    @Resource
    private McoinMallAdapterService mcoinMallAdapterService;
    
    @Override
    public String getAppToken() {
        return mcoinMallAdapterService.getAppToken();
    }
} 