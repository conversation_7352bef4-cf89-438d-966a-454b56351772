package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import java.util.List;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferRuleDO;
import com.baomidou.mybatisplus.extension.service.IService;

public interface TransferRuleDAO extends IService<TransferRuleDO> {

    TransferRuleDO queryTransferRuleByGrade(String grade);


    /**
     * Query transfer rule list
     *
     * @return List of transfer rules
     */
    List<TransferRuleDO> selectTransferRuleList();

    /**
     * Get transfer rule detail
     *
     * @param ruleId Rule ID
     * @return Transfer rule DO
     */
    TransferRuleDO getTransferRuleDetail(String ruleId);

    /**
     * Disable other rules with the same level
     *
     * @param userLevel User level
     * @return Number of rows affected
     */
    boolean disableOtherRules(String userLevel,String modify);

    /**
     * Check if it's the last enabled rule for this level
     *
     * @param userLevel User level
     * @param ruleId Rule ID
     * @return true if it's the last enabled rule
     */
    boolean isLastEnabledRule(String userLevel, String ruleId);
}
