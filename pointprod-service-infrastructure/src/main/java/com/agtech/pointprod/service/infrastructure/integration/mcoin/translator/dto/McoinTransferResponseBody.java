package com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class McoinTransferResponseBody {

    private String orderId;
    private String integral;
    private String outTradeNo;
    private String orderTime;
    private String orderStatus;
    private String counterpartyOrderId;

}
