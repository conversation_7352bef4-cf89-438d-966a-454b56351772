package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import com.agtech.pointprod.service.domain.common.enums.TransferRuleStatusEnum;
import org.springframework.stereotype.Component;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.domain.common.enums.YesOrNoEnum;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.TransferRuleDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferRuleDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.TransferRuleMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * Transfer rule DAO implementation
 */
@Component
public class TransferRuleDAOImpl extends ServiceImpl<TransferRuleMapper, TransferRuleDO> implements TransferRuleDAO {

    /**
     * Query transfer rule list
     */
    @Override
    public List<TransferRuleDO> selectTransferRuleList() {
        LambdaQueryWrapper<TransferRuleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TransferRuleDO::getIsDeleted, YesOrNoEnum.NO.getValue())
                   .orderByDesc(TransferRuleDO::getGmtModified);

        return list(queryWrapper);
    }

    /**
     * Get transfer rule detail
     */
    @Override
    public TransferRuleDO getTransferRuleDetail(String ruleId) {
        LambdaQueryWrapper<TransferRuleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TransferRuleDO::getLimitRuleIds, ruleId)
                   .eq(TransferRuleDO::getIsDeleted, YesOrNoEnum.NO.getValue());

        return getOne(queryWrapper);
    }
    
    /**
     * Disable other rules with the same level
     * 
     * @param userLevel User level
     * @return Number of rows affected
     */
    @Override
    public boolean disableOtherRules(String userLevel,String modify) {
        return this.lambdaUpdate()
                .eq(TransferRuleDO::getLevelKey, userLevel)
                .eq(TransferRuleDO::getStatus,"VALID")
                .set(TransferRuleDO::getStatus,"INVALID")
                .set(TransferRuleDO::getGmtModified, ZonedDateUtil.now())
                .set(TransferRuleDO::getModifier, modify)
                .update();
    }

    /**
     * Check if it's the last enabled rule for this level
     * 
     * @param userLevel User level
     * @param ruleId Rule ID to exclude
     * @return true if it's the last enabled rule
     */
    @Override
    public boolean isLastEnabledRule(String userLevel, String ruleId) {
        // Create a query wrapper to count enabled rules with the same level
        LambdaQueryWrapper<TransferRuleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TransferRuleDO::getLevelKey, userLevel)
                   .eq(TransferRuleDO::getIsDeleted, YesOrNoEnum.NO.getValue())
                   .eq(TransferRuleDO::getStatus, TransferRuleStatusEnum.VALID.getValue());
                   
        // Exclude the specified rule ID
        if (ruleId != null) {
            queryWrapper.ne(TransferRuleDO::getTransferRuleId, ruleId);
        }
        
        // Count and check if there are no other enabled rules
        return count(queryWrapper) == 0;
    }
    
    /**
     * Query transfer rule by grade
     */
    @Override
    public TransferRuleDO queryTransferRuleByGrade(String grade) {
        LambdaQueryWrapper<TransferRuleDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TransferRuleDO::getLevelKey, grade);
        lambdaQueryWrapper.eq(TransferRuleDO::getStatus, TransferRuleStatusEnum.VALID.getValue());
        lambdaQueryWrapper.eq(TransferRuleDO::getIsDeleted, 0);
        List<TransferRuleDO> transferRuleList= this.list(lambdaQueryWrapper);
        return CollectionUtils.isNotEmpty(transferRuleList) ? transferRuleList.get(0) : null;
    }
}
