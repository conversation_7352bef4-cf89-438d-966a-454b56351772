package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.agtech.pointprod.service.domain.common.enums.YesOrNoEnum;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.ShareRecordDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.ShareRecordDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.ShareRecordMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 分享记录DAO实现
 */
@Component
public class ShareRecordDAOImpl extends ServiceImpl<ShareRecordMapper, ShareRecordDO> implements ShareRecordDAO {
    
    @Override
    public List<ShareRecordDO> selectByShareCode(String shareCode) {
        LambdaQueryWrapper<ShareRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShareRecordDO::getShareCode, shareCode)
                   .eq(ShareRecordDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        return list(queryWrapper);
    }
    
    @Override
    public List<ShareRecordDO> selectByContentIds(List<String> contentIds) {
        if (CollectionUtils.isEmpty(contentIds)) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<ShareRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ShareRecordDO::getContentId, contentIds)
                   .eq(ShareRecordDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        return list(queryWrapper);
    }
    
    @Override
    public List<ShareRecordDO> selectByShareCodeAndTypeAndContentType(String shareCode, String shareRecordsType, String contentType) {
        LambdaQueryWrapper<ShareRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShareRecordDO::getShareCode, shareCode)
                   .eq(ShareRecordDO::getShareRecordsType, shareRecordsType)
                   .eq(ShareRecordDO::getContentType, contentType)
                   .eq(ShareRecordDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        return list(queryWrapper);
    }
    
    @Override
    public ShareRecordDO selectByUniqueBusinessKey(String contentId, String contentType, String shareRecordsType, String channelType, String userId) {
        LambdaQueryWrapper<ShareRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShareRecordDO::getContentId, contentId)
                   .eq(ShareRecordDO::getContentType, contentType)
                   .eq(ShareRecordDO::getShareRecordsType, shareRecordsType)
                   .eq(ShareRecordDO::getChannelType, channelType)
                   .eq(ShareRecordDO::getUserId, userId)
                   .eq(ShareRecordDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        return getOne(queryWrapper);
    }
    
    @Override
    public ShareRecordDO selectByUniqueBusinessKeyForUpdate(String contentId, String contentType, String shareRecordsType, String channelType, String userId) {
        LambdaQueryWrapper<ShareRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShareRecordDO::getContentId, contentId)
                   .eq(ShareRecordDO::getContentType, contentType)
                   .eq(ShareRecordDO::getShareRecordsType, shareRecordsType)
                   .eq(ShareRecordDO::getChannelType, channelType)
                   .eq(ShareRecordDO::getUserId, userId)
                   .eq(ShareRecordDO::getIsDeleted, YesOrNoEnum.NO.getValue())
                   .last("FOR UPDATE");
        
        return getOne(queryWrapper);
    }
    
    @Override
    public boolean updateByShareId(ShareRecordDO record) {
        if (record == null || record.getShareRecordsId() == null) {
            return false;
        }
        
        LambdaUpdateWrapper<ShareRecordDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ShareRecordDO::getShareRecordsId, record.getShareRecordsId())
        .eq(ShareRecordDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        // 设置需要更新的字段
        if (record.getShareCode() != null) {
            updateWrapper.set(ShareRecordDO::getShareCode, record.getShareCode());
        }
        if (record.getShareUrl() != null) {
            updateWrapper.set(ShareRecordDO::getShareUrl, record.getShareUrl());
        }
        if (record.getExpireAt() != null) {
            updateWrapper.set(ShareRecordDO::getExpireAt, record.getExpireAt());
        }
        if (record.getGmtModified() != null) {
            updateWrapper.set(ShareRecordDO::getGmtModified, record.getGmtModified());
        }
        
        return update(updateWrapper);
    }
} 