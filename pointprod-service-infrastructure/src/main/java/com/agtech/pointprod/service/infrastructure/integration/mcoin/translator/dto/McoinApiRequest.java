package com.agtech.pointprod.service.infrastructure.integration.mcoin.translator.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class McoinApiRequest <T> {
    private String appId;
    private String token;
    private String nonceStr;
    private String timestamp;
    private T body;
    private String sign;
}
