package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import java.util.Date;
import java.util.List;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.TaskRetryDO;

/**
 * <AUTHOR>
 * @version TaskRetryDao.java, v0.1 2025/6/18 11:21 zhongqigang Exp $
 */
public interface TaskRetryDao {
    /**
     * 创建任务重试
     *
     * @param taskRetryDO 任务重试
     * @return 是否成功
     */
    boolean createTaskRetry(TaskRetryDO taskRetryDO);

    /**
     * 更新任务重试状态为FINISH
     *
     * @param taskRetryId 任务重试ID
     * @return 是否成功
     */
    boolean finishTaskRetry(String taskRetryId);

    /**
     * 根据任务重试ID查询任务重试信息
     *
     * @param taskRetryId 任务重试ID
     * @return 任务重试信息
     */
    TaskRetryDO getTaskRetryByIdForUpdate(String taskRetryId);

    /**
     * 根据任务重试ID查询任务重试信息（不加锁）
     *
     * @param taskRetryId 任务重试ID
     * @return 任务重试信息
     */
    TaskRetryDO getTaskRetryById(String taskRetryId);

    /**
     * 根据资源ID查询任务重试信息
     *
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @return 任务重试信息
     */
    TaskRetryDO getTaskRetryByResourceIdForUpdate(String resourceId, String resourceType);


    /**
     * 查询未投递成功且未达到重试上限的任务列表（带时间过滤）
     * @param limit 查询数量限制
     * @param startTime 查询数据开始时间，通过next_retry字段过滤
     * @return 任务重试列表
     */
    List<TaskRetryDO> listFailedTasksForRetry(int limit, Date startTime);

    /**
     * 更新任务重试次数和下次重试时间
     * @param taskRetryDO 任务重试数据对象
     * @return 是否成功
     */
    boolean incrTaskRetryCount(TaskRetryDO taskRetryDO);

    /**
     * 更新任务重试状态
     * @param taskRetryId 任务重试ID
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return 是否成功
     */
    boolean updateTaskRetryStatus(String taskRetryId, String fromStatus, String toStatus);
}
