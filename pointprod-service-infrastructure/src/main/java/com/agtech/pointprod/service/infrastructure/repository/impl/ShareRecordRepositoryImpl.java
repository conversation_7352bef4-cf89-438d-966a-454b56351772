package com.agtech.pointprod.service.infrastructure.repository.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.agtech.pointprod.service.domain.model.share.ShareRecord;
import com.agtech.pointprod.service.infrastructure.dal.converter.ShareRecordConverter;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.ShareRecordDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.ShareRecordDO;
import com.agtech.pointprod.service.infrastructure.repository.ShareRecordRepository;

/**
 * 分享记录仓储实现
 */
@Component
public class ShareRecordRepositoryImpl implements ShareRecordRepository {
    
    @Resource
    private ShareRecordDAO shareRecordDAO;
    
    @Resource
    private ShareRecordConverter shareRecordConverter;
    
    @Override
    public List<ShareRecord> findByShareCode(String shareCode) {
        List<ShareRecordDO> shareRecordDOList = shareRecordDAO.selectByShareCode(shareCode);
        return shareRecordConverter.doListToDomainList(shareRecordDOList);
    }
    
    @Override
    public List<ShareRecord> findByContentIds(List<String> contentIds) {
        List<ShareRecordDO> shareRecordDOList = shareRecordDAO.selectByContentIds(contentIds);
        return shareRecordConverter.doListToDomainList(shareRecordDOList);
    }
    
    @Override
    public List<ShareRecord> findByShareCodeAndTypeAndContentType(String shareCode, String shareRecordsType, String contentType) {
        List<ShareRecordDO> shareRecordDOList = shareRecordDAO.selectByShareCodeAndTypeAndContentType(shareCode, shareRecordsType, contentType);
        return shareRecordConverter.doListToDomainList(shareRecordDOList);
    }
    
    @Override
    public ShareRecord findByUniqueBusinessKey(String contentId, String contentType, String shareRecordsType, String channelType, String userId) {
        ShareRecordDO shareRecordDO = shareRecordDAO.selectByUniqueBusinessKey(contentId, contentType, shareRecordsType, channelType, userId);
        return shareRecordDO != null ? shareRecordConverter.doToDomain(shareRecordDO) : null;
    }
    
    @Override
    public boolean saveShareRecord(ShareRecord shareRecord) {
        ShareRecordDO shareRecordDO = shareRecordConverter.domainToDO(shareRecord);
        return shareRecordDAO.save(shareRecordDO);
    }
    
    @Override
    public ShareRecord findByUniqueBusinessKeyForUpdate(String contentId, String contentType, String shareRecordsType, String channelType, String userId) {
        ShareRecordDO shareRecordDO = shareRecordDAO.selectByUniqueBusinessKeyForUpdate(contentId, contentType, shareRecordsType, channelType, userId);
        return shareRecordDO != null ? shareRecordConverter.doToDomain(shareRecordDO) : null;
    }
    
    @Override
    public boolean updateShareRecord(ShareRecord shareRecord) {
        ShareRecordDO shareRecordDO = shareRecordConverter.domainToDO(shareRecord);
        return shareRecordDAO.updateByShareId(shareRecordDO);
    }
} 