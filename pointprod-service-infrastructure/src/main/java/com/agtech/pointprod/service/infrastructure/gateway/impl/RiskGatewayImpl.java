package com.agtech.pointprod.service.infrastructure.gateway.impl;

import com.agtech.common.lang.util.StringUtil;
import com.agtech.pointprod.service.domain.gateway.RiskGateway;
import com.agtech.pointprod.service.infrastructure.integration.risk.acl.RiskAclService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0, 2025/7/3 20:31
 */
@Component
@Slf4j
public class RiskGatewayImpl implements RiskGateway {
    @Resource
    private RiskAclService riskAclService;

    @Override
    public String validateText(String text) {
        if(StringUtil.isBlank(text)){
            return null;
        }
        return riskAclService.validateText(text);
    }
}
