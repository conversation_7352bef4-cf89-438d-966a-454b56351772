package com.agtech.pointprod.service.infrastructure.dal.converter;

import com.agtech.pointprod.service.domain.model.TransferFundAccumulation;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TransferFundAccumulationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 17:18
 */
@Mapper(componentModel = "spring")
public interface TransferFundAccumulationConverter {

    List<TransferFundAccumulation> convert2TransferFundAccumulationList(List<TransferFundAccumulationDO> transferFundAccumulationList);

        /**
     * DO转换为领域模型
     */
    TransferFundAccumulation doToDomain(TransferFundAccumulationDO transferFundAccumulationDO);
    
    /**
     * 领域模型转换为DO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    TransferFundAccumulationDO domainToDO(TransferFundAccumulation transferFundAccumulation);
}
