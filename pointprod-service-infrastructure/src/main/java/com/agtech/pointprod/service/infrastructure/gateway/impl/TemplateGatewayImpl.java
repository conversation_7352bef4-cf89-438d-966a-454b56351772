package com.agtech.pointprod.service.infrastructure.gateway.impl;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TemplateStatusEnum;
import com.agtech.pointprod.service.domain.gateway.TemplateGateway;
import com.agtech.pointprod.service.domain.model.Template;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.facade.dto.req.template.QueryTemplateInfoListReq;
import com.agtech.pointprod.service.infrastructure.config.TemplateValidCountLimitConfig;
import com.agtech.pointprod.service.infrastructure.dal.converter.TemplateConvertor;
import com.agtech.pointprod.service.infrastructure.dto.request.TemplateValidCountCheckReq;
import com.agtech.pointprod.service.infrastructure.dto.response.TemplateValidCountCheckRsp;
import com.agtech.pointprod.service.infrastructure.dto.response.TemplateValidCountQueryRsp;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import com.agtech.pointprod.service.infrastructure.repository.TemplateRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Slf4j
@Component
public class TemplateGatewayImpl implements TemplateGateway {
    @Resource
    private TemplateRepository templateRepository;

    @Resource
    private BizSequenceRepository bizSequenceRepository;

    @Resource
    private TemplateValidCountLimitConfig templateValidCountLimitConfig;

    @Resource
    private TemplateConvertor templateConvertor;

    @Override
    public PageInfoDTO<Template> queryValidTemplateListByPage(String bizType, Integer pageNo, Integer pageSize) {
        if (pageNo == null || pageSize == null || StringUtils.isBlank(bizType)) {
            log.error("queryTemplateListByPage param error, pageNo:{}, pageSize:{},bizType: {}", pageNo, pageSize, bizType);
            return new PageInfoDTO<>();
        }
        return templateRepository.queryValidTemplateListByPage(bizType,pageNo, pageSize);
    }

    @Override
    public PageInfoDTO<Template> queryTemplateListByPage(QueryTemplateInfoListReq queryTemplateInfoListReq) {
        Integer pageNo = queryTemplateInfoListReq.getPageNo();
        Integer pageSize = queryTemplateInfoListReq.getPageSize();
        if (pageNo == null || pageSize == null) {
            log.error("queryTemplateListByPage param error, pageNo:{}, pageSize:{}", pageNo, pageSize);
            return new PageInfoDTO<>();
        }
        return templateRepository.queryTemplateListByPage(queryTemplateInfoListReq);
    }

    @Override
    public String createTemplate(Template template) {
        checkValidCountLimit(templateConvertor.templateToValidCheckReq(template));
        String templateId = bizSequenceRepository.getBizId(null, SequenceCodeEnum.TEMPLATE);
        template.setTemplateId(templateId);
        AssertUtil.assertTrue(templateRepository.createTemplate(template), PointProdBizErrorCodeEnum.SYS_ERROR, "模板保存失敗[{0}]", "Template saving failed[{0}]", templateId);
        return templateId;
    }

    @Override
    public boolean updateTemplate(Template template) {
        checkValidCountLimit(templateConvertor.templateToValidCheckReq(template));
        return templateRepository.updateTemplate(template);
    }

    @Override
    public boolean deleteTemplate(String templateId, String operator) {
        return templateRepository.deleteTemplate(templateId, operator);
    }

    private void checkValidCountLimit(TemplateValidCountCheckReq templateValidCheckReq){
        TemplateValidCountCheckRsp templateValidCountCheckResult = checkExceedValidTempCountLimit(templateValidCheckReq);
        AssertUtil.assertFalse(templateValidCountCheckResult.isExceedLimit(), PointProdBizErrorCodeEnum.PARAMS_ERROR,
                "有效留言模板數量已達上限[{0}]", "The number of valid message templates has reached the upper limit[{0}]",
                templateValidCountCheckResult.getLimitCount());
    }

    private TemplateValidCountCheckRsp checkExceedValidTempCountLimit(TemplateValidCountCheckReq templateValidCheckReq) {
        Integer limit = templateValidCountLimitConfig.getTemplateValidCountLimit(templateValidCheckReq.getBizType());
        if (limit < 0){
            return new TemplateValidCountCheckRsp(false, limit);
        }
        TemplateValidCountQueryRsp templateValidCount = templateRepository.selectValidCount(templateValidCheckReq.getBizType(), templateValidCheckReq.getTemplateId(), templateValidCheckReq.getTime());
        // 有效數量未達到上限，或者此模板本身就是有效的，返回false
        if (templateValidCount.getValidCount() < limit || templateValidCount.getSpecTempValidCount() > 0){
            return new TemplateValidCountCheckRsp(false, limit);
        }
        boolean result = TemplateStatusEnum.ABLE.getCode().equals(templateValidCheckReq.getStatus()) && !templateValidCheckReq.getTime().after(templateValidCheckReq.getEndTime());
        // 達到數量後，再有新增有效的則返回true
        return new TemplateValidCountCheckRsp(result, limit);
    }

}
