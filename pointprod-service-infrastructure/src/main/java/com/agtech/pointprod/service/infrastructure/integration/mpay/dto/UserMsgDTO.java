package com.agtech.pointprod.service.infrastructure.integration.mpay.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class UserMsgDTO {
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 用户等级
     */
    private String grade;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 区号
     */
    private String areaCode;
    /**
     * 用户状态:
     * 正常：normal
     * 冻结：freeze
     * 注销：cancel
     */
    private String status;
    /**
     * 是否是黑名单用户:
     * 是:true
     * 否:false
     */
    private Boolean blackList;
    /**
     * 头像url
     */
    private String headLogo;
    /**
     * 用户标识
     */
    private String custId;
    /**
     * 是否认证
     */
    private String isCertified;
    /**
     * 英文名称
     */
    private String enName;
}
