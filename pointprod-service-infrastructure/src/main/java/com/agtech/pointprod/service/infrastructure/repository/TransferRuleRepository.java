package com.agtech.pointprod.service.infrastructure.repository;

import java.util.List;

import com.agtech.pointprod.service.domain.model.TransferRule;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/12 10:46
 */
public interface TransferRuleRepository {

    TransferRule queryTransferRuleByGrade(String grade);

    /**
     * Query transfer rule list
     *
     * @return List of transfer rules
     */
    List<TransferRule> selectTransferRuleList();

    /**
     * Add transfer rule
     *
     * @param rule Transfer rule to add
     * @return true if successful
     */
    boolean addTransferRule(TransferRule rule);

    /**
     * Update transfer rule
     *
     * @param rule Transfer rule to update
     * @return true if successful
     */
    boolean updateTransferRule(TransferRule rule);

    /**
     * Get transfer rule detail
     *
     * @param ruleId Rule ID
     * @return Transfer rule
     */
    TransferRule getTransferRuleDetail(String ruleId);

    /**
     * Delete transfer rule
     *
     * @param ruleId Rule ID
     * @param username Username
     * @return true if successful
     */
    boolean deleteTransferRule(String ruleId, String username);

    /**
     * Disable other rules with the same level
     *
     * @param userLevel User level
     * @return true if successful
     */
    boolean disableOtherRules(String userLevel,String modify);

    /**
     * Check if it's the last enabled rule for this level
     *
     * @param userLevel User level
     * @param ruleId Rule ID
     * @return true if it's the last enabled rule
     */
    boolean isLastEnabledRule(String userLevel, String ruleId);
}
