package com.agtech.pointprod.service.infrastructure.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version McoinConfig.java, v0.1 2025/06/16 14:21 zhongqigang Exp $
 */
@Getter
@Setter
@RefreshScope
@Component
//@ConfigurationProperties(prefix = "mcoin")
public class McoinConfig {
    @Value("${mcoin.appid:}")
    private String appid;
    @Value("${mcoin.privatekey:}")
    private String privateKey;
    @Value("${mcoin.publickey:}")
    private String publicKey;
    @Value("${mcoin.url:}")
    private String url;
}
