package com.agtech.pointprod.service.infrastructure.common.enums.sort;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/24 16:41
 */
@Getter
public enum TemplateSortKeyEnum {
	/** 创建时间 */
    GMT_CREATE("gmt_create", "创建时间"),
    TEMPLATE_ID("template_id", "模版ID"),
    ID("id", "ID"),
    SORT("sort", "模版排序值"),
    ;

    private final String code;
    private final String description;

    TemplateSortKeyEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static boolean containCode(String code){
        for (TemplateSortKeyEnum templateSortKeyEnum : values()){
            if (templateSortKeyEnum.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }

}
