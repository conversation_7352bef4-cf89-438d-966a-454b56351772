package com.agtech.pointprod.service.infrastructure.integration.mpay.acl.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.agtech.pointprod.service.infrastructure.dto.request.PushMessageRequestDTO;
import com.agtech.pointprod.service.infrastructure.integration.mpay.acl.MPayPushAclService;
import com.agtech.pointprod.service.infrastructure.integration.mpay.adapter.MPayPushAdapterService;


/**
 * MPay Push Anti-Corruption Layer Service Implementation
 */
@Service
public class MPayPushAclServiceImpl implements MPayPushAclService {
    
    @Resource
    private MPayPushAdapterService mPayPushAdapterService;
    
    @Override
    public boolean pushMessage(PushMessageRequestDTO requestDTO) {
        return mPayPushAdapterService.pushMessage(requestDTO);
    }
} 