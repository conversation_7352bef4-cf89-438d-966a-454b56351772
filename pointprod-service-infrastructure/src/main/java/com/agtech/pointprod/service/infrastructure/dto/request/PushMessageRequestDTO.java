package com.agtech.pointprod.service.infrastructure.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 推送消息请求DTO
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PushMessageRequestDTO {
    
    /**
     * 应用令牌
     */
    private String appToken;
    
    /**
     * 商户appid
     */
    private String mPayPushAppId;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 消息模板code
     * 若此值不为空，msg_context和msg_title则会被忽略
     */
    private String msgCode;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 消息提示类型 (proto：原生页面；h5：h5全屏；h5Alter：h5弹窗；redpackage：红包弹窗)
     */
    private String openType;
    
    /**
     * 路由 (当open_type = h5，route应是一条http url地址)
     */
    private String route;
    
    /**
     * 模板参数
     * 模板需要替换的占位符参数
     */
    private TemplateParams templateParams;
    
    /**
     * 扩展信息
     */
    private ExtMap extMap;
    
    /**
     * 模板参数类
     */
    @Getter
    @Setter
    public static class TemplateParams {
        /**
         * mCoin值，格式化后的金额
         */
        private String mCoin;
        
        // 可以添加其他模板参数字段
    }
    
    /**
     * 扩展信息类
     */
    @Getter
    @Setter
    public static class ExtMap {
        /**
         * 内容扩展信息
         */
        private ContentExt contentExt;
        
        /**
         * 内容扩展信息类
         */
        @Getter
        @Setter
        public static class ContentExt {
            /**
             * 格式化金额，如"1,000"
             */
            private String amount;
            
            /**
             * 交易类型
             * 16=mCoin转出
             * 17=mCoin转入
             */
            private String tradeType;
            
            /**
             * 对方用户ID
             */
            private String otherCustId;
            
            /**
             * 货币类型，如"mCoin"
             */
            private String currency;
        }
    }
} 