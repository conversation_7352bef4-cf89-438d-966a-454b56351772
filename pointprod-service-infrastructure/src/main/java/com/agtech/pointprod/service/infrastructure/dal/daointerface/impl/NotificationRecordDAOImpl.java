package com.agtech.pointprod.service.infrastructure.dal.daointerface.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.domain.common.enums.NumbersEnum;
import com.agtech.pointprod.service.domain.common.enums.YesOrNoEnum;
import com.agtech.pointprod.service.infrastructure.common.constant.NotificationConstants;
import com.agtech.pointprod.service.infrastructure.dal.daointerface.NotificationRecordDAO;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.NotificationRecordDO;
import com.agtech.pointprod.service.infrastructure.dal.mapper.NotificationRecordMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 通知记录DAO实现
 */
@Component
public class NotificationRecordDAOImpl extends ServiceImpl<NotificationRecordMapper, NotificationRecordDO> implements NotificationRecordDAO {
    
    @Override
    public List<NotificationRecordDO> selectNotificationList(String userId, String notificationType) {
        LambdaQueryWrapper<NotificationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础查询条件 - 只查询notification_records表，不使用JOIN或EXISTS
        queryWrapper.eq(NotificationRecordDO::getStatus, NotificationConstants.Status.UNREAD)
                   .eq(NotificationRecordDO::getUserId, userId)
                   .eq(NotificationRecordDO::getNotificationType, NotificationConstants.Type.ACCEPT)
                   .eq(NotificationRecordDO::getResourceType, notificationType)
                   .eq(NotificationRecordDO::getIsDeleted, NotificationConstants.Database.NOT_DELETED);
        
        // 排序：按创建时间倒序
        queryWrapper.orderByDesc(NotificationRecordDO::getGmtCreate);
        
        // 限制更多记录供gateway层过滤，避免分页问题
        queryWrapper.last(NotificationConstants.Sql.LIMIT_100);
        
        return list(queryWrapper);
    }
    
    @Override
    public NotificationRecordDO selectByNotificationId(String notificationId, String userId) {
        LambdaQueryWrapper<NotificationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据业务唯一键notificationId和userId查询，防止越权访问
        queryWrapper.eq(NotificationRecordDO::getNotificationId, notificationId)
                   .eq(NotificationRecordDO::getUserId, userId)
                   .eq(NotificationRecordDO::getIsDeleted, NotificationConstants.Database.NOT_DELETED);
        
        return getOne(queryWrapper);
    }
    
    @Override
    public List<NotificationRecordDO> selectNotificationListExcludeIds(String userId, List<String> excludeNotificationIds, int limit, String notificationType) {
        LambdaQueryWrapper<NotificationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础查询条件 - 只查询notification_records表，不使用JOIN或EXISTS
        queryWrapper.eq(NotificationRecordDO::getStatus, NotificationConstants.Status.UNREAD)
                   .eq(NotificationRecordDO::getUserId, userId)
                   .eq(NotificationRecordDO::getNotificationType, NotificationConstants.Type.ACCEPT)
                   .eq(NotificationRecordDO::getResourceType, notificationType)
                   .eq(NotificationRecordDO::getIsDeleted, NotificationConstants.Database.NOT_DELETED);
        
        // 排除已添加的通知ID
        if (!CollectionUtils.isEmpty(excludeNotificationIds)) {
            queryWrapper.notIn(NotificationRecordDO::getNotificationId, excludeNotificationIds);
        }
        
        // 排序：按创建时间倒序
        queryWrapper.orderByDesc(NotificationRecordDO::getResourceTime);
        
        // 使用SQL LIMIT限制查询数量
        queryWrapper.last("LIMIT " + limit);
        
        return list(queryWrapper);
    }
    
    @Override
    public List<NotificationRecordDO> selectByUserIdAndResourceIds(String userId, List<String> resourceIds, String resourceType) {
        if (CollectionUtils.isEmpty(resourceIds)) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<NotificationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础查询条件 - 只查询notification_records表，不使用JOIN或EXISTS
        queryWrapper.eq(NotificationRecordDO::getStatus, NotificationConstants.Status.UNREAD)
                   .eq(NotificationRecordDO::getUserId, userId)
                   .eq(NotificationRecordDO::getNotificationType, NotificationConstants.Type.ACCEPT)
                   .eq(NotificationRecordDO::getResourceType, resourceType)
                   .eq(NotificationRecordDO::getIsDeleted, NotificationConstants.Database.NOT_DELETED)
                   .in(NotificationRecordDO::getResourceId, resourceIds);
        
        // 排序：按创建时间倒序
        queryWrapper.orderByDesc(NotificationRecordDO::getGmtCreate);
        
        return list(queryWrapper);
    }
    
    @Override
    public List<NotificationRecordDO> selectByResourceIds(List<String> resourceIds, String resourceType) {
        if (CollectionUtils.isEmpty(resourceIds)) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<NotificationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础查询条件 - 只查询notification_records表，不使用JOIN或EXISTS
        queryWrapper.eq(NotificationRecordDO::getResourceType, resourceType)
                   .eq(NotificationRecordDO::getIsDeleted, NotificationConstants.Database.NOT_DELETED)
                   .in(NotificationRecordDO::getResourceId, resourceIds);
        
        // 排序：按创建时间倒序
        queryWrapper.orderByDesc(NotificationRecordDO::getGmtCreate);
        
        return list(queryWrapper);
    }
    
    @Override
    public boolean batchUpdateNotificationToRead(List<String> notificationIds, String userId) {
        if (CollectionUtils.isEmpty(notificationIds)) {
            return true;
        }
        
        LambdaUpdateWrapper<NotificationRecordDO> updateWrapper = new LambdaUpdateWrapper<>();
        
        // 更新条件：通知ID列表、用户ID、通知类型为ACCEPT、未删除
        updateWrapper.in(NotificationRecordDO::getNotificationId, notificationIds)
                    .eq(NotificationRecordDO::getUserId, userId) 
                    .eq(NotificationRecordDO::getIsDeleted, NumbersEnum.ZERO.getIntValue());
        
        // 设置更新字段
        Date now = ZonedDateUtil.now();
        updateWrapper.set(NotificationRecordDO::getStatus, NotificationConstants.Status.READ)
                    .set(NotificationRecordDO::getReadTime, now)
                    .set(NotificationRecordDO::getGmtModified, now);
        
        return update(updateWrapper);
    }
    
    @Override
    public boolean updateNotificationPushStatus(String notificationId, String pushStatus) {
        LambdaUpdateWrapper<NotificationRecordDO> updateWrapper = new LambdaUpdateWrapper<>();
        
        // 更新条件：通知ID、未删除
        updateWrapper.eq(NotificationRecordDO::getNotificationId, notificationId)
                    .eq(NotificationRecordDO::getIsDeleted, NumbersEnum.ZERO.getIntValue());
        
        // 设置更新字段
        Date now = ZonedDateUtil.now();
        updateWrapper.set(NotificationRecordDO::getPushStatus, pushStatus)
                    .set(NotificationRecordDO::getPushTime, now)
                    .set(NotificationRecordDO::getGmtModified, now);
        
        return update(updateWrapper);
    }
    
    @Override
    public List<NotificationRecordDO> selectNotificationForUpdate(String resourceId, String resourceType, String userId, String notificationType) {
        LambdaQueryWrapper<NotificationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 查询条件：资源ID、资源类型、用户ID、通知类型、未删除
        queryWrapper.eq(NotificationRecordDO::getResourceId, resourceId)
                    .eq(NotificationRecordDO::getResourceType, resourceType)
                    .eq(NotificationRecordDO::getNotificationType, notificationType)
                    .eq(NotificationRecordDO::getUserId, userId)
                    .eq(NotificationRecordDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        // 加锁查询（FOR UPDATE）
        queryWrapper.last("FOR UPDATE");
        
        return list(queryWrapper);
    }
    
    @Override
    public List<NotificationRecordDO> selectNotification(String resourceId, String resourceType, String userId, String notificationType) {
        LambdaQueryWrapper<NotificationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 查询条件：资源ID、资源类型、用户ID、通知类型、未删除
        queryWrapper.eq(NotificationRecordDO::getResourceId, resourceId)
                    .eq(NotificationRecordDO::getResourceType, resourceType)
                    .eq(NotificationRecordDO::getNotificationType, notificationType)
                    .eq(NotificationRecordDO::getUserId, userId)
                    .eq(NotificationRecordDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        return list(queryWrapper);
    }
    
    @Override
    public boolean batchUpdateNotificationByTypeToRead(String resourceType, String userId) {
        if (resourceType == null || resourceType.isEmpty()) {
            return false;
        }
        
        LambdaUpdateWrapper<NotificationRecordDO> updateWrapper = new LambdaUpdateWrapper<>();
        
        // 更新条件：资源类型、用户ID、通知类型为ACCEPT、状态为未读、未删除
        updateWrapper.eq(NotificationRecordDO::getResourceType, resourceType)
                    .eq(NotificationRecordDO::getUserId, userId)
                    .eq(NotificationRecordDO::getStatus, NotificationConstants.Status.UNREAD)
                    .eq(NotificationRecordDO::getIsDeleted, YesOrNoEnum.NO.getValue());
        
        // 设置更新字段
        Date now = ZonedDateUtil.now();
        updateWrapper.set(NotificationRecordDO::getStatus, NotificationConstants.Status.READ)
                    .set(NotificationRecordDO::getReadTime, now)
                    .set(NotificationRecordDO::getGmtModified, now);
        
        return update(updateWrapper);
    }
} 