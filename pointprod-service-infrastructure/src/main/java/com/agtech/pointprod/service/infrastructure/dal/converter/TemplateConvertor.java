package com.agtech.pointprod.service.infrastructure.dal.converter;

import com.agtech.pointprod.service.domain.common.enums.TemplateStatusEnum;
import com.agtech.pointprod.service.domain.model.Template;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.TemplateDO;
import com.agtech.pointprod.service.infrastructure.dto.request.TemplateValidCountCheckReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Mapper(componentModel = "spring")
public interface TemplateConvertor {

    List<Template> convertFromDoToDomainModels(List<TemplateDO> templateList);

    @Mapping(target = "status", expression = "java(this.convertTemplateStatus(templateDO.getStatus(), templateDO.getEndTime()))")
    Template doToDomain(TemplateDO templateDO);

    TemplateDO domainToDo(Template template);

    TemplateValidCountCheckReq templateToValidCheckReq(Template template);

    default String convertTemplateStatus(String status, Date endTime){
        if (TemplateStatusEnum.ABLE.getCode().equals(status) && endTime!=null){
            return endTime.before(new Date())?TemplateStatusEnum.EXPIRED.getCode() : TemplateStatusEnum.ABLE.getCode();
        }
        return status;
    }
}

