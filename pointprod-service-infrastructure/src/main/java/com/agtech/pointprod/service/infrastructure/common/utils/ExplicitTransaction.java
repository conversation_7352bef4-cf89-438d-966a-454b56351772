package com.agtech.pointprod.service.infrastructure.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 显式事务
 * <AUTHOR>
 */
@Component
@Slf4j
public class ExplicitTransaction {


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public <T> T invokeWithNewTransaction(TxCallable<T> callable) {
        return callable.call();
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public <T> T invokeWithRequiredTransaction(TxCallable<T> callable){
        return callable.call();
    }

    /**
     * <AUTHOR>
     */
    @FunctionalInterface
    public interface TxCallable<V> {
        V call() ;
    }
}
