package com.agtech.pointprod.service.infrastructure.integration.risk.translator.impl;

import com.agtech.common.result.GenericResult;
import com.agtech.pointprod.order.service.domain.common.enums.RiskBizTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.infrastructure.integration.risk.dto.RiskTextValueDTO;
import com.agtech.pointprod.service.infrastructure.integration.risk.dto.TextSecurityRequest;
import com.agtech.pointprod.service.infrastructure.integration.risk.dto.TextSecurityResponse;
import com.agtech.pointprod.service.infrastructure.integration.risk.dto.TextValueDTO;
import com.agtech.pointprod.service.infrastructure.integration.risk.translator.RiskClient;
import com.agtech.pointprod.service.infrastructure.integration.risk.translator.RiskTranslatorService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RiskTranslatorServiceImpl implements RiskTranslatorService {

    @Resource
    private RiskClient riskClient;

    private static final String ORDER_MEMO = "orderMemo";

    @Override
    public String validateText(String text) {
        TextSecurityRequest textSecurityRequest = new TextSecurityRequest();
        List<TextValueDTO> textValueList = new ArrayList<>();
        TextValueDTO textValue = new TextValueDTO();
        textValue.setTag(ORDER_MEMO);
        textValue.setValue(text);
        textValueList.add(textValue);
        textSecurityRequest.setTexts(textValueList);
        textSecurityRequest.setBizType(RiskBizTypeEnum.MCOIN_TRANSFER_REMARK.getCode());
        long startTime = System.currentTimeMillis();
        try {
            GenericResult<TextSecurityResponse> genericResult = riskClient.textSecurityV2(textSecurityRequest);
            long endTime = System.currentTimeMillis();
            log.info("[validateText] request:{}, response:{}, cost:{}ms",
                    com.alibaba.fastjson2.JSON.toJSONString(textSecurityRequest),
                    com.alibaba.fastjson2.JSON.toJSONString(genericResult),
                    endTime - startTime);
            if(genericResult.isSuccess() && null != genericResult.getValue() && CollectionUtils.isNotEmpty(genericResult.getValue().getRisks())){
                List<RiskTextValueDTO> riskTextValues = genericResult.getValue().getRisks();
                return parseRiskError(riskTextValues);
            }
        }catch (Exception e){
            long endTime = System.currentTimeMillis();
            log.error("[validateText] exception, request:{}, errMsg:{}, cost:{}ms",
                    com.alibaba.fastjson2.JSON.toJSONString(textSecurityRequest), e.getMessage(), endTime - startTime, e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ACCESS_MCOIN_SERVICE_FAIL);
        }

        return null;
    }

    private String parseRiskError(List<RiskTextValueDTO> riskTextValues) {
        Map<String, Set<String>> errors = Maps.newHashMap();
        for (RiskTextValueDTO riskTextValueDTO : riskTextValues) {
            if(!errors.containsKey(riskTextValueDTO.getTag())){
                errors.put(riskTextValueDTO.getTag(), Sets.newHashSet());
            }
            errors.get(riskTextValueDTO.getTag()).add(riskTextValueDTO.getValue());
        }
        if(MapUtils.isEmpty(errors)){
            return null;
        }
        Map<String,String> errorShow = Maps.newHashMap();
        for (Map.Entry<String, Set<String>> errorEntry : errors.entrySet()) {
            errorShow.put(errorEntry.getKey(),"message invalid: "+ StringUtils.join(errorEntry.getValue(),'、'));
        }
        return JSON.toJSONString(errorShow);
    }

}
