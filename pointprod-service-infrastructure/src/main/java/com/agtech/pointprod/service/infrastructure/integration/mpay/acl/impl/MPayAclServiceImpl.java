package com.agtech.pointprod.service.infrastructure.integration.mpay.acl.impl;

import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.infrastructure.config.MpayConfig;
import com.agtech.pointprod.service.infrastructure.integration.mpay.acl.MPayAclService;
import com.agtech.pointprod.service.infrastructure.integration.mpay.adapter.MPayAdapterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class MPayAclServiceImpl implements MPayAclService {
    @Resource
    private MPayAdapterService mPayAdapterService;

    @Resource
    private MpayConfig mpayConfig;

    @Override
    public MPayUserInfo getUserMsg(String custId) {
        return mPayAdapterService.getUserMsg(custId);
    }

    @Override
    public boolean checkSecurityId(String custId, String orderId, String securityId) {
        return mPayAdapterService.checkSecurityId(custId, orderId, securityId);
    }

    @Override
    public MPayUserInfo getUserInfo(String areaCode, String phone) {
        return mPayAdapterService.getUserInfo(areaCode, phone);
    }

    @Override
    public List<MPayUserInfo> getUserMsgList(List<String> custIds) {
        // custid数据拆分查询,合并
        List<MPayUserInfo> mPayUserInfoList = new ArrayList<>();
        List<MPayUserInfo> mPayUserInfoPart = null;
        int limit = mpayConfig.getUserBatchQueryCountLimit();
        for (int i=0;i<custIds.size();i+=limit){
            mPayUserInfoPart = mPayAdapterService.getUserMsgList(custIds.subList(i, Math.min(i+limit, custIds.size())));
            if (CollectionUtils.isNotEmpty(mPayUserInfoPart)){
                mPayUserInfoList.addAll(mPayUserInfoPart);
            }
        }
        return mPayUserInfoList;
    }
}
