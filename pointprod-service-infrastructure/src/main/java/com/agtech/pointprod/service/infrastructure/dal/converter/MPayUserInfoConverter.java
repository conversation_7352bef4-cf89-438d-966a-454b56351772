package com.agtech.pointprod.service.infrastructure.dal.converter;

import com.agtech.pointprod.service.infrastructure.integration.mpay.dto.UserMsgDTO;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MPayUserInfoConverter {
    MPayUserInfo dto2Model(UserMsgDTO userMsgDTO);

    List<MPayUserInfo> dtoListToModelList(List<UserMsgDTO> userMsgDtoList);
}
