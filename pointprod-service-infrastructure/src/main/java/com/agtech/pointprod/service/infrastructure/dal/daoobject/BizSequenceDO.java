package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@TableName("fund_biz_sequence")
@Getter
@Setter
public class BizSequenceDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String name;
    private Long currentValue;
    private Long minValue;
    private Long maxValue;
    private Integer step;
    private Integer isDeleted;
    private String description;
    private Date gmtCreate;
    private Date gmtModified;
//    private String creator;
//    private String modifier;
//    private String bizCode;
//    private Integer version;
}
