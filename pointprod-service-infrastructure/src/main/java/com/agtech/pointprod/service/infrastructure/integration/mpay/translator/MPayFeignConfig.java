package com.agtech.pointprod.service.infrastructure.integration.mpay.translator;

import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.agtech.pointprod.service.domain.common.enums.NumbersEnum;

import feign.RequestInterceptor;
import feign.Retryer;

@Configuration
public class MPayFeignConfig {

    @Resource
    private KeyService keyService;

    @Bean
    public RequestInterceptor mPayFeignSignatureInterceptor() {
        return new MPayFeignSignatureInterceptor(keyService);
    }
    
    /**
     * Configure retry mechanism (3 retries)
     */
    @Bean
    public Retryer retryer() {
        // First parameter is initial backoff in ms, second is max backoff in ms, third is max retry attempts
        return new Retryer.Default(
                NumbersEnum.ONE_HUNDRED.getIntValue(), 
                TimeUnit.SECONDS.toMillis(NumbersEnum.ONE.getIntValue()),
                NumbersEnum.THREE.getIntValue());
    }
} 