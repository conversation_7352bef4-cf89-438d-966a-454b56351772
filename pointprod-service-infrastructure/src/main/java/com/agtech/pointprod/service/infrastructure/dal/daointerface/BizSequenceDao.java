package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.BizSequenceDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 业务序列表：一旦从数据成功获取并更新可分配的序列区间后，事务需要单独提交，不能和上游业务代码共用一个事务
 * 否则并发场景下，当上游因业务等失败回滚事务后，会导致多个服务拥有相同的序列号区间，即存在同时生成重复序列号的可能。<br/>
 * 需要注意：涉及调用<code>IService</code>中写方法的地方，没有新开事务，就不要直接调用
 */
public interface BizSequenceDao extends IService<BizSequenceDO> {

    BizSequenceDO queryBizSequenceEntity(String bizName);

    boolean updateCurrentValue(Long currentValue, String tableName);

    ConcurrentHashMap<String, BizSequenceDO> queryBizSequenceEntitys();

    int nextValue(String bizName);

    // int syncNextValue(String bizName);

}
