package com.agtech.pointprod.service.infrastructure.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;


/**
 * 线程池配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Autowired
    private ThreadPoolProperties threadPoolProperties;

    public static final String THREAD_NAME_ORDER_SERVICE = "order-";

    @Bean("orderServiceThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setThreadNamePrefix(THREAD_NAME_ORDER_SERVICE);
        threadPoolTaskExecutor.setCorePoolSize(threadPoolProperties.getOrderService().getCorePoolSize());
        threadPoolTaskExecutor.setMaxPoolSize(threadPoolProperties.getOrderService().getMaxPoolSize());
        threadPoolTaskExecutor.setQueueCapacity(threadPoolProperties.getOrderService().getQueueCapacity());
        threadPoolTaskExecutor.setKeepAliveSeconds(threadPoolProperties.getOrderService().getKeepAliveSeconds());
        threadPoolTaskExecutor.setTaskDecorator(new MdcTaskDecorator());
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(threadPoolProperties.getOrderService().getAwaitTerminationSeconds());
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return threadPoolTaskExecutor;
    }

    /**
     * 事务后消息处理线程池
     * 专门用于处理事务提交后的异步消息发送
     */
    @Bean("postTransactionMessageExecutor")
    public Executor postTransactionMessageExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(threadPoolProperties.getPostTransactionMessage().getCorePoolSize());
        
        // 最大线程数
        executor.setMaxPoolSize(threadPoolProperties.getPostTransactionMessage().getMaxPoolSize());
        
        // 队列容量
        executor.setQueueCapacity(threadPoolProperties.getPostTransactionMessage().getQueueCapacity());
        
        // 线程名前缀
        executor.setThreadNamePrefix("PostTxMsg-");
        
        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(threadPoolProperties.getPostTransactionMessage().getAwaitTerminationSeconds());
        
        executor.setTaskDecorator(new MdcTaskDecorator());
        executor.initialize();
        return executor;
    }



    /**
     * 支付异步处理线程池
     */
    @Bean("paymentExecutor")
    public Executor paymentExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(threadPoolProperties.getPayment().getCorePoolSize());

        // 最大线程数
        executor.setMaxPoolSize(threadPoolProperties.getPayment().getMaxPoolSize());

        // 队列容量
        executor.setQueueCapacity(threadPoolProperties.getPayment().getQueueCapacity());

        // 线程名前缀
        executor.setThreadNamePrefix("Payment-");

        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间
        executor.setAwaitTerminationSeconds(threadPoolProperties.getPayment().getAwaitTerminationSeconds());

        executor.setTaskDecorator(new MdcTaskDecorator());
        executor.initialize();
        return executor;
    }


    /**
     * 后端处理线程池
     */
    @Bean("backendExecutor")
    public Executor backendExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(threadPoolProperties.getBackend().getCorePoolSize());

        // 最大线程数
        executor.setMaxPoolSize(threadPoolProperties.getBackend().getMaxPoolSize());

        // 队列容量
        executor.setQueueCapacity(threadPoolProperties.getBackend().getQueueCapacity());

        // 线程名前缀
        executor.setThreadNamePrefix("Backend-");

        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间
        executor.setAwaitTerminationSeconds(threadPoolProperties.getBackend().getAwaitTerminationSeconds());

        executor.setTaskDecorator(new MdcTaskDecorator());
        executor.initialize();
        return executor;
    }

}