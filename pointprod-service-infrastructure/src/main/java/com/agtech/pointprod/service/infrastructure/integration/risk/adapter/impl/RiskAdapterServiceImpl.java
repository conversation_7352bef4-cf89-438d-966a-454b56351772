package com.agtech.pointprod.service.infrastructure.integration.risk.adapter.impl;

import com.agtech.pointprod.service.infrastructure.integration.risk.adapter.RiskAdapterService;
import com.agtech.pointprod.service.infrastructure.integration.risk.translator.RiskTranslatorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version RiskAdapterServiceImpl.java, v0.1 2025/7/3 17:32 zhongqigang.zqg Exp $
 */
@Service
@Slf4j
public class RiskAdapterServiceImpl implements RiskAdapterService {
    @Resource
    private RiskTranslatorService riskTranslatorService;

    @Override
    public String validateText(String text){
        return riskTranslatorService.validateText(text);
    }
}
