package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@TableName("contract_confirm")
public class ContractConfirmDO {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String contractConfirmId;
    private String contractId;
    private String userId;
    private String status;
    private Date gmtCreate;
    private Date gmtModified;
    private Integer isDeleted;
}
