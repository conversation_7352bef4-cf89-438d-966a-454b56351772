package com.agtech.pointprod.service.infrastructure.dal.daoobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Table: black_list
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("black_list")
public class BlackListDO {
    /**
     * Column: id
     * Type: BIGINT
     * Remark: 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Column: black_list_id
     * Type: VARCHAR(64)
     * Remark: 业务id
     */
    private String blackListId;

    /**
     * Column: entity_type
     * Type: VARCHAR(64)
     * Remark: 实体类型(PERSON-个人/COMPANY-企业)
     */
    private String entityType;

    /**
     * Column: entity_id
     * Type: VARCHAR(64)
     * Remark: 实体唯一标识
     */
    private String entityId;

    /**
     * Column: reason
     * Type: VARCHAR(255)
     * Remark: 加入黑名单原因
     */
    private String reason;

    /**
     * Column: account_type
     * Type: VARCHAR(32)
     * Remark: 账户类型(IP/EMAIL/ID_CARD等)
     */
    private String accountType;

    /**
     * Column: gmt_create
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date gmtCreate;

    /**
     * Column: gmt_modified
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date gmtModified;

    /**
     * Column: is_deleted
     * Type: TINYINT(3)
     * Default value: 0
     */
    private Integer isDeleted;

    private String creator;

    private String modifier;
}