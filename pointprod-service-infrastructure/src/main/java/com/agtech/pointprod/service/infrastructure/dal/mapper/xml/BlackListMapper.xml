<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agtech.pointprod.service.infrastructure.dal.mapper.BlackListMapper">
  <resultMap id="BaseResultMap" type="com.agtech.pointprod.service.infrastructure.dal.daoobject.BlackListDO">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="black_list_id" jdbcType="VARCHAR" property="blackListId" />
    <result column="entity_type" jdbcType="VARCHAR" property="entityType" />
    <result column="entity_id" jdbcType="VARCHAR" property="entityId" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="account_type" jdbcType="VARCHAR" property="accountType" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="valid_until" jdbcType="VARCHAR" property="validUntil" />
  </resultMap>

</mapper>