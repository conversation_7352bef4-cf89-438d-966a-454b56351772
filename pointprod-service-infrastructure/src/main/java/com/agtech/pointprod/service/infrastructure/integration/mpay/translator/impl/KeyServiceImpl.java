package com.agtech.pointprod.service.infrastructure.integration.mpay.translator.impl;

import com.agtech.pointprod.service.infrastructure.integration.mpay.translator.KeyService;
import com.agtech.pointprod.service.infrastructure.config.sign.SecretKeyConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class KeyServiceImpl implements KeyService {
    @Resource
    private SecretKeyConfig secretKeyConfig;
    @Value("${mpay.key-version:1}")
    private String keyVersion;
    // partnerId在secret-key.config中约定为"mpay"
    private static final String PARTNER_ID = "mpay";

    @Override
    public String getPrivateKey(String keyVersion) {
        SecretKeyConfig.KeyInfo keyInfo = secretKeyConfig.findKeyInfo(PARTNER_ID);
        return keyInfo != null ? keyInfo.getPrivateKey() : null;
    }

    @Override
    public String getPublicKey(String keyVersion) {
        SecretKeyConfig.KeyInfo keyInfo = secretKeyConfig.findKeyInfo(PARTNER_ID);
        return keyInfo != null ? keyInfo.getPublicKey() : null;
    }

    @Override
    public String getCurrentKeyVersion() {
        return keyVersion;
    }

    @Override
    public String getClientId() {
        SecretKeyConfig.KeyInfo keyInfo = secretKeyConfig.findKeyInfo(PARTNER_ID);
        return keyInfo != null ? keyInfo.getClientId() : null;
    }
} 