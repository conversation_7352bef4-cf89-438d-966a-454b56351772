package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.ContractConfirmDO;
import com.agtech.pointprod.service.infrastructure.dto.response.UserConfirmContractRsp;

import java.util.List;

public interface ContractConfirmDAO {
    ContractConfirmDO queryContractConfirmByUserIdAndContractId(String userId, String contractId);

//    ContractConfirmDO queryContractConfirmByUserIdAndContractIdForLock(String userId, String contractId);

    boolean updateContractConfirmForUser(String userId, String contractId, String status);

    boolean saveContractConfirm(ContractConfirmDO contractConfirmDO);

    List<UserConfirmContractRsp> queryMultiUserConfirmContractList(String contractType, String status, List<String> userIds);
}
