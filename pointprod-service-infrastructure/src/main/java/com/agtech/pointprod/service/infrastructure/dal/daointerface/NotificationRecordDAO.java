package com.agtech.pointprod.service.infrastructure.dal.daointerface;

import java.util.List;

import com.agtech.pointprod.service.infrastructure.dal.daoobject.NotificationRecordDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 通知记录DAO接口
 */
public interface NotificationRecordDAO extends IService<NotificationRecordDO> {
    
    /**
     * 查询用户的通知列表
     * 
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 通知记录列表
     */
    List<NotificationRecordDO> selectNotificationList(String userId, String notificationType);
    
    /**
     * 根据通知ID和用户ID查询单个通知记录
     * 
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 通知记录，如果不存在则返回null
     */
    NotificationRecordDO selectByNotificationId(String notificationId, String userId);
    
    /**
     * 查询用户的通知列表（排除指定的通知ID）
     * 
     * @param userId 用户ID
     * @param excludeNotificationIds 需要排除的通知ID列表
     * @param limit 查询数量限制
     * @param notificationType 通知类型
     * @return 通知记录列表
     */
    List<NotificationRecordDO> selectNotificationListExcludeIds(String userId, List<String> excludeNotificationIds, int limit, String notificationType);
    
    /**
     * 根据用户ID和资源ID列表查询通知记录
     * 
     * @param userId 用户ID
     * @param resourceIds 资源ID列表
     * @param resourceType 资源类型
     * @return 通知记录列表
     */
    List<NotificationRecordDO> selectByUserIdAndResourceIds(String userId, List<String> resourceIds, String resourceType);
    
    /**
     * 根据资源ID列表和资源类型查询通知记录（不过滤用户ID）
     * 
     * @param resourceIds 资源ID列表
     * @param resourceType 资源类型
     * @return 通知记录列表
     */
    List<NotificationRecordDO> selectByResourceIds(List<String> resourceIds, String resourceType);
    
    /**
     * 批量更新通知记录为已读状态
     * 
     * @param notificationIds 通知ID列表
     * @param userId 用户ID
     * @return 更新是否成功
     */
    boolean batchUpdateNotificationToRead(List<String> notificationIds, String userId);
    
    /**
     * 根据资源类型批量更新通知记录为已读状态
     * 
     * @param resourceType 资源类型
     * @param userId 用户ID
     * @return 更新是否成功
     */
    boolean batchUpdateNotificationByTypeToRead(String resourceType, String userId);
    
    /**
     * 更新通知推送状态
     * 
     * @param notificationId 通知ID
     * @param pushStatus 推送状态
     * @return 更新是否成功
     */
    boolean updateNotificationPushStatus(String notificationId, String pushStatus);
    
    /**
     * 带锁查询通知记录（用于检查重复推送）
     * 
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 通知记录列表
     */
    List<NotificationRecordDO> selectNotificationForUpdate(String resourceId, String resourceType, String userId, String notificationType);
    
    /**
     * 不带锁查询通知记录
     * 
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 通知记录列表
     */
    List<NotificationRecordDO> selectNotification(String resourceId, String resourceType, String userId, String notificationType);
} 