package com.agtech.pointprod.service.infrastructure.dal.converter;

import com.agtech.pointprod.service.domain.model.ContractConfirm;
import com.agtech.pointprod.service.infrastructure.dal.daoobject.ContractConfirmDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ContractConfirmConverter {
    ContractConfirm do2Model(ContractConfirmDO contractConfirmDO);

    ContractConfirmDO model2Do(ContractConfirm contractConfirm);
}
