package com.agtech.pointprod.service.infrastructure.gateway.impl;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.domain.common.enums.*;
import com.agtech.pointprod.service.domain.gateway.BlackListGateway;
import com.agtech.pointprod.service.domain.gateway.TransferredUserGateway;
import com.agtech.pointprod.service.domain.model.BlackList;
import com.agtech.pointprod.service.domain.model.TransferFundAccumulation;
import com.agtech.pointprod.service.facade.dto.req.QueryTransferredUserListReq;
import com.agtech.pointprod.service.facade.dto.rsp.QueryTransferredUserListRsp;
import com.agtech.pointprod.service.infrastructure.dto.response.UserConfirmContractRsp;
import com.agtech.pointprod.service.infrastructure.repository.ContractConfirmRepository;
import com.agtech.pointprod.service.infrastructure.repository.TransferFundAccumulationRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 15:09
 */
@Component
public class TransferredUserGatewayImpl implements TransferredUserGateway {

    @Resource
    private TransferFundAccumulationRepository transferFundAccumulationRepository;

    @Resource
    private BlackListGateway blackListGateway;

    @Resource
    private ContractConfirmRepository contractConfirmRepository;

    @Override
    public PageInfoDTO<QueryTransferredUserListRsp> queryTemplateListByPage(QueryTransferredUserListReq listReq) {
        PageInfoDTO<TransferFundAccumulation> pageInfoDTO = CollectionUtils.isEmpty(listReq.getUserIds())?selectAll(listReq):selectSpecifiedUserList(listReq);
        return PageInfoDTO.of(pageInfoDTO.getTotalCount(), pageInfoDTO.getPageSize(), pageInfoDTO.getCurrentPage(), buildQueryTransferredUserListRspList(pageInfoDTO.getRecords()));
    }

    private PageInfoDTO<TransferFundAccumulation> selectAll(QueryTransferredUserListReq listReq){
        List<String> includeUserIds = null;
        List<String> excludeUserIds = null;
        String specStatus = listReq.getStatus();
        if (TransferredUserStatusEnum.ABLE.getCode().equals(specStatus)){
            excludeUserIds = blackListGateway.selectAllEntityIdsInBlackList(BlackListEntityTypeEnum.PERSON.getValue(), BlackListAccountTypeEnum.USER_ID.getValue(), null);
        }else if (TransferredUserStatusEnum.DISABLED.getCode().equals(specStatus)){
            includeUserIds = blackListGateway.selectAllEntityIdsInBlackList(BlackListEntityTypeEnum.PERSON.getValue(), BlackListAccountTypeEnum.USER_ID.getValue(), null);
        }
        return transferFundAccumulationRepository.queryTemplateListByPage(includeUserIds, excludeUserIds, listReq.getPageSize(), listReq.getPageNo());
    }

    private PageInfoDTO<TransferFundAccumulation> selectSpecifiedUserList(QueryTransferredUserListReq listReq){
        List<String> includeUserIds = new ArrayList<>(listReq.getUserIds());
        List<String> excludeUserIds = null;
        String specStatus = listReq.getStatus();
        if (TransferredUserStatusEnum.ABLE.getCode().equals(specStatus)){
            List<String> blackListUserIds = blackListGateway.selectAllEntityIdsInBlackList(BlackListEntityTypeEnum.PERSON.getValue(), BlackListAccountTypeEnum.USER_ID.getValue(), includeUserIds);
            includeUserIds.removeAll(blackListUserIds);
        }else if (TransferredUserStatusEnum.DISABLED.getCode().equals(specStatus)){
            includeUserIds = blackListGateway.selectAllEntityIdsInBlackList(BlackListEntityTypeEnum.PERSON.getValue(), BlackListAccountTypeEnum.USER_ID.getValue(), includeUserIds);
        }

        if (CollectionUtils.isEmpty(includeUserIds)){
            return PageInfoDTO.of(0L, listReq.getPageSize().longValue(), listReq.getPageNo().longValue(), new ArrayList<TransferFundAccumulation>());
        }

        return transferFundAccumulationRepository.queryTemplateListByPage(includeUserIds, excludeUserIds, listReq.getPageSize(), listReq.getPageNo());
    }


    @Override
    public void ableUser(String userId, String operator) {
        changeUserBlackListStatus(userId, DeletedEnum.YES.getCode(), operator);
    }

    @Override
    public void disabledUser(String userId, String operator) {
        BlackList blackList = blackListGateway.selectBlackList(BlackListEntityTypeEnum.PERSON.getValue(), BlackListAccountTypeEnum.USER_ID.getValue(), userId);
        if (blackList == null){
            blackList = new BlackList();
            blackList.setEntityId(userId);
            blackList.setEntityType(BlackListEntityTypeEnum.PERSON.getValue());
            blackList.setAccountType(BlackListAccountTypeEnum.USER_ID.getValue());
            blackList.setReason("");
            blackList.setCreator(operator);
            blackList.setModifier(operator);
            try {
                blackListGateway.save(blackList);
                return;
            }catch (DuplicateKeyException e){
                blackList = blackListGateway.selectBlackList(BlackListEntityTypeEnum.PERSON.getValue(), BlackListAccountTypeEnum.USER_ID.getValue(), userId);
            }
        }
        if (DeletedEnum.YES.getCode().equals(blackList.getIsDeleted())){
            changeUserBlackListStatus(userId, DeletedEnum.NO.getCode(), operator);
        }
    }

    /**
     *
     * @param transferFundAccumulations
     * @return
     */
    private List<QueryTransferredUserListRsp> buildQueryTransferredUserListRspList(List<TransferFundAccumulation> transferFundAccumulations){
        List<QueryTransferredUserListRsp> queryTransferredUserListRspList = new ArrayList<>();
        if (CollectionUtils.isEmpty(transferFundAccumulations)){
            return queryTransferredUserListRspList;
        }
        // 获取用户状态Map
        List<String> userIds = transferFundAccumulations.stream().map(TransferFundAccumulation::getUserId).collect(Collectors.toList());
        Map<String, BlackList> disableMap = new HashMap<>();
        Map<String, BlackList> ableMap = new HashMap<>();
        fillIntoBlackListMapByUserIds(disableMap, ableMap, userIds);

        // 查询协议确认时间
        Map<String, Date> agreementConfirmTimeMap = queryContractConfirmTimeMap(userIds);

        // 数据填充
        for (TransferFundAccumulation transferFundAccumulation : transferFundAccumulations){
            QueryTransferredUserListRsp queryTransferredUserListRsp = new QueryTransferredUserListRsp();
            String userId = transferFundAccumulation.getUserId();

            queryTransferredUserListRsp.setUserId(userId);
            queryTransferredUserListRsp.setTransferredPoints(transferFundAccumulation.getFundOutAmount()==null?0:transferFundAccumulation.getFundOutAmount().intValue());
            queryTransferredUserListRsp.setReceivedPoints(transferFundAccumulation.getFundInAmount()==null?0:transferFundAccumulation.getFundInAmount().intValue());

            // 协议确认时间
            queryTransferredUserListRsp.setLastContractConfirmTime(agreementConfirmTimeMap.get(userId));

            // 填充解封，禁用时间
            BlackList disable = disableMap.get(userId);
            if(disable == null){
                queryTransferredUserListRsp.setStatus(TransferredUserStatusEnum.ABLE.getCode());
                BlackList able = ableMap.get(userId);
                if (able != null){
                    queryTransferredUserListRsp.setAbleTime(able.getGmtModified());
                    queryTransferredUserListRsp.setOperator(able.getModifier());
                }
            }else {
                queryTransferredUserListRsp.setStatus(TransferredUserStatusEnum.DISABLED.getCode());
                queryTransferredUserListRsp.setDisableTime(disable.getGmtModified());
                queryTransferredUserListRsp.setOperator(disable.getModifier());
            }
            queryTransferredUserListRspList.add(queryTransferredUserListRsp);
        }
        return queryTransferredUserListRspList;
    }

    private void fillIntoBlackListMapByUserIds(Map<String, BlackList> disableMap, Map<String, BlackList> ableMap, List<String> userIds){
        List<BlackList> blackLists = blackListGateway.selectBlackLists(BlackListEntityTypeEnum.PERSON.getValue(), BlackListAccountTypeEnum.USER_ID.getValue(), userIds);
        for (BlackList blackList : blackLists){
            if (DeletedEnum.NO.getCode().equals(blackList.getIsDeleted())){
                disableMap.put(blackList.getEntityId(), blackList);
            }else {
                ableMap.put(blackList.getEntityId(), blackList);
            }
        }
    }

    /**
     * 获取确认表有效，且合约表版本号最大的那条记录的修改时间
     * @param userIds
     * @return
     */
    private Map<String, Date> queryContractConfirmTimeMap(List<String> userIds){
        Map<String, Date> result = new HashMap<>();
        List<UserConfirmContractRsp> contractConfirms = contractConfirmRepository.queryMultiUserConfirmContract(ContractTypeEnum.MCOIN_TRANSFER.getValue(), ContractConfirmStatusEnum.AGREE.getValue(), userIds);
        if (CollectionUtils.isEmpty(contractConfirms)){
            return result;
        }
        Map<String, Integer> versionMap = new HashMap<>(userIds.size());
        for (UserConfirmContractRsp contractConfirm : contractConfirms){
            String userId = contractConfirm.getUserId();
            Integer version = versionMap.get(userId);
            if (version == null || contractConfirm.getVersion() > version){
                result.put(userId, contractConfirm.getGmtModified());
                versionMap.put(userId, contractConfirm.getVersion());
            }
        }
        return result;
    }

    private void changeUserBlackListStatus(String userId, Integer isDeleted, String operator){
        BlackList blackList = new BlackList();
        blackList.setIsDeleted(isDeleted);
        blackList.setModifier(operator);
        blackListGateway.updateByEntity(blackList, BlackListEntityTypeEnum.PERSON.getValue(), BlackListAccountTypeEnum.USER_ID.getValue(), userId);
    }
}
