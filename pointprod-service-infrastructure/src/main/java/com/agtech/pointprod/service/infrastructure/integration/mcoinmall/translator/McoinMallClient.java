package com.agtech.pointprod.service.infrastructure.integration.mcoinmall.translator;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import com.agtech.pointprod.service.infrastructure.integration.mcoinmall.dto.MPayAppTokenResponse;

@FeignClient(name = "mCoinMallClient", url = "${mcoin.mall.url:}")
public interface McoinMallClient {

    /**
     * 商户向用户推送消息接口
     * 
     * @return 推送消息响应
     */
    @GetMapping(value = "/api/management/app/token")
    MPayAppTokenResponse getAppToken();
}
