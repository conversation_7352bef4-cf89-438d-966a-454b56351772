package com.agtech.pointprod.service.infrastructure.repository;

import com.agtech.pointprod.service.domain.model.BlackList;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/18 10:46
 */
public interface BlackListRepository {
    BlackList queryTransferBlackListByUserId(String userId);

    long countBlackList(String userId);

    BlackList selectBlackList(String entityType, String accountType, String entityId);

    List<BlackList> selectBlackLists(String entityType, String accountType, List<String> entityIds);

    void save(BlackList blackList);

    void updateByEntity(BlackList blackList, String entityType, String accountType, String entityId);

    List<String> selectAllEntityIdsInBlackList(String entityType, String accountType, List<String> entityIds);
}
