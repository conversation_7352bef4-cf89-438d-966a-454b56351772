package com.agtech.pointprod.limit.service.infrastructure.dal.repository;

import com.agtech.pointprod.limit.service.domain.gateway.condition.CumulateLogQueryCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateLog;

import java.util.List;

public interface CumulateLogRepository  {

    void insert(CumulateLog cumulateLog);

    int batchInsert(List<CumulateLog> cumulateLogs);

    List<CumulateLog> queryLog(CumulateLogQueryCondition accumulateLogCondition);
}
