package com.agtech.pointprod.limit.service.infrastructure.dal.repository;

import com.agtech.pointprod.limit.service.domain.gateway.condition.AccumulateConfigQueryCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateConfig;

import java.util.List;

public interface AccumulateConfigRepository {

    List<AccumulateConfig> queryAccumulateConfigs(AccumulateConfigQueryCondition condition);
}
