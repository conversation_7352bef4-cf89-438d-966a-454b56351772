package com.agtech.pointprod.limit.service.infrastructure.convert;

import com.agtech.pointprod.limit.service.domain.common.convert.Converter;
import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.facade.dto.enums.PrincipalTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.infrastructure.dal.dataobject.CumulateAccountDO;
import com.agtech.common.lang.money.MultiCurrencyMoney;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ class:CumulateAccountConvert, v1.0 2025/06/22 14:39 xiaoming Exp $
 */
@Mapper(componentModel = "spring")
public interface CumulateAccountConvert extends Converter<CumulateAccount, CumulateAccountDO> {

    CumulateAccountConvert INSTANCE = Mappers.getMapper(CumulateAccountConvert.class);

    @Override
    default CumulateAccount doBackward(CumulateAccountDO cumulateAccountDO) {
        if (Objects.isNull(cumulateAccountDO)) {
            return null;
        }
        CumulateAccount cumulateAccount = new CumulateAccount();
        cumulateAccount.setId(cumulateAccountDO.getId());
        cumulateAccount.setAccountId(cumulateAccountDO.getAccountId());
        cumulateAccount.setPrincipalId(cumulateAccountDO.getPrincipalId());
        cumulateAccount.setPrincipalType(PrincipalTypeEnum.valueOf(cumulateAccountDO.getPrincipalType()));
        cumulateAccount.setSceneCode(cumulateAccountDO.getSceneCode());
        cumulateAccount.setBizTime(cumulateAccountDO.getBizTime());
        if(null != cumulateAccountDO.getAmount() && StringUtils.isNotBlank(cumulateAccountDO.getCurrency())) {
            cumulateAccount.setAmount(new MultiCurrencyMoney(cumulateAccountDO.getAmount(), cumulateAccountDO.getCurrency()));
        }
        cumulateAccount.setCount(cumulateAccountDO.getCount());
        cumulateAccount.setStrategy(LimitTypeEnum.getLimitTypeEnum(cumulateAccountDO.getStrategy()));
        return cumulateAccount;
    }

    @Override
    default CumulateAccountDO doForward(CumulateAccount cumulateAccount) {
        if (Objects.isNull(cumulateAccount)) {
            return null;
        }
        CumulateAccountDO cumulateAccountDO = new CumulateAccountDO();
        cumulateAccountDO.setId(cumulateAccount.getId());
        cumulateAccountDO.setBizTime(cumulateAccount.getBizTime());
        cumulateAccountDO.setTntInstId(cumulateAccount.getTntInst().getCode());
        cumulateAccountDO.setAccountId(cumulateAccount.getAccountId());
        cumulateAccountDO.setPrincipalId(cumulateAccount.getPrincipalId());
        cumulateAccountDO.setPrincipalType(cumulateAccount.getPrincipalType().name());
        cumulateAccountDO.setSceneCode(cumulateAccount.getSceneCode());
        if(null != cumulateAccount.getAmount()) {
            cumulateAccountDO.setCurrency(cumulateAccount.getAmount().getCurrency().getCurrencyCode());
            cumulateAccountDO.setAmount(cumulateAccount.getAmount().getAmount().longValue());
        }
        cumulateAccountDO.setCount(cumulateAccount.getCount());
        cumulateAccountDO.setStrategy(cumulateAccount.getStrategy().getCode());
        return cumulateAccountDO;
    }

}
