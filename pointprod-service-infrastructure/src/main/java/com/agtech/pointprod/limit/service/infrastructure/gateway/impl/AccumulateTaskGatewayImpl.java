package com.agtech.pointprod.limit.service.infrastructure.gateway.impl;

import com.agtech.pointprod.limit.service.domain.gateway.AccumulateTaskGateway;
import com.agtech.pointprod.limit.service.domain.gateway.condition.AccumulateTaskCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;
import com.agtech.pointprod.limit.service.infrastructure.dal.repository.AccumulateTaskRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AccumulateTaskGatewayImpl implements AccumulateTaskGateway {

    @Resource
    private AccumulateTaskRepository accumulateTaskRepository;
    @Override
    public AccumulateTask queryTask(AccumulateTaskCondition condition) {
        return accumulateTaskRepository.queryTask(condition);
    }

    @Override
    public void insert(AccumulateTask accumulateTask) {
        accumulateTaskRepository.insert(accumulateTask);
    }

    @Override
    public void updateTaskStatus(AccumulateTask accumulateTask) {
        accumulateTaskRepository.updateTaskStatus(accumulateTask);
    }
}
