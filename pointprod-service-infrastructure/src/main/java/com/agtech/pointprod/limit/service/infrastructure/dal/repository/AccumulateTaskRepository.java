package com.agtech.pointprod.limit.service.infrastructure.dal.repository;

import com.agtech.pointprod.limit.service.domain.gateway.condition.AccumulateTaskCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;

public interface AccumulateTaskRepository {

    AccumulateTask queryTask(AccumulateTaskCondition condition);

    void insert(AccumulateTask accumulateTask);

    void updateTaskStatus(AccumulateTask accumulateTask);
}
