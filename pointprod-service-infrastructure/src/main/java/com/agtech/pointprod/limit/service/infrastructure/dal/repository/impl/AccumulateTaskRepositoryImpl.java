package com.agtech.pointprod.limit.service.infrastructure.dal.repository.impl;

import com.agtech.pointprod.limit.service.domain.gateway.condition.AccumulateTaskCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;
import com.agtech.pointprod.limit.service.infrastructure.convert.AccumulateTaskConvert;
import com.agtech.pointprod.limit.service.infrastructure.dal.dao.AccumulateTaskDao;
import com.agtech.pointprod.limit.service.infrastructure.dal.dataobject.AccumulateTaskDO;
import com.agtech.pointprod.limit.service.infrastructure.dal.repository.AccumulateTaskRepository;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class AccumulateTaskRepositoryImpl implements AccumulateTaskRepository {

    @Resource
    private AccumulateTaskDao accumulateTaskDao;

    @Resource
    BizSequenceRepository bizSequenceRepository;

    @Override
    public AccumulateTask queryTask(AccumulateTaskCondition condition) {
        AccumulateTaskDO taskDO = accumulateTaskDao.queryTask(condition);
        return AccumulateTaskConvert.INSTANCE.doBackward(taskDO);
    }

    @Override
    public void insert(AccumulateTask accumulateTask) {
        String bizId = bizSequenceRepository.getBizId(null, SequenceCodeEnum.CUMULATE_TASK);
        accumulateTask.setTaskId(bizId);
        AccumulateTaskDO taskDO = AccumulateTaskConvert.INSTANCE.doForward(accumulateTask);
        int affectedRows = accumulateTaskDao.insert(taskDO);
        AssertUtil.assertTrue(affectedRows == 1, PointProdBizErrorCodeEnum.INSERT_TABLE_ERROR);
        accumulateTask.setId(taskDO.getId());
    }

    @Override
    public void updateTaskStatus(AccumulateTask accumulateTask) {
        AccumulateTaskDO taskDO = AccumulateTaskConvert.INSTANCE.doForward(accumulateTask);
        int affectedRow = accumulateTaskDao.updateTaskStatusById(taskDO.getId(), taskDO.getBizNo(), taskDO.getStatus());
        AssertUtil.assertTrue(affectedRow == 1, PointProdBizErrorCodeEnum.UPDATE_TABLE_ERROR);
    }
}
