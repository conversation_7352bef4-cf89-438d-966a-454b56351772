package com.agtech.pointprod.limit.service.infrastructure.gateway.impl;

import com.agtech.pointprod.limit.service.domain.gateway.AccumulateRuleGateway;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.limit.service.infrastructure.dal.repository.AccumulateProductRepository;
import com.agtech.pointprod.limit.service.infrastructure.dal.repository.AccumulateRuleRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AccumulateRuleGatewayImpl implements AccumulateRuleGateway {

    @Resource
    private AccumulateProductRepository accumulateProductRepository;
    
    @Resource
    private AccumulateRuleRepository accumulateRuleRepository;

    /**
     * Create a new accumulate rule
     *
     * @param accumulateRule The accumulate rule to create
     * @return true if successful
     */
    @Override
    public boolean createAccumulateRule(AccumulateRule accumulateRule) {
        return accumulateRuleRepository.createAccumulateRule(accumulateRule);
    }
    
    /**
     * Update an existing accumulate rule
     *
     * @param accumulateRule The accumulate rule to update
     * @return true if successful
     */
    @Override
    public boolean updateAccumulateRule(AccumulateRule accumulateRule) {
        return accumulateRuleRepository.updateAccumulateRule(accumulateRule);
    }
    
    /**
     * Get accumulate rule by rule ID
     *
     * @param ruleId The rule ID
     * @return The accumulate rule
     */
    @Override
    public AccumulateRule getAccumulateRuleByRuleId(String ruleId) {
        return accumulateRuleRepository.getAccumulateRuleByRuleId(ruleId);
    }

    
    /**
     * Query accumulate rules with pagination
     *
     * @param title Rule title (user level) filter
     * @param status Rule status filter
     * @param pageNum Page number
     * @param pageSize Page size
     * @return List of accumulate rules for the requested page
     */
    @Override
    public List<AccumulateRule> queryAccumulateRulesWithPagination(String title, String status, Integer pageNum, Integer pageSize) {
        return accumulateRuleRepository.queryAccumulateRulesWithPagination(title, status, pageNum, pageSize);
    }

    /**
     * Count accumulate rules with filters
     *
     * @param title Rule title (user level) filter
     * @param status Rule status filter
     * @return Total count of matching rules
     */
    @Override
    public Integer countAccumulateRules(String title, String status) {
        return accumulateRuleRepository.countAccumulateRules(title, status);
    }
    
    /**
     * Logical delete an accumulate rule
     *
     * @param ruleId The rule ID
     * @return true if successful
     */
    @Override
    public boolean deleteAccumulateRule(String ruleId) {
        return accumulateRuleRepository.deleteAccumulateRule(ruleId);
    }
    
    /**
     * Disable other rules with the same title
     *
     * @param title The rule title (user level)
     * @return true if successful
     */
    @Override
    public boolean disableOtherAccumulateRules(String title) {
        return accumulateRuleRepository.disableOtherAccumulateRules(title);
    }
    
    /**
     * Check if this is the last enabled rule for this level
     *
     * @param title Rule title (user level)
     * @return true if this is the last enabled rule
     */
    @Override
    public boolean isLastEnabledAccumulateRule(String title) {
        return accumulateRuleRepository.isLastEnabledAccumulateRule(title);
    }

    /**
     * 检查等级下是否存在生效的规则
     * @param title
     * @return
     */
    @Override
    public boolean isAccumulateRuleByLevelAndValidExists(String title) {
        return accumulateRuleRepository.isAccumulateRuleByLevelAndValidExists(title);
    }

}
