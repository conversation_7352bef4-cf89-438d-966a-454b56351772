package com.agtech.pointprod.limit.service.infrastructure.convert;

import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.pointprod.limit.service.domain.common.convert.Converter;
import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.AmountRange;
import com.agtech.pointprod.limit.service.domain.model.CountRange;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.limit.service.infrastructure.dal.dataobject.AccumulateRuleDO;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version $ class:LimitRuleConvert, v1.0 2025/06/22 14:39 xiaoming Exp $
 */
@Mapper(componentModel = "spring")
public interface AccumulateRuleConvert extends Converter<AccumulateRule, AccumulateRuleDO> {

    AccumulateRuleConvert INSTANCE = Mappers.getMapper(AccumulateRuleConvert.class);

    @Override
    default AccumulateRule doBackward(AccumulateRuleDO accumulateRuleDO) {
        if (Objects.isNull(accumulateRuleDO)) {
            return null;
        }
        AccumulateRule accumulateRule = new AccumulateRule();
        accumulateRule.setId(accumulateRuleDO.getId());
        accumulateRule.setRuleId(accumulateRuleDO.getRuleId());
        accumulateRule.setTitle(accumulateRuleDO.getTitle());
        accumulateRule.setAmountRange(accumulateRuleDO.getAmountRange());
        accumulateRule.setCountRange(accumulateRuleDO.getCountRange());
        accumulateRule.setSceneCode(accumulateRuleDO.getSceneCode());
        accumulateRule.setExpression(accumulateRuleDO.getExpression());
        accumulateRule.setStatus(accumulateRuleDO.getStatus());
        accumulateRule.setGmtModified(accumulateRuleDO.getGmtModified());
        accumulateRule.setGmtCreate(accumulateRuleDO.getGmtCreate());
        List<AmountRange> amountRanges = new ArrayList<>();
        Map<String, Map<String, AmountRange>> amountRangeMap = Maps.newHashMap();
        List<CountRange> countRanges = new ArrayList<>();
        Map<String, CountRange> countRangeMap = Maps.newHashMap();
        // 金额范围 转对象SM{limit:[100:MCOIN,+:MCOIN]};DM{interval:100,limit:[0:MCOIN,300:MCOIN]}
        String amountRangeExpression = accumulateRuleDO.getAmountRange();
        if (StringUtils.isNotBlank(amountRangeExpression)) {
            String[] split = amountRangeExpression.split(";");
            for (String var : split) {
                // 解析策略类型，如 SM, DM
                LimitTypeEnum strategy = LimitTypeEnum.valueOf(var.substring(0, var.indexOf("{")));
                
                // 解析大括号内的内容，如 {limit:[100:MCOIN,+:MCOIN]} 或 {interval:100,limit:[0:MCOIN,300:MCOIN]}
                String content = var.substring(var.indexOf("{") + 1, var.lastIndexOf("}"));
                
                // 解析 interval 值
                Long interval = null;
                if (content.contains("interval:")) {
                    String intervalStr = content.substring(content.indexOf("interval:") + 9);
                    if (intervalStr.contains(",")) {
                        intervalStr = intervalStr.substring(0, intervalStr.indexOf(","));
                    }
                    interval = Long.valueOf(intervalStr);
                }
                
                // 提取 limit 部分的内容
                String limitPart = content.substring(content.indexOf("limit:[") + 7, content.lastIndexOf("]"));
                
                // 解析最小值和最大值
                String minxTemp = limitPart.substring(0, limitPart.indexOf(","));
                String maxTemp = limitPart.substring(limitPart.indexOf(",") + 1);
                
                // 解析最小金额
                BigDecimal min = new BigDecimal(minxTemp.substring(0, minxTemp.indexOf(":")));
                String minCurrency = minxTemp.substring(minxTemp.indexOf(":") + 1);
                
                // 解析最大金额，最大值如果是+表示无穷大，用long的最大值表示
                String maxStr = maxTemp.substring(0, maxTemp.indexOf(":"));
                maxStr = "+".equals(maxStr) ? String.valueOf(Integer.MAX_VALUE) : maxStr;
                BigDecimal max = new BigDecimal(maxStr);
                String maxCurrency = maxTemp.substring(maxTemp.indexOf(":") + 1);
                
                MultiCurrencyMoney minAmount = new MultiCurrencyMoney(min,minCurrency);
                MultiCurrencyMoney maxAmount = new MultiCurrencyMoney(max,maxCurrency);
                AmountRange amountRangeObj = AmountRange.builder().limitTypeEnum(strategy).maxAmount(maxAmount).minAmount(
                        minAmount).interval(interval).build();
                amountRanges.add(amountRangeObj);
                amountRangeMap.computeIfAbsent(strategy.getCode(), k -> Maps.newHashMap())
                        .put(minAmount.getCurrency().getCurrencyCode(), amountRangeObj);
            }
        }

        // 次数范围转对象DC{interval:10,limit:[0,5]}
        String countRangeExpression = accumulateRuleDO.getCountRange();
        if (StringUtils.isNotBlank(countRangeExpression)) {
            String[] split = countRangeExpression.split(";");
            for (String var : split) {
                // 解析策略类型，如 DC
                LimitTypeEnum strategy = LimitTypeEnum.valueOf(var.substring(0, var.indexOf("{")));
                
                // 解析大括号内的内容，如 {interval:10,limit:[0,5]}
                String content = var.substring(var.indexOf("{") + 1, var.lastIndexOf("}"));
                
                // 解析 interval 值
                Long interval = null;
                if (content.contains("interval:")) {
                    String intervalStr = content.substring(content.indexOf("interval:") + 9);
                    if (intervalStr.contains(",")) {
                        intervalStr = intervalStr.substring(0, intervalStr.indexOf(","));
                    }
                    interval = Long.valueOf(intervalStr);
                }
                
                // 提取 limit 部分的内容
                String limitPart = content.substring(content.indexOf("limit:[") + 7, content.lastIndexOf("]"));
                
                // 解析最小值和最大值
                String minxTemp = limitPart.substring(0, limitPart.indexOf(","));
                String maxTemp = limitPart.substring(limitPart.indexOf(",") + 1);
                
                // 解析最小次数
                Long min = Long.valueOf(minxTemp);
                
                // 解析最大次数，最大值为+的时候，用long的最大值表示
                maxTemp = "+".equals(maxTemp) ? String.valueOf(Integer.MAX_VALUE) : maxTemp;
                Long max = Long.valueOf(maxTemp);
                
                CountRange countRangeObj = CountRange.builder().limitTypeEnum(strategy).minCount(min).maxCount(max).interval(interval).build();
                countRanges.add(countRangeObj);
                countRangeMap.put(strategy.getCode(), countRangeObj);
            }
        }

        accumulateRule.setAmountRanges(amountRanges);
        accumulateRule.setAmountRangeMap(amountRangeMap);
        accumulateRule.setCountRanges(countRanges);
        accumulateRule.setCountRangeMap(countRangeMap);
        return accumulateRule;
    }

    /**
     * Convert domain to DO
     *
     * @param accumulateRule AccumulateRule domain
     * @return AccumulateRule DO
     */
    AccumulateRuleDO toDO(AccumulateRule accumulateRule);

    /**
     * Convert DO list to domain list
     *
     * @param accumulateRuleDOList AccumulateRule DO list
     * @return AccumulateRule domain list
     */
    List<AccumulateRule> toDomainList(List<AccumulateRuleDO> accumulateRuleDOList);

}
