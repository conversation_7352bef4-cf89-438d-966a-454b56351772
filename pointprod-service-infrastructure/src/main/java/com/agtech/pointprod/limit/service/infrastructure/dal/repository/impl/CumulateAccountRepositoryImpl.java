package com.agtech.pointprod.limit.service.infrastructure.dal.repository.impl;

import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.gateway.condition.CumulateAccountQueryCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.infrastructure.convert.CumulateAccountConvert;
import com.agtech.pointprod.limit.service.infrastructure.dal.dao.CumulateAccountDao;
import com.agtech.pointprod.limit.service.infrastructure.dal.dataobject.CumulateAccountDO;
import com.agtech.pointprod.limit.service.infrastructure.dal.repository.CumulateAccountRepository;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.SequenceCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.agtech.pointprod.service.infrastructure.repository.BizSequenceRepository;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class CumulateAccountRepositoryImpl implements CumulateAccountRepository {

    @Resource
    private CumulateAccountDao cumulateAccountDao;

    @Resource
    private BizSequenceRepository bizSequenceRepository;

    @Override
    public Map<LimitTypeEnum,List<CumulateAccount>> queryCumulateAccount(CumulateAccountQueryCondition condition) {
        List<CumulateAccountDO> cumulateAccountDOList = cumulateAccountDao.queryCumulateAccount(condition);
        List<CumulateAccount> cumulateAccounts = cumulateAccountDOList.stream().map(CumulateAccountConvert.INSTANCE::doBackward).collect(Collectors.toList());
        Map<LimitTypeEnum,List<CumulateAccount>> rst = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(cumulateAccounts)){
            for (CumulateAccount cumulateAccount : cumulateAccounts) {
                rst.computeIfAbsent(cumulateAccount.getStrategy(), k -> Lists.newArrayList()).add(cumulateAccount);
            }
        }
        return rst;
    }

    @Override
    public Map<LimitTypeEnum,List<CumulateAccount>> queryForUpdateCumulateAccount(CumulateAccountQueryCondition condition) {
        List<CumulateAccountDO> cumulateAccountDOList = cumulateAccountDao.queryForUpdateCumulateAccount(condition);
        List<CumulateAccount> cumulateAccounts = cumulateAccountDOList.stream().map(CumulateAccountConvert.INSTANCE::doBackward).collect(Collectors.toList());
        Map<LimitTypeEnum,List<CumulateAccount>> rst = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(cumulateAccounts)){
            for (CumulateAccount cumulateAccount : cumulateAccounts) {
                rst.computeIfAbsent(cumulateAccount.getStrategy(), k -> Lists.newArrayList()).add(cumulateAccount);
            }
        }
        return rst;
    }

    @Override
    public int insertCumulateAccount(List<CumulateAccount> cumulateAccounts) {
        if (CollectionUtils.isEmpty(cumulateAccounts)) {
            return 0;
        }
        List<CumulateAccountDO> cumulateAccountDOList = Lists.newArrayList();
        for (CumulateAccount cumulateAccount : cumulateAccounts) {
            String sequence = bizSequenceRepository.getBizId(null, SequenceCodeEnum.CUMULATE_ACCOUNT);
            cumulateAccount.setAccountId(sequence);
            CumulateAccountDO cumulateAccountDO = CumulateAccountConvert.INSTANCE.doForward(cumulateAccount);
            cumulateAccount.getCumulateLog().setCumulateAccountId(cumulateAccountDO.getAccountId());
            cumulateAccountDOList.add(cumulateAccountDO);
        }
        return cumulateAccountDao.insertCumulateAccount(cumulateAccountDOList);
    }

    @Override
    public int updateCumulateAccount(List<CumulateAccount> cumulateAccounts) {
        if (CollectionUtils.isEmpty(cumulateAccounts)) {
            return 0;
        }
        List<CumulateAccountDO> cumulateAccountDos = CumulateAccountConvert.INSTANCE.doForwardList(cumulateAccounts);
        boolean affectRow = cumulateAccountDao.updateCumulateAccounts(cumulateAccountDos);
        if(affectRow){
            return cumulateAccountDos.size();
        }else{
            throw new PointProdBizException(PointProdBizErrorCodeEnum.UPDATE_TABLE_ERROR);
        }
    }

    @Override
    public void batchInsertCumulateAccounts(Map<LimitTypeEnum, List<CumulateAccount>> insertCumulateAccountMap) {
        if (MapUtils.isEmpty(insertCumulateAccountMap)) {
            return;
        }
        for (Map.Entry<LimitTypeEnum, List<CumulateAccount>> entry : insertCumulateAccountMap.entrySet()) {
            List<CumulateAccount> cumulateAccounts = entry.getValue();
            if (CollectionUtils.isEmpty(cumulateAccounts)) {
                continue;
            }
            int affectRows = insertCumulateAccount(cumulateAccounts);
            AssertUtil.assertTrue(affectRows == cumulateAccounts.size(), PointProdBizErrorCodeEnum.INSERT_TABLE_ERROR);
        }
    }

    @Override
    public void batchUpdateCumulateAccounts(Map<LimitTypeEnum, List<CumulateAccount>> updateCumulateAccountMap) {
        if (MapUtils.isEmpty(updateCumulateAccountMap)) {
            return;
        }
        for (Map.Entry<LimitTypeEnum, List<CumulateAccount>> entry : updateCumulateAccountMap.entrySet()) {
            List<CumulateAccount> cumulateAccounts = entry.getValue();
            if (CollectionUtils.isEmpty(cumulateAccounts)) {
                continue;
            }
            int affectRows = updateCumulateAccount(cumulateAccounts);
            AssertUtil.assertTrue(affectRows == cumulateAccounts.size(), PointProdBizErrorCodeEnum.UPDATE_TABLE_ERROR);
        }
    }
}
