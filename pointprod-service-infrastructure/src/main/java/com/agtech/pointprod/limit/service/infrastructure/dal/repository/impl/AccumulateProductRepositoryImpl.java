package com.agtech.pointprod.limit.service.infrastructure.dal.repository.impl;

import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.gateway.condition.*;
import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.model.AmountRange;
import com.agtech.pointprod.limit.service.domain.model.CountRange;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.*;
import com.agtech.pointprod.limit.service.infrastructure.dal.repository.*;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ class:AccumulateProductRepositoryImpl, v1.0 2025/06/15 16:15 xiaoming Exp $
 */
@Slf4j
@Repository
public class AccumulateProductRepositoryImpl implements AccumulateProductRepository {

    @Resource
    private CumulateAccountRepository cumulateAccountRepository;

    @Resource
    private AccumulateConfigRepository accumulateConfigRepository;

    @Resource
    private AccumulateRuleRepository accumulateRuleRepository;

    @Resource
    private CumulateLogRepository cumulateLogRepository;

    @Override
    public AccumulateProduct loadConfig(LoadAccumulateProductCondition loadAccumulateProductCondition) {

        // 累额配置对象
        List<AccumulateConfig> accumulateConfigList = accumulateConfigRepository.queryAccumulateConfigs(
                new AccumulateConfigQueryCondition(loadAccumulateProductCondition.getSceneCode()));
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(accumulateConfigList),PointProdBizErrorCodeEnum.CONFIG_NOT_EXIST);

        // 累额规则对象
        List<AccumulateRule> accumulateRuleList = accumulateRuleRepository.queryRules(buildLimitRuleCondition(loadAccumulateProductCondition));
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(accumulateRuleList), PointProdBizErrorCodeEnum.RULE_NOT_EXIST);

        // 转model
        return new AccumulateProduct(loadAccumulateProductCondition.getSceneCode(),accumulateConfigList, accumulateRuleList);
    }

    @Override
    public void loadCumulateAccount(AccumulateProduct accumulateProduct) {
        // 查询产品账本
        CumulateAccountQueryCondition accountQueryCondition = buildCumulateAccountQueryCondition(accumulateProduct);
        Map<LimitTypeEnum, List<CumulateAccount>> cumulateAccountMap = null;
        if (accumulateProduct.needAddLock()) {
            cumulateAccountMap = cumulateAccountRepository.queryForUpdateCumulateAccount(accountQueryCondition);
        } else {
            cumulateAccountMap = cumulateAccountRepository.queryCumulateAccount(accountQueryCondition);
        }
        accumulateProduct.initCumulateAccountMap(cumulateAccountMap);
    }

    private static CumulateAccountQueryCondition buildCumulateAccountQueryCondition(AccumulateProduct accumulateProduct) {
        AccumulateTask task = accumulateProduct.getAccumulateTask();
        CumulateAccountQueryCondition cumulateAccountQueryCondition = new CumulateAccountQueryCondition();
        cumulateAccountQueryCondition.setPrincipalId(task.getLimitTarget().getPrincipalId());
        cumulateAccountQueryCondition.setPrincipalType(task.getLimitTarget().getPrincipalType().name());
        cumulateAccountQueryCondition.setSceneCode(accumulateProduct.getSceneCode().getCode());
        cumulateAccountQueryCondition.setCurrency(task.getAmount().getCurrency().getCurrencyCode());
        cumulateAccountQueryCondition.setBizTime(accumulateProduct.getAccumulateTask().getBizTime());
        if(CollectionUtils.isNotEmpty(accumulateProduct.getAccumulateRules())){
            List<CumulateAccountStrategyQueryCondition> strategyQueryConditions = new ArrayList<>();
            for (AccumulateRule accumulateRule : accumulateProduct.getAccumulateRules()) {
                // 处理金额范围
                if (CollectionUtils.isNotEmpty(accumulateRule.getAmountRanges())) {
                    for (AmountRange amountRange : accumulateRule.getAmountRanges()) {
                        //单次限制规则不需要查询account表
                        if(LimitTypeEnum.SM != amountRange.getLimitTypeEnum()) {
                            CumulateAccountStrategyQueryCondition condition = new CumulateAccountStrategyQueryCondition();
                            condition.setLimitType(amountRange.getLimitTypeEnum());
                            condition.setInterval(amountRange.getInterval());
                            strategyQueryConditions.add(condition);
                        }
                    }
                }
                // 处理次数范围
                if (CollectionUtils.isNotEmpty(accumulateRule.getCountRanges())) {
                    for (CountRange countRange : accumulateRule.getCountRanges()) {
                        CumulateAccountStrategyQueryCondition condition = new CumulateAccountStrategyQueryCondition();
                        condition.setLimitType(countRange.getLimitTypeEnum());
                        condition.setInterval(countRange.getInterval());
                        strategyQueryConditions.add(condition);
                    }
                }
            }
            cumulateAccountQueryCondition.setStrategyQueryConditions(strategyQueryConditions);
        }
        return cumulateAccountQueryCondition;
    }

    private AccumulateRuleCondition buildLimitRuleCondition(LoadAccumulateProductCondition loadAccumulateProductCondition) {
        return new AccumulateRuleCondition()
                .setSceneCodeIn(Lists.newArrayList(loadAccumulateProductCondition.getSceneCode().getCode()));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void store(AccumulateProduct accumulateProduct) {
        if (MapUtils.isEmpty(accumulateProduct.getInsertCumulateAccountMap()) && MapUtils.isEmpty(accumulateProduct.getUpdateCumulateAccountMap())) {
            log.error("accumulateProduct is empty, skip store, application:{}", accumulateProduct.getAccumulateTask());
            return;
        }
        // 保存产品帐(新增)
        cumulateAccountRepository.batchInsertCumulateAccounts(accumulateProduct.getInsertCumulateAccountMap());

        // 再更新产品帐(原有)
        cumulateAccountRepository.batchUpdateCumulateAccounts(accumulateProduct.getUpdateCumulateAccountMap());

        // 保存日志
        List<CumulateLog> cumulateLogs = Lists.newArrayList();
        cumulateLogs.addAll(accumulateProduct.getInsertCumulateAccountMap().values().stream().flatMap(List::stream).map(CumulateAccount::getCumulateLog).collect(Collectors.toList()));
        cumulateLogs.addAll(accumulateProduct.getUpdateCumulateAccountMap().values().stream().flatMap(List::stream).map(CumulateAccount::getCumulateLog).collect(Collectors.toList()));
        cumulateLogRepository.batchInsert(cumulateLogs);
    }

}
