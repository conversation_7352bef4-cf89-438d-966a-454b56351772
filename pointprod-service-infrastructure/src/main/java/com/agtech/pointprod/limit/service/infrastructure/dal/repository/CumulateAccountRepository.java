package com.agtech.pointprod.limit.service.infrastructure.dal.repository;

import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.gateway.condition.CumulateAccountQueryCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;

import java.util.List;
import java.util.Map;

public interface CumulateAccountRepository {

    Map<LimitTypeEnum,List<CumulateAccount>> queryCumulateAccount(CumulateAccountQueryCondition condition);

    Map<LimitTypeEnum,List<CumulateAccount>> queryForUpdateCumulateAccount(CumulateAccountQueryCondition condition);

    int insertCumulateAccount(List<CumulateAccount> cumulateAccounts);

    int updateCumulateAccount(List<CumulateAccount> cumulateAccounts);


    void batchInsertCumulateAccounts(Map<LimitTypeEnum, List<CumulateAccount>> insertCumulateAccountMap);


    void batchUpdateCumulateAccounts(Map<LimitTypeEnum, List<CumulateAccount>> updateCumulateAccountMap);

}
