package com.agtech.pointprod.limit.service.infrastructure.convert;

import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.pointprod.limit.service.domain.common.convert.Converter;
import com.agtech.pointprod.limit.service.facade.dto.enums.PrincipalTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateLog;
import com.agtech.pointprod.limit.service.infrastructure.dal.dataobject.CumulateLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ class:CumulateLogConvert, v1.0 2025/06/22 14:39 xiaoming Exp $
 */
@Mapper(componentModel = "spring")
public interface CumulateLogConvert extends Converter<CumulateLog, CumulateLogDO> {

    CumulateLogConvert INSTANCE = Mappers.getMapper(CumulateLogConvert.class);

    @Override
    default CumulateLog doBackward(CumulateLogDO cumulateLogDO) {
        if (Objects.isNull(cumulateLogDO)) {
            return null;
        }
        CumulateLog cumulateLog = new CumulateLog();
        cumulateLog.setId(cumulateLogDO.getId());
        cumulateLog.setLogId(cumulateLogDO.getLogId());
        cumulateLog.setGmtCreate(cumulateLogDO.getGmtCreate());
        cumulateLog.setGmtModified(cumulateLogDO.getGmtModified());
        cumulateLog.setCumulateAccountId(cumulateLogDO.getCumulateAccountId());
        cumulateLog.setPrincipalId(cumulateLogDO.getPrincipalId());
        cumulateLog.setPrincipalType(PrincipalTypeEnum.valueOf(cumulateLogDO.getPrincipalType()));
        cumulateLog.setSceneCode(cumulateLogDO.getSceneCode());
        cumulateLog.setBizNo(cumulateLogDO.getBizNo());
        cumulateLog.setBizTime(cumulateLogDO.getBizTime());
        MultiCurrencyMoney amount = new MultiCurrencyMoney(cumulateLogDO.getAmount(), cumulateLogDO.getCurrency());
        cumulateLog.setAmount(amount);
        cumulateLog.setCount(cumulateLogDO.getCount());
        cumulateLog.setStrategy(LimitTypeEnum.getLimitTypeEnum(cumulateLogDO.getStrategy()));
        return cumulateLog;
    }

    @Override
    default CumulateLogDO doForward(CumulateLog cumulateLog) {
        if (Objects.isNull(cumulateLog)) {
            return null;
        }
        CumulateLogDO cumulateLogDO = new CumulateLogDO();
        cumulateLogDO.setId(cumulateLog.getId());
        cumulateLogDO.setTntInstId(cumulateLog.getTntInst().getCode());
        cumulateLogDO.setLogId(cumulateLog.getLogId());
        cumulateLogDO.setCumulateAccountId(cumulateLog.getCumulateAccountId());
        cumulateLogDO.setPrincipalId(cumulateLog.getPrincipalId());
        cumulateLogDO.setPrincipalType(cumulateLog.getPrincipalType().name());
        cumulateLogDO.setBizNo(cumulateLog.getBizNo());
        cumulateLogDO.setBizTime(cumulateLog.getBizTime());
        cumulateLogDO.setSceneCode(cumulateLog.getSceneCode());
        cumulateLogDO.setStrategy(cumulateLog.getStrategy().getCode());
        if(null != cumulateLog.getAmount()) {
            cumulateLogDO.setCurrency(cumulateLog.getAmount().getCurrency().getCurrencyCode());
            cumulateLogDO.setAmount(cumulateLog.getAmount().getAmount().longValue());
        }
        cumulateLogDO.setCount(cumulateLog.getCount());
        return cumulateLogDO;

    }

}
