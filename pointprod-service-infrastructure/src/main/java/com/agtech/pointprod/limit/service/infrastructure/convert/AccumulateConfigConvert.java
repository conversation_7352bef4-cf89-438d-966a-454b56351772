package com.agtech.pointprod.limit.service.infrastructure.convert;

import com.agtech.pointprod.limit.service.domain.common.constants.LimitCenterConstants;
import com.agtech.pointprod.limit.service.domain.common.convert.Converter;
import com.agtech.pointprod.limit.service.facade.dto.enums.LimitConfigStatusEnum;
import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateConfig;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.limit.service.infrastructure.dal.dataobject.AccumulateConfigDO;
import com.agtech.pointprod.limit.service.infrastructure.dal.dataobject.AccumulateRuleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ class:AccumulateConfigConvert, v1.0 2025/06/22 14:37 xiaoming Exp $
 */
@Mapper(componentModel = "spring")
public interface AccumulateConfigConvert extends Converter<AccumulateConfig, AccumulateConfigDO> {

    AccumulateConfigConvert INSTANCE = Mappers.getMapper(AccumulateConfigConvert.class);

    @Override
    default AccumulateConfig doBackward(AccumulateConfigDO accumulateConfigDO) {
        if (Objects.isNull(accumulateConfigDO)) {
            return null;
        }
        return AccumulateConfig.builder()
                .id(accumulateConfigDO.getId())
                .sceneCode(accumulateConfigDO.getSceneCode())
                .sceneName(accumulateConfigDO.getSceneName())
                .tntInstId(accumulateConfigDO.getTntInstId())
                .strategies(Arrays.stream(accumulateConfigDO.getStrategy().split(LimitCenterConstants.COMMA)).map(s -> LimitTypeEnum.valueOf(s)).collect(Collectors.toList()))
                .status(LimitConfigStatusEnum.getLimitConfigStatusEnumByValue(accumulateConfigDO.getStatus()))
                .build();
    }

    /**
     * Convert domain list to DO list
     *
     * @param accumulateRuleList AccumulateRule DO list
     * @return AccumulateRule domain list
     */
    List<AccumulateRuleDO> toDOList(List<AccumulateRule> accumulateRuleList);

}
