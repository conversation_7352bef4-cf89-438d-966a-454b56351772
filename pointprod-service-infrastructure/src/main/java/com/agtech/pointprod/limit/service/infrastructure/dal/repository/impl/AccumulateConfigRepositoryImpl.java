package com.agtech.pointprod.limit.service.infrastructure.dal.repository.impl;

import com.agtech.pointprod.limit.service.domain.gateway.condition.AccumulateConfigQueryCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateConfig;
import com.agtech.pointprod.limit.service.infrastructure.convert.AccumulateConfigConvert;
import com.agtech.pointprod.limit.service.infrastructure.dal.dao.AccumulateConfigDao;
import com.agtech.pointprod.limit.service.infrastructure.dal.dataobject.AccumulateConfigDO;
import com.agtech.pointprod.limit.service.infrastructure.dal.repository.AccumulateConfigRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class AccumulateConfigRepositoryImpl implements AccumulateConfigRepository {

    @Resource
    private AccumulateConfigDao accumulateConfigDao;

    @Override
    public List<AccumulateConfig> queryAccumulateConfigs(AccumulateConfigQueryCondition condition) {
        List<AccumulateConfigDO> accumulateConfigLists = accumulateConfigDao.queryAccumulateConfigs(condition);
        return accumulateConfigLists.stream().map(AccumulateConfigConvert.INSTANCE::doBackward).collect(Collectors.toList());
    }
}
