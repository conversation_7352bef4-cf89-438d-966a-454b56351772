package com.agtech.pointprod.limit.service.infrastructure.gateway.impl;

import com.agtech.pointprod.limit.service.domain.gateway.AccumulateConfigGateway;
import com.agtech.pointprod.limit.service.domain.gateway.condition.AccumulateConfigQueryCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateConfig;
import com.agtech.pointprod.limit.service.infrastructure.dal.repository.AccumulateConfigRepository;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

@Component
public class AccumulateConfigGatewayImpl implements AccumulateConfigGateway {

    @Resource
    private AccumulateConfigRepository accumulateConfigRepository;

    @Override
    public List<AccumulateConfig> queryAccumulateConfigs(AccumulateConfigQueryCondition condition) {
        return accumulateConfigRepository.queryAccumulateConfigs(condition);
    }

}
