package com.agtech.pointprod.limit.service.infrastructure.dal.repository;

import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.gateway.condition.LoadAccumulateProductCondition;


/**
 * <AUTHOR>
 * @version $ class:AccumulateProductRepository, v1.0 2025/06/15 16:07 xiaoming Exp $
 */
public interface AccumulateProductRepository {

    /**
     * 加载累计产品
     *
     * @param condition 查询条件
     * @return AccumulateProduct
     */
    AccumulateProduct loadConfig(LoadAccumulateProductCondition condition);

    /**
     * 加载账本
     */
    void loadCumulateAccount(AccumulateProduct accumulateProduct);

    /**
     * 保存/更新
     *
     * @param accumulateProduct
     * @return AccumulateProduct
     */
    void store(AccumulateProduct accumulateProduct);

}

