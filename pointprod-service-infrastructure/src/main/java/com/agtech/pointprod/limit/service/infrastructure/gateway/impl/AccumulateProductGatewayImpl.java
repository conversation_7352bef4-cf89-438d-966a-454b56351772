package com.agtech.pointprod.limit.service.infrastructure.gateway.impl;

import com.agtech.pointprod.limit.service.domain.gateway.AccumulateProductGateway;
import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.gateway.condition.LoadAccumulateProductCondition;
import com.agtech.pointprod.limit.service.infrastructure.dal.repository.AccumulateProductRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AccumulateProductGatewayImpl implements AccumulateProductGateway {

    @Resource
    private AccumulateProductRepository accumulateProductRepository;

    @Override
    public AccumulateProduct loadConfig(LoadAccumulateProductCondition condition) {
        return accumulateProductRepository.loadConfig(condition);
    }

    @Override
    public void loadCumulateAccount(AccumulateProduct accumulateProduct) {
        accumulateProductRepository.loadCumulateAccount(accumulateProduct);
    }

    @Override
    public void store(AccumulateProduct accumulateProduct) {
        accumulateProductRepository.store(accumulateProduct);
    }
}
