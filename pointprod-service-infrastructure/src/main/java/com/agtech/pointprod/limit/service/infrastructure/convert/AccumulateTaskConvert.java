package com.agtech.pointprod.limit.service.infrastructure.convert;

import com.agtech.pointprod.limit.service.domain.common.convert.Converter;
import com.agtech.pointprod.limit.service.domain.model.LimitContext;
import com.agtech.pointprod.limit.service.facade.dto.enums.AccumulateTaskStatusEnum;
import com.agtech.pointprod.limit.service.facade.dto.enums.SceneCodeEnum;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;
import com.agtech.pointprod.limit.service.infrastructure.dal.dataobject.AccumulateTaskDO;
import com.alibaba.fastjson2.JSONObject;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Objects;

@Mapper(componentModel = "spring")
public interface AccumulateTaskConvert extends Converter<AccumulateTask, AccumulateTaskDO> {

    AccumulateTaskConvert INSTANCE = Mappers.getMapper(AccumulateTaskConvert.class);

    @Override
    default AccumulateTask doBackward(AccumulateTaskDO taskDO) {
        if (Objects.isNull(taskDO)) {
            return null;
        }
        AccumulateTask task = new AccumulateTask();
        task.setId(taskDO.getId());
        task.setSceneCode(SceneCodeEnum.getInstanceByCode(taskDO.getSceneCode()));
        task.setBizNo(taskDO.getBizNo());
        task.setBizTime(taskDO.getBizTime());
        task.setLimitContext(JSONObject.parseObject(taskDO.getBizContext(), LimitContext.class));
        task.setStatus(AccumulateTaskStatusEnum.valueOf(taskDO.getStatus()));
        return task;
    }

    @Override
    default AccumulateTaskDO doForward(AccumulateTask task) {
        if (Objects.isNull(task)) {
            return null;
        }
        AccumulateTaskDO taskDO = new AccumulateTaskDO();
        taskDO.setTntInstId(task.getTntInst().getCode());
        taskDO.setId(task.getId());
        taskDO.setTaskId(task.getTaskId());
        taskDO.setSceneCode(task.getSceneCode().getCode());
        taskDO.setBizNo(task.getBizNo());
        taskDO.setBizTime(task.getBizTime());
        taskDO.setBizContext(JSONObject.toJSONString(task.getLimitContext()));
        taskDO.setStatus(task.getStatus().name());
        return taskDO;
    }
}
