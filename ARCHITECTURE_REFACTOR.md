# 支付服务架构重构说明

## 重构概述

基于DDD（领域驱动设计）原则，对支付服务中"事务提交后，异步发送消息"的代码块进行了架构重构，将其从业务服务层迁移到专门的应用服务层，实现了更清晰的职责分离。

## 重构前后对比

### 重构前

```java
// PayBizServiceImpl.java
// 事务提交后，异步发送消息
try {
    transaction.invokeWithNewTransaction(() -> {
        messageReliabilityDomainService.sendMessageAndMarkDelivered(messageId);
    });
} catch (Exception e) {
    log.error("发送订单状态变更消息失败，messageId={}, orderId={}", 
            messageId, payment.getOrderInfo().getFundOrderId(), e);
}
```

### 重构后

```java
// PayBizServiceImpl.java
// 事务提交后，异步发送消息
// 使用PostTransactionMessageService处理事务后的异步操作
postTransactionMessageService.sendPaymentCompletionMessageAsync(payment, messageId);
```

## 新增组件

### 1. PostTransactionMessageService

**位置**: `pointprod-service-app/src/main/java/com/agtech/pointprod/order/service/app/service/`

**职责**:
- 专门处理事务提交后的异步消息发送
- 符合DDD架构中应用层的职责边界
- 提供统一的异步消息处理接口

**主要方法**:
- `sendPaymentCompletionMessageAsync()`: 异步发送支付完成消息
- `sendOrderStatusChangeMessageAsync()`: 异步发送订单状态变更消息

### 2. PostTransactionMessageServiceImpl

**特性**:
- 使用`@Async`注解实现真正的异步处理
- 使用专用线程池`postTransactionMessageExecutor`
- 在新事务中处理消息发送，确保事务隔离
- 完善的异常处理和日志记录

### 3. AsyncConfig

**位置**: `pointprod-service-app/src/main/java/com/agtech/pointprod/service/app/config/`

**配置**:
- 核心线程数: 2
- 最大线程数: 8
- 队列容量: 100
- 拒绝策略: CallerRunsPolicy
- 优雅关闭支持

## 架构优势

### 1. 职责分离

- **PayBizService**: 专注于核心支付业务逻辑编排
- **PostTransactionMessageService**: 专门处理事务后的异步操作
- **PaymentApplicationService**: 负责支付领域的业务用例协调

### 2. 符合DDD原则

- **应用层**: 负责事务管理和后续处理编排
- **领域层**: 专注于核心业务逻辑
- **基础设施层**: 提供技术支撑

### 3. 可扩展性

- 新的事务后处理需求可以轻松添加到`PostTransactionMessageService`
- 支持不同类型的异步消息处理
- 线程池配置可根据业务需求调整

### 4. 可测试性

- 异步消息处理逻辑独立，便于单元测试
- 可以通过Mock轻松测试异步行为
- 职责单一，测试用例更加聚焦

### 5. 可维护性

- 代码结构更清晰，易于理解和维护
- 异步处理逻辑集中管理
- 统一的异常处理和日志记录

## 使用示例

### 基本用法

```java
@Service
public class SomeBusinessService {
    
    @Resource
    private PostTransactionMessageService postTransactionMessageService;
    
    public void someBusinessMethod() {
        // 业务逻辑处理...
        
        // 事务提交后异步发送消息
        postTransactionMessageService.sendPaymentCompletionMessageAsync(payment, messageId);
    }
}
```

### 扩展新的异步处理

```java
// 在PostTransactionMessageService接口中添加新方法
void sendOrderCancelMessageAsync(String orderId, String reason);

// 在实现类中添加具体实现
@Override
@Async("postTransactionMessageExecutor")
public void sendOrderCancelMessageAsync(String orderId, String reason) {
    // 异步处理逻辑
}
```

## 注意事项

1. **异步执行**: 方法调用后立即返回，不会阻塞主线程
2. **事务隔离**: 异步方法在新事务中执行，与主事务隔离
3. **异常处理**: 异步方法中的异常不会影响主流程
4. **消息可靠性**: 依赖`MessageReliabilityDomainService`确保消息最终一致性
5. **线程池监控**: 建议监控线程池使用情况，根据实际负载调整配置

## 后续优化建议

1. **监控告警**: 添加异步处理的监控和告警机制
2. **配置外化**: 将线程池配置参数外化到配置文件
3. **性能优化**: 根据实际业务量调整线程池参数
4. **容错增强**: 考虑添加重试机制和熔断保护
5. **链路追踪**: 集成分布式链路追踪，便于问题排查