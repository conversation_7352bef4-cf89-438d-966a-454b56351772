# pointprod-service 项目规则

## 1. 项目架构

### 1.1 模块结构

pointprod-service 采用领域驱动设计(DDD)架构，分为以下几个主要模块：

- **pointprod-service-facade**: 对外暴露的接口层，包含DTO定义、接口定义等
- **pointprod-service-app**: 应用层，负责编排领域服务，处理请求和响应
- **pointprod-service-domain**: 领域层，包含核心业务逻辑和领域模型
- **pointprod-service-infrastructure**: 基础设施层，负责数据持久化和外部系统集成
- **pointprod-service-test**: 测试模块，包含单元测试和集成测试

### 1.2 依赖关系

模块间依赖关系遵循DDD原则：

- facade 不依赖其他模块
- app 依赖 facade 和 infrastructure
- domain 依赖 facade
- infrastructure 依赖 domain

## 2. 编码规范

### 2.1 命名规范

- **包命名**: 采用 `com.agtech.pointprod.{模块}.{功能}` 的格式
- **类命名**: 使用大驼峰命名法，如 `ContractServiceImpl`
- **方法命名**: 使用小驼峰命名法，如 `queryContract`
- **变量命名**: 使用小驼峰命名法，如 `contractBizService`

### 2.2 代码组织

- **接口定义**: 所有对外服务必须先定义接口，再实现
- **实现类**: 实现类应放在 `impl` 包下
- **DTO对象**: 请求/响应对象应放在 `dto.req` 和 `dto.rsp` 包下
- **枚举类**: 枚举类应放在 `enums` 包下

## 3. 服务调用模式

### 3.1 服务模板

所有服务调用应使用 `PointProdServiceTemplate` 模板类，该模板提供：

- 统一的参数校验
- 统一的异常处理
- 统一的日志记录
- 统一的结果转换

### 3.2 回调接口

服务实现需要实现 `PointProdServiceCallback` 接口，包含：

- `checkParameter()`: 参数校验
- `process()`: 业务处理
- `composeDigestLog()`: 摘要日志生成

## 4. 异常处理

### 4.1 异常类型

- **PointProdBizException**: 业务异常，包含错误码和错误信息
- **系统异常**: 非业务异常，如数据库异常、网络异常等

### 4.2 错误码

错误码定义在 `PointProdBizErrorCodeEnum` 中，包含：

- 参数错误 (100-199)
- 业务错误 (200-299)
- 限额服务错误 (300-399)
- 其他错误 (400+)

### 4.3 异常处理流程

1. 业务异常通过 `processAfterPointProdBizException` 处理
2. 系统异常通过 `processAfterThrowable` 处理
3. 所有异常都会记录到日志中

## 5. 数据访问

### 5.1 数据库访问

- 使用 MyBatis-Plus 进行数据库访问
- 使用动态数据源实现多数据源支持
- Repository 接口定义在领域层，实现在基础设施层

### 5.2 缓存使用

- 使用 Redis 作为缓存
- 缓存访问应通过基础设施层的 Repository 实现

## 6. 日志规范

### 6.1 日志级别

- **ERROR**: 系统或业务异常
- **WARN**: 需要注意的警告信息
- **INFO**: 正常业务流程信息
- **DEBUG**: 调试信息，生产环境不开启

### 6.2 日志内容

- 请求参数和响应结果应记录在 INFO 级别
- 异常信息应记录在 ERROR 级别，包含完整的堆栈信息
- 敏感信息（如密码、身份证号等）不应记录在日志中

## 7. 限流与熔断

- 使用 Sentinel 实现限流和熔断
- 关键服务应配置合理的限流规则和熔断策略

## 8. 配置管理

- 使用 Nacos 进行配置管理
- 环境相关配置应通过配置中心管理，不应硬编码

## 9. 消息队列

- 使用 RabbitMQ 作为消息队列
- 异步处理和解耦应考虑使用消息队列

## 10. 任务调度

- 使用 XXL-Job 进行任务调度
- 定时任务应通过任务调度中心管理，不应使用 @Scheduled 注解

## 11. 测试规范

### 11.1 单元测试

- 核心业务逻辑必须编写单元测试
- 单元测试应使用 JUnit 和 Mockito

### 11.2 集成测试

- 关键流程应编写集成测试
- 集成测试应使用 Spring Boot Test

## 12. 安全规范

- 敏感数据应加密存储
- API 接口应进行权限控制
- 避免 SQL 注入、XSS 等安全漏洞