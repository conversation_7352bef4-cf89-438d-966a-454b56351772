package com.agtech.pointprod.service.domain.common.enums;

/**
 * 通知类型枚举
 */
public enum NotificationTypeEnum {
    
    /**
     * 接收通知
     */
    ACCEPT("ACCEPT", "接收"),
    
    /**
     * 发送通知
     */
    SEND("SEND", "发送");
    
    private final String code;
    private final String desc;
    
    NotificationTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
} 