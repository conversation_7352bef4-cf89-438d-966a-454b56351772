package com.agtech.pointprod.service.domain.gateway;

import com.agtech.pointprod.service.domain.model.BlackList;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 20:31
 */
public interface BlackListGateway {
    /**
     * 查询转赠黑名单
     *
     * @param userId
     * @return
     */
    BlackList queryTransferBlackListByUserId(String userId);

    long countBlackList(String userId);

    /**
     * 此方法不过滤is_deleted
     * @param entityType
     * @param accountType
     * @param entityId
     * @return
     */
    BlackList selectBlackList(String entityType, String accountType, String entityId);

    /**
     * 此方法不过滤is_deleted
     * @param entityType
     * @param accountType
     * @param entityIds
     * @return
     */
    List<BlackList> selectBlackLists(String entityType, String accountType, List<String> entityIds);

    void save(BlackList blackList);

    void updateByEntity(BlackList blackList, String entityType, String accountType, String entityId);

    List<String> selectAllEntityIdsInBlackList(String entityType, String accountType, List<String> entityIds);
}
