package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;

public enum BlackListAccountTypeEnum {
    USER_ID("USER_ID", "用户ID"),
    IP("IP", "IP地址"),
    EMAIL("EMAIL", "邮箱"),
    ID_CARD("ID_CARD", "身份证"),
    ;
    @Getter
    private String value;
    @Getter
    private String desc;

    BlackListAccountTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
