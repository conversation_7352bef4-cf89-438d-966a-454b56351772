package com.agtech.pointprod.service.domain.common.enums;

import com.agtech.pointprod.order.service.domain.common.constants.LangConstants;

import lombok.Getter;

/**
 * 付款方用户信息页面错误提示码
 */
public enum TransferUserErrorCodeEnum {
    ACCOUNT_IN_BLACKLIST("ACCOUNT_IN_BLACKLIST", PointProdBizErrorCodeEnum.USER_STATUS_ERROR.getErrorMessageTw(), PointProdBizErrorCodeEnum.USER_STATUS_ERROR.getErrorMessage()),
    ACCOUNT_STATUS_ABNORMAL("ACCOUNT_STATUS_ABNORMAL", PointProdBizErrorCodeEnum.USER_FREEZE.getErrorMessageTw(), PointProdBizErrorCodeEnum.USER_FREEZE.getErrorMessage()),
    TRANSFER_MIN_AMOUNT_EXCEED("TRANSFER_MIN_AMOUNT_EXCEED", PointProdBizErrorCodeEnum.MCOIN_USER_BALANCE_INSUFFICIENT.getErrorMessageTw(), PointProdBizErrorCodeEnum.MCOIN_USER_BALANCE_INSUFFICIENT.getErrorMessage());
    @Getter
    private String code;
    private String message;
    @Getter
    private String enMessage;

    TransferUserErrorCodeEnum(String code, String message, String enMessage) {
        this.code = code;
        this.message = message;
        this.enMessage = enMessage;
    }

    public String getMessage() {
        return getMessage(LangConstants.ZH_MO);
    }

    public String getMessage(String lang) {
        if (LangConstants.EN_GB.equals(lang)) {
            return enMessage;
        } else {
            return message;
        }
    }
}
