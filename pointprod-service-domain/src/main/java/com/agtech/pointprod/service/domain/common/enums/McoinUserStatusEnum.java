package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum McoinUserStatusEnum {

    /**
     * 0正常 1凍結
     */
    NORMAL("0", "正常"),
    FREEZE("1", "凍結"),
    ;

    private final String value;

    private final String desc;

    McoinUserStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
