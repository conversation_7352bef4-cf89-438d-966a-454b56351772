package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;

/**
 * 分享码状态枚举
 */
public enum ShareCodeStatusEnum {
    
    /**
     * 有效
     */
    VALID("VALID", "有效"),
    
    /**
     * 已过期
     */
    EXPIRED("EXPIRED", "已过期");
    
    /**
     * 枚举值
     */
    @Getter
    private final String value;
    
    /**
     * 枚举描述
     */
    @Getter
    private final String description;
    
    ShareCodeStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
} 