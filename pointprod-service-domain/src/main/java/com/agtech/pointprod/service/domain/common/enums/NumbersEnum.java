package com.agtech.pointprod.service.domain.common.enums;

import com.agtech.pointprod.service.domain.exception.PointProdBizException;

/**
 * 数字枚举类
 */
public enum NumbersEnum {
    
    /**
     * 零
     */
    ZERO(0, "0"),
    
    /**
     * 一
     */
    ONE(1, "1"),
    
    /**
     * 二
     */
    TWO(2, "2"),
    
    /**
     * 三
     */
    THREE(3, "3"),
    
    /**
     * 四
     */
    FOUR(4, "4"),
    
    /**
     * 五
     */
    FIVE(5, "5"),
    
    /**
     * 六
     */
    SIX(6, "6"),
    
    /**
     * 七
     */
    SEVEN(7, "7"),
    
    /**
     * 八
     */
    EIGHT(8, "8"),
    
    /**
     * 九
     */
    NINE(9, "9"),
    
    /**
     * 十
     */
    TEN(10, "10"), 
    
    /**
     * 二十
     */
    TWENTY(20, "20"), 

    /**
     * 一百
     */
    ONE_HUNDRED(100, "100");
    
    /**
     * 数字值
     */
    private final int intValue;
    
    /**
     * 字符串值
     */
    private final String stringValue;
    
    NumbersEnum(int intValue, String stringValue) {
        this.intValue = intValue;
        this.stringValue = stringValue;
    }
    
    /**
     * 获取整数值
     * @return 整数值
     */
    public int getIntValue() {
        return intValue;
    }
    
    /**
     * 获取字符串值
     * @return 字符串值
     */
    public String getStringValue() {
        return stringValue;
    }
    
    /**
     * 根据整数值获取枚举
     * @param intValue 整数值
     * @return 枚举对象
     */
    public static NumbersEnum fromIntValue(int intValue) {
        for (NumbersEnum number : NumbersEnum.values()) {
            if (number.getIntValue() == intValue) {
                return number;
            }
        }
        throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
    }
    
    /**
     * 根据字符串值获取枚举
     * @param stringValue 字符串值
     * @return 枚举对象
     */
    public static NumbersEnum fromStringValue(String stringValue) {
        for (NumbersEnum number : NumbersEnum.values()) {
            if (number.getStringValue().equals(stringValue)) {
                return number;
            }
        }
        throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
    }
} 