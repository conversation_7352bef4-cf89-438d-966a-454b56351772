package com.agtech.pointprod.service.domain.util;

/**
 * 分享码生成工具类
 * 使用自定义乱序字符集对数字进行编码，避免标准Base62的规律性，增加安全性
 */
public class ShareCodeUtil {
    
    /**
     * 分享码最小长度
     */
    private static final int MIN_SHARE_CODE_LENGTH = 6;
    
    /**
     * 自定义乱序字符集 - 避免使用标准Base62顺序，增加安全性
     * 包含数字0-9、小写字母a-z、大写字母A-Z，共62个字符，顺序打乱
     * 标准顺序: 0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
     * 乱序排列: RfwiqJdxg7oICB1FHKSuXEcV4TWhjrQm6vstA3MlapeG8DObPy0N2LnUkz9Z5Y
     */
    private static final String CUSTOM_CHARSET = "RfwiqJdxg7oICB1FHKSuXEcV4TWhjrQm6vstA3MlapeG8DObPy0N2LnUkz9Z5Y";
    
    /**
     * 私有构造函数，防止实例化
     */
    private ShareCodeUtil() {
        throw new IllegalStateException("Utility class");
    }
    
    /**
     * 生成分享码：通过序列号转换成自定义乱序字符集编码，至少6位
     * @param sequenceNumber 序列号
     * @return 至少6位的自定义编码分享码
     */
    public static String generateShareCode(long sequenceNumber) {
        // 对于负数，取绝对值处理
        long absNumber = Math.abs(sequenceNumber);
        
        // 使用自定义字符集进行编码
        String customCode = encodeWithCustomCharset(absNumber);
        
        // 确保长度至少为6位，不足6位的前面补自定义字符集的第一个字符
        if (customCode.length() < MIN_SHARE_CODE_LENGTH) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < MIN_SHARE_CODE_LENGTH - customCode.length(); i++) {
                sb.append(CUSTOM_CHARSET.charAt(0));
            }
            sb.append(customCode);
            return sb.toString();
        } else {
            // 超过6位的情况直接返回，保持原始编码长度
            return customCode;
        }
    }
    
    /**
     * 使用自定义字符集对数字进行编码
     * @param number 要编码的数字
     * @return 编码后的字符串
     */
    private static String encodeWithCustomCharset(long number) {
        if (number == 0) {
            return String.valueOf(CUSTOM_CHARSET.charAt(0));
        }
        
        StringBuilder result = new StringBuilder();
        int base = CUSTOM_CHARSET.length();
        
        while (number > 0) {
            result.insert(0, CUSTOM_CHARSET.charAt((int)(number % base)));
            number = number / base;
        }
        
        return result.toString();
    }
} 