package com.agtech.pointprod.service.domain.config;

import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 重试策略配置类
 * 基于TaskResouceTypeEnum枚举定义不同任务类型的重试策略
 * 从Nacos中获取配置参数
 */
@Data
@Component
@ConfigurationProperties(prefix = "task-retry.strategy")
public class RetryStrategyProperties {
    
    /**
     * 默认重试配置
     */
    private RetryConfig defaultConfig = new RetryConfig();
    
    /**
     * 针对不同任务类型的重试配置
     * key: TaskResouceTypeEnum的code值
     * value: 对应的重试配置
     */
    private Map<String, RetryConfig> taskConfigs = new HashMap<>();
    
    /**
     * 重试类型枚举
     */
    public enum RetryType {
        /**
         * 定时任务重试
         */
        SCHEDULED_TASK("SCHEDULED_TASK", "定时任务重试"),
        
        /**
         * 延迟队列重试
         */
        DELAY_QUEUE("DELAY_QUEUE", "延迟队列重试");
        
        private final String code;
        private final String description;
        
        RetryType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 重试配置内部类
     */
    @Data
    public static class RetryConfig {
        
        /**
         * 重试类型，默认为定时任务重试
         */
        private RetryType retryType = RetryType.SCHEDULED_TASK;
        
        /**
         * 最大重试次数
         */
        private int maxRetryCount = 3;
        
        /**
         * 初始重试延迟时间（秒）
         */
        private long initialDelaySeconds = 60;
        
        /**
         * 最大重试延迟时间（秒）
         */
        private long maxDelaySeconds = 3600;
        
        /**
         * 重试延迟倍数（指数退避）
         */
        private double backoffMultiplier = 2.0;
        
        /**
         * 是否启用指数退避
         */
        private boolean enableExponentialBackoff = true;
    }
    
    /**
     * 根据任务类型获取重试配置
     * @param taskType 任务类型枚举
     * @return 重试配置
     */
    public RetryConfig getRetryConfig(TaskResouceTypeEnum taskType) {
        if (taskType == null) {
            return defaultConfig;
        }
        return taskConfigs.getOrDefault(taskType.getCode(), defaultConfig);
    }
    
    /**
     * 根据任务类型代码获取重试配置
     * @param taskTypeCode 任务类型代码
     * @return 重试配置
     */
    public RetryConfig getRetryConfig(String taskTypeCode) {
        if (taskTypeCode == null || taskTypeCode.trim().isEmpty()) {
            return defaultConfig;
        }
        return taskConfigs.getOrDefault(taskTypeCode, defaultConfig);
    }
    
    /**
     * 根据任务类型获取最大重试次数
     * @param taskType 任务类型枚举
     * @return 最大重试次数
     */
    public int getMaxRetryCount(TaskResouceTypeEnum taskType) {
        return getRetryConfig(taskType).getMaxRetryCount();
    }
    
    /**
     * 根据任务类型获取初始延迟时间
     * @param taskType 任务类型枚举
     * @return 初始延迟时间（秒）
     */
    public long getInitialDelaySeconds(TaskResouceTypeEnum taskType) {
        return getRetryConfig(taskType).getInitialDelaySeconds();
    }
    
    /**
     * 根据任务类型获取重试类型
     * @param taskType 任务类型枚举
     * @return 重试类型
     */
    public RetryType getRetryType(TaskResouceTypeEnum taskType) {
        return getRetryConfig(taskType).getRetryType();
    }
    
    /**
     * 根据任务类型判断是否使用定时任务重试
     * @param taskType 任务类型枚举
     * @return 是否使用定时任务重试
     */
    public boolean isScheduledTaskRetry(TaskResouceTypeEnum taskType) {
        return RetryType.SCHEDULED_TASK.equals(getRetryType(taskType));
    }
    
    /**
     * 根据任务类型判断是否使用延迟队列重试
     * @param taskType 任务类型枚举
     * @return 是否使用延迟队列重试
     */
    public boolean isDelayQueueRetry(TaskResouceTypeEnum taskType) {
        return RetryType.DELAY_QUEUE.equals(getRetryType(taskType));
    }
}