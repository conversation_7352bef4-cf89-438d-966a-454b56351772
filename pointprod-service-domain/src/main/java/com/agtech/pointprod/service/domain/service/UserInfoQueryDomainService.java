package com.agtech.pointprod.service.domain.service;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import lombok.Getter;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 用户信息查询领域服务
 * 负责异步查询用户相关信息，包括用户基本信息、积分账户信息等
 * 
 * <AUTHOR>
 */
public interface UserInfoQueryDomainService {
    
    /**
     * 异步查询用户信息结果
     */
    @Getter
    class UserInfoQueryResult {
        private final List<CompletableFuture<McoinAccount>> payerMcoinAccounts;
        private final List<CompletableFuture<McoinAccount>> payeeMcoinAccounts;
        private final List<CompletableFuture<MPayUserInfo>> payerUserInfos;
        private final List<CompletableFuture<MPayUserInfo>> payeeUserInfos;
        private final List<CompletableFuture<Boolean>> securityValidations;
        
        public UserInfoQueryResult(
                List<CompletableFuture<McoinAccount>> payerMcoinAccounts,
                List<CompletableFuture<McoinAccount>> payeeMcoinAccounts,
                List<CompletableFuture<MPayUserInfo>> payerUserInfos,
                List<CompletableFuture<MPayUserInfo>> payeeUserInfos,
                List<CompletableFuture<Boolean>> securityValidations) {
            this.payerMcoinAccounts = payerMcoinAccounts;
            this.payeeMcoinAccounts = payeeMcoinAccounts;
            this.payerUserInfos = payerUserInfos;
            this.payeeUserInfos = payeeUserInfos;
            this.securityValidations = securityValidations;
        }
    }
    
    /**
     * 异步查询订单相关的用户信息
     * 
     * @param orderInfo 订单信息
     * @param orderId 订单ID
     * @param securityId 安全ID
     * @return 异步查询结果
     */
    UserInfoQueryResult queryUserInfoAsync(BaseOrderInfo orderInfo, String orderId, String securityId);
    
    /**
     * 等待异步查询完成并获取结果
     * 
     * @param queryResult 异步查询结果
     * @return 同步的查询结果
     */
    SyncUserInfoResult waitForResults(UserInfoQueryResult queryResult);
    
    /**
     * 同步查询结果
     */
    @Getter
    class SyncUserInfoResult {
        private final List<McoinAccount> payerMcoinAccounts;
        private final List<McoinAccount> payeeMcoinAccounts;
        private final List<MPayUserInfo> payerUserInfos;
        private final List<MPayUserInfo> payeeUserInfos;
        private final List<Boolean> securityValidations;
        
        public SyncUserInfoResult(
                List<McoinAccount> payerMcoinAccounts,
                List<McoinAccount> payeeMcoinAccounts,
                List<MPayUserInfo> payerUserInfos,
                List<MPayUserInfo> payeeUserInfos,
                List<Boolean> securityValidations) {
            this.payerMcoinAccounts = payerMcoinAccounts;
            this.payeeMcoinAccounts = payeeMcoinAccounts;
            this.payerUserInfos = payerUserInfos;
            this.payeeUserInfos = payeeUserInfos;
            this.securityValidations = securityValidations;
        }
    }
}