package com.agtech.pointprod.service.domain.common.enums;

import com.agtech.common.result.AqcResultCode;
import com.agtech.common.result.code.ResultCodeLevel;
import com.agtech.common.result.code.ResultCodeType;
import com.agtech.pointprod.order.service.domain.common.constants.LangConstants;
import com.agtech.pointprod.order.service.domain.common.context.BizContextHolder;

/**
 * 基础错误码见：{@link com.agtech.common.error.enums.AqcProdResultCode}
 * <AUTHOR>
 * @version v1.0, 2025/6/11 16:01
 */
public enum PointProdBizErrorCodeEnum implements AqcResultCode {
    //參數錯誤
    UN_SUPPORT_BUSINESS(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "013", "不支持該業務","unSupport business"),
    PARAMS_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "100", "request param error", "參數錯誤"),
    SYS_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "101", "System error", "系統異常"),
    DB_UPDATE_FAIL(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "102", "System error", "系統異常"),
    REQUEST_TRAFFIC_EXCEED_LIMIT(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "103", "Please refresh the page or try again later", "請嘗試重新加載或稍候再試"),

/** 模型拒絕當前操作 */
    /**
     * 模型拒絕當前操作
     */
    ACTION_NOT_ACCEPTED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "106", "System error","系統異常"),
    BIZ_PROCESSED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "117", "biz processed", "業務已處理"),
    TO_RETRY_TASK(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "118", "biz retry", "業務重試"),
    CONTRACT_NOT_EXISTS(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "119", "contract not exists", "協議不存在"),
    PARAMS_MISSING(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "120", "request param missing", "參數缺失"),
    USER_NOT_EXISTS(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "121", "user not exists", "用戶不存在"),
    USER_FREEZE(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "122", "The account has been frozen, Please contact us", "此賬戶已被凍結，請儘快與澳門通聯絡"),
    TRANSFER_COUNT_EXCEED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "123", "transfer count exceed", "本週期可轉贈次數已達上限"),
    MCOIN_ACCOUNT_NOT_EXISTS(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "124", "mcoin account not exists", "mCoin賬戶不存在"),
    GRAY_SWITCH_CONFIG_NOT_EXISTS(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "321", "gray switch config not exists", "灰度開關配置不存在"),
    GRAY_FEATURE_KEY_REQUIRED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "322", "feature key is required", "功能鍵不能為空"),
    GRAY_CUST_ID_REQUIRED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "323", "customer id is required", "客戶ID不能為空"),
    SHARE_SHORT_LINK_CONFIG_NOT_FOUND(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "324", "short link config not found", "短鏈配置未找到"),
    SHARE_TRANSFER_LINK_CONFIG_NOT_FOUND(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "325", "transfer link config not found", "轉換鏈接配置未找到"),
    URL_ENCODING_FAILED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "326", "url encoding failed", "URL編碼失敗"),
    NOTIFICATION_ID_LIST_EMPTY(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "327", "notification id list empty", "通知ID列表不能為空"),
    SHARE_CONTENT_ID_REQUIRED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "328", "share content id is required", "分享內容ID不能為空"),
    SHARE_CONTENT_TYPE_REQUIRED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "329", "share content type is required", "分享內容類型不能為空"),
    SHARE_CHANNEL_TYPE_REQUIRED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "330", "share channel type is required", "分享渠道類型不能為空"),
    INVALID_CONTENT_TYPE(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "331", "invalid content type", "無效的內容類型"),
    INVALID_CHANNEL_TYPE(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "332", "invalid channel type", "無效的分享渠道類型"),
    FUND_ORDER_NOT_FOUND(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "333", "fund order not found", "訂單不存在"),
    MESSAGE_JSON_PARSE_FAILED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "334", "message json parse failed", "消息JSON解析失敗"),
    NOTIFICATION_ID_MISSING_IN_MESSAGE(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "335", "notification id missing in message", "消息中缺少通知ID"),

    /**
     * 獲取下單token失敗
     */
    GET_ORDER_TOKEN_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "201", "System error", "系統異常"),
    /**
     * 訂單token不存在
     */
    ORDER_TOKEN_NOT_EXIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "202", "System error", "系統異常"),
    /**
     * 訂單token無效
     */
    ORDER_TOKEN_INVALID(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "203", "System error", "系統異常"),
    /**
     * 付款人或收款人相同
     */
    PAYER_AND_PAYEE_SAME(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "204", "You cannot transfer to yourself", "不能轉贈給自己"),
    /**
     * 付款人或收款人在黑名單
     */
    PAYER_IN_BLACK_LIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "205", "Abnormal account status， \nGot it", "轉贈人賬戶狀態異常, 請盡快與澳門通聯絡"),
    /**
     * 收款人在黑名單
     */
    PAYEE_IN_BLACK_LIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "206", "Recipient's status is abnormal，\nplease contact us", "被轉贈人賬戶狀態異常, 請盡快與澳門通聯絡"),
    /**
     * 查詢付款人活收款人信息失敗
     */
    GET_PAYER_OR_PAYEE_INFO_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "207", "System error", "系統異常"),
    /**
     * mcoin用戶不存在
     */
    MCOIN_USER_NOT_EXIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "208", "Transfers to unregistered users are temporarily unavailable", "暫時不能轉賬給未註冊的用戶"),
    /**
     * MPay用戶不存在
     */
    MPAY_USER_NOT_EXIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "209", "Transfers to unregistered users are temporarily unavailable", "暫時不能轉賬給未註冊的用戶"),
    /**
     * mcoin用戶積分餘額不足
     */
    MCOIN_USER_BALANCE_INSUFFICIENT(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "211", "Insufficient Balance", "mCoin餘額不足"),
    /**
     * 下單請求重複
     */
    PLACE_ORDER_REQUEST_REPEAT(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "212", "System error", "系統異常"),
    /**
     * 限額校驗失敗
     */
    LIMIT_CHECK_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "213", "System error", "系統異常"),
    /**
     * 訪問MCOIN服務失敗
     */
    ACCESS_MCOIN_SERVICE_FAIL(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "214", "System error", "系統異常"),
    /**
     * 訪問MPAY服務失敗
     */
    ACCESS_MPAY_SERVICE_FAIL(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "215", "System error", "系統異常"),
    /**
     * 加簽失敗
     */
    SIGN_FAIL(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "216", "System error", "系統異常"),
    /**
     * 驗簽失敗
     */
    VERIFY_SIGN_FAIL(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "217", "System error", "系統異常"),
    /**
     * 訂單id與用戶id不匹配
     */
    ORDER_USER_NOT_MATCH(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "218", "System error", "系統異常"),
    /**
     * 訂單狀態不為待支付
     */
    ORDER_STATUS_NOT_WAITING_PAY(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "219", "System error", "系統異常"),
    /**
     * 操作超時，訂單已關閉
     */
    ORDER_STATUS_CLOSE(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "220", "Order has been closed", "操作超時，訂單已關閉"),
    /**
     * 轉贈人賬號已凍結
     */
    PAYER_ACCOUNT_FREEZE(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "221", "The account has been frozen,\n please contact us", "轉贈人賬戶已被凍結, 請盡快與澳門通聯絡"),
    /**
     * 轉贈人賬號已註銷
     */
    PAYER_ACCOUNT_CANCEL(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "222", "System error", "系統異常"),
    /**
     * 被轉贈人賬號已凍結
     */
    PAYEE_ACCOUNT_FREEZE(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "223", "The recipient's account has been frozen，\n please contact us", "被轉贈人賬戶已被凍結, 請盡快與澳門通聯絡"),
    /**
     * 被轉贈人賬號已註銷
     */
    PAYEE_ACCOUNT_CANCEL(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "224", "Transfers to unregistered users are temporarily unavailable", "暫時不能轉賬給未註冊的用戶"),
    /**
     * 訪問RISK服務失敗
     */
    ACCESS_RISK_SERVICE_FAIL(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "225", "System error", "系統異常"),
    /**
     * 訂單留言不合法
     */
    ORDER_MEMO_INVALID(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "226", "Illegal content", "留言內容不合法"),
    /**
     * 限額校驗失敗-本週期可轉贈次數已達上限
     */
    LIMIT_CHECK_UPPER_COUNT_EXCEED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "227", "Transfer limit reached in this cycle", "本週期可轉贈次數已達上限"),
    /**
     * 限額校驗失敗-額度不足
     */
    LIMIT_CHECK_UPPER_AMOUNT_EXCEED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "228", "Insufficient quota", "額度不足"),
    /**
     * 限額校驗失敗-轉贈金額小於對應付款人等級最小轉贈金額
     */
    LIMIT_CHECK_LOWER_AMOUNT_EXCEED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "229", "System error", "系統異常"),

    PAYER_STATUS_INVALID(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "230", "Abnormal account status， \nGot it", "轉贈人賬戶狀態異常, 請盡快與澳門通聯絡"),
    PAYEE_STATUS_INVALID(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "231", "Recipient's status is abnormal，\nplease contact us", "被轉贈人賬戶狀態異常, 請盡快與澳門通聯絡"),
    /**
     * 付款人轉贈金額與等級對應的轉贈規則中的金額不匹配
     */
    PAYER_TRANSFER_RULE_NOT_MATCH(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "232", "System error", "系統異常"),


    /**
     * 訂單不存在
     */
    ORDER_NOT_EXIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "250", "System error", "系統異常"),
    PAY_FAIL(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "251", "Pay fail", "支付失敗"),
    PHONE_UNREGISTER_MPAY(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "252", "You cannot transfer to an unregistered user", "此號碼未註冊MPay，無法轉贈"),
    CANNOT_TRANSFER_ONESELF(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "253", "You cannot transfer to yourself", "不能轉贈給自己"),
    USER_STATUS_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "254", "Abnormal account status", "此賬戶狀態異常"),

    PAY_RESULT_UNKNOWN(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "255", "Pay result unknown", "支付結果未知"),
    PAY_SUCCESS_UPDATE_STATUS_EXCEPTION(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "256", "System error", "系統異常"),
    PAY_SUCCESS_UPDATE_LIMIT_EXCEPTION(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "257", "System error", "系統異常"),
    PAY_SUCCESS_INSERT_FLUX_EXCEPTION(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "258", "System error", "系統異常"),


    // <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<以下錯誤碼為限額服務的錯誤碼,從300開始>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
    CONFIG_NOT_EXIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "303","config not found", "未找到限額配置"),
    RULE_NOT_EXIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,"304","rule not found", "未找到限額規則"),
    RULE_NOT_MATCH(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "305","rule not match", "限額規則不匹配"),
    GROOVY_SCRIPT_RUN_ERROR(ResultCodeLevel.ERROR, ResultCodeType.SYS_ERROR,  "306","script run error", "腳本執行異常"),
    STRATEGY_NOT_EXIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,  "307","strategy not exist", "策略不存在"),
    ACCOUNT_METHOD_NOT_EXIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,  "308","account method not exist", "賬戶相關方法不存在"),
    ACCOUNT_MORE_THAN_ONE_MATCH(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,  "309","account more than one match", "賬戶匹配到多個"),
    DUPLICATE_REQUEST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,  "310","bizNo  duplicate request", "重複請求"),
    ACCOUNT_EXCEED(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,  "311","account exceed", "賬戶超額"),
    INSERT_TABLE_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,  "312","insert table error", "插入表錯誤"),
    UPDATE_TABLE_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,  "313","update table error", "更新表錯誤"),
    TASK_NOT_EXIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,  "314","bizNo  task not exist", "任務不存在"),
    TASK_STATUS_NOT_ALLOW_CANCEL(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,  "315","bizNo  task status not allow cancel", "任務狀態不允許取消"),
    EXTENSION_NOT_FOUND(ResultCodeLevel.ERROR, ResultCodeType.SYS_ERROR,  "316","extension not found", "找不到擴展點"),
    RULE_DELETE_STATUS_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,"317","rule not delete", "限額規則開啟狀態，無法刪除"),
    CHECK_RULE_LAST_ENABLE(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR,"318","no online gear rules at this level, so it cannot be disabled", "當前該等級下無在線檔位規則，無法停用"),

    TEMPLATE_COUNT_EXCEED_LIMIT(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "400", "template count exceed limit", "留言數量超過限制"),

    /**
     * 用戶id為空
     */
    ACCESS_DENIED(ResultCodeLevel.ERROR, ResultCodeType.SYS_ERROR,  "401","System error", "系統異常"),

    /**
     * 通知不存在
     */
    NOTIFICATION_NOT_EXIST(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "500", "Notification does not exist", "通知不存在"),

    /**
     * 通知資源類型錯誤
     */
    NOTIFICATION_RESOURCE_TYPE_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "501", "Notification resource type error", "通知資源類型錯誤"),

    /**
     * 訂單不存在
     */
    ORDER_NOT_EXISTS(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "502", "Order does not exist", "訂單不存在"),

    /**
     * 訂單狀態錯誤
     */
    ORDER_STATUS_ERROR(ResultCodeLevel.ERROR, ResultCodeType.BIZ_ERROR, "503", "Order status error", "訂單狀態錯誤"),

    ;
    /**
     * 错误级别
     */
    private final String errorLevel;
    /**
     * 错误类别
     */
    private final String errorType;
    /**
     * 枚举编码
     */
    private final String errorCode;
    /**
     * 描述说明-英文
     */
    private final String errorMessage;
    /**
     * 描述说明-繁体
     */
    private final String errorMessageTw;

    PointProdBizErrorCodeEnum(String errorLevel, String errorType, String errorCode, String errorMessage, String errorMessageTw) {
        this.errorLevel = errorLevel;
        this.errorType = errorType;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.errorMessageTw = errorMessageTw;
    }

    @Override
    public String getErrorLevel() {
        return errorLevel;
    }

    @Override
    public String getErrorType() {
        return errorType;
    }

    @Override
    public String getResultCode() {
        return errorCode;
    }

    @Override
    public String getResultMsg() {
    	if(BizContextHolder.getLang().equalsIgnoreCase(LangConstants.ZH_MO)) {
    		return errorMessageTw;
    	}
        return errorMessage;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public String getErrorMessageTw() {
        return errorMessageTw;
    }


}
