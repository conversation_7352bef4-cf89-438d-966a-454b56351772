package com.agtech.pointprod.service.domain.common.enums;

/**
 * 删除状态枚举
 */
public enum DeletedEnum {

    NO(0, "未删除"),
    YES(1, "已删除");

    /**
     * 状态值
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    DeletedEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    /**
     * 根据code获取枚举
     */
    public static DeletedEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeletedEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否删除
     */
    public static boolean isDeleted(Integer code) {
        return YES.getCode().equals(code);
    }

    /**
     * 判断是否未删除
     */
    public static boolean isNotDeleted(Integer code) {
        return NO.getCode().equals(code);
    }

    /**
     * 获取默认值（未删除）
     */
    public static Integer getDefaultCode() {
        return NO.getCode();
    }
}
