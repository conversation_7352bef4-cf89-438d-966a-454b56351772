package com.agtech.pointprod.service.domain.common.enums;

import com.agtech.pointprod.service.domain.exception.PointProdBizException;

/**
 * 内容类型枚举
 */
public enum ContentTypeEnum {
    
    /**
     * 转赠积分
     */
    TRANSFER_COIN("TRANSFER_COIN", "转赠积分");
    
    /**
     * 枚举值
     */
    private final String value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    ContentTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
    
    public String getValue() {
        return value;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据值获取枚举
     * @param value 枚举值
     * @return 枚举对象
     */
    public static ContentTypeEnum fromValue(String value) {
        for (ContentTypeEnum type : ContentTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
    }
} 