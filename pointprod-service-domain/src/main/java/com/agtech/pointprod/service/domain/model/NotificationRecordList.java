package com.agtech.pointprod.service.domain.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 通知记录列表，包含通知记录列表和分享码是否过期标志
 */
@Getter
@Setter
public class NotificationRecordList {
    
    /**
     * 通知记录列表
     */
    private List<NotificationRecord> notificationRecords;
    
    /**
     * 分享码是否过期
     */
    private boolean isShareCodeExpired;
    
    /**
     * 分享码通知状态 (READ/UNREAD)
     */
    private String shareCodeNotificationStatus;
    
    /**
     * 分享码对应的订单ID
     */
    private String shareCodeOrderId;
    
    /**
     * 分享码对应的用户角色
     */
    private String shareCodeRole;
    
    /**
     * 构造通知记录列表
     * @param notificationRecords 通知记录列表
     */
    public NotificationRecordList(List<NotificationRecord> notificationRecords) {
        this.notificationRecords = notificationRecords;
        this.isShareCodeExpired = false;
        this.shareCodeNotificationStatus = null;
        this.shareCodeOrderId = null;
        this.shareCodeRole = null;
    }
    
    /**
     * 构造通知记录列表
     * @param notificationRecords 通知记录列表
     * @param isShareCodeExpired 分享码是否过期
     */
    public NotificationRecordList(List<NotificationRecord> notificationRecords, boolean isShareCodeExpired) {
        this.notificationRecords = notificationRecords;
        this.isShareCodeExpired = isShareCodeExpired;
        this.shareCodeNotificationStatus = null;
        this.shareCodeOrderId = null;
        this.shareCodeRole = null;
    }
    
    /**
     * 构造通知记录列表
     * @param notificationRecords 通知记录列表
     * @param isShareCodeExpired 分享码是否过期
     * @param shareCodeNotificationStatus 分享码通知状态
     */
    public NotificationRecordList(List<NotificationRecord> notificationRecords, boolean isShareCodeExpired, String shareCodeNotificationStatus) {
        this.notificationRecords = notificationRecords;
        this.isShareCodeExpired = isShareCodeExpired;
        this.shareCodeNotificationStatus = shareCodeNotificationStatus;
        this.shareCodeOrderId = null;
        this.shareCodeRole = null;
    }
} 