package com.agtech.pointprod.service.domain.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.order.service.domain.gateway.OrderGateway;
import com.agtech.pointprod.service.domain.common.enums.DeletedEnum;
import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.config.RetryStrategyProperties.RetryType;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.MessageSenderGateway;
import com.agtech.pointprod.service.domain.gateway.QueueConfigGateway;
import com.agtech.pointprod.service.domain.gateway.TaskRetryGateway;
import com.agtech.pointprod.service.domain.model.MqTaskConfig;
import com.agtech.pointprod.service.domain.model.TaskConfig;
import com.agtech.pointprod.service.domain.model.TaskConfigFactory;
import com.agtech.pointprod.service.domain.model.TaskData;
import com.agtech.pointprod.service.domain.model.TaskRetry;
import com.agtech.pointprod.service.domain.service.MessageReliabilityDomainService;
import com.agtech.pointprod.service.domain.service.RetryStrategyDomainService;
import com.agtech.pointprod.service.facade.dto.enums.TaskRetryStatusEnum;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务重试领域服务实现类
 * <AUTHOR>
 * @version TaskRetryDomainServiceImpl.java, v0.1 2025/6/18 14:29
 */
@Service
@Slf4j
public class MessageReliabilityDomainServiceImpl implements MessageReliabilityDomainService {

    @Resource
    private TaskRetryGateway taskRetryGateway;

    @Resource
    private TaskConfigFactory taskConfigFactory;

    @Resource
    private OrderGateway orderGateway;

    @Resource
    private MessageSenderGateway messageSenderGateway;

    @Resource
    private QueueConfigGateway queueConfigGateway;

    @Resource
    private RetryStrategyDomainService retryStrategyDomainService;

    @Value("${task-retry.batch-size:100}")
    private int batchSize;

    @Value("${task-retry.query-start-days:30}")
    private int queryStartDays;



    @Transactional(rollbackFor = Exception.class)
    @Override
    public <D extends TaskData> boolean sendMessageAndMarkDelivered(String messageId, Class<D> taskDataClass) {
        if (StringUtils.isBlank(messageId)) {
            log.error("sendMessageAndMarkDelivered fail, messageId is null");
            return false;
        }
        // 1. 查询任务重试记录
        TaskRetry taskRetry = taskRetryGateway.getTaskRetryByIdForUpdate(messageId, MqTaskConfig.class, taskDataClass);
        if (taskRetry == null) {
            log.error("TaskRetry not found for messageId: {}", messageId);
            return false;
        }

        // 2. 解析MQ配置
        MqTaskConfig taskConfig = (MqTaskConfig)taskRetry.getTaskConfig();
        if (taskConfig == null) {
            log.error("Invalid MqConfig for messageId: {}", messageId);
            return false;
        }

        // 3. 发送消息
        try {
            messageSenderGateway.sendMessage(
                taskRetry.getTaskRetryId(),
                taskRetry.getTaskData(),
                taskConfig.getExchange(),
                taskConfig.getRoutingKey(),
                null
            );
        } catch (Exception e) {
            log.error("Failed to send message for messageId: {}", messageId, e);
            return false;
        }

        // 4. 标记消息已发送
        return taskRetryGateway.finishTaskRetry(messageId);
    }



    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public <T extends TaskData> TaskRetry ensureMessageConsume(String messageId, String resourceId, TaskResouceTypeEnum resourceType,
                                                               MessageQueueEnum messageQueue, T taskData) {
        if (StringUtils.isBlank(messageId)) {
            log.error("ensureMessageConsume fail, messageId is null");
            return null;
        }
        if (resourceType == null || messageQueue == null || taskData == null) {
            log.error("ensureMessageConsume fail, resourceType is null or messageQueue is null or taskData is null");
            return null;
        }
        if (!resourceType.getCode().startsWith(TaskResouceTypeEnum.MQ_CONSUME.getCode())) {
            log.error("ensureMessageConsume fail, resourceType is not a message consume task");
            return null;
        }
        // 1. 查询任务重试记录
        TaskRetry taskRetry = taskRetryGateway.getTaskRetryById(messageId, MqTaskConfig.class, taskData.getClass());
        if (taskRetry != null && taskRetry.getResourceType() == resourceType) {
            log.info("TaskRetry already exists for messageId: {}, resourceType: {}", messageId, resourceType);
            return taskRetry;
        }
        // 2. 根据resourceId查询任务重试记录
        TaskRetry taskRetryByResourceId = taskRetryGateway.getTaskRetryByResourceIdForUpdate(resourceId, resourceType, MqTaskConfig.class, taskData.getClass());
        if (taskRetryByResourceId != null) {
            log.info("TaskRetry already exists for resourceId: {}, resourceType: {}", resourceId, resourceType);
            return taskRetryByResourceId;
        }
        // 3. 确保消息投递
        return createMessageDeliveryTask(resourceType, messageQueue, taskData);
    }



    @Transactional(rollbackFor = Exception.class)
    @Override
    public <D extends TaskData> boolean markConsumeFinish(String messageId, Class<D> taskDataClass) {
        if (StringUtils.isBlank(messageId)) {
            log.error("markConsumeFinish fail, messageId is null");
            return false;
        }
        // 1. 查询任务重试记录
        TaskRetry taskRetry = taskRetryGateway.getTaskRetryByIdForUpdate(messageId, MqTaskConfig.class, taskDataClass);
        if (taskRetry == null) {
            log.error("TaskRetry not found for messageId: {}", messageId);
            return false;
        }
        
        // 2. 直接标记消息已投递，不发送消息
        return taskRetryGateway.finishTaskRetry(messageId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public <D extends TaskData> boolean sendDelayMessageAndMarkDelivered(String messageId, Integer delay, Class<D> taskDataClass) {
        if (StringUtils.isBlank(messageId)) {
            log.error("sendDelayMessageAndMarkDelivered fail, messageId is null");
            return false;
        }
        // 1. 查询任务重试记录
        TaskRetry taskRetry = taskRetryGateway.getTaskRetryByIdForUpdate(messageId, MqTaskConfig.class, taskDataClass);
        if (taskRetry == null) {
            log.error("TaskRetry not found for messageId: {}", messageId);
            return false;
        }
        
        // 2. 解析MQ配置
        MqTaskConfig taskConfig = (MqTaskConfig)taskRetry.getTaskConfig();
        if (taskConfig == null) {
            log.error("Invalid MqConfig for messageId: {}", messageId);
            return false;
        }
        
        // 3. 发送延迟消息
        try {
            messageSenderGateway.sendMessage(
                taskRetry.getTaskRetryId(),
                taskRetry.getTaskData(),
                taskConfig.getExchange(),
                taskConfig.getRoutingKey(),
                delay
            );
        } catch (Exception e) {
            log.error("Failed to send delay message for messageId: {}", messageId, e);
            return false;
        }
        
        // 4. 标记消息已发送
        return taskRetryGateway.finishTaskRetry(messageId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public <D extends TaskData> boolean sendDelayMessageAndIncrRetryCount(String messageId, Class<D> taskDataClass) {
        if (StringUtils.isBlank(messageId)) {
            log.error("sendDelayMessageAndIncrRetryCount fail, messageId is null");
            return false;
        }
        // 1. 查询任务重试记录
        TaskRetry taskRetry = taskRetryGateway.getTaskRetryByIdForUpdate(messageId, MqTaskConfig.class, taskDataClass);
        if (taskRetry == null) {
            log.error("TaskRetry not found for messageId: {}", messageId);
            return false;
        }
        
        // 2. 解析MQ配置
        MqTaskConfig taskConfig = (MqTaskConfig)taskRetry.getTaskConfig();
        if (taskConfig == null) {
            log.error("Invalid MqConfig for messageId: {}", messageId);
            return false;
        }

        // 使用重试策略服务检查是否达到最大重试次数
        String retryStrategyDescription = retryStrategyDomainService.getRetryStrategyDescription(taskRetry.getResourceType(), taskRetry.getMaxTryCount());
        log.info("messageId:{}, retryStrategyDescription: {}", messageId, retryStrategyDescription);
        if (!taskRetry.canRetryForDelayQueue()) {
            // 标记为失败
            taskRetry.setStatus(TaskRetryStatusEnum.FAILED);
            taskRetry.setGmtModified(ZonedDateUtil.now());
            log.info("Task retry reached max count, marked as failed: {}, strategy: {}", 
                    taskRetry.getTaskRetryId(), retryStrategyDescription);
            return false;
        }
        
        // 3. 发送延迟消息
        try {
            LocalDateTime nextRetryTime = retryStrategyDomainService.calculateNextRetryTime(taskRetry.getResourceType(), taskRetry.getTryCount());
            if (nextRetryTime == null) {
                log.info("messageId:{}, calculateNextRetryTime fail, nextRetryTime is null", messageId);
                return false;
            }
            messageSenderGateway.sendMessage(
                taskRetry.getTaskRetryId(),
                taskRetry.getTaskData(),
                taskConfig.getExchange(),
                taskConfig.getRoutingKey(),
                (int)(nextRetryTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() - System.currentTimeMillis())
            );
            // 4. 递增重试次数
            taskRetry.setTryCount(taskRetry.getTryCount() + 1);
            
            log.info("messageId:{}, send delay message success, incr tryCount to: {}", messageId, taskRetry.getTryCount());
            
            // 更新重试次数和下次重试时间到数据库
            return taskRetryGateway.incrTaskRetryCount(taskRetry);
        } catch (Exception e) {
            log.error("Failed to send delay message for messageId: {}", messageId, e);
            return false;
        }
    }



    /**
     * 通用的消息投递保障方法
     * @param resourceType 资源类型
     * @param messageQueue 消息队列枚举
     * @param taskData 任务数据
     * @param <T> 任务数据类型
     * @return TaskRetry对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public <T extends TaskData> TaskRetry ensureMessageDelivery(
            TaskResouceTypeEnum resourceType,
            MessageQueueEnum messageQueue,
            T taskData) {
        if (resourceType == null || messageQueue == null || taskData == null) {
            log.error("ensureMessageDelivery fail, resourceType is null or messageQueue is null or taskData is null");
            return null;
        }
        if (!resourceType.getCode().startsWith(TaskResouceTypeEnum.MQ_DELIVERY.getCode())) {
            log.error("ensureMessageDelivery fail, resourceType is not a message delivery task");
            return null;
        }

        return createMessageDeliveryTask(resourceType, messageQueue, taskData);
    }

    /**
     * 创建消息投递任务的公共方法
     * @param resourceType 资源类型
     * @param taskData 任务数据
     * @param messageQueue 消息队列枚举
     * @return TaskRetry对象
     */
    private <T extends TaskData> TaskRetry createMessageDeliveryTask(
            TaskResouceTypeEnum resourceType,
            MessageQueueEnum messageQueue,
            T taskData) {
        
        TaskRetry taskRetry = new TaskRetry();
        Date now = ZonedDateUtil.now();

        String taskRetryId = orderGateway.createNewTaskRetryId(taskData.getUserId());
        
        // 设置基本属性
        taskRetry.setTaskRetryId(taskRetryId);
        taskRetry.setResourceId(taskData.getResourceId());
        taskRetry.setResourceType(resourceType);
        taskRetry.setTryCount(0);
        taskRetry.setStatus(TaskRetryStatusEnum.INIT);
        taskRetry.setGmtCreate(now);
        taskRetry.setGmtModified(now);
        taskRetry.setIsDeleted(DeletedEnum.NO);

        RetryType retryType = retryStrategyDomainService.getRetryType(resourceType);
        String exchangeName;
        String routingKey;
        LocalDateTime nextRetryTime;
        if (retryType == RetryType.DELAY_QUEUE) {
            exchangeName = queueConfigGateway.getDelayExchange(messageQueue);
            routingKey = queueConfigGateway.getDelayRoutingKey(messageQueue);
            // 如果是延迟队列，直接设置下次重试时间为当前时间+queryStartDays天
            nextRetryTime = LocalDateTime.now().plusDays(queryStartDays);
        } else {
            exchangeName = queueConfigGateway.getExchange(messageQueue);
            routingKey = queueConfigGateway.getRoutingKey(messageQueue);
            // 使用重试策略服务计算下次重试时间
            nextRetryTime = retryStrategyDomainService.calculateNextRetryTime(resourceType, 0);
        }
        taskRetry.setNextRetry(nextRetryTime != null ? Date.from(nextRetryTime.atZone(ZoneId.systemDefault()).toInstant()) : null);
        taskRetry.setMaxTryCount(retryStrategyDomainService.getMaxRetryCount(resourceType));
        
        // 创建任务配置
        TaskConfig taskConfig = taskConfigFactory.createMqConfig(exchangeName, routingKey, retryType);
        taskRetry.setTaskConfig(taskConfig);
        taskRetry.setTaskData(taskData);
        
        // 保存任务重试记录
        boolean success = taskRetryGateway.createTaskRetry(taskRetry);
        if (!success) {
            log.error("创建消息投递任务失败, resourceType={}, messageQueue={}, orderId={}", 
                resourceType.getDescription(), messageQueue.name(), taskData.getResourceId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
        return taskRetry;
    }

    @Override
    public int retryMessages() {
        int processedCount = 0;
        try {
            // 计算查询数据开始时间（默认30天前）
            Date startTime = Date.from(LocalDateTime.now().minusDays(queryStartDays)
                    .atZone(ZoneId.systemDefault()).toInstant());
            
            List<TaskRetry> failedTasks;
            // 使用while循环，基于创建时间分页查询，避免死循环
            while ((failedTasks = taskRetryGateway.listFailedTasksForRetry(batchSize, startTime)) != null && !failedTasks.isEmpty()) {
                for (TaskRetry taskRetry : failedTasks) {
                    String retryStrategyDescription = retryStrategyDomainService.getRetryStrategyDescription(taskRetry.getResourceType(), taskRetry.getMaxTryCount());
                    log.info("taskRetryId: {}, {}", taskRetry.getTaskRetryId(), retryStrategyDescription);
                    try {
                        // 使用重试策略服务检查是否达到最大重试次数
                        if (!taskRetry.canRetryForScheduledTask()) {
                            // 标记为失败
                            taskRetry.setStatus(TaskRetryStatusEnum.FAILED);
                            taskRetry.setGmtModified(ZonedDateUtil.now());
                            log.info("Task retry reached max count, marked as failed: {}, strategy: {}", 
                                    taskRetry.getTaskRetryId(), retryStrategyDescription);
                            continue;
                        }
                        // 解析MQ配置
                        MqTaskConfig taskConfig = (MqTaskConfig) taskRetry.getTaskConfig();
                        if (taskConfig == null) {
                            log.error("Invalid MqConfig for taskRetryId: {}", taskRetry.getTaskRetryId());
                            continue;
                        }
                        // 重新发送消息
                        messageSenderGateway.sendMessage(
                            taskRetry.getTaskRetryId(),
                            taskRetry.getTaskDataString(),
                            taskConfig.getExchange(),
                            taskConfig.getRoutingKey(),
                            null
                        );

                        // 增加重试次数
                        taskRetry.setTryCount(taskRetry.getTryCount() + 1);
                        // 使用重试策略服务计算下次重试时间
                        LocalDateTime nextRetryTime = retryStrategyDomainService.calculateNextRetryTime(taskRetry.getResourceType(), taskRetry.getTryCount());
                        taskRetry.setNextRetry(nextRetryTime != null ? Date.from(nextRetryTime.atZone(ZoneId.systemDefault()).toInstant()) : null);
                        taskRetry.setGmtModified(ZonedDateUtil.now());
                        
                        log.info("Updated retry time for task: {}, strategy: {}", 
                                taskRetry.getTaskRetryId(), retryStrategyDescription);
                        
                        // 更新重试次数和下次重试时间到数据库
                        boolean updateTaskRetryCount = taskRetryGateway.incrTaskRetryCount(taskRetry);
                        if (!updateTaskRetryCount) {
                            log.error("Failed to update task retry count for task: {}", taskRetry.getTaskRetryId());
                        }
                        // 如果是消息投递，则只要发送成功则状态改为FINISH
                        if (taskRetry.getResourceType().getCode().startsWith(TaskResouceTypeEnum.MQ_DELIVERY.getCode())) {
                            boolean finishTaskRetry = taskRetryGateway.finishTaskRetry(taskRetry.getTaskRetryId());
                            if (!finishTaskRetry) {
                                log.error("Failed to finish task retry for task: {}", taskRetry.getTaskRetryId());
                            }
                        }
                        // 更新重试数量
                        processedCount++;
                        log.info("Successfully retried message: {}, strategy: {}", taskRetry.getTaskRetryId(), retryStrategyDescription);
                    } catch (Exception e) {
                        log.error("Failed to retry message: {}", taskRetry.getTaskRetryId(), e);
                        if (taskRetry.getResourceType().getCode().startsWith(TaskResouceTypeEnum.MQ_DELIVERY.getCode())) {
                            // 标记为失败
                            markExceededRetryTaskAsFailedById(taskRetry.getTaskRetryId(), TaskData.class);
                        }
                    }
                }
                
                // 更新startTime为当前批次最后一条数据的创建时间，实现基于创建时间的分页查询
                TaskRetry lastTask = failedTasks.get(failedTasks.size() - 1);
                startTime = lastTask.getNextRetry();
                
                log.info("Processed batch of {} failed messages, next startTime: {}", failedTasks.size(), DateUtil.formatDateTime(startTime));
            }
            log.info("Retry failed messages completed, total processed count: {}", processedCount);
        } catch (Exception e) {
            log.error("Error occurred while retrying failed messages", e);
        }
        return processedCount;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public <D extends TaskData> boolean markExceededRetryTaskAsFailedById(String messageId, Class<D> taskDataClass) {
        if (StringUtils.isBlank(messageId)) {
            log.error("markExceededRetryTaskAsFailedById fail, messageId is null");
            return false;
        }
        try {
            // 1. 查询任务重试记录
            TaskRetry taskRetry = taskRetryGateway.getTaskRetryByIdForUpdate(messageId, MqTaskConfig.class, taskDataClass);
            if (taskRetry == null) {
                log.error("TaskRetry not found for messageId: {}", messageId);
                return false;
            }
            
            // 2. 检查重试次数是否大于等于最大重试次数且状态为INIT
            if (taskRetry.getTryCount() >= taskRetry.getMaxTryCount() && 
                taskRetry.getStatus() == TaskRetryStatusEnum.INIT) {
                
                // 3. 标记为失败状态
                taskRetry.setStatus(TaskRetryStatusEnum.FAILED);
                taskRetry.setGmtModified(ZonedDateUtil.now());
                
                // 4. 使用专门的updateTaskRetryStatus方法更新状态
                boolean success = taskRetryGateway.updateTaskRetryStatus(taskRetry.getTaskRetryId(),
                        TaskRetryStatusEnum.INIT,
                        TaskRetryStatusEnum.FAILED);
                if (success) {
                    log.info("Marked exceeded retry task as failed by messageId: {}, tryCount: {}, maxTryCount: {}", 
                            messageId, taskRetry.getTryCount(), taskRetry.getMaxTryCount());
                    return true;
                } else {
                    log.error("Failed to update task retry status for messageId: {}", messageId);
                    return false;
                }
            } else {
                log.info("Task does not meet criteria for marking as failed, messageId: {}, tryCount: {}, maxTryCount: {}, status: {}", 
                        messageId, taskRetry.getTryCount(), taskRetry.getMaxTryCount(), taskRetry.getStatus());
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to mark exceeded retry task as failed for messageId: {}", messageId, e);
            return false;
        }
    }

}