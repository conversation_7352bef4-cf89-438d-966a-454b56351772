package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;

/**
 * 模板状态枚举
 */
@Getter
public enum TntInstIdEnum {

    /**
     * MCOIN租户
     */
    MCOIN("MCOIN", "MCOIN租户");

    private String value;
    private String desc;

    TntInstIdEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据value获取枚举
     * @param value 状态代码
     * @return 对应的枚举值
     */
    public static TntInstIdEnum getByValue(String value) {
        for (TntInstIdEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据value获取描述
     * @param value 状态代码
     * @return 对应的显示名称
     */
    public static String getDescByValue(String value) {
        TntInstIdEnum status = getByValue(value);
        return status != null ? status.getDesc() : null;
    }

}
