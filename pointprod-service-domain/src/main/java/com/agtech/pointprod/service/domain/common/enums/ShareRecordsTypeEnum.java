package com.agtech.pointprod.service.domain.common.enums;

import com.agtech.pointprod.service.domain.exception.PointProdBizException;

/**
 * 分享记录类型枚举
 */
public enum ShareRecordsTypeEnum {
    
    /**
     * 分享
     */
    SEND("SEND", "发送"),
    /**
     * 接受
     */
    ACCEPT("ACCEPT", "接受");
    
    /**
     * 枚举值
     */
    private final String value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    ShareRecordsTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
    
    public String getValue() {
        return value;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据值获取枚举
     * @param value 枚举值
     * @return 枚举对象
     */
    public static ShareRecordsTypeEnum fromValue(String value) {
        for (ShareRecordsTypeEnum type : ShareRecordsTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
    }
} 