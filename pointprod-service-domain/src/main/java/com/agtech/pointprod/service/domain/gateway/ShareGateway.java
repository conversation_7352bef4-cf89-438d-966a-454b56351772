package com.agtech.pointprod.service.domain.gateway;

import com.agtech.pointprod.service.domain.model.share.ShareRecord;

/**
 * 分享领域网关
 */
public interface ShareGateway {
    
    /**
     * 根据业务唯一键查找分享记录
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @param channelType 渠道类型
     * @param userId 用户ID
     * @return 分享记录
     */
    ShareRecord findByUniqueBusinessKey(String contentId, String contentType, String recordType, String channelType, String userId);
    
    /**
     * 根据业务唯一键查找分享记录（加锁）
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @param channelType 渠道类型
     * @param userId 用户ID
     * @return 分享记录
     */
    ShareRecord findByUniqueBusinessKeyForUpdate(String contentId, String contentType, String recordType, String channelType, String userId);
    
    /**
     * 保存分享记录
     * @param shareRecord 分享记录
     */
    void saveShareRecord(ShareRecord shareRecord);
    
    /**
     * 更新分享记录
     * @param shareRecord 分享记录
     */
    void updateShareRecord(ShareRecord shareRecord);
} 