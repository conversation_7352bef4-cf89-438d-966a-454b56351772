package com.agtech.pointprod.service.domain.common.enums;

import com.agtech.pointprod.order.service.domain.common.constants.LangConstants;

import lombok.Getter;

/**
 * 分享过期消息枚举
 */
public enum ShareExpiredMsgEnum {
    
    SHARE_EXPIRED_MSG("SHARE_EXPIRED_MSG", "該分享鏈接已過期，無法查看相關信息", "This share link has expired and related information cannot be viewed");
    
    @Getter
    private String code;
    private String message;
    @Getter
    private String enMessage;
    
    ShareExpiredMsgEnum(String code, String message, String enMessage) {
        this.code = code;
        this.message = message;
        this.enMessage = enMessage;
    }
    
    public String getMessage() {
        return getMessage(LangConstants.ZH_MO);
    }
    
    public String getMessage(String lang) {
        if (LangConstants.EN_GB.equals(lang)) {
            return enMessage;
        } else {
            return message;
        }
    }
} 