package com.agtech.pointprod.service.domain.model.share;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.domain.common.enums.NumbersEnum;

/**
 * 分享有效期值对象
 */
public class SharePeriod {
    private final Date shareTime;
    private final Date expireAt;
    
    private SharePeriod(Date shareTime, Date expireAt) {
        this.shareTime = shareTime;
        this.expireAt = expireAt;
    }
    
    /**
     * 创建一个有效期对象
     * @param shareTime 分享时间
     * @param expireAt 过期时间
     * @return 有效期对象
     */
    public static SharePeriod of(Date shareTime, Date expireAt) {
        if (shareTime == null) {
            throw new IllegalArgumentException("分享时间不能为空");
        }
        return new SharePeriod(shareTime, expireAt);
    }
    
    /**
     * 创建默认有效期（当前时间起3个月）
     * @return 有效期对象
     */
    public static SharePeriod createDefault() {
        Date now = ZonedDateUtil.now();
        Date expireAt = ZonedDateUtil.add(now, Calendar.MONTH, NumbersEnum.THREE.getIntValue());
        return new SharePeriod(now, expireAt);
    }
    
    /**
     * 延长有效期
     * @param newExpireAt 新的过期时间
     * @return 新的有效期对象
     */
    public SharePeriod extend(Date newExpireAt) {
        return new SharePeriod(this.shareTime, newExpireAt);
    }
    
    /**
     * 检查是否已过期
     * @param currentTime 当前时间
     * @return 是否已过期
     */
    public boolean isExpired(Date currentTime) {
        return expireAt != null && currentTime.after(expireAt);
    }
    
    public Date getShareTime() {
        return shareTime;
    }
    
    public Date getExpireAt() {
        return expireAt;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) {
        	return true;
        } 
        if (o == null || getClass() != o.getClass()) {
        	return false;
        } 
        SharePeriod that = (SharePeriod) o;
        return Objects.equals(shareTime, that.shareTime) && 
               Objects.equals(expireAt, that.expireAt);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(shareTime, expireAt);
    }
} 