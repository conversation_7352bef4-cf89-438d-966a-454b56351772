package com.agtech.pointprod.service.domain.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 转赠记录领域模型
 */
@Getter
@Setter
public class TransferRecord {
    
    /**
     * 资金订单ID
     */
    private String fundOrderId;
    
    /**
     * 资金流向类型 (PAY:转出, ACCEPT:收到)
     */
    private String userOrderRole;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 付款订单信息列表
     */
    private List<PayOrderInfo> payOrderInfoList;
    
    /**
     * 收款订单信息列表
     */
    private List<PayOrderInfo> acceptOrderInfoList;
    
    /**
     * 支付时间
     */
    private Date paidTime;
    
    /**
     * 资金金额
     */
    private BigDecimal fundAmount;
    
    /**
     * 资金订单状态
     */
    private String fundOrderStatus;
    
    /**
     * 留言
     */
    private String msg;
    
    /**
     * 创建时间
     */
    private Date gmtCreate;
    
    /**
     * 支付订单信息
     */
    @Getter
    @Setter
    public static class PayOrderInfo {
        /**
         * 用户昵称
         */
        private String nickName;
        
        /**
         * 用户头像URL
         */
        private String headImg;
        
        /**
         * 用户电话
         */
        private String phone;
        
        /**
         * 区号
         */
        private String areaCode;
        
        /**
         * 用户ID
         */
        private String userId;
    }
} 