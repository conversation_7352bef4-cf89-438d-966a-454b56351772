package com.agtech.pointprod.service.domain.common.enums;

/**
 * 序列配置
 *
 * <AUTHOR>
 * @version $Id: SequenceCodeEnum.java, v 0.1 2024年7月11日 10:46:35 仲其刚 Exp $
 */
public enum SequenceCodeEnum {
    /**
     * 协议相关表
     */
    CONTRACT("contract", "401", "条款表"),
    CONTRACT_CONFIRM("contract_confirm", "402", "条款同意表"),

    /**
     * 分享相关表
     */
    SHARE_RECORD("share_record", "501", "分享记录表"),
    SHARE_CODE("share_code", "504", "分享码序列号"),

    /**
     * 通知相关表
     */
    NOTIFICATION_RECORD("notification_record", "502", "通知记录表"),
    MPAY_PUSH_TASK("mpay_push_task", "503", "MPay推送任务表"),

    ORDER_TOKEN("order_token", "201", "訂單token表"),
    ORDER_TOKEN_VALUE("order_token_value", "202", "訂單token表中token值"),
    FUND_UNIQUE("fund_unique", "203", "冪等表"),
    FUND_ORDER("fund_order", "204", "訂單表"),
    FUND_ORDER_ENV("fund_order_env", "205", "訂單環境信息表"),
    FUND_PAY("fund_pay", "206", "支付表"),
    TASK_RETRY("task_retry", "207", "重试表"),
    FUND_FLUX("fund_flux", "208", "资金流水表"),
    /**
     * 轉贈用戶相關
     */
    BLACK_LIST("black_list", "242", "黑名單表"),
    TEMPLATE("template", "243", "留言模板"),
    TRANSFER_FUND_ACCUMULATION("transfer_fund_accumulation", "244", "转出获赠累计表"),
    TRANSFER_RELATION("transfer_relation", "245", "转赠关系表"),

    TRANSFER_RULE("transfer_rule", "246", "轉贈規則"),


    //<<<<<<<<<<<<<<限额相关>>>>>>>>>>>>>>>>>>

    CUMULATE_RULE("limit_cumulate_rule", "601", "限額規則"),

    CUMULATE_ACCOUNT("cumulate_account", "603", "限额累积表"),

    CUMULATE_ACCOUNT_LOG("cumulate_account_log", "604", "限额累积日志表"),

    CUMULATE_TASK("cumulate_task", "605", "限额累积任务表"),

    CUMULATE_CONFIG("cumulate_config", "606", "限额配置表"),

    ;


    private final String bizName;
    private final String bizCode;
    private final String description;

    SequenceCodeEnum(String bizName, String bizCode, String description) {
        this.bizCode = bizCode;
        this.description = description;
        this.bizName = bizName;
    }

    public String getBizCode() {
        return bizCode;
    }


    public String getDescription() {
        return description;
    }


    public String getBizName() {
        return bizName;
    }


    public static SequenceCodeEnum getByBizName(String bizCode) {
        for (SequenceCodeEnum sequenceCodeEnum : SequenceCodeEnum.values()) {
            if (sequenceCodeEnum.getBizName().equals(bizCode)) {
                return sequenceCodeEnum;
            }
        }
        return null;
    }

}
