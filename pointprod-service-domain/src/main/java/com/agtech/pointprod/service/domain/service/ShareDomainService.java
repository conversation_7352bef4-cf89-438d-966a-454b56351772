package com.agtech.pointprod.service.domain.service;

import java.util.Date;
import java.util.List;

import com.agtech.pointprod.service.domain.model.UserId;
import com.agtech.pointprod.service.domain.model.share.ContentReference;
import com.agtech.pointprod.service.domain.model.share.ShareCode;
import com.agtech.pointprod.service.domain.model.share.ShareRecord;
import com.agtech.pointprod.service.domain.model.share.ShareUrl;

/**
 * 分享领域服务
 */
public interface ShareDomainService {
    
    /**
     * 生成分享码
     * @param sequenceNumber 序列号
     * @return 分享码
     */
    ShareCode generateShareCode(long sequenceNumber);
    
    /**
     * 构建分享链接
     * @param shareCode 分享码
     * @return 分享链接
     */
    ShareUrl buildShareUrl(ShareCode shareCode);
    
    /**
     * 创建分享记录对
     * @param contentRef 内容引用
     * @param channelType 渠道类型
     * @param payerUserId 付款人ID
     * @param payeeUserId 收款人ID
     * @param sendRecordId 发送方记录ID
     * @param acceptRecordId 接收方记录ID
     * @param expireAt 过期时间
     * @param shareCode 分享码
     * @param shareUrl 分享链接
     * @return 分享记录列表
     */
    List<ShareRecord> createShareRecordPair(
            ContentReference contentRef, 
            String channelType, 
            UserId payerUserId, 
            UserId payeeUserId,
            String sendRecordId,
            String acceptRecordId,
            Date expireAt,
            ShareCode shareCode,
            ShareUrl shareUrl);
} 