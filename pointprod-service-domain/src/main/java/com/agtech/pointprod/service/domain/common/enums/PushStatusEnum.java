package com.agtech.pointprod.service.domain.common.enums;

/**
 * Push状态枚举
 */
public enum PushStatusEnum {
    
    /**
     * 未发送
     */
    INIT("INIT", "未发送"),
    
    /**
     * 已发送
     */
    SUCCESS("SUCCESS", "已发送"),
    
    /**
     * 发送失败
     */
    FAILED("FAILED", "发送失败");
    
    private final String value;
    private final String description;
    
    PushStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
    
    public String getValue() {
        return value;
    }
    
    public String getDescription() {
        return description;
    }
} 