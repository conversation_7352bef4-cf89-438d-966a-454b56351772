package com.agtech.pointprod.service.domain.common.enums;

import com.agtech.pointprod.service.domain.exception.PointProdBizException;

/**
 * 资源类型枚举
 */
public enum ResourceTypeEnum {
    
    /**
     * 转移成功
     */
    TRANSFER_SUCCESS("TRANSFER_SUCCESS", "转移成功");
    
    /**
     * 枚举值
     */
    private final String value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    ResourceTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
    
    public String getValue() {
        return value;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据值获取枚举
     * @param value 枚举值
     * @return 枚举对象
     */
    public static ResourceTypeEnum fromValue(String value) {
        for (ResourceTypeEnum type : ResourceTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
    }
} 