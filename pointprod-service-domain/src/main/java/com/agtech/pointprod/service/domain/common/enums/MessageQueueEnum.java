package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;

/**
 * 消息队列类型枚举
 */
@Getter
public enum MessageQueueEnum {


    ORDER_STATUS_CHANGED("order-status-changed"),
    ORDER_STATUS_CREATED("order-status-created"),
    ORDER_PAYMENT_TIMEOUT("order-payment-timeout"),
    ORDER_PAYMENT_SUCCESS_PAYER("order-payment-success-payer"),
    ORDER_PAYMENT_SUCCESS_PAYEE("order-payment-success-payee"),
    ORDER_PAYMENT_SUCCESS_FUND_ACCUMULATION("order-payment-success-fund-accumulation"),
    ORDER_PAYMENT_SUCCESS_TRANSFER_RELATION("order-payment-success-transfer-relation"),
    PAYMENT_RESULT_UNKNOWN_QUERY("payment-result-unknown-query");

    private final String configKey;

    MessageQueueEnum(String configKey) {
        this.configKey = configKey;
    }
}
