package com.agtech.pointprod.service.domain.model.share;

import java.util.Objects;

/**
 * 分享内容值对象
 */
public class ShareContent {
    private final String value;
    
    private ShareContent(String value) {
        this.value = value;
    }
    
    public static ShareContent of(String value) {
        return new ShareContent(value == null ? "" : value);
    }
    
    public static ShareContent empty() {
        return new ShareContent("");
    }
    
    public static ShareContent defaultContent() {
        return new ShareContent("mCoin转赠分享");
    }
    
    public String getValue() {
        return value;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) {
        	return true;
        }
        if (o == null || getClass() != o.getClass()) {
        	return false;
        } 
        ShareContent that = (ShareContent) o;
        return Objects.equals(value, that.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    @Override
    public String toString() {
        return value;
    }
} 