package com.agtech.pointprod.service.domain.gateway;

import com.agtech.pointprod.service.domain.model.MPayUserInfo;

import java.util.List;

public interface MPayUserInfoGateway {

    MPayUserInfo getUserMsg(String custId);

    MPayUserInfo getUserMsg(String areaCode, String phone);

    boolean checkSecurityId(String custId, String orderId, String securityId);

    List<MPayUserInfo> getUserMsgList(List<String> custIds);
}
