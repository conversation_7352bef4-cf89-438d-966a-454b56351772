package com.agtech.pointprod.service.domain.util;

import com.agtech.pointprod.order.service.domain.common.constants.LangConstants;
import com.agtech.pointprod.order.service.domain.common.context.BizContextHolder;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/9/27 14:45
 */
public class MessageUtil {

    private MessageUtil(){}

    /**
     * @param message "api[{0}]{1}"
     * @param params "test.api", "缺少参数信息"
     * @return "api[test.api]缺少参数信息"
     */
    public static String format(String message, Object... params){
        if (params == null || params.length == 0){
            return message;
        }
        for (int i=0;i<params.length;i++){
            if (params[i] instanceof Number){
                params[i] = params[i].toString();
            }
        }
        return MessageFormat.format(message, params);
    }

    /**
     * 根据语言返回对应的文案
     * @param messageMo 繁体
     * @param messageEn 英语
     * @param params 参数
     * @return 根据语言返回对应的文案
     */
    public static String formatByLang(String messageMo, String messageEn, Object... params) {
        if(BizContextHolder.getLang().equalsIgnoreCase(LangConstants.ZH_MO)) {
            return format(messageMo, params);
        }
        return format(messageEn, params);
    }

    public static void main(String[] args) {
        String value = MessageUtil.formatByLang("可用額度: {0}", "available balance: {}", "1000");
        System.out.println(value);

    }
}
