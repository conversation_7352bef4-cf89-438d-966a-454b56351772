package com.agtech.pointprod.service.domain.service;

import com.agtech.pointprod.service.domain.model.TransferRelation;

import java.util.List;

/**
 * 转赠关系领域服务接口
 */
public interface TransferRelationDomainService {
    
    /**
     * 保存转赠关系
     * @param transferRelation 转赠关系
     * @return 保存成功与否
     */
    boolean saveTransferRelation(TransferRelation transferRelation);
    
    /**
     * 根据付款方和收款方查询转赠关系
     * @param actorUserId 付款方ID
     * @param participantUserId 收款方ID
     * @return 转赠关系
     */
    TransferRelation queryByActorAndParticipant(String actorUserId, String participantUserId);
    
    /**
     * 查询用户最近的转赠关系
     * @param userId 用户ID
     * @param recentDay 最近天数
     * @return 转赠关系列表
     */
    List<TransferRelation> queryLatestTransferRelations(String userId, Integer recentDay);
    
    /**
     * 更新转赠关系
     * @param transferRelation 转赠关系
     * @return 更新成功与否
     */
    boolean updateTransferRelation(TransferRelation transferRelation);
    
    /**
     * 保存或更新转赠关系（如果关系已存在则更新，否则新增）
     * @param transferRelation 转赠关系
     * @return 保存或更新成功与否
     */
    boolean saveOrUpdateTransferRelation(TransferRelation transferRelation);
}