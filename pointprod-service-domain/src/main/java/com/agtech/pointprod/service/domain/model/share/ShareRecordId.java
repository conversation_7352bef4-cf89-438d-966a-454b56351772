package com.agtech.pointprod.service.domain.model.share;

import java.util.Objects;

/**
 * 分享记录ID值对象
 */
public class ShareRecordId {
    private final String value;
    
    private ShareRecordId(String value) {
        this.value = value;
    }
    
    public static ShareRecordId of(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("ShareRecordId不能为空");
        }
        return new ShareRecordId(value);
    }
    
    public String getValue() {
        return value;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) {
        	return true;
        }
        if (o == null || getClass() != o.getClass()) {
        	return false;
        } 
        ShareRecordId that = (ShareRecordId) o;
        return Objects.equals(value, that.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    @Override
    public String toString() {
        return value;
    }
} 