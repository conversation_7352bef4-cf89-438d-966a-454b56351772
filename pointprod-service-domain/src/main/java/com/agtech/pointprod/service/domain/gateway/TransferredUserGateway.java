package com.agtech.pointprod.service.domain.gateway;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.facade.dto.req.QueryTransferredUserListReq;
import com.agtech.pointprod.service.facade.dto.rsp.QueryTransferredUserListRsp;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 15:06
 */
public interface TransferredUserGateway {

    PageInfoDTO<QueryTransferredUserListRsp> queryTemplateListByPage(QueryTransferredUserListReq listReq);

    void ableUser(String userId, String operator);

    void disabledUser(String userId, String operator);

}
