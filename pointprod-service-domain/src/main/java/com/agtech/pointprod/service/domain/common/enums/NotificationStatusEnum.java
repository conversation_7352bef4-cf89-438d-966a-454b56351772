package com.agtech.pointprod.service.domain.common.enums;

/**
 * 通知状态枚举
 */
public enum NotificationStatusEnum {
    
    /**
     * 未读
     */
    UNREAD("UNREAD", "未读"),
    
    /**
     * 已读
     */
    READ("READ", "已读");
    
    private final String code;
    private final String desc;
    
    NotificationStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
} 