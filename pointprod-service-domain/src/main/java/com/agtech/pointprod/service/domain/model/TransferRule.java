package com.agtech.pointprod.service.domain.model;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class TransferRule {
    private Long id;
    private String transferRuleId;
    private String levelKey;
    private String optionalInfo;
    private String limitRuleIds;
    private String status;
    private String creator;
    private String modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private Integer isDeleted;
    private List<TransferOptional> transferOptionals;

    public List<TransferOptional> transferOptionals() {
        if (CollectionUtils.isNotEmpty(transferOptionals)) {
            return transferOptionals;
        }
        if (StringUtils.isNotBlank(optionalInfo)) {
            List<Integer> optionalInfos = JSONObject.parseObject(optionalInfo, List.class);
            if (CollectionUtils.isNotEmpty(optionalInfos)) {
                transferOptionals = Lists.newArrayListWithCapacity(optionalInfos.size());
                for (Integer optional : optionalInfos) {
                    TransferOptional transferOptional = new TransferOptional();
                    transferOptional.setValue(optional);
                    transferOptionals.add(transferOptional);
                }
            }
        }
        return transferOptionals;
    }
}
