package com.agtech.pointprod.service.domain.gateway;

/**
 * 消息发送网关接口
 * 定义消息发送的领域接口，由基础设施层实现
 * <AUTHOR>
 */
public interface MessageSenderGateway {
    
    /**
     * 发送MQ消息
     * @param messageId 消息ID
     * @param message 消息体
     * @param exchange 交换机
     * @param routingKey 路由键
     * @param delay 延迟时间(单位:毫秒)
     */
    void sendMessage(String messageId, Object message, String exchange, String routingKey, Integer delay);
    
    /**
     * 发送MQ消息（无延迟）
     * @param messageId 消息ID
     * @param message 消息体
     * @param exchange 交换机
     * @param routingKey 路由键
     */
    default void sendMessage(String messageId, Object message, String exchange, String routingKey) {
        sendMessage(messageId, message, exchange, routingKey, null);
    }
}