package com.agtech.pointprod.service.domain.util;

import com.agtech.common.error.enums.AqcProdResultCode;
import com.agtech.common.result.AqcResultCode;
import com.agtech.common.result.BaseResult;
import com.agtech.common.result.GenericResult;
import com.agtech.common.result.util.ResultFillUtil;
import com.agtech.mpass.common.enums.SystemCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 16:06
 */
public class ResultUtil {

    private ResultUtil(){}

    private static final String SYSTEM_CODE = SystemCodeEnum.POINTPROD_SERVICE.getCode();

    public static String getErrMsg(GenericResult genericResult){
        if(genericResult==null){
            return "";
        }
        if(genericResult.isSuccess() || genericResult.getErrorContext()==null){
            return genericResult.getMsg();
        }
        return genericResult.getErrorContext().fetchCurrentErrorMsg();
    }

    public static void mappingAndFillFailureResult(BaseResult result, PointProdBizException ex){
        ResultFillUtil.mappingAndfillFailureResult(SYSTEM_CODE, ex.getResultCode(),
                ex.getMessage(), result);
        result.setReadableResultCode(ex.getResultCode().toString());
    }

    public static void fillFailureResult(BaseResult result, Throwable throwable){
        ResultFillUtil.fillFailureResult(SYSTEM_CODE, AqcProdResultCode.SYSTEM_ERROR,
                AqcProdResultCode.SYSTEM_ERROR.getResultMsg(), result);
        result.setReadableResultCode(AqcProdResultCode.SYSTEM_ERROR.name());
    }

    public static void fillFailureResult(BaseResult result, AqcResultCode resultCode, String message, Object... msgParams){
        ResultFillUtil.mappingAndfillFailureResult(SYSTEM_CODE, resultCode,
                MessageUtil.format(message, msgParams), result);
        result.setReadableResultCode(resultCode.toString());
    }

}
