package com.agtech.pointprod.service.domain.model;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 任务配置抽象基类
 * 基于DDD重构，支持多种任务类型的配置
 * 使用策略模式和多态设计，支持MQ、HTTP、Database等多种类型
 *
 * <AUTHOR>
 */
@Getter
@Setter
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "configType"
)
@JsonSubTypes({
    @JsonSubTypes.Type(value = MqTaskConfig.class, name = "MQ")
})
@Accessors(chain = true)
public abstract class TaskConfig {
    
    
}
