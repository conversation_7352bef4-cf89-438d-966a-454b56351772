package com.agtech.pointprod.service.domain.model;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * 通知记录领域模型
 */
@Getter
@Setter
public class NotificationRecord {
    
    private String notificationId;
    private String notificationType;
    private String resourceId;
    private String resourceType;
    private Date resourceTime;
    private String taskId;
    private String userId;
    private String title;
    private String content;
    private String extraData;
    private String status;
    private String pushStatus;
    private Date pushTime;
    private Date readTime;
    private Date gmtCreate;
    private Date gmtModified;
    private Integer isDeleted;

} 