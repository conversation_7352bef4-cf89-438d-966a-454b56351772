package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;

public enum ContractConfirmStatusEnum {
    AGREE("AGREE", "同意"), DISAGREE("DISAGREE", "不同意");
    @Getter
    private String value;
    @Getter
    private String desc;

    ContractConfirmStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ContractConfirmStatusEnum get(String value) {
        for (ContractConfirmStatusEnum contractConfirmStatusEnum : values()) {
            if (contractConfirmStatusEnum.value.equals(value)) {
                return contractConfirmStatusEnum;
            }
        }
        return null;
    }
}
