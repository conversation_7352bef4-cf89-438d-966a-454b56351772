package com.agtech.pointprod.service.domain.util;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class DateFormatUtil {

    private static final Locale CHINESE_LOCALE = Locale.CHINESE;
    private static final Locale ENGLISH_LOCALE = Locale.ENGLISH;

    /**
     * 格式化日期为中文格式 (yyyy年MM月dd日)
     */
    public static String formatDateChinese(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日", CHINESE_LOCALE);
        return sdf.format(date);
    }

    /**
     * 格式化日期为英文格式 (15th September 2023)
     */
    public static String formatDateEnglish(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int day = cal.get(Calendar.DAY_OF_MONTH);
        String dayWithSuffix = day + getDayOfMonthSuffix(day);
        SimpleDateFormat sdf = new SimpleDateFormat("MMMM yyyy", ENGLISH_LOCALE);
        return dayWithSuffix + " " + sdf.format(date);
    }


    /**
     * 获取日期的序数后缀
     */
    private static String getDayOfMonthSuffix(int day) {
        if (day >= 11 && day <= 13) {
            return "th";
        }
        switch (day % 10) {
            case 1:  return "st";
            case 2:  return "nd";
            case 3:  return "rd";
            default: return "th";
        }
    }

}
