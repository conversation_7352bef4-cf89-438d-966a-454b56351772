package com.agtech.pointprod.service.domain.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Setter
@Getter
public class Contract {
    private Long id;
    private String contractId;
    private String title;
    private String titleEn;
    private String contentUrl;
    private String contentEnUrl;
    private String contractType;
    private Integer version;
    private Date validTime;
    private String status;
    private Date gmtCreate;
    private Date gmtModified;
    private Integer isDeleted;
    private List<ContractConfirm> contractConfirms;
}
