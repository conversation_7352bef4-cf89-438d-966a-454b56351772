package com.agtech.pointprod.service.domain.service;

import com.agtech.pointprod.service.domain.model.TransferFundAccumulation;

import java.math.BigDecimal;

/**
 * 转出获赠累计领域服务接口
 */
public interface TransferFundAccumulationDomainService {
    
    /**
     * 根据用户ID查询累计记录
     * @param userId 用户ID
     * @return 累计记录
     */
    TransferFundAccumulation queryByUserId(String userId);
    
    /**
     * 初始化用户累计记录
     * @param userId 用户ID
     * @param description 描述
     * @return 累计记录
     */
    TransferFundAccumulation initUserAccumulation(String userId, String description);
    
    /**
     * 累加转出金额和次数
     * @param userId 用户ID
     * @param amount 转出金额
     * @param count 转出次数
     * @return 更新成功与否
     */
    boolean accumulateFundOut(String userId, BigDecimal amount, Integer count);
    
    /**
     * 累加转入金额和次数
     * @param userId 用户ID
     * @param amount 转入金额
     * @param count 转入次数
     * @return 更新成功与否
     */
    boolean accumulateFundIn(String userId, BigDecimal amount, Integer count);
    
    /**
     * 累加转出金额和次数（如果记录不存在则创建）
     * @param userId 用户ID
     * @param amount 转出金额
     * @param count 转出次数
     * @param description 描述
     * @return 更新成功与否
     */
    boolean accumulateFundOutWithInit(String userId, BigDecimal amount, Integer count, String description);
    
    /**
     * 累加转入金额和次数（如果记录不存在则创建）
     * @param userId 用户ID
     * @param amount 转入金额
     * @param count 转入次数
     * @param description 描述
     * @return 更新成功与否
     */
    boolean accumulateFundInWithInit(String userId, BigDecimal amount, Integer count, String description);
}