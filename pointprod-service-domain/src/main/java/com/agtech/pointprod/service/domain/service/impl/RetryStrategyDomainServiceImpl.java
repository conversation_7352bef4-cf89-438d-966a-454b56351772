package com.agtech.pointprod.service.domain.service.impl;

import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.config.RetryStrategyProperties;
import com.agtech.pointprod.service.domain.config.RetryStrategyProperties.RetryType;
import com.agtech.pointprod.service.domain.service.RetryStrategyDomainService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 重试策略领域服务实现类
 * 提供基于TaskResouceTypeEnum的重试策略计算
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RetryStrategyDomainServiceImpl implements RetryStrategyDomainService {
    
    private final RetryStrategyProperties retryStrategyProperties;
    
    @Override
    public LocalDateTime calculateNextRetryTime(TaskResouceTypeEnum taskType, int currentRetryCount) {
        RetryStrategyProperties.RetryConfig config = retryStrategyProperties.getRetryConfig(taskType);
        
        // 检查是否已达到最大重试次数
        if (currentRetryCount > config.getMaxRetryCount()) {
            log.info("任务已达到最大重试次数 - 任务类型: {}, 当前重试次数: {}, 最大重试次数: {}", 
                    taskType.getCode(), currentRetryCount, config.getMaxRetryCount());
            return null;
        }
        
        long delaySeconds = calculateRetryDelay(taskType, currentRetryCount);
        
        log.info("计算下次重试时间 - 任务类型: {}, 当前重试次数: {}, 延迟时间: {}秒", 
                taskType.getCode(), currentRetryCount, delaySeconds);
        
        return LocalDateTime.now().plusSeconds(delaySeconds);
    }
    
    @Override
    public int getMaxRetryCount(TaskResouceTypeEnum taskType) {
        return retryStrategyProperties.getMaxRetryCount(taskType);
    }
    
    @Override
    public boolean isMaxRetryCountReached(TaskResouceTypeEnum taskType, int currentRetryCount) {
        return currentRetryCount > getMaxRetryCount(taskType);
    }
    
    @Override
    public long calculateRetryDelay(TaskResouceTypeEnum taskType, int currentRetryCount) {
        RetryStrategyProperties.RetryConfig config = retryStrategyProperties.getRetryConfig(taskType);
        
        long delaySeconds;
        if (config.isEnableExponentialBackoff()) {
            // 指数退避算法：delay = initial_delay * (backoff_multiplier ^ retry_count)
            // 注意：currentRetryCount从0开始，所以第一次重试使用初始延迟
            delaySeconds = (long) (config.getInitialDelaySeconds() * 
                Math.pow(config.getBackoffMultiplier(), currentRetryCount));
            // 限制最大延迟时间
            delaySeconds = Math.min(delaySeconds, config.getMaxDelaySeconds());
        } else {
            // 固定延迟
            delaySeconds = config.getInitialDelaySeconds();
        }
        
        // 确保延迟时间不小于1秒
        return Math.max(delaySeconds, 1);
    }
    
    @Override
    public boolean canRetry(TaskResouceTypeEnum taskType, int currentRetryCount) {
        return currentRetryCount <= getMaxRetryCount(taskType);
    }
    
    @Override
    public String getRetryStrategyDescription(TaskResouceTypeEnum taskType, int maxRetryCount) {
        RetryStrategyProperties.RetryConfig config = retryStrategyProperties.getRetryConfig(taskType);
        
        StringBuilder description = new StringBuilder();
        description.append("任务类型: ").append(taskType.getDescription())
                  .append(", 最大重试次数: ").append(maxRetryCount)
                  .append(", 初始延迟: ").append(config.getInitialDelaySeconds()).append("秒")
                  .append(", 最大延迟: ").append(config.getMaxDelaySeconds()).append("秒");
        
        if (config.isEnableExponentialBackoff()) {
            description.append(", 指数退避倍数: ").append(config.getBackoffMultiplier());
        } else {
            description.append(", 固定延迟模式");
        }
        
        return description.toString();
    }
    
    /**
     * 获取重试时间序列描述（用于调试和监控）
     * @param taskType 任务类型
     * @return 重试时间序列描述
     */
    public String getRetryTimeSequence(TaskResouceTypeEnum taskType) {
        RetryStrategyProperties.RetryConfig config = retryStrategyProperties.getRetryConfig(taskType);
        StringBuilder sequence = new StringBuilder();
        sequence.append("重试时间序列 - ").append(taskType.getDescription()).append(": ");
        
        for (int i = 0; i < config.getMaxRetryCount(); i++) {
            long delay = calculateRetryDelay(taskType, i);
            sequence.append("第").append(i + 1).append("次: ").append(delay).append("秒后");
            if (i < config.getMaxRetryCount() - 1) {
                sequence.append(", ");
            }
        }
        
        return sequence.toString();
    }

    /**
     * 获取重试类型
     * @param taskType 任务类型枚举
     * @return 重试类型
     */
    @Override
    public RetryType getRetryType(TaskResouceTypeEnum taskType) {
        return retryStrategyProperties.getRetryType(taskType);
    }

}