package com.agtech.pointprod.service.domain.gateway;

import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.McoinTransferInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;

public interface McoinAccountGateway {
    McoinAccount queryMcoinAccount(String userId);

    McoinTransferResult transferPoint(McoinTransferInfo transferInfo,
                                      MPayUserInfo payerUserInfo, MPayUserInfo payeeUserInfo);

    McoinTransferResult queryTransfer(String outOrderId, String payeeCustId);
}
