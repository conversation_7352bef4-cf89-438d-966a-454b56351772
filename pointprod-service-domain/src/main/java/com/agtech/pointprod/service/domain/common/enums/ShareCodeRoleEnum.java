package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;

/**
 * 分享码角色枚举
 */
public enum ShareCodeRoleEnum {
    
    /**
     * 当前用户是该分享码的付款人
     */
    IS_PAYER("IS_PAYER", "当前用户是该分享码的付款人"),
    
    /**
     * 当前用户是该分享码的收款人
     */
    IS_PAYEE("IS_PAYEE", "当前用户是该分享码的收款人"),
    
    /**
     * 分享码是第三方
     */
    IS_THIRD_PARTY("IS_THIRD_PARTY", "分享码是第三方");
    
    /**
     * 枚举值
     */
    @Getter
    private final String value;
    
    /**
     * 枚举描述
     */
    @Getter
    private final String description;
    
    ShareCodeRoleEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
} 