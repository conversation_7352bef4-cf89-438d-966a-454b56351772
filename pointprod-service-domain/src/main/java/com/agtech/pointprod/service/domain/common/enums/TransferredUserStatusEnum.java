package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 14:21
 */
@Getter
public enum TransferredUserStatusEnum {

    ABLE("able", "啟用"),
    DISABLED("disabled", "禁用");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    TransferredUserStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static TransferredUserStatusEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (TransferredUserStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否生效
     */
    public static boolean isAble(String code) {
        return ABLE.getCode().equals(code);
    }


    public static boolean containCode(String code){
        for (TransferredUserStatusEnum status : values()){
            if (status.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }
}
