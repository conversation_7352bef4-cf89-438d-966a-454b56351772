package com.agtech.pointprod.service.domain.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class BlackList {
    /**
     * Remark: 主键ID
     */
    private Long id;

    /**
     * Remark: 业务id
     */
    private String blackListId;

    /**
     * Remark: 实体类型(PERSON-个人/COMPANY-企业)
     */
    private String entityType;

    /**
     * Remark: 实体唯一标识
     */
    private String entityId;

    /**
     * Remark: 加入黑名单原因
     */
    private String reason;

    /**
     * Remark: 账户类型(IP/EMAIL/ID_CARD等)
     */
    private String accountType;

    /**
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date gmtCreate;

    /**
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date gmtModified;

    /**
     * Default value: 0
     */
    private Integer isDeleted;

    private String creator;

    private String modifier;
}