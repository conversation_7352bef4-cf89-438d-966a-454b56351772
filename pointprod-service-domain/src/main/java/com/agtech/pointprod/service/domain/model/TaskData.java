package com.agtech.pointprod.service.domain.model;

import lombok.Getter;
import lombok.Setter;

/**
 * Task data for message processing with retry mechanism
 */
@Getter
@Setter
public class TaskData {
    /**
     * 订单号
     */
    private String resourceId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * Current retry count for the task
     */
    private Integer delayCount;
    
    /**
     * Maximum number of retry attempts allowed
     */
    private Integer delayMax;
    
    /**
     * Delay time in milliseconds between retry attempts
     */
    private Integer delayTime;

}
