package com.agtech.pointprod.service.domain.gateway;

import java.util.List;

import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.model.TaskConfig;
import com.agtech.pointprod.service.domain.model.TaskData;
import com.agtech.pointprod.service.domain.model.TaskRetry;
import com.agtech.pointprod.service.facade.dto.enums.TaskRetryStatusEnum;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 20:31
 */
public interface TaskRetryGateway {
    /**
     * 创建任务重试
     * @param taskRetry 任务重试
     * @return 是否成功
     */
    boolean createTaskRetry(TaskRetry taskRetry);

    /**
     * 更新任务重试状态为FINISH
     * @param taskRetryId 任务重试ID
     * @return 是否成功
     */
    boolean finishTaskRetry(String taskRetryId);


    /**
     * 根据任务重试ID查询任务重试信息
     * @param taskRetryId 任务重试ID
     * @param taskConfigClass 任务配置类型
     * @param taskDataClass 任务数据类型
     * @return 任务重试信息
     */
    <T extends TaskConfig, D extends TaskData> TaskRetry getTaskRetryByIdForUpdate(String taskRetryId, Class<T> taskConfigClass, Class<D> taskDataClass);

    /**
     * 根据任务重试ID查询任务重试信息（不加锁）
     * @param taskRetryId 任务重试ID
     * @param taskConfigClass 任务配置类型
     * @param taskDataClass 任务数据类型
     * @return 任务重试信息
     */
    <T extends TaskConfig, D extends TaskData> TaskRetry getTaskRetryById(String taskRetryId, Class<T> taskConfigClass, Class<D> taskDataClass);

    /**
     * 根据任务重试ID查询任务重试信息
     * @param resourceId 任务重试ID
     * @param resourceType 资源类型
     * @param taskConfigClass 任务配置类型
     * @param taskDataClass 任务数据类型
     * @return 任务重试信息
     */
    <T extends TaskConfig, D extends TaskData> TaskRetry getTaskRetryByResourceIdForUpdate(String resourceId, TaskResouceTypeEnum resourceType, Class<T> taskConfigClass, Class<D> taskDataClass);


    /**
     * 查询未投递成功且未达到重试上限的任务列表（带时间过滤）
     * @param limit 查询数量限制
     * @param startTime 查询数据开始时间，通过next_retry字段过滤
     * @return 任务重试列表
     */
    List<TaskRetry> listFailedTasksForRetry(int limit, java.util.Date startTime);

    /**
     * 更新任务重试次数和下次重试时间
     * @param taskRetry 任务重试对象
     * @return 是否成功
     */
    boolean incrTaskRetryCount(TaskRetry taskRetry);

    /**
     * 更新任务重试状态
     * @param taskRetryId 任务重试ID
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return 是否成功
     */
    boolean updateTaskRetryStatus(String taskRetryId, TaskRetryStatusEnum fromStatus, TaskRetryStatusEnum toStatus);
}
