package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/24 18:02
 */
@Getter
public enum TemplateBizTypeEnum {
    MCOIN_TRANSFER("MCOIN_TRANSFER", "MCOIN积分转账"),
    ;
    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    TemplateBizTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static TemplateBizTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (TemplateBizTypeEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }


    public static boolean containCode(String code){
        for (TemplateBizTypeEnum templateBizTypeEnum : values()){
            if (templateBizTypeEnum.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }

}
