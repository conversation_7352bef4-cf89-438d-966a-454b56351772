package com.agtech.pointprod.service.domain.gateway;

import com.agtech.common.dto.PageInfoDTO;
import com.agtech.pointprod.service.domain.model.Template;
import com.agtech.pointprod.service.facade.dto.req.template.QueryTemplateInfoListReq;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
public interface TemplateGateway {
    PageInfoDTO<Template> queryValidTemplateListByPage(String bizType, Integer pageNo, Integer pageSize);

    PageInfoDTO<Template> queryTemplateListByPage(QueryTemplateInfoListReq queryTemplateInfoListReq);

    String createTemplate(Template template);

    boolean updateTemplate(Template template);

    boolean deleteTemplate(String templateId, String operator);

}
