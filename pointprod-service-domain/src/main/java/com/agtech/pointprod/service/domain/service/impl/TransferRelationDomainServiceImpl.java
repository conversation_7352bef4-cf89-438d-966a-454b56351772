package com.agtech.pointprod.service.domain.service.impl;

import com.agtech.pointprod.service.domain.gateway.TransferRelationGateway;
import com.agtech.pointprod.service.domain.model.TransferRelation;
import com.agtech.pointprod.service.domain.service.TransferRelationDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 转赠关系领域服务实现
 */
@Service
@Slf4j
public class TransferRelationDomainServiceImpl implements TransferRelationDomainService {
    
    @Resource
    private TransferRelationGateway transferRelationGateway;
    
    @Override
    public boolean saveTransferRelation(TransferRelation transferRelation) {
        if (transferRelation == null) {
            log.error("保存转赠关系失败，转赠关系为空");
            return false;
        }
        
        // 参数校验
        if (StringUtils.isBlank(transferRelation.getActorUserId()) || 
            StringUtils.isBlank(transferRelation.getParticipantUserId())) {
            log.error("保存转赠关系失败，付款方或收款方用户ID为空");
            return false;
        }
        
        // 设置创建时间
        Date now = new Date();
        if (transferRelation.getGmtCreate() == null) {
            transferRelation.setGmtCreate(now);
        }
        if (transferRelation.getGmtModified() == null) {
            transferRelation.setGmtModified(now);
        }
        
        // 领域对象不再包含isDeleted字段，该字段在转换器中设置默认值
        
        return transferRelationGateway.saveTransferRelation(transferRelation);
    }
    
    @Override
    public TransferRelation queryByActorAndParticipant(String actorUserId, String participantUserId) {
        if (StringUtils.isBlank(actorUserId) || StringUtils.isBlank(participantUserId)) {
            log.error("查询转赠关系失败，付款方或收款方用户ID为空");
            return null;
        }
        
        return transferRelationGateway.queryByActorAndParticipant(actorUserId, participantUserId);
    }
    
    @Override
    public List<TransferRelation> queryLatestTransferRelations(String userId, Integer recentDay) {
        if (StringUtils.isBlank(userId)) {
            log.error("查询最近转赠关系失败，用户ID为空");
            return null;
        }
        
        if (recentDay == null || recentDay <= 0) {
            recentDay = 30; // 默认查询最近30天
        }
        Date startTime = Date.from(LocalDate.now().minusDays(recentDay).atStartOfDay(ZoneId.systemDefault()).toInstant());
        return transferRelationGateway.queryLatestTransferRelations(userId, startTime);
    }
    
    @Override
    public boolean updateTransferRelation(TransferRelation transferRelation) {
        if (transferRelation == null) {
            log.error("更新转赠关系失败，转赠关系对象为空");
            return false;
        }
        
        // 更新修改时间
        transferRelation.setGmtModified(new Date());
        
        return transferRelationGateway.updateTransferRelation(transferRelation);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateTransferRelation(TransferRelation transferRelation) {
        if (transferRelation == null) {
            log.error("保存或更新转赠关系失败，转赠关系为空");
            return false;
        }
        
        // 参数校验
        if (StringUtils.isBlank(transferRelation.getActorUserId()) || 
            StringUtils.isBlank(transferRelation.getParticipantUserId())) {
            log.error("保存或更新转赠关系失败，付款方或收款方用户ID为空");
            return false;
        }
        
        // 查询是否已存在关系
        TransferRelation existing = queryByActorAndParticipant(
            transferRelation.getActorUserId(), 
            transferRelation.getParticipantUserId());
        
        if (existing != null) {
            // 更新现有关系
            transferRelation.setTransferRelationId(existing.getTransferRelationId());
            transferRelation.setGmtCreate(existing.getGmtCreate());
            return updateTransferRelation(transferRelation);
        } else {
            // 新增关系
            return saveTransferRelation(transferRelation);
        }
    }
}