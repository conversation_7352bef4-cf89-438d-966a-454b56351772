package com.agtech.pointprod.service.domain.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.agtech.pointprod.service.domain.model.UserId;
import com.agtech.pointprod.service.domain.model.share.ContentReference;
import com.agtech.pointprod.service.domain.model.share.ShareCode;
import com.agtech.pointprod.service.domain.model.share.ShareRecord;
import com.agtech.pointprod.service.domain.model.share.ShareUrl;
import com.agtech.pointprod.service.domain.service.ShareDomainService;
import com.agtech.pointprod.service.domain.util.ShareCodeUtil;

/**
 * 分享领域服务实现
 */
@Service
public class ShareDomainServiceImpl implements ShareDomainService {
    
    @Value("${point.share.short.domain:}")
    private String shareShortLinkConfig;
    
    /**
     * 短链路径
     */
    private static final String SHORT_LINK_PATH = "/s/";

    @Override
    public ShareCode generateShareCode(long sequenceNumber) {
        String code = ShareCodeUtil.generateShareCode(sequenceNumber);
        return ShareCode.of(code);
    }

    @Override
    public ShareUrl buildShareUrl(ShareCode shareCode) {
        return ShareUrl.fromShareCode(shareCode, shareShortLinkConfig, SHORT_LINK_PATH);
    }

    @Override
    public List<ShareRecord> createShareRecordPair(
            ContentReference contentRef,
            String channelType,
            UserId payerUserId,
            UserId payeeUserId,
            String sendRecordId,
            String acceptRecordId,
            Date expireAt,
            ShareCode shareCode,
            ShareUrl shareUrl) {
        
        List<ShareRecord> shareRecords = new ArrayList<>();
        
        // 创建发送方记录 - 直接传入所有参数
        ShareRecord sendRecord = ShareRecord.createSendRecord(
                sendRecordId,
                payerUserId.getValue(),
                contentRef,
                channelType,
                shareCode,
                shareUrl,
                expireAt
        );
        
        // 创建接收方记录 - 直接传入所有参数
        ShareRecord acceptRecord = ShareRecord.createAcceptRecord(
                acceptRecordId,
                payeeUserId.getValue(),
                contentRef,
                channelType,
                shareCode,
                shareUrl,
                expireAt
        );
        
        shareRecords.add(sendRecord);
        shareRecords.add(acceptRecord);
        
        return shareRecords;
    }
} 