package com.agtech.pointprod.service.domain.exception;

import com.agtech.common.result.AqcResultCode;
import com.agtech.common.result.ErrorContext;
import com.agtech.common.result.ErrorContextHolder;
import com.agtech.common.result.code.ResultCodeLevel;
import com.agtech.common.result.code.ResultCodeType;
import com.agtech.common.util.assertion.AssertionException;
import com.agtech.common.util.assertion.AssertionResultCode;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 16:00
 */
public class PointProdBizException extends AssertionException {

    private static final long serialVersionUID = 5248139290085249037L;
    private AqcResultCode resultCode;

    public PointProdBizException(AqcResultCode resultCode) {
        super(resultCode.getResultMsg());
        this.resultCode = resultCode;
    }

    public PointProdBizException(AqcResultCode resultCode, String errorMsg) {
        super(errorMsg);
        this.resultCode = resultCode;
    }

    public PointProdBizException(AqcResultCode resultCode, ErrorContext errorContext) {
        super(resultCode.getResultMsg());
        this.resultCode = resultCode;

        if (errorContext != null) {
            ErrorContextHolder.setContext(errorContext);
        }
    }

    public PointProdBizException(AqcResultCode resultCode, ErrorContext errorContext, String errorMsg) {
        super(errorMsg);
        this.resultCode = resultCode;

        if (errorContext != null) {
            ErrorContextHolder.setContext(errorContext);
        }
    }


    public AqcResultCode getResultCode() {
        return this.resultCode;
    }

    @Override
    public void setResultCode(final AssertionResultCode resultCode) {
        if ((resultCode instanceof AqcResultCode)) {
            this.resultCode = ((AqcResultCode) resultCode);
            return;
        }

        this.resultCode = new AqcResultCode() {
            @Override
            public String getResultCode() {
                return resultCode.getResultCode();
            }
            @Override
            public String getResultMsg() {
                return resultCode.getResultMsg();
            }
            @Override
            public String getErrorLevel() {
                if (AqcResultCode.class.isInstance(resultCode)) {
                    return ((AqcResultCode) resultCode).getErrorLevel();
                }

                return ResultCodeLevel.WARN;
            }
            @Override
            public String getErrorType() {
                if (AqcResultCode.class.isInstance(resultCode)) {
                    return ((AqcResultCode) resultCode).getErrorType();
                }

                return ResultCodeType.BIZ_ERROR;
            }
        };
    }

}
