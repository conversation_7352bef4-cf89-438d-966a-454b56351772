package com.agtech.pointprod.service.domain.gateway;

import com.agtech.pointprod.service.domain.model.Contract;
import com.agtech.pointprod.service.domain.model.ContractConfirm;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 20:31
 */
public interface ContractGateway {
    Contract queryLatestContractByBizType(String bizType);

    ContractConfirm queryContractConfirmByUserIdAndContractId(String userId, String contractId);

    boolean updateContractConfirmForUser(String userId, String contractId, String status);

    boolean saveContractConfirm(String userId, String contractId, String status);

    Contract queryContractByContractId(String contractId);
    
    /**
     * Save contract
     * 
     * @param contract Contract to save
     * @return true if successful, false otherwise
     */
    boolean saveContract(Contract contract);
}
