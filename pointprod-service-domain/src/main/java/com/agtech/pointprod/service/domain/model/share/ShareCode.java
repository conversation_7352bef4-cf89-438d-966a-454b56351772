package com.agtech.pointprod.service.domain.model.share;

import java.util.Objects;

/**
 * 分享码值对象
 */
public class ShareCode {
    private final String value;
    
    private ShareCode(String value) {
        this.value = value;
    }
    
    public static ShareCode of(String value) {
        return new ShareCode(value == null ? "" : value);
    }
    
    public static ShareCode empty() {
        return new ShareCode("");
    }
    
    public boolean isEmpty() {
        return value == null || value.trim().isEmpty();
    }
    
    public String getValue() {
        return value;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) {
        	return true;
        }
        if (o == null || getClass() != o.getClass()) {
        	return false;
        }
        ShareCode shareCode = (ShareCode) o;
        return Objects.equals(value, shareCode.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    @Override
    public String toString() {
        return value;
    }
} 