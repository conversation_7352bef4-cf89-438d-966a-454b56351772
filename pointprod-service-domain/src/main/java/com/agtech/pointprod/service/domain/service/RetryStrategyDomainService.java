package com.agtech.pointprod.service.domain.service;

import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.config.RetryStrategyProperties.RetryType;

import java.time.LocalDateTime;

/**
 * 重试策略领域服务接口
 * 提供基于TaskResouceTypeEnum的重试策略相关业务逻辑
 */
public interface RetryStrategyDomainService {
    
    /**
     * 根据任务类型和当前重试次数计算下次重试时间
     * @param taskType 任务类型枚举
     * @param currentRetryCount 当前重试次数
     * @return 下次重试时间，如果已达到最大重试次数则返回null
     */
    LocalDateTime calculateNextRetryTime(TaskResouceTypeEnum taskType, int currentRetryCount);
    
    /**
     * 根据任务类型获取最大重试次数
     * @param taskType 任务类型枚举
     * @return 最大重试次数
     */
    int getMaxRetryCount(TaskResouceTypeEnum taskType);
    
    /**
     * 检查是否已达到最大重试次数
     * @param taskType 任务类型枚举
     * @param currentRetryCount 当前重试次数
     * @return 是否已达到最大重试次数
     */
    boolean isMaxRetryCountReached(TaskResouceTypeEnum taskType, int currentRetryCount);
    
    /**
     * 计算重试延迟时间（秒）
     * @param taskType 任务类型枚举
     * @param currentRetryCount 当前重试次数
     * @return 延迟时间（秒）
     */
    long calculateRetryDelay(TaskResouceTypeEnum taskType, int currentRetryCount);
    
    /**
     * 检查任务是否可以重试
     * @param taskType 任务类型枚举
     * @param currentRetryCount 当前重试次数
     * @return 是否可以重试
     */
    boolean canRetry(TaskResouceTypeEnum taskType, int currentRetryCount);
    
    /**
     * 获取重试策略描述信息
     * @param taskType 任务类型枚举
     * @param maxRetryCount 最大重试次数
     * @return 重试策略描述
     */
    String getRetryStrategyDescription(TaskResouceTypeEnum taskType, int maxRetryCount);

    /**
     * 获取重试类型
     * @param taskType 任务类型枚举
     * @return 重试类型
     */
    RetryType getRetryType(TaskResouceTypeEnum taskType);

}