package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 模板状态枚举
 */
@Getter
public enum TemplateStatusEnum {

    ABLE("able", "启用, 且未到超过结束时间"),
    DISABLED("disabled", "禁用"),
    EXPIRED("expired", "过期，启用且超过结束时间"),
    ;

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    TemplateStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static TemplateStatusEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (TemplateStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否生效
     */
    public static boolean isAble(String code) {
        return ABLE.getCode().equals(code);
    }


    public static boolean containCode(String code){
        for (TemplateStatusEnum templateStatusEnum : values()){
            if (templateStatusEnum.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }

    public static boolean containRealCode(String code){
        return TemplateStatusEnum.ABLE.getCode().equals(code) || TemplateStatusEnum.DISABLED.getCode().equals(code);
    }
}
