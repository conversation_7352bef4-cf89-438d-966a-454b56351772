package com.agtech.pointprod.service.domain.common.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 重试任务类型枚举
 * 通过前缀区分不同的任务类型，合并具体的业务资源类型
 * 前缀规则：MQ_xxx表示MQ类型任务：
 *          MQ_DELIVERY_xxx 表示消息投递
 *          MQ_CONSUME_xxx 表示消息消费
 *
 * HTTP_xxx表示HTTP类型任务
 * 
 * <AUTHOR>
 * @version RetryTaskTypeEnum.java, v0.1 2025/1/20 重构 zhongqigang Exp $
 */
@Getter
public enum TaskResouceTypeEnum {

    // ========== MQ类型任务 ==========

    /**
     * MQ投递消息
     */
    MQ_DELIVERY("MQ_DELIVERY", "MQ投递消息"),

    /**
     * MQ投递订单状态推进消息
     */
    MQ_DELIVERY_ORDER_STATUS_CHANGED("MQ_DELIVERY_ORDER_STATUS_CHANGED", "MQ投递訂單狀態推進消息"),

    /**
     * MQ投递订单延迟关单消息
     */
    MQ_DELIVERY_ORDER_PAYMENT_TIMEOUT("MQ_DELIVERY_ORDER_PAYMENT_TIMEOUT", "MQ投递訂單支付超時消息"),

    /**
     * MQ投递支付结果未知查询消息
     */
    MQ_DELIVERY_PAYMENT_RESULT_UNKNOWN_QUERY("MQ_DELIVERY_PAYMENT_RESULT_UNKNOWN_QUERY", "MQ投递支付结果未知查询消息"),

    /**
     * MQ消费消息
     */
    MQ_CONSUME("MQ_CONSUME", "MQ消费消息"),

    /**
     * MQ消费订单资金累加消息
     */
    MQ_CONSUME_ORDER_PAYMENT_SUCCESS_FUND_ACCUMULATION("MQ_CONSUME_ORDER_PAYMENT_SUCCESS_FUND_ACCUMULATION", "MQ消费訂單支付成功资金累加消息"),

    /**
     * MQ消费订单转赠关系消息
     */
    MQ_CONSUME_ORDER_PAYMENT_SUCCESS_TRANSFER_RELATION("MQ_CONSUME_ORDER_PAYMENT_SUCCESS_TRANSFER_RELATION", "MQ消费訂單支付成功转赠关系消息"),

    /**
     * MQ消费订单状态创建消息
     */
    MQ_CONSUME_ORDER_STATUS_CREATED("MQ_CONSUME_ORDER_STATUS_CREATED", "MQ消费訂單状态创建消息"),

    /**
     * MQ消费订单支付超时消息
     */
    MQ_CONSUME_ORDER_PAYMENT_TIMEOUT("MQ_CONSUME_ORDER_PAYMENT_TIMEOUT", "MQ消费訂單支付超时消息"),

    /**
     * MQ消费支付结果未知查询消息
     */
    MQ_CONSUME_PAYMENT_RESULT_UNKNOWN_QUERY("MQ_CONSUME_PAYMENT_RESULT_UNKNOWN_QUERY", "MQ消费支付结果未知查询消息")
    ;


    /**
     * 任务类型代码
     */
    private final String code;

    /**
     * 任务类型描述
     */
    private final String description;

    TaskResouceTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     * @param code 任务类型代码
     * @return 枚举值
     */
    public static TaskResouceTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (TaskResouceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效的任务类型代码
     * @param code 任务类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

    /**
     * 获取任务类型前缀
     * @return 任务类型前缀（MQ、HTTP、DATABASE、FILE、CACHE）
     */
    public String getTaskTypePrefix() {
        String code = this.getCode();
        if (code.contains("_")) {
            return code.substring(0, code.indexOf("_"));
        }
        return code;
    }

    /**
     * 检查是否为MQ类型任务
     * @return 是否为MQ类型
     */
    public boolean isMqType() {
        return "MQ".equals(getTaskTypePrefix());
    }

    /**
     * 检查是否为HTTP类型任务
     * @return 是否为HTTP类型
     */
    public boolean isHttpType() {
        return "HTTP".equals(getTaskTypePrefix());
    }

    /**
     * 根据任务类型前缀获取所有相关枚举
     * @param prefix 任务类型前缀
     * @return 相关枚举列表
     */
    public static List<TaskResouceTypeEnum> getByPrefix(String prefix) {
        if (prefix == null || prefix.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        List<TaskResouceTypeEnum> result = new ArrayList<>();
        for (TaskResouceTypeEnum type : values()) {
            if (prefix.equals(type.getTaskTypePrefix())) {
                result.add(type);
            }
        }
        return result;
    }

    /**
     * 获取所有MQ类型的枚举
     * @return MQ类型枚举列表
     */
    public static List<TaskResouceTypeEnum> getMqTypes() {
        return getByPrefix("MQ");
    }

    /**
     * 获取所有HTTP类型的枚举
     * @return HTTP类型枚举列表
     */
    public static List<TaskResouceTypeEnum> getHttpTypes() {
        return getByPrefix("HTTP");
    }

    /**
     * 获取业务子类型（去除前缀后的部分）
     * @return 业务子类型，如果没有子类型则返回GENERAL
     */
    public String getBusinessSubType() {
        String code = this.getCode();
        if (code.contains("_")) {
            String subType = code.substring(code.indexOf("_") + 1);
            return subType.isEmpty() ? "GENERAL" : subType;
        }
        return "GENERAL";
    }
}