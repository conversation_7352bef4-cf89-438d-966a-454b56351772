package com.agtech.pointprod.service.domain.common.enums;

import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 20:31
 */
@Getter
public enum ContractTypeEnum {

    /**
     * 转赠积分
     */
    MCOIN_TRANSFER("MCOIN_TRANSFER", "转赠积分");

    /**
     * 枚举值
     */
    private final String value;

    /**
     * 枚举描述
     */
    private final String description;

    ContractTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
    
    /**
     * 根据值获取枚举
     * @param value 枚举值
     * @return 枚举对象
     */
    public static ContractTypeEnum fromValue(String value) {
        for (ContractTypeEnum type : ContractTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
    }
} 