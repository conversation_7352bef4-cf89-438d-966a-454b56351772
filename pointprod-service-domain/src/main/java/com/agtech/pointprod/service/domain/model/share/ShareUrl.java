package com.agtech.pointprod.service.domain.model.share;

import java.util.Objects;

/**
 * 分享链接值对象
 */
public class ShareUrl {
    private final String value;
    
    private ShareUrl(String value) {
        this.value = value;
    }
    
    public static ShareUrl of(String value) {
        return new ShareUrl(value == null ? "" : value);
    }
    
    public static ShareUrl empty() {
        return new ShareUrl("");
    }
    
    public static ShareUrl fromShareCode(ShareCode shareCode, String domain, String path) {
        if (shareCode == null || shareCode.isEmpty()) {
            return empty();
        }
        return new ShareUrl(domain + path + shareCode.getValue());
    }
    
    public boolean isEmpty() {
        return value == null || value.trim().isEmpty();
    }
    
    public String getValue() {
        return value;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) {
        	return true;
        } 
        if (o == null || getClass() != o.getClass()) {
        	return false;
        } 
        ShareUrl shareUrl = (ShareUrl) o;
        return Objects.equals(value, shareUrl.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    @Override
    public String toString() {
        return value;
    }
} 