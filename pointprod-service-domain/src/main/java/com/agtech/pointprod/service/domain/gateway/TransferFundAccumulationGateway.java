package com.agtech.pointprod.service.domain.gateway;

import com.agtech.pointprod.service.domain.model.TransferFundAccumulation;

import java.math.BigDecimal;

/**
 * 转出获赠累计领域网关接口
 */
public interface TransferFundAccumulationGateway {
    
    /**
     * 根据用户ID查询累计记录
     * @param userId 用户ID
     * @return 累计记录
     */
    TransferFundAccumulation queryByUserId(String userId);
    
    /**
     * 根据用户ID查询累计记录并加锁
     * @param userId 用户ID
     * @return 累计记录
     */
    TransferFundAccumulation queryByUserIdForUpdate(String userId);
    
    /**
     * 根据业务ID查询累计记录
     * @param fundAccumulationId 业务ID
     * @return 累计记录
     */
    TransferFundAccumulation queryByFundAccumulationId(String fundAccumulationId);
    
    /**
     * 保存累计记录
     * @param transferFundAccumulation 累计记录
     * @return 保存成功与否
     */
    boolean saveTransferFundAccumulation(TransferFundAccumulation transferFundAccumulation);
    
    /**
     * 累加转出金额和次数
     * @param userId 用户ID
     * @param amount 转出金额
     * @param count 转出次数
     * @return 更新成功与否
     */
    boolean accumulateFundOut(String userId, BigDecimal amount, Integer count);
    
    /**
     * 累加转入金额和次数
     * @param userId 用户ID
     * @param amount 转入金额
     * @param count 转入次数
     * @return 更新成功与否
     */
    boolean accumulateFundIn(String userId, BigDecimal amount, Integer count);
}