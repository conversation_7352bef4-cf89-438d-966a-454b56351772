package com.agtech.pointprod.service.domain.model.share;

import java.util.Objects;

/**
 * 内容引用值对象 - 组合了contentId和contentType
 */
public class ContentReference {
    private final String contentId;
    private final String contentType;
    
    private ContentReference(String contentId, String contentType) {
        this.contentId = contentId;
        this.contentType = contentType;
    }
    
    public static ContentReference of(String contentId, String contentType) {
        if (contentId == null || contentId.trim().isEmpty()) {
            throw new IllegalArgumentException("contentId不能为空");
        }
        if (contentType == null || contentType.trim().isEmpty()) {
            throw new IllegalArgumentException("contentType不能为空");
        }
        return new ContentReference(contentId, contentType);
    }
    
    public String getContentId() {
        return contentId;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) {
        	return true;
        } 
        if (o == null || getClass() != o.getClass()) {
        	return false;
        } 
        ContentReference that = (ContentReference) o;
        return Objects.equals(contentId, that.contentId) && 
               Objects.equals(contentType, that.contentType);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(contentId, contentType);
    }
    
    @Override
    public String toString() {
        return "ContentReference{" +
                "contentId='" + contentId + '\'' +
                ", contentType='" + contentType + '\'' +
                '}';
    }
} 