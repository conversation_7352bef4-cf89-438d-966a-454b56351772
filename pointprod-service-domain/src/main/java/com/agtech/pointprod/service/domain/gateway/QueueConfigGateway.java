package com.agtech.pointprod.service.domain.gateway;

import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;

/**
 * 队列配置网关接口
 * 根据MessageQueueEnum枚举类型获取RabbitMQ队列配置信息
 * <AUTHOR>
 */
public interface QueueConfigGateway {
    
    /**
     * 获取队列名称
     * @param messageQueueEnum 消息队列枚举
     * @return 队列名称
     */
    String getQueue(MessageQueueEnum messageQueueEnum);
    
    /**
     * 获取交换机名称
     * @param messageQueueEnum 消息队列枚举
     * @return 交换机名称
     */
    String getExchange(MessageQueueEnum messageQueueEnum);
    
    /**
     * 获取路由键
     * @param messageQueueEnum 消息队列枚举
     * @return 路由键
     */
    String getRoutingKey(MessageQueueEnum messageQueueEnum);

    /**
     * 获取延迟交换机名称
     * @param messageQueueEnum 消息队列枚举
     * @return 延迟交换机名称
     */
    String getDelayExchange(MessageQueueEnum messageQueueEnum);

    /**
     * 获取延迟路由键
     * @param messageQueueEnum 消息队列枚举
     * @return 延迟路由键
     */
    String getDelayRoutingKey(MessageQueueEnum messageQueueEnum);

}