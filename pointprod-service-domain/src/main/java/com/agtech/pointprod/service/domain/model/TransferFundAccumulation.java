package com.agtech.pointprod.service.domain.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/25 17:14
 */
@Getter
@Setter
public class TransferFundAccumulation {

    private String fundAccumulationId;
    private String userId;
    private BigDecimal fundOutAmount;
    private Integer fundOutCount;
    private BigDecimal fundInAmount;
    private Integer fundInCount;
    private String description;
    private Date gmtCreate;
    private Date gmtModified;

}
