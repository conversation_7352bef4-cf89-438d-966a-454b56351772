package com.agtech.pointprod.service.domain.common.enums;

import com.agtech.pointprod.service.domain.exception.PointProdBizException;

/**
 * 分享渠道类型枚举
 */
public enum ChannelTypeEnum {
    
    /**
     * 微信
     */
    WECHAT("WECHAT", "微信"),
    
    /**
     * WhatsApp
     */
    WHATSAPP("WHATSAPP", "WhatsApp"),
    
    /**
     * 其他
     */
    OTHER("OTHER", "其他"),
    
    GROUP("GROUP","朋友圈");
    
    /**
     * 枚举值
     */
    private final String value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    ChannelTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
    
    public String getValue() {
        return value;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据值获取枚举
     * @param value 枚举值
     * @return 枚举对象
     */
    public static ChannelTypeEnum fromValue(String value) {
        for (ChannelTypeEnum type : ChannelTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
    }
} 