package com.agtech.pointprod.service.domain.model;

import com.agtech.pointprod.service.domain.config.RetryStrategyProperties.RetryType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 任务配置工厂类
 * 基于DDD设计，提供统一的配置创建和转换服务
 * 支持JSON序列化和反序列化
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TaskConfigFactory {
    
    private final ObjectMapper objectMapper;
    
    public TaskConfigFactory(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }


    /**
     * 将配置对象序列化为JSON字符串
     * @param config 配置对象
     * @return JSON字符串
     */
    public String toJson(TaskConfig config) {
        if (config == null) {
            return null;
        }
        
        try {
            return objectMapper.writeValueAsString(config);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize TaskConfig to JSON", e);
            throw new RuntimeException("Failed to serialize TaskConfig to JSON", e);
        }
    }
    
    /**
     * 从JSON字符串反序列化为指定类型的配置对象
     * @param json JSON字符串
     * @param configClass 配置类型
     * @param <T> 配置类型泛型
     * @return 配置对象
     */
    public <T extends TaskConfig> T fromJson(String json, Class<T> configClass) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(json, configClass);
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize JSON to {}: {}", configClass.getSimpleName(), json, e);
            throw new RuntimeException("Failed to deserialize JSON to " + configClass.getSimpleName(), e);
        }
    }


    /**
     * 创建MQ配置的便捷方法
     * @param exchange 交换机
     * @param routingKey 路由键
     * @return MQ配置
     */
    public MqTaskConfig createMqConfig(String exchange, String routingKey, RetryType retryType) {
        return MqTaskConfig.createDefault(exchange, routingKey, retryType);
    }
    

}