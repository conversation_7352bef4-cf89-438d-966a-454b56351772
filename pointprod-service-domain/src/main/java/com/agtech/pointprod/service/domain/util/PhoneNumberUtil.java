package com.agtech.pointprod.service.domain.util;

import com.agtech.common.lang.util.StringUtil;

/**
 * Phone number utility class for parsing and formatting phone numbers
 */
public class PhoneNumberUtil {

    private static final String AREA_CODE_SEPARATOR = "-";

    /**
     * Parse identifyId with format "areaCode-phoneNumber"
     * 
     * @param identifyId the identifyId string with format "areaCode-phoneNumber"
     * @return String[] array with 2 elements: [areaCode, phoneNumber], or [null, identifyId] if no area code found
     */
    public static String[] parsePhoneWithAreaCode(String identifyId) {
        String[] result = new String[2];
        
        if (StringUtil.isBlank(identifyId)) {
            // Return empty result if identifyId is null or empty
            result[0] = null;
            result[1] = null;
            return result;
        }
        
        // Check if identifyId contains area code separator
        if (identifyId.contains(AREA_CODE_SEPARATOR)) {
            String[] parts = identifyId.split(AREA_CODE_SEPARATOR, 2);
            result[0] = parts[0]; // Area code
            result[1] = parts[1]; // Phone number
        } else {
            // If no separator found, use whole string as phone number
            result[0] = null;
            result[1] = identifyId;
        }
        
        return result;
    }
    
    /**
     * Format area code and phone number into identifyId
     * 
     * @param areaCode the area code
     * @param phoneNumber the phone number
     * @return formatted identifyId string or just phoneNumber if areaCode is null/empty
     */
    public static String formatPhoneWithAreaCode(String areaCode, String phoneNumber) {
        if (StringUtil.isBlank(areaCode)) {
            return phoneNumber;
        }
        return areaCode + AREA_CODE_SEPARATOR + phoneNumber;
    }
} 