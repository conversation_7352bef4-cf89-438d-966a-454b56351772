package com.agtech.pointprod.service.domain.gateway;

import java.util.List;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.service.domain.model.NotificationRecord;
import com.agtech.pointprod.service.domain.model.NotificationRecordList;

/**
 * 通知领域网关接口
 */
public interface NotificationGateway {
    
    /**
     * 查询用户的通知列表
     * 
     * @param userId 用户ID
     * @param shareCode 分享编码（可选）
     * @param resourceType 通知类型（可选）
     * @return 通知记录列表，包含通知列表和分享码是否过期标志
     */
    NotificationRecordList queryNotificationList(String userId, String shareCode, String resourceType);
    
    /**
     * 根据通知ID查询单个通知记录
     *
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 通知记录
     */
    NotificationRecord queryByNotificationId(String notificationId, String userId);
    
    /**
     * 批量更新通知记录为已读状态
     * 
     * @param notificationIds 通知ID列表
     * @param userId 用户ID
     * @return 更新是否成功
     */
    boolean batchUpdateNotificationToRead(List<String> notificationIds, String userId);
    
    /**
     * 根据类型批量更新通知记录为已读状态
     * 
     * @param resourceType 资源类型
     * @param userId 用户ID
     * @return 更新是否成功
     */
    boolean batchUpdateNotificationByTypeToRead(String resourceType, String userId);
    
    /**
     * 创建通知记录
     * 
     * @param notification 通知记录
     * @param userId 用户ID
     * @return 创建是否成功
     */
    boolean createNotification(NotificationRecord notification, String userId);
    
    /**
     * 更新通知推送状态
     * 
     * @param notificationId 通知ID
     * @param pushStatus 推送状态
     * @return 更新是否成功
     */
    boolean updateNotificationPushStatus(String notificationId, String pushStatus);
    
    /**
     * 统一推送通知给用户（支持付款和收款场景）
     * 
     * @param notification 通知记录
     * @param orderInfo 订单信息（基类）
     * @return 推送是否成功
     */
    boolean pushNotificationToUser(NotificationRecord notification, BaseOrderInfo orderInfo);
 
    
    /**
     * 带锁查询通知记录（用于检查重复推送）
     * 
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 通知记录列表
     */
    List<NotificationRecord> queryNotificationForUpdate(String resourceId, String resourceType, String userId, String notificationType);
    
    /**
     * 不带锁查询通知记录
     * 
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 通知记录列表
     */
    List<NotificationRecord> queryNotification(String resourceId, String resourceType, String userId, String notificationType);
} 