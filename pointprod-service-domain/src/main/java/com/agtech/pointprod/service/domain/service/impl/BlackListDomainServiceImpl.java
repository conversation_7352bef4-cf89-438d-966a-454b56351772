package com.agtech.pointprod.service.domain.service.impl;

import com.agtech.pointprod.service.domain.gateway.BlackListGateway;
import com.agtech.pointprod.service.domain.service.BlackListDomainService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class BlackListDomainServiceImpl implements BlackListDomainService {

    @Resource
    private BlackListGateway blackListGateway;
    @Override
    public boolean isInBlackList(String userId) {
        return blackListGateway.countBlackList(userId) > 0;
    }
}
