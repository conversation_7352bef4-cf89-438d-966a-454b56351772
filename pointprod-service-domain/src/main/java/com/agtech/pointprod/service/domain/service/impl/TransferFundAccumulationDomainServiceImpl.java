package com.agtech.pointprod.service.domain.service.impl;

import java.math.BigDecimal;
import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.agtech.pointprod.service.domain.gateway.TransferFundAccumulationGateway;
import com.agtech.pointprod.service.domain.model.TransferFundAccumulation;
import com.agtech.pointprod.service.domain.service.TransferFundAccumulationDomainService;

import lombok.extern.slf4j.Slf4j;

/**
 * 转出获赠累计领域服务实现
 */
@Service
@Slf4j
public class TransferFundAccumulationDomainServiceImpl implements TransferFundAccumulationDomainService {
    
    @Resource
    private TransferFundAccumulationGateway transferFundAccumulationGateway;
    
    @Override
    public TransferFundAccumulation queryByUserId(String userId) {
        return transferFundAccumulationGateway.queryByUserId(userId);
    }
    
    @Override
    public TransferFundAccumulation initUserAccumulation(String userId, String description) {
        TransferFundAccumulation accumulation = new TransferFundAccumulation();
        accumulation.setUserId(userId);
        accumulation.setFundOutAmount(BigDecimal.ZERO);
        accumulation.setFundOutCount(0);
        accumulation.setFundInAmount(BigDecimal.ZERO);
        accumulation.setFundInCount(0);
        accumulation.setDescription(description);
        accumulation.setGmtCreate(new Date());
        accumulation.setGmtModified(new Date());
        
        boolean success = transferFundAccumulationGateway.saveTransferFundAccumulation(accumulation);
        return success ? accumulation : null;
    }
    
    @Override
    public boolean accumulateFundOut(String userId, BigDecimal amount, Integer count) {
        return transferFundAccumulationGateway.accumulateFundOut(userId, amount, count);
    }
    
    @Override
    public boolean accumulateFundIn(String userId, BigDecimal amount, Integer count) {
        return transferFundAccumulationGateway.accumulateFundIn(userId, amount, count);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean accumulateFundOutWithInit(String userId, BigDecimal amount, Integer count, String description) {
        // 先尝试直接累加
        boolean success = accumulateFundOut(userId, amount, count);
        if (success) {
            return true;
        }
        
        // 如果累加失败，可能是记录不存在，先初始化再累加
        TransferFundAccumulation existing = transferFundAccumulationGateway.queryByUserIdForUpdate(userId);
        if (existing == null) {
            // 初始化记录
            TransferFundAccumulation accumulation = initUserAccumulation(userId, description);
            if (accumulation != null) {
                // 再次尝试累加
                return accumulateFundOut(userId, amount, count);
            }
        }
        
        return false;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean accumulateFundInWithInit(String userId, BigDecimal amount, Integer count, String description) {
        // 先尝试直接累加
        boolean success = accumulateFundIn(userId, amount, count);
        if (success) {
            return true;
        }
        
        // 如果累加失败，可能是记录不存在，先初始化再累加
        TransferFundAccumulation existing = transferFundAccumulationGateway.queryByUserIdForUpdate(userId);
        if (existing == null) {
            // 初始化记录
            TransferFundAccumulation accumulation = initUserAccumulation(userId, description);
            if (accumulation != null) {
                // 再次尝试累加
                return accumulateFundIn(userId, amount, count);
            }
        }
        
        return false;
    }

}