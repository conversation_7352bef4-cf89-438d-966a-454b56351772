package com.agtech.pointprod.service.domain.common.enums;


public enum TransferRuleStatusEnum {
    VALID("VALID", "啟用"), INVALID("INVALID", "未啟用");
    private String value;
    private String desc;

    TransferRuleStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }
    
    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param value 状态代码
     * @return 对应的枚举值
     */
    public static TransferRuleStatusEnum getByValue(String value) {
        for (TransferRuleStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据code获取显示名称
     * @param value 状态代码
     * @return 对应的显示名称
     */
    public static String getDisplayNameByCode(String value) {
        TransferRuleStatusEnum status = getByValue(value);
        return status != null ? status.getDesc() : null;
    }
    

        
    /**
     * 判断是否为有效状态
     * @param value 状态代码
     * @return 是否有效
     */
    public static boolean isValid(String value) {
        return VALID.getValue().equals(value);
    }
}
