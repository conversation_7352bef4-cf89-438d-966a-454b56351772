package com.agtech.pointprod.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

/**
 * mCoin交易类型枚举
 */
public enum McoinTradeTypeEnum {
    
    /**
     * mCoin转出
     */
    MCOIN_TRANSFER_OUT("16", "mCoin转出"),
    
    /**
     * mCoin转入
     */
    MCOIN_TRANSFER_IN("17", "mCoin转入");
    
    /** code */
    private String code;

    /** description */
    private String description;

    McoinTradeTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get enum by code
     */
    public static McoinTradeTypeEnum getByCode(String code) {
        for (McoinTradeTypeEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * get enum by notification type
     */
    public static McoinTradeTypeEnum getByNotificationType(NotificationTypeEnum notificationType) {
        if (notificationType == null) {
            return null;
        }
        
        if (NotificationTypeEnum.SEND.equals(notificationType)) {
            return MCOIN_TRANSFER_OUT;
        } else if (NotificationTypeEnum.ACCEPT.equals(notificationType)) {
            return MCOIN_TRANSFER_IN;
        }
        
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }
} 