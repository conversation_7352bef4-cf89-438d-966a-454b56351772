package com.agtech.pointprod.service.domain.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.MPayUserInfoGateway;
import com.agtech.pointprod.service.domain.gateway.McoinAccountGateway;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.service.UserInfoQueryDomainService;

import lombok.extern.slf4j.Slf4j;

/**
 * 用户信息查询领域服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserInfoQueryDomainServiceImpl implements UserInfoQueryDomainService {

    @Resource
    private McoinAccountGateway mcoinAccountGateway;
    
    @Resource
    private MPayUserInfoGateway mPayUserInfoGateway;

    @Resource
    private Executor paymentExecutor;

    @Override
    public UserInfoQueryResult queryUserInfoAsync(BaseOrderInfo orderInfo, String orderId, String securityId) {
        log.info("开始异步查询用户信息, orderId={}", orderId);
        
        List<CompletableFuture<McoinAccount>> payerMcoinAccounts = new ArrayList<>();
        List<CompletableFuture<McoinAccount>> payeeMcoinAccounts = new ArrayList<>();
        List<CompletableFuture<MPayUserInfo>> payerUserInfos = new ArrayList<>();
        List<CompletableFuture<MPayUserInfo>> payeeUserInfos = new ArrayList<>();
        List<CompletableFuture<Boolean>> securityValidations = new ArrayList<>();

        // 查询付款人信息
        List<PayOrderInfo> payOrderInfoList = orderInfo.getPayOrderInfoList();
        if (payOrderInfoList != null) {
            for (PayOrderInfo payOrderInfo : payOrderInfoList) {
                String payerUserId = payOrderInfo.getPayer().getUserId();
                
                // 异步查询付款人积分账户
                CompletableFuture<McoinAccount> payerMcoinAccount = CompletableFuture
                        .supplyAsync(() -> {
                            log.debug("查询付款人积分账户, userId={}", payerUserId);
                            return mcoinAccountGateway.queryMcoinAccount(payerUserId);
                        }, paymentExecutor);
                payerMcoinAccounts.add(payerMcoinAccount);
                
                // 异步查询付款人用户信息
                CompletableFuture<MPayUserInfo> payerUserInfo = CompletableFuture
                        .supplyAsync(() -> {
                            log.debug("查询付款人用户信息, userId={}", payerUserId);
                            return mPayUserInfoGateway.getUserMsg(payerUserId);
                        }, paymentExecutor);
                payerUserInfos.add(payerUserInfo);
                
                // 异步校验安全ID
                CompletableFuture<Boolean> securityValidation = CompletableFuture
                        .supplyAsync(() -> {
                            log.debug("校验安全ID, userId={}, orderId={}", payerUserId, orderId);
                            return mPayUserInfoGateway.checkSecurityId(payerUserId, orderId, securityId);
                        }, paymentExecutor);
                securityValidations.add(securityValidation);
            }
        }
        
        // 查询收款人信息
        List<AcceptOrderInfo> acceptOrderInfoList = orderInfo.getAcceptOrderInfoList();
        if (acceptOrderInfoList != null) {
            for (AcceptOrderInfo acceptOrderInfo : acceptOrderInfoList) {
                String payeeUserId = acceptOrderInfo.getPayee().getUserId();
                
                // 异步查询收款人积分账户
                CompletableFuture<McoinAccount> payeeMcoinAccount = CompletableFuture
                        .supplyAsync(() -> {
                            log.debug("查询收款人积分账户, userId={}", payeeUserId);
                            return mcoinAccountGateway.queryMcoinAccount(payeeUserId);
                        }, paymentExecutor);
                payeeMcoinAccounts.add(payeeMcoinAccount);
                
                // 异步查询收款人用户信息
                CompletableFuture<MPayUserInfo> payeeUserInfo = CompletableFuture
                        .supplyAsync(() -> {
                            log.debug("查询收款人用户信息, userId={}", payeeUserId);
                            return mPayUserInfoGateway.getUserMsg(payeeUserId);
                        }, paymentExecutor);
                payeeUserInfos.add(payeeUserInfo);
            }
        }
        
        log.info("用户信息异步查询任务已提交, orderId={}, 付款人数量={}, 收款人数量={}", 
                orderId, payerUserInfos.size(), payeeUserInfos.size());
        
        return new UserInfoQueryResult(
                payerMcoinAccounts,
                payeeMcoinAccounts,
                payerUserInfos,
                payeeUserInfos,
                securityValidations
        );
    }

    @Override
    public SyncUserInfoResult waitForResults(UserInfoQueryResult queryResult) {
        log.info("开始等待异步查询结果");
        try {
            // 等待所有异步任务完成并获取结果
            List<McoinAccount> payerMcoinAccounts = extractFutureResults(queryResult.getPayerMcoinAccounts());
            List<McoinAccount> payeeMcoinAccounts = extractFutureResults(queryResult.getPayeeMcoinAccounts());
            List<MPayUserInfo> payerUserInfos = extractFutureResults(queryResult.getPayerUserInfos());
            List<MPayUserInfo> payeeUserInfos = extractFutureResults(queryResult.getPayeeUserInfos());
            List<Boolean> securityValidations = extractFutureResults(queryResult.getSecurityValidations());
            
            log.info("异步查询结果获取完成, 付款人账户数量={}, 收款人账户数量={}, 付款人信息数量={}, 收款人信息数量={}, 安全校验数量={}",
                    payerMcoinAccounts.size(), payeeMcoinAccounts.size(), 
                    payerUserInfos.size(), payeeUserInfos.size(), securityValidations.size());
            
            return new SyncUserInfoResult(payerMcoinAccounts, payeeMcoinAccounts, payerUserInfos,  payeeUserInfos,
                    securityValidations
            );
        } catch(ExecutionException e) {
            if (e.getCause() instanceof PointProdBizException) {
                throw (PointProdBizException) e.getCause();
            }
            log.error("等待异步查询结果时发生异常", e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        } catch (Exception e) {
            log.error("等待异步查询结果时发生异常", e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
    }
    
    /**
     * 提取CompletableFuture的结果
     * 
     * @param futures CompletableFuture列表
     * @param <T> 结果类型
     * @return 结果列表
     */
    private <T> List<T> extractFutureResults(List<CompletableFuture<T>> futures) throws ExecutionException, InterruptedException {
        List<T> results = new ArrayList<>();
        for (CompletableFuture<T> future : futures) {
            try {
                T result = future.get();
                if (result != null) {
                    results.add(result);
                }
            } catch (ExecutionException | InterruptedException e) {
                log.error("异步查询结果异常", e);
                throw e; // 直接抛出原始异常
            }
        }
        return results;
    }
}