package com.agtech.pointprod.service.domain.model;

import java.util.Date;

import com.agtech.pointprod.service.domain.common.enums.DeletedEnum;
import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.facade.dto.enums.TaskRetryStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 任务重试领域模型
 * 重构后的通用重试任务模型，支持多种重试类型
 * 符合DDD领域模型设计原则
 * 
 * <AUTHOR>
 * @version TaskRetry.java, v0.1 2025/1/20 重构 zhongqigang Exp $
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskRetry {

    /**
     * Column: task_retry_id
     * Type: VARCHAR(64)
     * Remark: 业务id
     */
    private String taskRetryId;

    /**
     * Column: resource_id
     * Type: VARCHAR(64)
     * Remark: 来源id，可以用于关联某个业务表
     */
    private String resourceId;

    /**
     * Column: resource_type
     * Type: VARCHAR(16)
     * Remark: 来源id的类型，用于指明哪种业务表
     */
    private TaskResouceTypeEnum resourceType;

    /**
     * Column: try_count
     * Type: INT
     * Default value: 0
     * Remark: 已尝试次数
     */
    private Integer tryCount;

    /**
     * Column: status
     * Type: VARCHAR(16)
     * Default value: INIT
     * Remark: INIT-待处理，FINISH-已完成，FAILED-已达最大尝试次数
     */
    private TaskRetryStatusEnum status;

    /**
     * Column: gmt_create
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     */
    private Date gmtCreate;

    /**
     * Column: gmt_modified
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     */
    private Date gmtModified;

    /**
     * Column: is_deleted
     * Type: TINYINT(3)
     * Default value: 0
     * Remark: 1- 已删除
     */
    private DeletedEnum isDeleted;

    /**
     * Column: next_retry
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 下次尝试时间，注意这个是指在这个时间之后，并且与当前时间差小于一定范围。
     */
    private Date nextRetry;

    /**
     * Column: max_try_count
     * Type: INT
     * Default value: 0
     * Remark: 最大尝试次数
     */
    private Integer maxTryCount;

    /**
     * Column: task_config
     * Type: VARCHAR(2048)
     * Remark: 任务配置信息，JSON格式存储不同类型任务的配置
     */
    private TaskConfig taskConfig;

    /**
     * Column: task_data
     * Type: TEXT
     * Remark: 任务数据，JSON格式存储任务执行所需的数据
     */
    private TaskData taskData;

    /**
     * Column: task_data
     * Type: TEXT
     * Remark: 任务数据，JSON格式存储任务执行所需的数据
     */
    private String taskDataString;

    /**
     * Column: error_message
     * Type: VARCHAR(1024)
     * Remark: 最后一次执行失败的错误信息
     */
    private String errorMessage;

    /**
     * 检查是否可以重试
     * @return 是否可以重试
     */
    public boolean canRetryForScheduledTask() {
        return status == TaskRetryStatusEnum.INIT 
                && tryCount < maxTryCount 
                && nextRetry != null 
                && nextRetry.before(new Date());
    }

    /**
     * 检查是否可以重试
     * @return 是否可以重试
     */
    public boolean canRetryForDelayQueue() {
        return status == TaskRetryStatusEnum.INIT 
                && tryCount < maxTryCount 
                && nextRetry != null ;
    }

    /**
     * 增加重试次数
     */
    public void incrementTryCount() {
        this.tryCount = (this.tryCount == null ? 0 : this.tryCount) + 1;
        this.gmtModified = new Date();
    }

    /**
     * 标记为完成
     */
    public void markAsFinished() {
        this.status = TaskRetryStatusEnum.FINISH;
        this.gmtModified = new Date();
    }

    /**
     * 标记为失败
     * @param errorMessage 错误信息
     */
    public void markAsFailed(String errorMessage) {
        this.status = TaskRetryStatusEnum.FAILED;
        this.errorMessage = errorMessage;
        this.gmtModified = new Date();
    }

    /**
     * 设置下次重试时间
     * @param nextRetryTime 下次重试时间
     */
    public void setNextRetryTime(Date nextRetryTime) {
        this.nextRetry = nextRetryTime;
        this.gmtModified = new Date();
    }

    /**
     * 检查是否达到最大重试次数
     * @return 是否达到最大重试次数
     */
    public boolean isMaxTryCountReached() {
        return tryCount != null && maxTryCount != null && tryCount > maxTryCount;
    }

    /**
     * 检查任务是否已完成
     * @return 是否已完成
     */
    public boolean isFinished() {
        return TaskRetryStatusEnum.FINISH.equals(status);
    }

    /**
     * 检查任务是否已失败
     * @return 是否已失败
     */
    public boolean isFailed() {
        return TaskRetryStatusEnum.FAILED.equals(status);
    }
}