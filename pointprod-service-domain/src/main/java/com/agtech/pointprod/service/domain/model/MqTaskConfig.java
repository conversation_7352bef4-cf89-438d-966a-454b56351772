package com.agtech.pointprod.service.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

import com.agtech.pointprod.service.domain.config.RetryStrategyProperties.RetryType;

/**
 * MQ任务配置类
 * 专门用于MQ类型任务的配置
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class MqTaskConfig extends TaskConfig {

    /**
     * 交换机名称
     */
    private String exchange;
    
    /**
     * 路由键
     */
    private String routingKey;
    
    /**
     * 重试类型
     */
    private RetryType retryType;
    

    /**
     * 消息头信息
     */
    private Map<String, Object> headers;
    

    /**
     * 添加消息头
     * @param key 键
     * @param value 值
     */
    public void addHeader(String key, Object value) {
        if (this.headers == null) {
            this.headers = new HashMap<>();
        }
        this.headers.put(key, value);
    }
    
    /**
     * 获取消息头
     * @param key 键
     * @return 值
     */
    public Object getHeader(String key) {
        return this.headers != null ? this.headers.get(key) : null;
    }
    

    /**
     * 创建默认的MQ配置
     * @param exchange 交换机
     * @param routingKey 路由键
     * @return MQ配置
     */
    public static MqTaskConfig createDefault(String exchange, String routingKey, RetryType retryType) {
        return new MqTaskConfig()
                .setExchange(exchange)
                .setRoutingKey(routingKey)
                .setRetryType(retryType);
    }
    
}