package com.agtech.pointprod.service.domain.model.share;

import java.util.Date;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.service.domain.common.enums.ShareRecordsTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.YesOrNoEnum;
import com.agtech.pointprod.service.domain.model.UserId;

import lombok.Getter;
import lombok.Setter;

/**
 * 分享记录领域模型 - 聚合根
 */
@Getter
@Setter
public class ShareRecord {
    
    // 核心标识
    private ShareRecordId shareRecordsId;
    private String shareRecordsType;
    private ShareCode shareCode;
    
    // 值对象
    private UserId userId;
    private ContentReference contentRef;
    private ShareContent shareContent;
    private ShareUrl shareUrl;
    private String channelType;
    private String extraData;
    private SharePeriod validity;
    
    // 基础状态
    private Integer isDeleted;
    private Date gmtCreate;
    private Date gmtModified;
    
    /**
     * 检查记录是否已过期
     * @param currentTime 当前时间
     * @return 是否过期
     */
    public boolean isExpired(Date currentTime) {
        return validity.isExpired(currentTime);
    }
    
    /**
     * 延长过期时间
     * @param newExpireAt 新的过期时间
     */
    public void extend(Date newExpireAt) {
        this.validity = this.validity.extend(newExpireAt);
        this.gmtModified = new Date();
    }
    
    /**
     * 设置分享码和分享链接
     * @param shareCode 分享码
     * @param shareUrl 分享链接
     */
    public void setShareInfo(ShareCode shareCode, ShareUrl shareUrl) {
        this.shareCode = shareCode;
        this.shareUrl = shareUrl;
        this.gmtModified = new Date();
    }
    
    /**
     * 设置分享码和分享链接的兼容方法（用于现有代码）
     * @param shareCode 分享码字符串
     * @param shareUrl 分享链接字符串
     */
    public void setShareInfo(String shareCode, String shareUrl) {
        this.shareCode = ShareCode.of(shareCode);
        this.shareUrl = ShareUrl.of(shareUrl);
        this.gmtModified = new Date();
    }
    
    /**
     * 创建新的分享记录
     */
    public static ShareRecord createNew(
            String shareRecordsId, 
            String shareRecordsType, 
            String userId,
            String contentType, 
            String contentId, 
            String channelType,
            Date shareTime, 
            Date expireAt,
            Integer isDeleted) {
        
        ShareRecord record = new ShareRecord();
        record.shareRecordsId = ShareRecordId.of(shareRecordsId);
        record.shareRecordsType = shareRecordsType;
        record.shareCode = ShareCode.empty();
        record.userId = UserId.of(userId);
        record.contentRef = ContentReference.of(contentId, contentType);
        record.shareContent = ShareContent.defaultContent();
        record.shareUrl = ShareUrl.empty();
        record.channelType = channelType;
        record.extraData = "";
        record.validity = SharePeriod.of(shareTime, expireAt);
        record.isDeleted = isDeleted;
        record.gmtCreate = shareTime;
        record.gmtModified = shareTime;
        
        return record;
    }
    
    /**
     * 创建SEND类型记录
     */
    public static ShareRecord createSendRecord(
            String recordId,
            String payerUserId,
            ContentReference contentRef,
            String channelType) {
        
        Date now = ZonedDateUtil.now();
        SharePeriod validity = SharePeriod.createDefault();
        
        ShareRecord record = new ShareRecord();
        record.shareRecordsId = ShareRecordId.of(recordId);
        record.shareRecordsType = ShareRecordsTypeEnum.SEND.getValue();
        record.shareCode = ShareCode.empty();
        record.userId = UserId.of(payerUserId);
        record.contentRef = contentRef;
        record.shareContent = ShareContent.defaultContent();
        record.shareUrl = ShareUrl.empty();
        record.channelType = channelType;
        record.extraData = "";
        record.validity = validity;
        record.isDeleted = YesOrNoEnum.NO.getValue();
        record.gmtCreate = now;
        record.gmtModified = now;
        
        return record;
    }
    
    /**
     * 创建SEND类型记录（带分享码、链接和过期时间）
     */
    public static ShareRecord createSendRecord(
            String recordId,
            String payerUserId,
            ContentReference contentRef,
            String channelType,
            ShareCode shareCode,
            ShareUrl shareUrl,
            Date expireAt) {
        
        Date now = ZonedDateUtil.now();
        
        ShareRecord record = new ShareRecord();
        record.shareRecordsId = ShareRecordId.of(recordId);
        record.shareRecordsType = ShareRecordsTypeEnum.SEND.getValue();
        record.shareCode = shareCode != null ? shareCode : ShareCode.empty();
        record.userId = UserId.of(payerUserId);
        record.contentRef = contentRef;
        record.shareContent = ShareContent.defaultContent();
        record.shareUrl = shareUrl != null ? shareUrl : ShareUrl.empty();
        record.channelType = channelType;
        record.extraData = "";
        record.validity = SharePeriod.of(now, expireAt);
        record.isDeleted = YesOrNoEnum.NO.getValue();
        record.gmtCreate = now;
        record.gmtModified = now;
        
        return record;
    }
    
    /**
     * 创建ACCEPT类型记录
     */
    public static ShareRecord createAcceptRecord(
            String recordId,
            String payeeUserId,
            ContentReference contentRef,
            String channelType) {
        
        Date now = ZonedDateUtil.now();
        SharePeriod validity = SharePeriod.createDefault();
        
        ShareRecord record = new ShareRecord();
        record.shareRecordsId = ShareRecordId.of(recordId);
        record.shareRecordsType = ShareRecordsTypeEnum.ACCEPT.getValue();
        record.shareCode = ShareCode.empty();
        record.userId = UserId.of(payeeUserId);
        record.contentRef = contentRef;
        record.shareContent = ShareContent.defaultContent();
        record.shareUrl = ShareUrl.empty();
        record.channelType = channelType;
        record.extraData = "";
        record.validity = validity;
        record.isDeleted = YesOrNoEnum.NO.getValue();
        record.gmtCreate = now;
        record.gmtModified = now;
        
        return record;
    }
    
    /**
     * 创建ACCEPT类型记录（带分享码、链接和过期时间）
     */
    public static ShareRecord createAcceptRecord(
            String recordId,
            String payeeUserId,
            ContentReference contentRef,
            String channelType,
            ShareCode shareCode,
            ShareUrl shareUrl,
            Date expireAt) {
        
        Date now = ZonedDateUtil.now();
        
        ShareRecord record = new ShareRecord();
        record.shareRecordsId = ShareRecordId.of(recordId);
        record.shareRecordsType = ShareRecordsTypeEnum.ACCEPT.getValue();
        record.shareCode = shareCode != null ? shareCode : ShareCode.empty();
        record.userId = UserId.of(payeeUserId);
        record.contentRef = contentRef;
        record.shareContent = ShareContent.defaultContent();
        record.shareUrl = shareUrl != null ? shareUrl : ShareUrl.empty();
        record.channelType = channelType;
        record.extraData = "";
        record.validity = SharePeriod.of(now, expireAt);
        record.isDeleted = YesOrNoEnum.NO.getValue();
        record.gmtCreate = now;
        record.gmtModified = now;
        
        return record;
    }
    
    // Getters and Setters for compatibility with existing code
    
    public String getShareRecordsId() {
        return shareRecordsId.getValue();
    }
    
    public void setShareRecordsId(String shareRecordsId) {
        this.shareRecordsId = ShareRecordId.of(shareRecordsId);
    }
    
    public void setShareRecordsId(ShareRecordId shareRecordsId) {
        this.shareRecordsId = shareRecordsId;
    }
    
    public String getShareRecordsType() {
        return shareRecordsType;
    }
    
    public void setShareRecordsType(String shareRecordsType) {
        this.shareRecordsType = shareRecordsType;
    }
    
    public String getShareCode() {
        return shareCode == null ? null : shareCode.getValue();
    }
    
    public void setShareCode(String shareCode) {
        this.shareCode = ShareCode.of(shareCode);
    }
    
    public void setShareCode(ShareCode shareCode) {
        this.shareCode = shareCode;
    }
    
    public String getUserId() {
        return userId.getValue();
    }
    
    public void setUserId(String userId) {
        this.userId = UserId.of(userId);
    }
    
    public void setUserId(UserId userId) {
        this.userId = userId;
    }
    
    public String getContentType() {
        return contentRef.getContentType();
    }
    
    public String getContentId() {
        return contentRef.getContentId();
    }
    
    public void setContentType(String contentType) {
        String contentId = this.contentRef != null ? this.contentRef.getContentId() : "";
        this.contentRef = ContentReference.of(contentId, contentType);
    }
    
    public void setContentId(String contentId) {
        String contentType = this.contentRef != null ? this.contentRef.getContentType() : "";
        this.contentRef = ContentReference.of(contentId, contentType);
    }
    
    public void setContentReference(ContentReference contentRef) {
        this.contentRef = contentRef;
    }
    
    public ContentReference getContentReference() {
        return this.contentRef;
    }
    
    public String getShareContent() {
        return shareContent.getValue();
    }
    
    public void setShareContent(String shareContent) {
        this.shareContent = ShareContent.of(shareContent);
    }
    
    public String getShareUrl() {
        return shareUrl.getValue();
    }
    
    public void setShareUrl(String shareUrl) {
        this.shareUrl = ShareUrl.of(shareUrl);
    }
    
    public void setShareUrl(ShareUrl shareUrl) {
        this.shareUrl = shareUrl;
    }
    
    public String getChannelType() {
        return channelType;
    }
    
    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }
    
    public String getExtraData() {
        return extraData;
    }
    
    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }
    
    public Date getShareTime() {
        return validity.getShareTime();
    }
    
    public void setShareTime(Date shareTime) {
        Date expireAt = this.validity != null ? this.validity.getExpireAt() : null;
        this.validity = SharePeriod.of(shareTime, expireAt);
    }
    
    public Date getExpireAt() {
        return validity.getExpireAt();
    }
    
    public void setExpireAt(Date expireAt) {
        Date shareTime = this.validity != null ? this.validity.getShareTime() : new Date();
        this.validity = SharePeriod.of(shareTime, expireAt);
    }
    
    public Integer getIsDeleted() {
        return isDeleted;
    }
    
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
    
    public Date getGmtCreate() {
        return gmtCreate;
    }
    
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }
    
    public Date getGmtModified() {
        return gmtModified;
    }
    
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
} 