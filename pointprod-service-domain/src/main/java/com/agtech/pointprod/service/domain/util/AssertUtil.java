package com.agtech.pointprod.service.domain.util;

import com.agtech.common.result.AqcResultCode;
import com.agtech.pointprod.order.service.domain.common.constants.LangConstants;
import com.agtech.pointprod.order.service.domain.common.context.BizContextHolder;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 16:08
 */
public class AssertUtil {

    private AssertUtil(){}

    /**
     * 断言不为null值
     * 
     * @param obj
     * @param aqcResultCode
     * @param messageMo			繁体错误信息
     * @param messageEn			英语错误信息
     * @param params
     */
    public static void assertNotNull(Object obj, AqcResultCode aqcResultCode, String messageMo, String messageEn, Object... params) {
        if(Objects.isNull(obj)){
        	if(BizContextHolder.getLang().equalsIgnoreCase(LangConstants.ZH_MO)) {
        		throw new PointProdBizException(aqcResultCode, MessageUtil.format(messageMo, params));
        	}
            throw new PointProdBizException(aqcResultCode, MessageUtil.format(messageEn, params));
        }
    }

    /**
     * 断言是否为空
     * 
     * @param param
     * @param aqcResultCode
     * @param messageMo			繁体错误信息
     * @param messageEn			英语错误信息
     * @param params
     */
    public static void assertNotBlank(String param, AqcResultCode aqcResultCode, String messageMo, String messageEn, Object... params) {
        if (StringUtils.isBlank(param)) {
        	if(BizContextHolder.getLang().equalsIgnoreCase(LangConstants.ZH_MO)) {
        		throw new PointProdBizException(aqcResultCode, MessageUtil.format(messageMo, params));
        	}
            throw new PointProdBizException(aqcResultCode, MessageUtil.format(messageEn, params));
        }
    }

    public static void assertTrue(boolean flag, AqcResultCode aqcResultCode){
        assertTrue(flag, aqcResultCode, aqcResultCode.getResultMsg(), aqcResultCode.getResultMsg());
    }

    public static void assertTrue(boolean flag, AqcResultCode aqcResultCode, String messageMo, String messageEn, Object... params){
        if (!flag){
        	if(BizContextHolder.getLang().equalsIgnoreCase(LangConstants.ZH_MO)) {
        		throw new PointProdBizException(aqcResultCode, MessageUtil.format(messageMo, params));
        	}
            throw new PointProdBizException(aqcResultCode, MessageUtil.format(messageEn, params));
        }
    }

    public static void assertFalse(boolean flag, AqcResultCode aqcResultCode){
        assertFalse(flag, aqcResultCode, aqcResultCode.getResultMsg(), aqcResultCode.getResultMsg());
    }

    public static void assertFalse(boolean flag, AqcResultCode aqcResultCode, String messageMo, String messageEn, Object... params){
        if (flag){
        	if(BizContextHolder.getLang().equalsIgnoreCase(LangConstants.ZH_MO)) {
        		throw new PointProdBizException(aqcResultCode, MessageUtil.format(messageMo, params));
        	}
            throw new PointProdBizException(aqcResultCode, MessageUtil.format(messageEn, params));
        }
    }

}
