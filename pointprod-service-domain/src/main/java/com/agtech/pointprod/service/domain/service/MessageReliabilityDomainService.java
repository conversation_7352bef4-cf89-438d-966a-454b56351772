package com.agtech.pointprod.service.domain.service;

import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.model.TaskData;
import com.agtech.pointprod.service.domain.model.TaskRetry;

/**
 * 消息可靠发送领域服务
 * - 在发送MQ消息前，先将消息存储到数据库
 * - 消息发送成功后，更新数据库中的状态
 * - 如果消息发送失败，通过定时任务重新处理
 * <AUTHOR>
 */
public interface MessageReliabilityDomainService {
    /**
     * 通用的消息投递保障方法
     * @param resourceType 资源类型
     * @param messageQueue 消息队列枚举
     * @param taskData 任务数据
     * @param <T> 任务数据类型
     * @return TaskRetry对象
     */
    <T extends TaskData> TaskRetry ensureMessageDelivery(TaskResouceTypeEnum resourceType, MessageQueueEnum messageQueue, T taskData);

    /**
     * 发送消息并标记为已成功发送
     * @param messageId 消息ID
     * @param taskDataClass 任务数据类型
     * @return 是否成功
     */
    <D extends TaskData> boolean sendMessageAndMarkDelivered(String messageId, Class<D> taskDataClass);

    /**
     * 发送延迟消息并标记为已成功发送
     * @param messageId 消息ID
     * @param delay 延迟时间（毫秒）
     * @param taskDataClass 任务数据类型
     * @return 是否成功
     */
    <D extends TaskData> boolean sendDelayMessageAndMarkDelivered(String messageId, Integer delay, Class<D> taskDataClass);

    /**
     * 发送延迟消息并赠机重试次数
     * @param messageId 消息ID
     * @param taskDataClass 任务数据类型
     * @return 是否成功
     */
    <D extends TaskData> boolean sendDelayMessageAndIncrRetryCount(String messageId, Class<D> taskDataClass);   

    /**
     * 确保消息消费，如果消息不存在则创建
     * @param messageId 消息ID
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @param messageQueue 消息队列枚举
     * @param taskData 任务数据
     * @param <T> 任务数据类型
     * @return TaskRetry对象
     */
    <T extends TaskData> TaskRetry ensureMessageConsume(String messageId, String resourceId, TaskResouceTypeEnum resourceType, MessageQueueEnum messageQueue, T taskData);


    /**
     * 标记消息为已消费成功
     * @param messageId 消息ID
     * @param taskDataClass 任务数据类型
     * @return 是否成功
     */
    <D extends TaskData> boolean markConsumeFinish(String messageId, Class<D> taskDataClass);


    /**
     * 循环获取未正确投递且未达到重试上限的数据，并重新发送到队列中
     * 该方法用于定时任务，处理失败的消息重试
     * @return 处理的消息数量
     */
    int retryMessages();

    /**
     * 根据消息ID单条更新超过最大重试次数且状态为INIT的任务为失败状态
     * 当重试次数大于等于最大重试次数时且状态为INIT，则将状态更新为失败
     * @param messageId 消息ID
     * @param taskDataClass 任务数据类型
     * @param <D> 任务数据类型
     * @return 是否成功更新
     */
    <D extends TaskData> boolean markExceededRetryTaskAsFailedById(String messageId, Class<D> taskDataClass);

}