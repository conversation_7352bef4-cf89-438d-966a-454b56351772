package com.agtech.pointprod.limit.service.domain.strategy.check;

import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.model.CountRange;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.domain.model.helper.CountLimit;
import com.agtech.pointprod.limit.service.domain.strategy.LimitStrategyExecutor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ class:CountRangeLimitCheckStrategyExecutor, v1.0 2025/06/24 17:20 xiaoming Exp $
 */
public interface CountRangeLimitCheckStrategyExecutor extends LimitStrategyExecutor {

    /**
     * 次数范围校验
     *
     * @param cumulateAccounts
     * @param countRange
     * @return
     */
    CountLimit executeCountRangeCheck(List<CumulateAccount> cumulateAccounts, CountRange countRange, AccumulateRule accumulateRule, AccumulateProduct accumulateProduct);

    default Long calculateUsedCount(List<CumulateAccount> cumulateAccounts) {
        if (CollectionUtils.isEmpty(cumulateAccounts)) {
            return 0L;
        }
        return cumulateAccounts.stream()
                .filter(Objects::nonNull)
                .filter(account -> account.getCount() != null && account.getCount()>0)
                .mapToLong(CumulateAccount::getCount)
                .sum();
    }
}
