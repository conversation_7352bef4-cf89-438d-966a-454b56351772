package com.agtech.pointprod.limit.service.domain.strategy.impl.check;

import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.common.lang.money.MultiCurrencyMoneyUtil;
import com.agtech.pointprod.limit.service.facade.dto.enums.ExceedStatusEnum;
import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.model.AmountRange;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.domain.model.helper.AmountLimit;
import com.agtech.pointprod.limit.service.domain.strategy.check.AmountRangeLimitCheckStrategyExecutor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ class:LMRemainLimitCheckExecutor, v1.0 2023/11/24 22:50 yongsheng Exp $
 */
@Component
public class DmLimitCheckExecutor implements AmountRangeLimitCheckStrategyExecutor {

    /**
     * 终身表校验额度是否充足
     *
     * @param cumulateAccounts
     * @param amountRange
     * @param accumulateRule
     * @param accumulateProduct
     * @return
     */
    @Override
    public AmountLimit executeAmountRangeCheck(List<CumulateAccount> cumulateAccounts, AmountRange amountRange, AccumulateRule accumulateRule, AccumulateProduct accumulateProduct) {

        AccumulateTask accumulateTask = accumulateProduct.getAccumulateTask();
        // 待检查的金额 可正可负
        MultiCurrencyMoney requestAmount = accumulateTask.getAmount();
        AmountLimit baseLimit = new AmountLimit();
        baseLimit.setIntervalAmount(amountRange.getInterval());
        baseLimit.setLimitTarget(accumulateTask.getLimitTarget());
        baseLimit.setLimitType(getStrategy());
        baseLimit.setMax(amountRange.getMaxAmount());
        baseLimit.setMin(amountRange.getMinAmount());
        MultiCurrencyMoney amount = sumUsedAmount(cumulateAccounts);
        MultiCurrencyMoney remain = calculateRemainAmount(amount, amountRange.getMaxAmount(), requestAmount, accumulateProduct.getNeedBuildWaitUpdateMap());
        baseLimit.setRemain(remain);
        MultiCurrencyMoney used = calculateUsedAmount(amount, requestAmount, accumulateProduct.getNeedBuildWaitUpdateMap());
        baseLimit.setUsed(used);
        boolean formulaUnExceedResult = accumulateProduct.getNeedBuildWaitUpdateMap()?!MultiCurrencyMoneyUtil.moneyLessZero(remain):!MultiCurrencyMoneyUtil.lessThan(remain, requestAmount);
        if (formulaUnExceedResult) {
            baseLimit.setExceedStatus(ExceedStatusEnum.NO_EXCEED);
        } else {
            baseLimit.setExceedStatus(ExceedStatusEnum.UPPER_EXCEED);
        }
        return baseLimit;
    }

    private MultiCurrencyMoney sumUsedAmount(List<CumulateAccount> cumulateAccounts) {
        MultiCurrencyMoney sum = new MultiCurrencyMoney("0");
        if (cumulateAccounts == null || cumulateAccounts.isEmpty()) {
            return sum;
        }
        for (CumulateAccount cumulateAccount : cumulateAccounts) {
            if (null == cumulateAccount.getAmount()) {
                continue;
            }
            sum = sum.add(cumulateAccount.getAmount());
        }
        return sum;
    }

    @Override
    public LimitTypeEnum getStrategy() {
        return LimitTypeEnum.DM;
    }
}
