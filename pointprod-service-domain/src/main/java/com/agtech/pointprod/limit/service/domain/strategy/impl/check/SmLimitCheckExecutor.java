package com.agtech.pointprod.limit.service.domain.strategy.impl.check;

import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.common.lang.money.MultiCurrencyMoneyUtil;
import com.agtech.pointprod.limit.service.facade.dto.enums.ExceedStatusEnum;
import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.model.AmountRange;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.domain.model.helper.AmountLimit;
import com.agtech.pointprod.limit.service.domain.strategy.check.AmountRangeLimitCheckStrategyExecutor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ class:SMLimitCheckExecutor, v1.0 2023/11/24 17:17 yongsheng Exp $
 */
@Component
public class SmLimitCheckExecutor implements AmountRangeLimitCheckStrategyExecutor {

    @Override
    public AmountLimit executeAmountRangeCheck(List<CumulateAccount> cumulateAccounts, AmountRange amountRange, AccumulateRule accumulateRule, AccumulateProduct accumulateProduct) {
        if(accumulateProduct.getOnlyConsult()){
            return null;
        }
        AmountLimit baseLimit = new AmountLimit();
        AccumulateTask accumulateTask = accumulateProduct.getAccumulateTask();
        // 待检查的金额 可正可负
        MultiCurrencyMoney requestAmount = accumulateTask.getAmount();
        baseLimit.setIntervalAmount(amountRange.getInterval());
        baseLimit.setLimitTarget(accumulateTask.getLimitTarget());
        baseLimit.setLimitType(getStrategy());
        baseLimit.setMax(amountRange.getMaxAmount());
        baseLimit.setMin(amountRange.getMinAmount());
        ExceedStatusEnum calculateExceedStatus = calculateExceedStatus(amountRange, requestAmount);
        baseLimit.setExceedStatus(calculateExceedStatus);
        //对于SM策略来说，已用的金额为0
        MultiCurrencyMoney usedMoney = new MultiCurrencyMoney(0);
        MultiCurrencyMoney remain = calculateRemainAmount(usedMoney, amountRange.getMaxAmount(), requestAmount, accumulateProduct.getNeedBuildWaitUpdateMap());
        baseLimit.setRemain(remain);
        MultiCurrencyMoney used = calculateUsedAmount(usedMoney, requestAmount, accumulateProduct.getNeedBuildWaitUpdateMap());
        baseLimit.setUsed(used);
        return baseLimit;
    }

    private ExceedStatusEnum calculateExceedStatus(AmountRange amountRange, MultiCurrencyMoney requestAmount) {
        if (MultiCurrencyMoneyUtil.lessThan(requestAmount, amountRange.getMinAmount())) {
            return ExceedStatusEnum.LOWER_EXCEED;
        } else if (MultiCurrencyMoneyUtil.greaterThan(requestAmount, amountRange.getMaxAmount())) {
            return ExceedStatusEnum.UPPER_EXCEED;
        } else {
            return ExceedStatusEnum.NO_EXCEED;
        }
    }

    @Override
    public LimitTypeEnum getStrategy() {
        return LimitTypeEnum.SM;
    }
}
