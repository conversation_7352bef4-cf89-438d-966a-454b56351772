package com.agtech.pointprod.limit.service.domain.common.utils;

public class PrincipalIdUtils {

    /**
     * 校验主体id是否为数字
     * @param principalId
     * @return
     */
    public static boolean principalIdNotDigit(String principalId) {
        char[] charArray = principalId.toCharArray();
        for (char c : charArray) {
            if (!Character.isDigit(c)) {
                return true;
            }
        }
        return false;
    }
}
