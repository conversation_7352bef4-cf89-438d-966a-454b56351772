package com.agtech.pointprod.limit.service.domain.strategy.check;

import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version $ class:LimitCheckStrategyFactory, v1.0 2025/06/24 17:30 xiaoming Exp $
 */
@Component
public class LimitCheckStrategyFactory implements InitializingBean, ApplicationContextAware {

    private static final String AMOUNT_RANGE_WITH_UNDERLINE = "AMOUNT_RANGE_";
    private static final String COUNT_RANGE_WITH_UNDERLINE = "COUNT_RANGE_";
    
    private static final Map<String, AmountRangeLimitCheckStrategyExecutor> AMOUNT_STRATEGY_FACTORY = new ConcurrentHashMap<>();
    private static final Map<String, CountRangeLimitCheckStrategyExecutor> COUNT_STRATEGY_FACTORY = new ConcurrentHashMap<>();
    private ApplicationContext appContext;

    @Override
    public void afterPropertiesSet() {

        appContext.getBeansOfType(AmountRangeLimitCheckStrategyExecutor.class)
            .values()
            .forEach(strategy -> AMOUNT_STRATEGY_FACTORY.put(AMOUNT_RANGE_WITH_UNDERLINE + strategy.getStrategy(), strategy));

        appContext.getBeansOfType(CountRangeLimitCheckStrategyExecutor.class)
            .values()
            .forEach(strategy -> COUNT_STRATEGY_FACTORY.put(COUNT_RANGE_WITH_UNDERLINE + strategy.getStrategy(), strategy));

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appContext = applicationContext;
    }

    public AmountRangeLimitCheckStrategyExecutor getAmountRangeLimitCheckStrategyExecutor(LimitTypeEnum strategy) {
        AmountRangeLimitCheckStrategyExecutor executor = AMOUNT_STRATEGY_FACTORY.getOrDefault(AMOUNT_RANGE_WITH_UNDERLINE + strategy.getCode(), null);
        AssertUtil.assertTrue(Objects.nonNull(executor), PointProdBizErrorCodeEnum.STRATEGY_NOT_EXIST);
        return executor;
    }

    public CountRangeLimitCheckStrategyExecutor getCountRangeLimitCheckStrategyExecutor(String strategy) {
        CountRangeLimitCheckStrategyExecutor executor = COUNT_STRATEGY_FACTORY.getOrDefault(COUNT_RANGE_WITH_UNDERLINE + strategy, null);
        AssertUtil.assertTrue(Objects.nonNull(executor), PointProdBizErrorCodeEnum.STRATEGY_NOT_EXIST);
        return executor;
    }

}
