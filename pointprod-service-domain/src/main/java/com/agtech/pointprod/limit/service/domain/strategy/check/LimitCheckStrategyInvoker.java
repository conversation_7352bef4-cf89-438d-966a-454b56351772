package com.agtech.pointprod.limit.service.domain.strategy.check;

import com.agtech.common.lang.money.MultiCurrencyMoneyUtil;
import com.agtech.pointprod.limit.service.facade.dto.enums.ExceedStatusEnum;
import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.model.AmountRange;
import com.agtech.pointprod.limit.service.domain.model.CountRange;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.domain.model.helper.AmountLimit;
import com.agtech.pointprod.limit.service.domain.model.helper.CountLimit;
import com.agtech.pointprod.limit.service.domain.model.helper.LimitInfo;
import com.agtech.pointprod.limit.service.domain.strategy.accumulate.LimitCumulateStrategyFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class LimitCheckStrategyInvoker {

    @Resource
    LimitCheckStrategyFactory factory;

    @Resource
    LimitCumulateStrategyFactory cumulateFactory;

    public void invokeLimitCheck(AccumulateProduct accumulateProduct) {
        // 校验
        List<AccumulateRule> accumulateRules = accumulateProduct.getAccumulateRules();

        // 可能会匹配到多条规则，循环校验
        for (AccumulateRule accumulateRule : accumulateRules) {
            // 每一条限额规则里有多个金额范围规则 如 SM{limit:[100:MCOIN,+:MCOIN]};DM{interval:100,limit:[0:MCOIN,300:MCOIN]}
            invokeAmountRangeCheck(accumulateProduct, accumulateRule, new ArrayList<>(accumulateRule.getAmountRanges()));
            invokeCountRangeCheck(accumulateProduct, accumulateRule, accumulateRule.getCountRanges());
        }
    }

    /**
     * 调用金额范围检查策略
     *
     * @param accumulateProduct 积累产品信息
     * @param accumulateRule 积累规则
     * @param amountRangeList 金额范围列表
     */
    private void invokeAmountRangeCheck(AccumulateProduct accumulateProduct, AccumulateRule accumulateRule, List<AmountRange> amountRangeList) {
        // 初始化限制信息
        LimitInfo limitInfo = new LimitInfo();
        List<AmountLimit> amountLimits = Lists.newArrayList();
        
        // 最严格的上限和下限
        AmountLimit strictestMaxAmountLimit = null;
        AmountLimit strictestMinAmountLimit = null;
        
        // 遍历所有金额范围进行检查
        for (AmountRange amountRange : amountRangeList) {
            // 检查单个金额范围
            AmountRangeLimitCheckStrategyExecutor executor = factory.getAmountRangeLimitCheckStrategyExecutor(amountRange.getLimitTypeEnum());
            List<CumulateAccount> cumulateAccounts = filterAccount(accumulateProduct, amountRange.getLimitTypeEnum());
            AmountLimit baseLimit = executor.executeAmountRangeCheck(cumulateAccounts, amountRange, accumulateRule, accumulateProduct);
            
            if(null == baseLimit) {
                continue;
            }
            
            amountLimits.add(baseLimit);
            
            // 更新最严格的上限和下限
            if (accumulateProduct.getOnlyConsult()) {
                // 仅咨询模式
                strictestMaxAmountLimit = updateStrictestMaxLimit(strictestMaxAmountLimit, baseLimit);
                strictestMinAmountLimit = updateStrictestMinLimit(strictestMinAmountLimit, baseLimit);
            } else {
                // 实际检查模式
                if (ExceedStatusEnum.UPPER_EXCEED == baseLimit.getExceedStatus()) {
                    strictestMaxAmountLimit = updateStrictestMaxLimit(strictestMaxAmountLimit, baseLimit);
                } else if (ExceedStatusEnum.LOWER_EXCEED == baseLimit.getExceedStatus()) {
                    strictestMinAmountLimit = updateStrictestMinLimit(strictestMinAmountLimit, baseLimit);
                }
                // 相关信息放入accumulateLimitInfo里，方便后续构建累额数据
                handleAmountLimitInfo(amountRange.getLimitTypeEnum(), accumulateProduct.getAccumulateTask(), accumulateRule, cumulateAccounts, accumulateProduct);
            }
        }
        
        // 设置限额信息状态
        setLimitInfoStatus(limitInfo, strictestMaxAmountLimit, strictestMinAmountLimit);
        limitInfo.setAmountLimits(amountLimits);
        limitInfo.calcRemainAmount();
        
        // 将限制信息初始化到产品信息中
        accumulateProduct.initLimitInfo(limitInfo);
    }
    
    /**
     * 更新最严格的上限
     */
    private AmountLimit updateStrictestMaxLimit(AmountLimit currentLimit, AmountLimit newLimit) {
        if (Objects.isNull(currentLimit)) {
            return newLimit;
        } else {
            return compareThenSetStrictestMaxAmountLimit(currentLimit, newLimit);
        }
    }
    
    /**
     * 更新最严格的下限
     */
    private AmountLimit updateStrictestMinLimit(AmountLimit currentLimit, AmountLimit newLimit) {
        if (Objects.isNull(currentLimit)) {
            return newLimit;
        } else {
            return compareThenSetStrictestMinAmountLimit(currentLimit, newLimit);
        }
    }
    
    /**
     * 设置限额信息状态
     */
    private void setLimitInfoStatus(LimitInfo limitInfo, AmountLimit strictestMaxAmountLimit, AmountLimit strictestMinAmountLimit) {
        if (null != strictestMaxAmountLimit) {
            limitInfo.setStrictestMaxAmountLimit(strictestMaxAmountLimit);
            limitInfo.setAmountExceedStatus(ExceedStatusEnum.UPPER_EXCEED.name());
        } else if (null != strictestMinAmountLimit) {
            limitInfo.setStrictestMinAmountLimit(strictestMinAmountLimit);
            limitInfo.setAmountExceedStatus(ExceedStatusEnum.LOWER_EXCEED.name());
        } else {
            limitInfo.setAmountExceedStatus(ExceedStatusEnum.NO_EXCEED.name());
        }
    }

    /**
     * 最小最严格金额限制
     * 要求越高越严格
     */
    private AmountLimit compareThenSetStrictestMinAmountLimit(AmountLimit strictestMinAmountLimit, AmountLimit baseLimit) {
        if(MultiCurrencyMoneyUtil.greaterThan(baseLimit.getMin(), strictestMinAmountLimit.getMin())
            || MultiCurrencyMoneyUtil.equalThan(baseLimit.getMin(), strictestMinAmountLimit.getMin())){
            return baseLimit;
        }else{
            return strictestMinAmountLimit;
        }
    }

    /**
     * 最大最严格金额限制
     */
    private AmountLimit compareThenSetStrictestMaxAmountLimit(AmountLimit strictestMaxAmountLimit, AmountLimit baseLimit) {
        if(MultiCurrencyMoneyUtil.lessThan(baseLimit.getRemain(), strictestMaxAmountLimit.getRemain())
            || MultiCurrencyMoneyUtil.equalThan(baseLimit.getRemain(), strictestMaxAmountLimit.getRemain())){
            return baseLimit;
        }else{
            return strictestMaxAmountLimit;
        }
    }

    /**
     * 调用次数范围检查策略
     *
     * @param accumulateProduct 积累产品信息
     * @param accumulateRule 积累规则
     * @param countRangeList 次数范围列表
     */
    private void invokeCountRangeCheck(AccumulateProduct accumulateProduct, AccumulateRule accumulateRule, List<CountRange> countRangeList) {
        // 获取限制信息
        LimitInfo limitInfo = accumulateProduct.getLimitInfo();
        List<CountLimit> countLimits = Lists.newArrayList();
        
        // 最严格的次数上限
        CountLimit strictestMaxCountLimit = null;
        
        // 遍历所有次数范围进行检查
        for (CountRange countRange : countRangeList) {
            // 获取次数范围限制检查策略执行器
            CountRangeLimitCheckStrategyExecutor executor = factory.getCountRangeLimitCheckStrategyExecutor(countRange.getLimitTypeEnum().getCode());
            
            // 根据限制类型过滤账户
            List<CumulateAccount> cumulateAccounts = filterAccount(accumulateProduct, countRange.getLimitTypeEnum());
            
            // 执行次数范围检查
            CountLimit baseLimit = executor.executeCountRangeCheck(cumulateAccounts, countRange, accumulateRule, accumulateProduct);
            
            // 添加到次数限制列表
            countLimits.add(baseLimit);
            
            // 更新最严格的次数限制
            if (accumulateProduct.getOnlyConsult()) {
                // 仅咨询模式
                strictestMaxCountLimit = baseLimit;
            } else {
                // 实际检查模式
                if (ExceedStatusEnum.NO_EXCEED != baseLimit.getExceedStatus()) {
                    if (Objects.isNull(strictestMaxCountLimit)) {
                        strictestMaxCountLimit = baseLimit;
                    }
                }
                
                // 相关信息放入accumulateLimitInfo里，方便后续构建累额数据
                handleCountLimitInfo(countRange.getLimitTypeEnum(), 
                        accumulateProduct.getAccumulateTask(), 
                        accumulateRule, 
                        cumulateAccounts, 
                        accumulateProduct);
            }
        }
        
        // 设置次数限制状态
        if(null != strictestMaxCountLimit) {
            limitInfo.setCountExceedStatus(strictestMaxCountLimit.getExceedStatus().name());
            limitInfo.setStrictestMaxCountLimit(strictestMaxCountLimit);
        }
        
        // 设置次数限制列表并计算剩余次数
        limitInfo.setCountLimits(countLimits);
        limitInfo.calcRemainCount();
    }

    private List<CumulateAccount> filterAccount(AccumulateProduct accumulateProduct, LimitTypeEnum limitType) {

        if(!accumulateProduct.getCumulateAccountMap().containsKey(limitType)){
            return Lists.newArrayList();
        }
        List<CumulateAccount> strategyCumulateAccounts = accumulateProduct.getCumulateAccountMap().get(limitType);
        if(CollectionUtils.isEmpty(strategyCumulateAccounts)){
            return Lists.newArrayList();
        }
        return strategyCumulateAccounts.stream()
                .filter(account -> account.getSceneCode().equals(accumulateProduct.getSceneCode().getCode()))
                .filter(account -> account.getStrategy().equals(limitType))
                .collect(Collectors.toList());
    }



    private void handleAmountLimitInfo(LimitTypeEnum strategy, AccumulateTask accumulateTask, AccumulateRule accumulateRule,
                                       List<CumulateAccount> cumulateAccounts, AccumulateProduct accumulateProduct) {
        if (BooleanUtils.isNotTrue(accumulateProduct.getNeedBuildWaitUpdateMap()) || !strategy.isNeedStoreAccount()) {
            return;
        }
        Map<LimitTypeEnum, List<CumulateAccount>> map = accumulateProduct.getRuleCumulateAccountMap();
        if (MapUtils.isEmpty(map)) {
            map = Maps.newHashMap();
        }
        List<CumulateAccount> mergeStragetyCumulateAccounts = mergeAmountAccount(strategy,cumulateAccounts,accumulateTask);
        map.put(strategy,mergeStragetyCumulateAccounts);
        accumulateProduct.refreshRuleCumulateAccountMap(map);
    }

    private List<CumulateAccount> mergeAmountAccount(LimitTypeEnum strategy,List<CumulateAccount> cumulateAccounts, AccumulateTask accumulateTask) {
        List<CumulateAccount> mergeCumulateAccounts = cumulateFactory.getLimitCumulateStrategyExecutor(strategy).mergeCumulateAccount(cumulateAccounts,accumulateTask);
        return mergeCumulateAccounts;
    }


    private void handleCountLimitInfo(LimitTypeEnum strategy, AccumulateTask accumulateTask, AccumulateRule accumulateRule,List<CumulateAccount> cumulateAccounts, AccumulateProduct accumulateProduct) {
        if (BooleanUtils.isNotTrue(accumulateProduct.getNeedBuildWaitUpdateMap()) || !strategy.isNeedStoreAccount()) {
            return;
        }
        Map<LimitTypeEnum, List<CumulateAccount>> ruleCumulateAccountMap = accumulateProduct.getRuleCumulateAccountMap();
        if (MapUtils.isEmpty(ruleCumulateAccountMap)) {
            ruleCumulateAccountMap = Maps.newHashMap();
        }
        List<CumulateAccount> mergeStragetyCumulateAccounts = mergeAmountAccount(strategy,cumulateAccounts,accumulateTask);
        ruleCumulateAccountMap.put(strategy,mergeStragetyCumulateAccounts);
        accumulateProduct.refreshRuleCumulateAccountMap(ruleCumulateAccountMap);
    }


}
