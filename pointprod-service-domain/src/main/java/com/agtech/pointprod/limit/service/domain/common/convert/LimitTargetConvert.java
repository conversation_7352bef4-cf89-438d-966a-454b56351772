package com.agtech.pointprod.limit.service.domain.common.convert;

import com.agtech.pointprod.limit.service.domain.model.helper.LimitTarget;
import com.agtech.pointprod.limit.service.facade.dto.LimitTargetDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface LimitTargetConvert extends Converter<LimitTargetDTO, LimitTarget> {

    LimitTargetConvert INSTANCE = Mappers.getMapper(LimitTargetConvert.class);

    @Override
    default LimitTargetDTO doBackward(LimitTarget limitTarget) {
        if(null == limitTarget){
            return null;
        }
        LimitTargetDTO limitTargetDTO = new LimitTargetDTO();
        limitTargetDTO.setPrincipalId(limitTarget.getPrincipalId());
        limitTargetDTO.setPrincipalType(limitTarget.getPrincipalType());
        return limitTargetDTO;
    }
}
