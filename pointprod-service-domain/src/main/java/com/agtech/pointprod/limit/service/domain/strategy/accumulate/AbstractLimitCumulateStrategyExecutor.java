package com.agtech.pointprod.limit.service.domain.strategy.accumulate;

import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.domain.strategy.LimitStrategyExecutor;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
public abstract class AbstractLimitCumulateStrategyExecutor implements LimitStrategyExecutor {

    protected abstract CumulateAccount buildCumulateAccount(AccumulateTask task);

    public List<CumulateAccount> mergeCumulateAccount(List<CumulateAccount> cumulateAccounts, AccumulateTask accumulateTask){
        List<CumulateAccount> rst = Lists.newArrayList();
        CumulateAccount newCumulateAccount = buildCumulateAccount(accumulateTask);
        if(CollectionUtils.isEmpty(cumulateAccounts)){
            rst.add(newCumulateAccount);
            return rst;
        }
        rst.addAll(cumulateAccounts);
        CumulateAccount existCumulateAccount = cumulateAccounts.stream().filter(new Predicate<CumulateAccount>() {
            @Override
            public boolean test(CumulateAccount cumulateAccount) {
                return Objects.equals(cumulateAccount.getTntInst(),newCumulateAccount.getTntInst())
                        && Objects.equals(cumulateAccount.getPrincipalId(),newCumulateAccount.getPrincipalId())
                        && Objects.equals(cumulateAccount.getPrincipalType(),newCumulateAccount.getPrincipalType())
                        && Objects.equals(cumulateAccount.getSceneCode(),newCumulateAccount.getSceneCode())
                        && Objects.equals(cumulateAccount.getStrategy(),newCumulateAccount.getStrategy())
                        && Objects.equals(cumulateAccount.getBizTime(),newCumulateAccount.getBizTime());
            }
        }).findFirst().orElseGet(() -> null);
        //如果是已经存在的，则需要更新DB
        if(null != existCumulateAccount){
            existCumulateAccount.updated();
            doMergeSingeCumulateAccount(existCumulateAccount,newCumulateAccount);
        }else{
            rst.add(newCumulateAccount);
        }
        return rst;
    }

    protected abstract void doMergeSingeCumulateAccount(CumulateAccount existCumulateAccount, CumulateAccount newCumulateAccount);


}
