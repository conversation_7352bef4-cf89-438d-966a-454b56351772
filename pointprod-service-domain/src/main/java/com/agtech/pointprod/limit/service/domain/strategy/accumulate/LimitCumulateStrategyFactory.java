package com.agtech.pointprod.limit.service.domain.strategy.accumulate;

import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version $ class:LimitCheckStrategyFactory, v1.0 2025/06/24 17:30 xiaoming Exp $
 */
@Component
public class LimitCumulateStrategyFactory implements InitializingBean, ApplicationContextAware {

    private static final String CUMULATE_KEY = "CUMULATE_";
    
    private static final Map<String, AbstractLimitCumulateStrategyExecutor> CUMULATE_STRATEGY_FACTORY = new ConcurrentHashMap<>();
    private ApplicationContext appContext;

    @Override
    public void afterPropertiesSet() {

        appContext.getBeansOfType(AbstractLimitCumulateStrategyExecutor.class)
            .values()
            .forEach(strategy -> CUMULATE_STRATEGY_FACTORY.put(CUMULATE_KEY + strategy.getStrategy(), strategy));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appContext = applicationContext;
    }

    public AbstractLimitCumulateStrategyExecutor getLimitCumulateStrategyExecutor(LimitTypeEnum strategy) {
        AbstractLimitCumulateStrategyExecutor executor = CUMULATE_STRATEGY_FACTORY.getOrDefault(CUMULATE_KEY + strategy.getCode(), null);
        AssertUtil.assertTrue(Objects.nonNull(executor), PointProdBizErrorCodeEnum.STRATEGY_NOT_EXIST);
        return executor;
    }

}
