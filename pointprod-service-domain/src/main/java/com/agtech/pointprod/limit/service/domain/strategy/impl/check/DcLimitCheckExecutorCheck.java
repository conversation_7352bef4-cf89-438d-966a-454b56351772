package com.agtech.pointprod.limit.service.domain.strategy.impl.check;

import com.agtech.pointprod.limit.service.facade.dto.enums.ExceedStatusEnum;
import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.model.CountRange;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.domain.model.helper.CountLimit;
import com.agtech.pointprod.limit.service.domain.strategy.check.CountRangeLimitCheckStrategyExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class DcLimitCheckExecutorCheck implements CountRangeLimitCheckStrategyExecutor {

    @Override
    public CountLimit executeCountRangeCheck(List<CumulateAccount> cumulateAccounts, CountRange countRange, AccumulateRule accumulateRule, AccumulateProduct accumulateProduct) {
        AccumulateTask accumulateTask = accumulateProduct.getAccumulateTask();
        CountLimit baseLimit = new CountLimit();
        baseLimit.setLimitType(getStrategy());
        baseLimit.setLimitTarget(accumulateTask.getLimitTarget());
        baseLimit.setUsedCount(calculateUsedCount(cumulateAccounts));
        baseLimit.setIntervalCount(countRange.getInterval());
        baseLimit.setMaxCount(countRange.getMaxCount());
        baseLimit.setRemainCount(countRange.getMaxCount() - baseLimit.getUsedCount());
        if(accumulateProduct.getOnlyConsult()){
            baseLimit.setExceedStatus(ExceedStatusEnum.NO_EXCEED);
        }else {
            Long requestCount = accumulateTask.getCount();
            boolean countUpperExceedResult = calculateUpperCountRangeCheck(cumulateAccounts, countRange, requestCount);
            //boolean countLowerUnExceedResult = calculateLowerCountRangeCheck(cumulateAccount, countRange, requestCount) ;
            if (countUpperExceedResult) {
                baseLimit.setExceedStatus(ExceedStatusEnum.UPPER_EXCEED);
            } else {
                baseLimit.setExceedStatus(ExceedStatusEnum.NO_EXCEED);
            }
        }
        return baseLimit;
    }

    private boolean calculateLowerCountRangeCheck(CumulateAccount cumulateAccount, CountRange countRange, Long requestCount) {
        long useCount = Objects.isNull(cumulateAccount) ? 0L : cumulateAccount.getCount();
        return requestCount + useCount >= countRange.getMinCount();
    }

    private boolean calculateUpperCountRangeCheck(List<CumulateAccount> cumulateAccounts, CountRange countRange, Long requestCount) {
        if (countRange.getMaxCount() == Integer.MAX_VALUE) {
            return true;
        }

        long useCount = cumulateAccounts.stream()
                .filter(Objects::nonNull)
                .filter(account -> account.getCount() != null && account.getCount()>0)
                .mapToLong(CumulateAccount::getCount)
                .sum();
        return countRange.getMaxCount() - requestCount - useCount < 0L;
    }

    @Override
    public LimitTypeEnum getStrategy() {
        return LimitTypeEnum.DC;
    }

}
