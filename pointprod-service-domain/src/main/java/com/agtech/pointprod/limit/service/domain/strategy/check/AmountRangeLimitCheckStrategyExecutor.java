package com.agtech.pointprod.limit.service.domain.strategy.check;

import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.model.AmountRange;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.domain.model.helper.AmountLimit;
import com.agtech.pointprod.limit.service.domain.strategy.LimitStrategyExecutor;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ class:AmountRangeLimitCheckStrategyExecutor, v1.0 2025/06/24 17:18 xiaoming Exp $
 */
public interface AmountRangeLimitCheckStrategyExecutor extends LimitStrategyExecutor {

    /**
     * 金额范围校验
     *
     * @param cumulateAccounts
     * @param amountRange
     * @param accumulateRule
     * @return
     */
    AmountLimit executeAmountRangeCheck(List<CumulateAccount> cumulateAccounts, AmountRange amountRange, AccumulateRule accumulateRule, AccumulateProduct accumulateProduct);

    /**
     * 看是否可以超限扣减
     * 当不进行累额校验时，该值会小于0
     */
    default MultiCurrencyMoney calculateRemainAmount(MultiCurrencyMoney usedAmount, MultiCurrencyMoney maxAmount, MultiCurrencyMoney requestAmount, Boolean needBuildWaitUpdateMap) {
        // 如果needBuildWaitUpdateMap为true，说明是实际requestAmount参与累额的，requestAmount需要参与计算
        if (BooleanUtils.isTrue(needBuildWaitUpdateMap)) {
            return maxAmount.subtract(usedAmount).subtract(requestAmount);
        } else {
            return maxAmount.subtract(usedAmount);
        }
    }

    default MultiCurrencyMoney calculateUsedAmount(MultiCurrencyMoney usedAmount, MultiCurrencyMoney requestAmount, Boolean needBuildWaitUpdateMap) {
        // 如果needBuildWaitUpdateMap为true，说明是实际requestAmount参与累额的，requestAmount需要参与计算
        if (BooleanUtils.isTrue(needBuildWaitUpdateMap)) {
            return usedAmount.add(requestAmount);
        } else {
            return usedAmount;
        }
    }
}
