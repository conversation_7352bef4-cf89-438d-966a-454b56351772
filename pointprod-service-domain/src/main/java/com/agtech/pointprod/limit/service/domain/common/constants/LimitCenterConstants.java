package com.agtech.pointprod.limit.service.domain.common.constants;

/**
 * 规范
 * 1. 字符串常量默认大写，单词之间用_分割
 * 2. 字符串常量默认常量名和内容值一致，如果不一致，尽量在常量名中体现
 * 3.
 */
public class LimitCenterConstants {

    public static final String AMOUNT_RANGE = "AMOUNT_RANGE";
    public static final String COUNT_RANGE = "COUNT_RANGE";
    public static final String UNDERLINE = "_";
    public static final String AMOUNT_RANGE_WITH_UNDERLINE = AMOUNT_RANGE + UNDERLINE;
    public static final String COUNT_RANGE_WITH_UNDERLINE = COUNT_RANGE + UNDERLINE;

    /**
     * , 常量
     */
    public static final String COMMA = ",";

    /**
     * _ 常量
     */

    public static final String PRODUCT_CODE = "PRODUCT_CODE";

    public static final String EVENT_CODE = "EVENT_CODE";

    public static final String YEAR = "YEAR";

    public static final String MONTH = "MONTH";

    public static final String DAY = "DAY";

    public static final String REQUEST_AMOUNT = "REQUEST_AMOUNT";

    public static final String AMOUNT = "AMOUNT";

    public static final String COUNT = "COUNT";

    public static final String REVERSE_AMOUNT = "REVERSE_AMOUNT";

    public static final String MAX = "MAX";

    public static final String MIN = "MIN";

    // 业务常量 KYCLevel
    public static final String KYC_LEVEL = "KYCLevel";

    public static final String BIZ_TIME = "BIZ_TIME";

    public static final String LIMIT_CENTER = "LIMIT_CENTER";

    public static final String LIMIT_TARGET = "LIMIT_TARGET";

    public static final String DIRECTION = "DIRECTION";
    public static final String AMOUNT_LIMIT_TYPES = "AMOUNT_LIMIT_TYPES";
    public static final String COUNT_LIMIT_TYPES = "COUNT_LIMIT_TYPES";

    public interface BizContextKey {
        String USER_LEVEL = "userLevel";
    }
}
