package com.agtech.pointprod.limit.service.domain.common.utils;

import com.agtech.pointprod.limit.service.domain.common.constants.LimitCenterConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

public class LimitZoneTimeUtils {


    private static String zoneId;

    @Value("${default.zoneId}")
    public void setZoneId(String zoneId) {
        LimitZoneTimeUtils.zoneId = zoneId;
    }

    // Long时间戳只获取年份
    public static String getYear(Long time, String zoneId) {
        ZonedDateTime zonedDateTime = getZonedDateTime(time, zoneId);
        return zonedDateTime.getYear() + "";
    }

    // 获取年份+月份
    public static String getYearAndMonth(Long time, String zoneId) {
        ZonedDateTime zonedDateTime = getZonedDateTime(time, zoneId);
        return zonedDateTime.getYear() + "" + zonedDateTime.getMonthValue();
    }

    // 获取年份+月份+日期
    public static String getYearAndMonthAndDay(Long time, String zoneId) {
        ZonedDateTime zonedDateTime = getZonedDateTime(time, zoneId);
        String month = zonedDateTime.getMonthValue() < 10 ? "0" + zonedDateTime.getMonthValue() : zonedDateTime.getMonthValue() + "";
        return zonedDateTime.getYear() + month + zonedDateTime.getDayOfMonth();
    }

    private static ZonedDateTime getZonedDateTime(@NotNull Long time, String zoneId) {
        // 通过Instant和atZone()直接创建ZonedDateTime对象
        if (StringUtils.isEmpty(zoneId)) {
            zoneId = LimitZoneTimeUtils.zoneId;
        }
        return Instant.ofEpochMilli(time).atZone(ZoneId.of(zoneId));
    }

    public static String buildBizTime(String strategy, Long bizTime) {
        switch (strategy) {
            case LimitCenterConstants.DAY:
                return LimitZoneTimeUtils.getYearAndMonthAndDay(bizTime, null);
            case LimitCenterConstants.MONTH:
                return LimitZoneTimeUtils.getYearAndMonth(bizTime, null);
            case LimitCenterConstants.YEAR:
                return LimitZoneTimeUtils.getYear(bizTime, null);
            default:
                return null;
        }
    }
}
