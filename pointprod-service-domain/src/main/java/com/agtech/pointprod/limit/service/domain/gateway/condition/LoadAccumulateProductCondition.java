package com.agtech.pointprod.limit.service.domain.gateway.condition;

import com.agtech.pointprod.limit.service.facade.dto.enums.PrincipalTypeEnum;
import com.agtech.pointprod.limit.service.facade.dto.enums.SceneCodeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version $ class:LoadAccumulateProductCondition, v1.0 2025/06/15 16:08 xiaoming Exp $
 */
@Getter
@Setter
@Accessors(chain = true)
public class LoadAccumulateProductCondition {

    private SceneCodeEnum sceneCode;

    private String principalId;

    private PrincipalTypeEnum principalType;

    private String bizNo;

    /**
     * 业务时间
     */
    private String bizTime;

    private String currency;

    private String bizInfo;
}
