package com.agtech.pointprod.limit.service.domain.gateway.condition;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ class:CumulateAccountQueryCondition, v1.0 2025/06/23 17:00 xiaoming Exp $
 */

@Getter
@Setter
public class CumulateAccountQueryCondition {

    private String principalId;

    private String principalType;

    private String sceneCode;

    private List<CumulateAccountStrategyQueryCondition> strategyQueryConditions;

    private String currency;

    private String bizTime;
}
