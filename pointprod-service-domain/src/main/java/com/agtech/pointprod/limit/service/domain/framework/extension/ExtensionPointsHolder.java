package com.agtech.pointprod.limit.service.domain.framework.extension;

import com.agtech.pointprod.limit.service.domain.common.ext.ExtensionPointsDefinition;
import com.agtech.pointprod.limit.service.domain.common.utils.SpringBeanUtils;
import com.agtech.pointprod.limit.service.domain.model.BizCodeIdentity;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * Stub class for missing Alibaba framework dependency
 */
@Component
public class ExtensionPointsHolder {

    public <T extends ExtensionPointsDefinition> T getExtensionPoint(BizCodeIdentity bizCodeIdentity,Class<T> extensionPointsClassType) {
        Collection<T> beansOfType = SpringBeanUtils.getBeansOfType(extensionPointsClassType);
        if(CollectionUtils.isNotEmpty(beansOfType)){
            for (T extensionPoint : beansOfType) {
                if (extensionPoint.bizIdentity(bizCodeIdentity)) {
                    return extensionPoint;
                }
            }
        }
        return null;
    }
} 