package com.agtech.pointprod.limit.service.domain.strategy;

import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class LimitCumulateInvoker {

    public void buildCumulateAccounts(AccumulateProduct accumulateProduct) {
        Map<LimitTypeEnum, List<CumulateAccount>> updateCumulateAccountMap = Maps.newHashMap();
        Map<LimitTypeEnum, List<CumulateAccount>> insertedCumulateAccountMap = Maps.newHashMap();
        for (Map.Entry<LimitTypeEnum, List<CumulateAccount>> entry : accumulateProduct.getRuleCumulateAccountMap().entrySet()) {
            LimitTypeEnum strategy = entry.getKey();
            //需要保存类的策略
            if(!strategy.isNeedStoreAccount()) {
                continue;
            }
            List<CumulateAccount> limitRuleCumulateAccountPairs = entry.getValue();
            for (CumulateAccount cumulateAccount : limitRuleCumulateAccountPairs) {
                cumulateAccount.updateCumulateAccount(cumulateAccount.getAccountId(), accumulateProduct.getAccumulateTask());
                if (Objects.isNull(cumulateAccount.getId())) {
                    insertedCumulateAccountMap.computeIfAbsent(strategy, k -> Lists.newArrayList()).add(cumulateAccount);
                } else if(cumulateAccount.getNeedUpdate()){
                    updateCumulateAccountMap.computeIfAbsent(strategy, k -> Lists.newArrayList()).add(cumulateAccount);
                }
            }
        }
        accumulateProduct.initInsertCumulateAccountMap(insertedCumulateAccountMap);
        accumulateProduct.initUpdateCumulateAccountMap(updateCumulateAccountMap);
    }
}
