/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2018 All Rights Reserved.
 */
package com.agtech.pointprod.limit.service.domain.common.convert;

import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public interface Converter<A, B> {

    /**
     * 逆向转换
     * 从存储层看，逆转就是往业务层模型转换
     * @param b the b
     * @return the a
     */
    default A doBackward(B b){
        throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
    }

    /**
     * 正向转换
     * 从存储层看，正转就是往存储层模型转换
     * @param a the a
     * @return the b
     */
    default B doForward(A a){
        throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
    }

    /**
     * List转换的默认实现
     *
     * @param fromList
     * @return
     */
    default List<A> doBackwardList(List<B> fromList){
        List<A> toList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(fromList)){
            fromList.forEach(item -> toList.add(doBackward(item)));
        }
        return toList;
    }


    /**
     * List转换的默认实现
     *
     * @param fromList
     * @return
     */
    default List<B> doForwardList(List<A> fromList){
        List<B> toList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(fromList)){
            fromList.forEach(item -> toList.add(doForward(item)));
        }
        return toList;
    }
}
