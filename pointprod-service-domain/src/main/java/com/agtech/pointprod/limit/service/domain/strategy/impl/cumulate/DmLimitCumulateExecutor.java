package com.agtech.pointprod.limit.service.domain.strategy.impl.cumulate;

import com.agtech.common.lang.money.MultiCurrencyMoneyUtil;
import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.domain.strategy.accumulate.AbstractLimitCumulateStrategyExecutor;
import org.springframework.stereotype.Component;

@Component
public class DmLimitCumulateExecutor extends AbstractLimitCumulateStrategyExecutor {

    @Override
    public LimitTypeEnum getStrategy() {
        return LimitTypeEnum.DM;
    }

    @Override
    public CumulateAccount buildCumulateAccount(AccumulateTask accumulateTask) {
        return CumulateAccount.buildAmountCumulateAccount(LimitTypeEnum.DM,
                accumulateTask.getBizTime(),
                accumulateTask.getAmount().getCurrency().getCurrencyCode(),
                accumulateTask.getAmount().getAmount().toString(),
                accumulateTask.getSceneCode().getCode(),
                accumulateTask.getLimitTarget().getPrincipalId(),
                accumulateTask.getLimitTarget().getPrincipalType().name());
    }

    @Override
    protected void doMergeSingeCumulateAccount(CumulateAccount existCumulateAccount, CumulateAccount newCumulateAccount) {
        existCumulateAccount.setAmount(MultiCurrencyMoneyUtil.add(existCumulateAccount.getAmount(),newCumulateAccount.getAmount()));
    }
}
