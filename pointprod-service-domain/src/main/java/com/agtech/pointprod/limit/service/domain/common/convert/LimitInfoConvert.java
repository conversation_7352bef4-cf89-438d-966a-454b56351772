package com.agtech.pointprod.limit.service.domain.common.convert;

import com.agtech.pointprod.limit.service.facade.dto.enums.ExceedStatusEnum;
import com.agtech.pointprod.limit.service.domain.model.helper.LimitInfo;
import com.agtech.pointprod.limit.service.facade.dto.LimitInfoDTO;
import com.agtech.common.lang.money.MultiCurrencyMoney;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Objects;

@Mapper(componentModel = "spring")
public interface LimitInfoConvert extends Converter<LimitInfoDTO, LimitInfo> {

    LimitInfoConvert INSTANCE = Mappers.getMapper(LimitInfoConvert.class);

    @Override
    default LimitInfoDTO doBackward(LimitInfo limitInfo){
        if (Objects.isNull(limitInfo)) {
            return null;
        }
        LimitInfoDTO limitInfoDTO = new LimitInfoDTO();
        limitInfoDTO.setIntervalAmount(limitInfo.getIntervalAmount());
        // Set remainAmount to 0 if it's negative
        MultiCurrencyMoney remainAmount = limitInfo.getRemainAmount();
        if (remainAmount != null && remainAmount.getAmount().signum() < 0) {
            limitInfoDTO.setRemainAmount(new MultiCurrencyMoney("0", remainAmount.getCurrencyCode()));
        } else {
            limitInfoDTO.setRemainAmount(remainAmount);
        }
        limitInfoDTO.setIntervalCount(limitInfo.getIntervalCount());
        // Set remainCount to 0 if it's negative
        Long remainCount = limitInfo.getRemainCount();
        if (remainCount != null && remainCount < 0) {
            limitInfoDTO.setRemainCount(0L);
        } else {
            limitInfoDTO.setRemainCount(remainCount);
        }
        limitInfoDTO.setAmountExceedStatus(ExceedStatusEnum.getByName(limitInfo.getAmountExceedStatus()));
        limitInfoDTO.setCountExceedStatus(ExceedStatusEnum.getByName(limitInfo.getCountExceedStatus()));
        if (Objects.nonNull(limitInfo.getStrictestMaxAmountLimit())) {
            limitInfoDTO.setStrictestMaxAmountLimit(AmountLimitConvert.INSTANCE.doBackward(limitInfo.getStrictestMaxAmountLimit()));
        }
        if (Objects.nonNull(limitInfo.getStrictestMinAmountLimit())) {
            limitInfoDTO.setStrictestMinAmountLimitDTO(AmountLimitConvert.INSTANCE.doBackward(limitInfo.getStrictestMinAmountLimit()));
        }
        limitInfoDTO.setStrictestMaxCountLimit(CountLimitConvert.INSTANCE.doBackward(limitInfo.getStrictestMaxCountLimit()));
        limitInfoDTO.setAmountLimits(AmountLimitConvert.INSTANCE.doBackwardList(limitInfo.getAmountLimits()));
        limitInfoDTO.setCountLimits(CountLimitConvert.INSTANCE.doBackwardList(limitInfo.getCountLimits()));
        return limitInfoDTO;
    }


}
