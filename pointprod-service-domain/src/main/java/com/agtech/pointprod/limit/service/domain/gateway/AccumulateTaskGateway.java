package com.agtech.pointprod.limit.service.domain.gateway;

import com.agtech.pointprod.limit.service.domain.gateway.condition.AccumulateTaskCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;

public interface AccumulateTaskGateway {

    AccumulateTask queryTask(AccumulateTaskCondition condition);

    void insert(AccumulateTask accumulateTask);

    void updateTaskStatus(AccumulateTask accumulateTask);
}
