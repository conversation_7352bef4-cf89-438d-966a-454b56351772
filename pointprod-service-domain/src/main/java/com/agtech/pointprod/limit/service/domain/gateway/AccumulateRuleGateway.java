package com.agtech.pointprod.limit.service.domain.gateway;


import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;

import java.util.List;


/**
 * <AUTHOR>
 * @version $ class:AccumulateProductRepository, v1.0 2025/06/15 16:07 xiaoming Exp $
 */
public interface AccumulateRuleGateway {

    /**
     * Create a new accumulate rule
     *
     * @param accumulateRule The accumulate rule to create
     * @return true if successful
     */
    boolean createAccumulateRule(AccumulateRule accumulateRule);
    
    /**
     * Update an existing accumulate rule
     *
     * @param accumulateRule The accumulate rule to update
     * @return true if successful
     */
    boolean updateAccumulateRule(AccumulateRule accumulateRule);
    
    /**
     * Get accumulate rule by rule ID
     *
     * @param ruleId The rule ID
     * @return The accumulate rule
     */
    AccumulateRule getAccumulateRuleByRuleId(String ruleId);

    /**
     * Query accumulate rules with pagination
     *
     * @param title Rule title (user level) filter
     * @param status Rule status filter
     * @param pageNum Page number
     * @param pageSize Page size
     * @return List of accumulate rules for the requested page
     */
    List<AccumulateRule> queryAccumulateRulesWithPagination(String title, String status, Integer pageNum, Integer pageSize);

    /**
     * Count accumulate rules with filters
     *
     * @param title Rule title (user level) filter
     * @param status Rule status filter
     * @return Total count of matching rules
     */
    Integer countAccumulateRules(String title, String status);
    
    /**
     * Logical delete an accumulate rule
     *
     * @param ruleId The rule ID
     * @return true if successful
     */
    boolean deleteAccumulateRule(String ruleId);
    
    /**
     * Disable other rules with the same title
     *
     * @param title The rule title (user level)
     * @param excludeRuleId The rule ID to exclude
     * @return true if successful
     */
    boolean disableOtherAccumulateRules(String title);
    
    /**
     * Check if this is the last enabled rule for this level
     * 
     * @param title Rule title (user level)
     * @return true if this is the last enabled rule
     */
    boolean isLastEnabledAccumulateRule(String title);

    /**
     * 检查同等级下是否存在有效的规则
     * @param title
     * @return
     */
    boolean isAccumulateRuleByLevelAndValidExists(String title);
}

