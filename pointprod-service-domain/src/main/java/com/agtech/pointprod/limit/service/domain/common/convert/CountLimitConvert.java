package com.agtech.pointprod.limit.service.domain.common.convert;

import com.agtech.pointprod.limit.service.domain.model.helper.CountLimit;
import com.agtech.pointprod.limit.service.facade.dto.CountLimitDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface CountLimitConvert extends Converter<CountLimitDTO, CountLimit> {

    CountLimitConvert INSTANCE = Mappers.getMapper(CountLimitConvert.class);

    @Override
    default CountLimitDTO doBackward(CountLimit countLimit) {
        if(null == countLimit){
            return null;
        }
        CountLimitDTO baseLimitDTO = new CountLimitDTO();
        baseLimitDTO.setLimitTargetDTO(LimitTargetConvert.INSTANCE.doBackward(countLimit.getLimitTarget()));
        baseLimitDTO.setLimitType(countLimit.getLimitType());
        baseLimitDTO.setExceedStatus(countLimit.getExceedStatus());
        baseLimitDTO.setIntervalCount(countLimit.getIntervalCount());
        baseLimitDTO.setMaxCount(countLimit.getMaxCount());
        baseLimitDTO.setUsedCount(countLimit.getUsedCount());
        // Set remainCount to 0 if it's negative
        Long remainCount = countLimit.getRemainCount();
        if (remainCount != null && remainCount < 0) {
            baseLimitDTO.setRemainCount(0L);
        } else {
            baseLimitDTO.setRemainCount(remainCount);
        }
        return baseLimitDTO;
    }
}
