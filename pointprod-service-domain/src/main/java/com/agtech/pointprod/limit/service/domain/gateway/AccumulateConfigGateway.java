package com.agtech.pointprod.limit.service.domain.gateway;


import com.agtech.pointprod.limit.service.domain.gateway.condition.AccumulateConfigQueryCondition;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateConfig;

import java.util.List;


/**
 * <AUTHOR>
 * @version $ class:AccumulateProductRepository, v1.0 2025/06/15 16:07 xiaoming Exp $
 */
public interface AccumulateConfigGateway {

    /**
     * 查询限额配置
     * @param condition 查询条件
     * @return AccumulateProduct
     */
    List<AccumulateConfig> queryAccumulateConfigs(AccumulateConfigQueryCondition condition);


}

