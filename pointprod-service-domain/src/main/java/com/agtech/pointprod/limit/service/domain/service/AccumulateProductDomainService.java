package com.agtech.pointprod.limit.service.domain.service;

import com.agtech.pointprod.limit.service.domain.gateway.AccumulateProductGateway;
import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.strategy.LimitCumulateInvoker;
import com.agtech.pointprod.limit.service.domain.strategy.check.LimitCheckStrategyInvoker;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ class:AccumulateProductDomainServiceImpl, v1.0 2025/06/15 15:46 xiaoming Exp $
 */
@Service
public class AccumulateProductDomainService {

    @Resource
    private LimitCheckStrategyInvoker limitCheckStrategyInvoker;

    @Resource
    private LimitCumulateInvoker limitCumulateInvoker;

    @Resource
    private AccumulateProductGateway accumulateProductRepository;
    
    public void limitCheck(AccumulateProduct accumulateProduct) {
        // 查询账本
        accumulateProductRepository.loadCumulateAccount(accumulateProduct);
        // 校验并组装相关信息
        limitCheckStrategyInvoker.invokeLimitCheck(accumulateProduct);
    }

    
    @Transactional(rollbackFor = Throwable.class)
    public void accumulateWithoutCheck(AccumulateProduct accumulateProduct) {
        // 额控检查并累额
        limitCheck(accumulateProduct);
        // 构建相关信息
        limitCumulateInvoker.buildCumulateAccounts(accumulateProduct);
        accumulateProductRepository.store(accumulateProduct);
    }
    
}
