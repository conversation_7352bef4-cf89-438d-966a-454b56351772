package com.agtech.pointprod.limit.service.domain.ability;

import com.agtech.pointprod.limit.service.domain.common.utils.GroovyShellUtils;
import com.agtech.pointprod.limit.service.domain.extensionpoints.LimitValidatorExtensionPoints;
import com.agtech.pointprod.limit.service.domain.framework.extension.ExtensionPointsHolder;
import com.agtech.pointprod.limit.service.domain.model.AccumulateProduct;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateRule;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.AssertUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class AccumulateProductFilterAbility {

    @Resource
    private ExtensionPointsHolder extensionPointsHolder;

    public void filter(AccumulateProduct accumulateProduct) {
        // 1. 构建表达式需要的参数
        LimitValidatorExtensionPoints extensionPoint = extensionPointsHolder.getExtensionPoint(accumulateProduct, LimitValidatorExtensionPoints.class);
        if(null == extensionPoint){
            throw new PointProdBizException(PointProdBizErrorCodeEnum.EXTENSION_NOT_FOUND);
        }
        Map<String, Object> expressParamMap = extensionPoint.buildLimitRuleRunParma(accumulateProduct.getAccumulateTask().getLimitContext());
        // 2. 对规则进行过滤
        List<AccumulateRule> finalAccumulateRuleList = accumulateProduct.getAccumulateRules().stream()
                .filter(limitRule -> containsCurrency(limitRule, accumulateProduct.getAccumulateTask().getAmount().getCurrency().getCurrencyCode()))
                .filter(limitRule -> BooleanUtils.isTrue((Boolean) GroovyShellUtils.invoke(limitRule.getExpression(), expressParamMap))).collect(Collectors.toList());
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(finalAccumulateRuleList), PointProdBizErrorCodeEnum.RULE_NOT_MATCH);
        // 3. 对过滤规则中的amountRange币种进一步过滤
        filterRuleAmountRange(finalAccumulateRuleList, accumulateProduct.getAccumulateTask().getAmount().getCurrency().getCurrencyCode());
        accumulateProduct.refresh(finalAccumulateRuleList);
    }

    private boolean containsCurrency(AccumulateRule limitRule, String currencyCode) {
        return limitRule.getAmountRanges().stream().anyMatch(amountRange -> StringUtils.equals(amountRange.getMaxAmount().getCurrency().getCurrencyCode(), currencyCode));
    }

    private void filterRuleAmountRange(List<AccumulateRule> accumulateRuleList, String currencyCode) {
        accumulateRuleList.forEach(limitRule -> {
            filterCurrencyAmountRange(limitRule, currencyCode);
        });
    }

    private void filterCurrencyAmountRange(AccumulateRule limitRule, String currencyCode) {
        limitRule.setAmountRanges(limitRule.getAmountRanges().stream()
                .filter(amountRange -> StringUtils.equals(amountRange.getMinAmount().getCurrency().getCurrencyCode(), currencyCode))
                .collect(Collectors.toList()));
    }

}
