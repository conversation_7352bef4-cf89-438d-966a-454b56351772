package com.agtech.pointprod.limit.service.domain.strategy.impl.cumulate;

import com.agtech.pointprod.limit.service.facade.dto.enums.LimitTypeEnum;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.AccumulateTask;
import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateAccount;
import com.agtech.pointprod.limit.service.domain.strategy.accumulate.AbstractLimitCumulateStrategyExecutor;
import org.springframework.stereotype.Component;

@Component
public class DcLimitCumulateExecutor extends AbstractLimitCumulateStrategyExecutor {

    @Override
    public LimitTypeEnum getStrategy() {
        return LimitTypeEnum.DC;
    }

    @Override
    protected CumulateAccount buildCumulateAccount(AccumulateTask accumulateTask) {
        return CumulateAccount.buildCountCumulateAccount(LimitTypeEnum.DC,
                accumulateTask.getBizTime(),
                accumulateTask.getCount(),
                accumulateTask.getSceneCode().getCode(),
                accumulateTask.getLimitTarget().getPrincipalId(),
                accumulateTask.getLimitTarget().getPrincipalType().name());
    }

    @Override
    protected void doMergeSingeCumulateAccount(CumulateAccount existCumulateAccount, CumulateAccount newCumulateAccount) {
        existCumulateAccount.setCount(existCumulateAccount.getCount() + newCumulateAccount.getCount());
    }
}
