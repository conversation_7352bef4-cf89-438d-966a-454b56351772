package com.agtech.pointprod.limit.service.domain.common.convert;

import com.agtech.pointprod.limit.service.domain.model.domainmodel.CumulateLog;
import com.agtech.pointprod.limit.service.facade.dto.CumulateLogDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface AccumulateLogConvert extends Converter<CumulateLog, CumulateLogDTO> {

    AccumulateLogConvert INSTANCE = Mappers.getMapper(AccumulateLogConvert.class);

    @Override
    default CumulateLogDTO doForward(CumulateLog cumulateLog) {
        CumulateLogDTO vo = new CumulateLogDTO();
        vo.setLogId(cumulateLog.getLogId());
        vo.setCumulateAccountId(cumulateLog.getCumulateAccountId());
        vo.setPrincipalId(cumulateLog.getPrincipalId());
        vo.setPrincipalType(cumulateLog.getPrincipalType().name());
        vo.setSceneCode(cumulateLog.getSceneCode());
        vo.setBizNo(cumulateLog.getBizNo());
        vo.setBizTime(cumulateLog.getBizTime());
        vo.setAmount(cumulateLog.getAmount());
        vo.setCount(cumulateLog.getCount());
        return vo;
    }

}
