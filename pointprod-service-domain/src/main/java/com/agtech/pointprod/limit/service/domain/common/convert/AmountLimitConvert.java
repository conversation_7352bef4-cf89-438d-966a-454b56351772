package com.agtech.pointprod.limit.service.domain.common.convert;

import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.pointprod.limit.service.domain.model.helper.AmountLimit;
import com.agtech.pointprod.limit.service.facade.dto.AmountLimitDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface AmountLimitConvert extends Converter<AmountLimitDTO, AmountLimit> {

    AmountLimitConvert INSTANCE = Mappers.getMapper(AmountLimitConvert.class);

    @Override
    default AmountLimitDTO doBackward(AmountLimit amountLimit) {
        if(null == amountLimit){
            return null;
        }
        AmountLimitDTO baseLimitDTO = new AmountLimitDTO();
        baseLimitDTO.setLimitTargetDTO(LimitTargetConvert.INSTANCE.doBackward(amountLimit.getLimitTarget()));
        baseLimitDTO.setLimitType(amountLimit.getLimitType());
        baseLimitDTO.setIntervalAmount(amountLimit.getIntervalAmount());
        baseLimitDTO.setUsed(amountLimit.getUsed());
        baseLimitDTO.setMin(amountLimit.getMin());
        baseLimitDTO.setMax(amountLimit.getMax());
        MultiCurrencyMoney remainAmount = amountLimit.getRemain();
        if (remainAmount != null && remainAmount.getAmount().signum() < 0) {
            baseLimitDTO.setRemain(new MultiCurrencyMoney("0", remainAmount.getCurrencyCode()));
        } else {
            baseLimitDTO.setRemain(remainAmount);
        }
        baseLimitDTO.setExceedStatus(amountLimit.getExceedStatus());
        return baseLimitDTO;
    }

}
