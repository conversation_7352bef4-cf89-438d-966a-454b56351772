package com.agtech.pointprod.limit.service.domain.extensionpoints;

import com.agtech.pointprod.limit.service.domain.common.ext.ExtensionPointsDefinition;
import com.agtech.pointprod.limit.service.domain.model.LimitContext;

import java.util.Map;

public interface LimitValidatorExtensionPoints extends ExtensionPointsDefinition {
    Map<String, Object> buildLimitRuleRunParma(LimitContext context);

}
