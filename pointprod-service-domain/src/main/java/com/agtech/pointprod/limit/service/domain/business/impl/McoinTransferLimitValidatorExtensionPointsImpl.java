package com.agtech.pointprod.limit.service.domain.business.impl;

import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.limit.service.domain.model.LimitContext;
import com.agtech.pointprod.limit.service.facade.dto.enums.LimitSceneBizCodeEnum;
import com.agtech.pointprod.limit.service.domain.extensionpoints.LimitValidatorExtensionPoints;
import com.agtech.pointprod.limit.service.domain.model.BizCodeIdentity;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class McoinTransferLimitValidatorExtensionPointsImpl implements LimitValidatorExtensionPoints {

    /**
     * @see LimitSceneBizCodeEnum#MCOIN_TRANSFER_LIMIT
     */
    public static final String BIZ_CODE = LimitSceneBizCodeEnum.MCOIN_TRANSFER_LIMIT.getBizCode();

    @Override
    public boolean bizIdentity(BizCodeIdentity bizCodeIdentity) {
        return Objects.equals(bizCodeIdentity.getBizCode(), LimitSceneBizCodeEnum.MCOIN_TRANSFER_LIMIT);
    }

    @Override
    public Map<String, Object> buildLimitRuleRunParma(LimitContext context) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("sceneCode", context.getSceneCode());
        paramMap.put("userLevel",context.getBizInfo().get("userLevel"));
        paramMap.put("currentTime", ZonedDateUtil.now());
        return paramMap;
    }

}
