package com.agtech.pointprod.order.service.domain.service;

import com.agtech.pointprod.order.service.domain.model.OrderStatusChangeMessage;
import com.agtech.pointprod.order.service.domain.model.Payment;
import com.agtech.pointprod.order.service.facade.dto.req.PaymentReq;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;

/**
 * 支付应用服务接口
 * 协调Payment聚合根和其他领域服务完成支付业务用例
 * 
 * <AUTHOR>
 */
public interface PaymentApplicationService {
    

    /**
     * 锁定订单并创建支付聚合
     * 
     * @param request 支付请求
     * @param userId 用户ID
     * @return 支付聚合
     */
    Payment lockOrderAndCreatePayment(String orderId, String userId);
    
    /**
     * 查询并设置用户信息
     * 
     * @param payment 支付聚合
     * @param request 支付请求
     * @param userId 用户ID
     */
    void queryAndSetUserInfo(Payment payment, PaymentReq request, String userId);
    
    /**
     * 校验支付条件
     * 
     * @param payment 支付聚合
     */
    void validatePaymentConditions(Payment payment);
    
    /**
     * 校验支付查詢条件
     * 
     * @param payment 支付聚合
     */
    void validatePaymentQueryConditions(Payment payment);
    
    /**
     * 执行积分转账
     * 
     * @param payment 支付聚合
     * @return 转账结果
     */
    McoinTransferResult executeTransfer(Payment payment);
    
    /**
     * 积分转账查詢
     * 
     * @param payment 支付聚合
     * @return 转账结果
     */
    McoinTransferResult queryTransfer(Payment payment);
    
    /**
     * 处理支付成功后续操作
     * 
     * @param payment 支付聚合
     * @param userId 用户ID
     */
    void handlePaymentSuccess(Payment payment, String userId);
    
    /**
     * 处理支付失败后续操作
     * 
     * @param exception 异常信息
     * @param orderId 订单ID
     * @param userId 用户ID
     */
    void handlePaymentFailure(Exception exception, String orderId, String userId);
    
    /**
     * 发送订单状态变更消息
     * 
     * @param statusChangeMessage 状态变更消息
     * @return 消息ID
     */
    String sendOrderStatusChangeMessage(OrderStatusChangeMessage statusChangeMessage);
    
}