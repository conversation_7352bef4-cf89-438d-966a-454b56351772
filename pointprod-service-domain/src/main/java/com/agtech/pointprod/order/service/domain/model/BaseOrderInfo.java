package com.agtech.pointprod.order.service.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.agtech.common.domain.model.BaseDomainModel;
import com.agtech.common.domain.statemachine.StateMachine;
import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.common.lang.util.StringUtil;
import com.agtech.common.util.CollectionUtil;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.order.service.domain.common.context.OrderBizContextHolder;
import com.agtech.pointprod.order.service.domain.common.enums.AcceptOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.ActionCodeEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundModeEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundStageLevelEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundTypeEnum;
import com.agtech.pointprod.order.service.domain.common.enums.PayOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.statemachine.FundStateMachineManager;
import com.agtech.pointprod.order.service.domain.statemachine.StateBizType;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.AssertUtil;

/**
 * 基础订单信息
 * 
 * <AUTHOR>
 * @version $Id: BaseOrderInfo.java, v 0.1 2025年6月20日 17:22:38 zhongqiang Exp $
 */
public class BaseOrderInfo extends BaseDomainModel implements Serializable {

	private static final long serialVersionUID = -8423806510762411950L;

	/** fund order id */
	private String fundOrderId;

	private FundTypeEnum fundType;

	/** fund mode */
	private FundModeEnum fundMode;

	/** fund status */
	private FundOrderStatusEnum orderStatus;
	/**
	 * 订单名称
	 */
	private String title;

	/**
	 * 英文订单名称
	 */
	private String titleEn;

	private String actorUserId;

	/** fund amount */
	private MultiCurrencyMoney fundAmount;
	/**
	 * 收费金额
	 */
	private MultiCurrencyMoney chargeAmount;
	/**
	 * 税费
	 */
	private MultiCurrencyMoney taxAmount;

	/**
	 * 支付总额
	 */
	private MultiCurrencyMoney paidTotalAmount;
	/**
	 * 收款金额
	 */
	private MultiCurrencyMoney acceptTotalAmount;

	/** extend info */
	private OrderExtendInfo extendInfo;

	/** pay expiry time */
	private Date payExpiryTime;

	/** accept expiry time */
	private Date acceptExpiryTime;

	/** create time */
	private Date createdTime;

	/** requestId */
	private String requestId;

	/** hasRevoke */
	private Boolean hasRevoke;
	/**
	 * 完成时间
	 */
	private Date completeTime;

	/** 修改时间 */
	private Date modifiedTime;

	/**
	 * 是否自动受理
	 */
	private String autoAccept;
	/**
	 * 
	 */
	private FundStageLevelEnum stageLevel;
	/**
	 * 订单备注
	 */
	private String memo;
	/**
	 * 收款单据集合
	 */
	private List<AcceptOrderInfo> acceptOrderInfoList;
	/**
	 * 付款单据集合
	 */
	private List<PayOrderInfo> payOrderInfoList;

	/**
     * 订单环境信息
     */
	private FundOrderEnv fundOrderEnv;
	
	@Override
	protected StateMachine getStateMachine() {
		StateBizType bizType = StateBizType.TRANSFER_MCOIN;
		switch (getStageLevel()) {
		case ONE_STAGE:
			bizType = StateBizType.TRANSFER_MCOIN;
			break;
//            case PAY_CONFIRM:
//
//                switch (getProductMode()) {
//                    case ESCROW:
//                        bizType = StateBizType.ESCROW;
//                        break;
//                    case AUTH_CAPTURE:
//                    case AUTH_AGREEMENT_PAY:
//                        bizType = StateBizType.AUTH_CAPTURE;
//                        break;
//
//                    default:
//                        throw new AcquireCommonException(AqcResultCode.UN_SUPPORT_BUSINESS,
//                            "productMode=[" + getProductMode().getCode() + "]");
//                }
//                break;

		default:
			throw new PointProdBizException(PointProdBizErrorCodeEnum.UN_SUPPORT_BUSINESS);
		}
		return FundStateMachineManager.getStateMachine(bizType);
//		return null;
	}

	/**
	 * 获取当前状态
	 *
	 * @see com.agtech.common.domain.model.BaseDomainModel#getCurrentStatus()
	 */
	@Override
	protected String getCurrentStatus() {

		return orderStatus.getCode();
	}

	/**
	 * 模型状态签转
	 *
	 * <p>
	 * 模型状态签转，包括
	 * <ul>
	 * <li>签转操作路径合法性校验
	 * <li>实际状态迁转操作
	 * <ul>
	 *
	 * @param targetStatus
	 */
	@Override
	public void transferStatus(String targetStatus) {
		// 模型操作合法性验证
		acceptAction();

		// 模型状态签转合法性检查
		transferStatusCheck(targetStatus);

		// 模型状态实际迁转
		doTransferStatus(targetStatus);

	}

	/**
	 * 状态推进
	 *
	 * @see com.agtech.common.domain.model.BaseDomainModel#doTransferStatus(java.lang.String)
	 */
	@Override
	protected void doTransferStatus(String targetStatus) {

//		FundOrderStatusEnum currentStatus = FundOrderStatusEnum.getByCode(getCurrentStatus());

		// 状态迁转
		orderStatus = FundOrderStatusEnum.getByCode(targetStatus);

//        ActionLog actionLog = ActionLogUtil.buildActionLog(currentStatus, orderStatus,
//            acquirementId);
//
//        actionLogList.add(actionLog);
	}

	/**
	 * 数据完整性检查
	 *
	 * @see com.agtech.common.domain.model.BaseDomainModel#dataIntegrityCheck()
	 */
	@Override
	protected void dataIntegrityCheck() {

	}

	/**
	 * 状态平衡检查
	 *
	 * @see com.agtech.common.domain.model.BaseDomainModel#statusBalanceCheck()
	 */
	@Override
	protected void statusBalanceCheck() {

	}

	/**
	 * 余额平衡性检查
	 *
	 * @see com.agtech.common.domain.model.BaseDomainModel#amountBalanceCheck()
	 */
	@Override
	protected void amountBalanceCheck() {
	}

	/**
	 * 订单是否为SUCCESS
	 *
	 * @return 是否为SUCCESS
	 */
	public boolean isSuccess() {
		return FundOrderStatusEnum.getByCode(getCurrentStatus()) == FundOrderStatusEnum.SUCCESS;
	}

    /**
     * 订单是否关闭
     * 
     * @return 是否关闭
     */
    public boolean isClosed() {
        return FundOrderStatusEnum.getByCode(getCurrentStatus()) == FundOrderStatusEnum.CLOSE;
    }

	/**
	 * 付款人和收款人是否是同一人
	 * @return 是否是同一人
	 */
	public boolean isPayerAndPayeeSame() {
		if(StringUtil.isBlank(actorUserId) || CollectionUtil.isEmpty(this.getAcceptOrderInfoList())
				|| CollectionUtil.isEmpty(this.getPayOrderInfoList())){
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PARAMS_ERROR);
        }
		if(this.getAcceptOrderInfoList().stream().anyMatch(ao->ao.getPayee().getUserId().equals(actorUserId))){
            return true;
        }
		for(PayOrderInfo po : this.getPayOrderInfoList()){
			for (AcceptOrderInfo ao : this.getAcceptOrderInfoList()){
                if(ao.getPayee().getUserId().equals(po.getPayer().getUserId())){
                    return true;
                }
            }
        }
        return false;
    }


	public String getFundOrderId() {
		return fundOrderId;
	}

	public void setFundOrderId(String fundOrderId) {
		this.fundOrderId = fundOrderId;
	}

	public FundTypeEnum getFundType() {
		return fundType;
	}

	public void setFundType(FundTypeEnum fundType) {
		this.fundType = fundType;
	}

	public FundModeEnum getFundMode() {
		return fundMode;
	}

	public void setFundMode(FundModeEnum fundMode) {
		this.fundMode = fundMode;
	}

	public FundOrderStatusEnum getOrderStatus() {
		return orderStatus;
	}

	public void setStatus(FundOrderStatusEnum orderStatus) {
		this.orderStatus = orderStatus;
	}

	public MultiCurrencyMoney getFundAmount() {
		return fundAmount;
	}

	public void setFundAmount(MultiCurrencyMoney fundAmount) {
		this.fundAmount = fundAmount;
	}

	public MultiCurrencyMoney getChargeAmount() {
		return chargeAmount;
	}

	public void setChargeAmount(MultiCurrencyMoney chargeAmount) {
		this.chargeAmount = chargeAmount;
	}

	public MultiCurrencyMoney getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(MultiCurrencyMoney taxAmount) {
		this.taxAmount = taxAmount;
	}

	public MultiCurrencyMoney getPaidTotalAmount() {
		return paidTotalAmount;
	}

	public void setPaidTotalAmount(MultiCurrencyMoney paidTotalAmount) {
		this.paidTotalAmount = paidTotalAmount;
	}

	public OrderExtendInfo getExtendInfo() {
		return extendInfo;
	}

	public void setExtendInfo(OrderExtendInfo extendInfo) {
		this.extendInfo = extendInfo;
	}

	public Date getPayExpiryTime() {
		return payExpiryTime;
	}

	public void setPayExpiryTime(Date payExpiryTime) {
		this.payExpiryTime = payExpiryTime;
	}

	public Date getAcceptExpiryTime() {
		return acceptExpiryTime;
	}

	public void setAcceptExpiryTime(Date acceptExpiryTime) {
		this.acceptExpiryTime = acceptExpiryTime;
	}

	public Date getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public Boolean getHasRevoke() {
		return hasRevoke;
	}

	public void setHasRevoke(Boolean hasRevoke) {
		this.hasRevoke = hasRevoke;
	}

	public Date getCompleteTime() {
		return completeTime;
	}

	public void setCompleteTime(Date completeTime) {
		this.completeTime = completeTime;
	}

	public String getAutoAccept() {
		return autoAccept;
	}

	public void setAutoAccept(String autoAccept) {
		this.autoAccept = autoAccept;
	}

	public FundStageLevelEnum getStageLevel() {
		return stageLevel;
	}

	public void setStageLevel(FundStageLevelEnum stageLevel) {
		this.stageLevel = stageLevel;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTitleEn() {
		return titleEn;
	}

	public void setTitleEn(String titleEn) {
		this.titleEn = titleEn;
	}

	public String getActorUserId() {
		return actorUserId;
	}

	public void setActorUserId(String actorUserId) {
		this.actorUserId = actorUserId;
	}

	public MultiCurrencyMoney getAcceptTotalAmount() {
		return acceptTotalAmount;
	}

	public void setAcceptTotalAmount(MultiCurrencyMoney acceptTotalAmount) {
		this.acceptTotalAmount = acceptTotalAmount;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public void setOrderStatus(FundOrderStatusEnum orderStatus) {
		this.orderStatus = orderStatus;
	}

	public List<AcceptOrderInfo> getAcceptOrderInfoList() {
		return acceptOrderInfoList;
	}

	public void setAcceptOrderInfoList(List<AcceptOrderInfo> acceptOrderInfoList) {
		this.acceptOrderInfoList = acceptOrderInfoList;
	}

	public List<PayOrderInfo> getPayOrderInfoList() {
		return payOrderInfoList;
	}

	public void setPayOrderInfoList(List<PayOrderInfo> payOrderInfoList) {
		this.payOrderInfoList = payOrderInfoList;
	}

	public Date getModifiedTime() {
		return modifiedTime;
	}

	public void setModifiedTime(Date modifiedTime) {
		this.modifiedTime = modifiedTime;
	}

    public FundOrderEnv getFundOrderEnv() {
        return fundOrderEnv;
    }

    public void setFundOrderEnv(FundOrderEnv fundOrderEnv) {
        this.fundOrderEnv = fundOrderEnv;
    }



	/**
	 * 主单支付动作
	 *
	 * @param payOrderInfo
	 */
	public void tryPay(PayOrderInfo payOrderInfo) {
		// 金额明细处理
		payBaseAmountSummaryProcess(payOrderInfo);

		switch (getStageLevel()) {
		// 一阶段支付，直接推进到终态
		case ONE_STAGE:
			tryFinish();
			break;
		case TWO_STAGE:
			transferStatus(FundOrderStatusEnum.PAY_SUCCESS.getCode());
			break;
		default:
			throw new PointProdBizException(PointProdBizErrorCodeEnum.UN_SUPPORT_BUSINESS);
		}
	}

	/**
	 * 尝试将交易推到完成状态
	 * 终态业务场景：<br>
	 * <b>ONE_STAGE</b>
	 * <li>第一笔支付成功，将主单推到SUCCESS<br><br>
	 * <b>TWO_STAGE</b>
	 * <li>如果是最后一个获取金额，则主单推到SUCCESS
	 * <li>如果是超时void，有过收款成功，则主单推到SUCCESS
	 *
	 */
	public void tryFinish() {
		switch (getStageLevel()) {
		case ONE_STAGE:
			for (PayOrderInfo payOrderInfo : payOrderInfoList) {
				if(PayOrderStatusEnum.SUCCESS.equals(payOrderInfo.getPayStatus())) {
					/**
					 * 此分支下要保证交易成功，参见tryPay()
					 */
					//如果有支付成功的单据，推进主单状态为成功
					transferStatus(FundOrderStatusEnum.SUCCESS.getCode());
					return;
				}
			}
			// 如果没有支付成功的单据，推进主单状态为关闭
			transferStatus(FundOrderStatusEnum.CLOSE.getCode());
			return;
		case TWO_STAGE:
			for (AcceptOrderInfo acceptOrderInfo : acceptOrderInfoList) {
				// 如果有成收款单，推进主单状态为成功
				if(AcceptOrderStatusEnum.SUCCESS.equals(acceptOrderInfo.getAcceptStatus())) {
					transferStatus(FundOrderStatusEnum.SUCCESS.getCode());
					return;
				}
			}
			// 如果没有收款成功的单据，推进主单状态为关闭
			transferStatus(FundOrderStatusEnum.CLOSE.getCode());
			return;
		default:
			throw new PointProdBizException(PointProdBizErrorCodeEnum.UN_SUPPORT_BUSINESS);
		}
	}

	public void tryClose() {

		this.setModifiedTime(ZonedDateUtil.now());

		transferStatus(FundOrderStatusEnum.CLOSE.getCode());

	}

	private void payBaseAmountSummaryProcess(PayOrderInfo payOrderInfo) {
		// 获得样例币种实例
//        baseAmountSummary = BaseAmountSummary
//            .getBaseAmountSummaryInitByCurrency(payBill.getPayClause().getPayAmount());
//
//        //主单支付金额登记
//        baseAmountSummary.getBaseAmountSummaryClause().getPayAmount()
//            .addTo(payBill.getPayClause().getPayAmount());
//
//        //金额明细单参数填充
//        baseAmountSummary.setAcquirementId(getAcquirementId());
//        baseAmountSummary.setAcquireMode(getAcquireMode());
	}

	/**
     * 返回订单的支付时间，多种支付场景、多条支付明细，逻辑放到一块
     *
     * @return 订单的支付时间
     */
    public Date getPaymentTime() {
        Date payTime = null;
        if (CollectionUtil.isNotEmpty(payOrderInfoList)) {
            for (PayOrderInfo payOrderInfo : payOrderInfoList) {
                if (payOrderInfo.isSuccess()) {
                    payTime = payOrderInfo.getPayTime();
                    break;
                }
            }
        }
        return payTime;
    }

	@Override
	public void acceptAction() {
		 //获取对应的状态机
        StateMachine stateMachine = getStateMachine();
		ActionCodeEnum action = OrderBizContextHolder.getAction();
        //根据状态机判断是否接受当前操作
        boolean isValid = stateMachine.checkAction(getCurrentStatus(), action.getCode());
		AssertUtil.assertTrue(isValid, PointProdBizErrorCodeEnum.ACTION_NOT_ACCEPTED,
				"模型拒絕當前操作",
				"action[" + action.getCode() + "]  is not accepted ");
	}

	@Override
	public void transferStatusCheck(String targetStatus) {

		StateMachine stateMachine = getStateMachine();

		ActionCodeEnum action = OrderBizContextHolder.getAction();

		boolean isValid = stateMachine.checkTransition(getCurrentStatus(), action.getCode(), targetStatus);

		AssertUtil.assertTrue(isValid, PointProdBizErrorCodeEnum.ACTION_NOT_ACCEPTED,
				"拒絕狀態轉換",
				"model status transition illegal when order[" + this.getFundOrderId() + "] at status[" + getCurrentStatus() + "], and target status is[" + targetStatus + "]");
	}
}
