package com.agtech.pointprod.order.service.domain.gateway;

import java.util.Date;
import java.util.List;

import com.agtech.pointprod.order.service.domain.common.enums.FundFluxStatusEnum;
import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.FluxDetailInfo;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;

public interface PayGateway {


    /**
     * 分页查询资金支付记录列表
     *
     * @param userId 用户ID
     * @param subOrderType 资金流向类型 (PAY:转出, ACCEPT:收到)
     * @param startTime 查询开始时间
     * @param currentPage 当前页数
     * @param perPage 每页数量
     * @return 资金支付记录列表
     */
    List<String> queryFundPayList(String userId, String subOrderType, Date startTime, Integer currentPage, Integer perPage);

    /**
     * 统计用户的转赠记录总数
     * 
     * @param userId 用户ID
     * @param subOrderType 子订单类型 (PAY/ACCEPT)
     * @param startTime 查询开始时间
     * @return 记录总数
     */
    Long countTransferRecords(String userId, String subOrderType, Date startTime);

    /**
     * 更新资金支付记录状态
     * @param payIds 资金支付记录ID集合
     * @param fromStatus 旧状态
     * @param toStatus 新状态
     * @return 是否更新成功
     */
    boolean updateFundPayStatus(List<String> payIds, String fromStatus, String toStatus);


    /**
     * 创建资金流水ID
     * @param userId 用户ID
     * @return 资金流水ID
     */
    String createNewFundFluxId(String userId);

    /**
     * 创建付款单
     * @param payOrderInfo 付款单
     * @return 是否创建成功
     */
    boolean createPayOrder(PayOrderInfo payOrderInfo);

    /**
     * 创建收款单
     * @param acceptOrderInfo 收款单
     * @return 是否创建成功
     */
    boolean createAcceptOrder(AcceptOrderInfo acceptOrderInfo);

    /**
     * 创建资金流转记录
     * @param fluxDetailInfo 资金流转详情
     * @return 是否成功
     */
    boolean createFlux(FluxDetailInfo fluxDetailInfo);
    
    /**
     * 根据资金订单ID查询流转记录
     * @param fundOrderId 资金订单ID
     * @return 流转记录列表
     */
    List<FluxDetailInfo> queryFluxByFundOrderId(String fundOrderId);
    
    /**
     * 更新流转记录的资产流水ID和关联流水ID
     * @param fundFluxId 资金流转ID
     * @param assetFluxId 资产流水ID
     * @param relationFluxId 关联流水ID（交易对手的assetFluxId）
     * @return 是否成功
     */
    boolean updateFluxAssetFluxId(String fundFluxId, String assetFluxId, String relationFluxId);
    
    /**
     * 更新流转记录状态和完成时间
     * @param fundFluxId 资金流转ID
     * @param status 新状态
     * @param completeTime 完成时间
     * @return 是否成功
     */
    boolean updateFluxStatusAndCompleteTime(String fundFluxId, FundFluxStatusEnum status, java.util.Date completeTime);
}
