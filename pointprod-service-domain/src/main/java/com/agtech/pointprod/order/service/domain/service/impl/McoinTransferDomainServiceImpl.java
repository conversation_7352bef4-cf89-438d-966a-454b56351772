package com.agtech.pointprod.order.service.domain.service.impl;

import java.math.BigDecimal;
import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.service.McoinTransferDomainService;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.gateway.McoinAccountGateway;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 积分转账领域服务实现
 * 负责处理积分转账的核心业务逻辑
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class McoinTransferDomainServiceImpl implements McoinTransferDomainService {

    @Resource
    private McoinAccountGateway mcoinAccountGateway;

    @Override
    public McoinTransferResult executeOneToOneTransfer(BaseOrderInfo orderInfo, 
                                                     MPayUserInfo payerUserInfo, 
                                                     MPayUserInfo payeeUserInfo) {
        log.info("开始执行一对一积分转账, orderId={}", orderInfo.getFundOrderId());
        
        // 1. 校验转账前置条件
        validateTransferPreconditions(orderInfo, payerUserInfo, payeeUserInfo);
        
        // 2. 构建转账信息
        McoinTransferInfo transferInfo = buildTransferInfo(orderInfo);

        // 3. 执行转账
        McoinTransferResult transferResult = mcoinAccountGateway.transferPoint(
                transferInfo, payerUserInfo, payeeUserInfo);
        
        // 4. 校验转账结果
        validateTransferResult(transferResult, orderInfo.getFundOrderId());
        
        log.info("一对一积分转账执行完成, orderId={}, transferResult={}", 
                orderInfo.getFundOrderId(), transferResult);
        
        return transferResult;
    }

    

    @Override
    public McoinTransferResult queryOneToOneTransfer(BaseOrderInfo orderInfo) {
        String outOrderId = orderInfo.getFundOrderId();
        String payeeCustId = orderInfo.getPayOrderInfoList().get(0).getUserId();
        return mcoinAccountGateway.queryTransfer(outOrderId, payeeCustId);
    }



    @Override
    public void validateTransferPreconditions(BaseOrderInfo orderInfo, 
                                            MPayUserInfo payerUserInfo, 
                                            MPayUserInfo payeeUserInfo) {
        if (orderInfo == null) {
            log.info("转账订单信息不能为空");
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
        
        if (payerUserInfo == null) {
            log.info("付款人信息不能为空, orderId={}", orderInfo.getFundOrderId()); 
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
        
        if (payeeUserInfo == null) {
            log.info("收款人信息不能为空, orderId={}", orderInfo.getFundOrderId()); 
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
        
        if (orderInfo.getFundAmount() == null || orderInfo.getFundAmount().getAmount() == null) {
            log.info("转账金额不能为空, orderId={}", orderInfo.getFundOrderId()); 
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
        
        if (orderInfo.getFundAmount().getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("转账金额必须大于0, orderId={}", orderInfo.getFundOrderId()); 
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
        
        log.info("转账前置条件校验通过, orderId={}", orderInfo.getFundOrderId());
    }
    
    /**
     * 构建转账信息
     */
    private McoinTransferInfo buildTransferInfo(BaseOrderInfo orderInfo) {
        String orderId = orderInfo.getFundOrderId();
        // TODO 未考虑手续费
        BigDecimal point = orderInfo.getFundAmount().getAmount();
        Date tradeTime = new Date();
        
        return new McoinTransferInfo()
                .setOrderId(orderId)
                .setPoint(point)
                .setTradeTime(tradeTime)
                .setDescription(orderInfo.getTitle());
    }
    
    /**
     * 校验转账结果
     */
    private void validateTransferResult(McoinTransferResult transferResult, String orderId) {
        if (transferResult == null) {
            log.error("转账结果未知, orderId={}", orderId);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAY_RESULT_UNKNOWN);
        }
    }
}