package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

/**
 * An enumeration of fund back order status.
 * 
 * <AUTHOR>
 * @version $Id: FundBackOrderStatusEnum.java, v 0.1 2025年6月20日 15:24:32 zhongqiang Exp $
 */
public enum FundBackOrderStatusEnum {
	/** fund back order initial */
    INIT("INIT", "back order initial"),

    /** fund back order failed */
    FAILED("FAILED", "back order failed"),

    /** fund back order success */
    SUCCESS("SUCCESS", "back order success"),

    ;

    /** code */
    private String code;

    /** description */
    private String description;

    FundBackOrderStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get BackOrderStatus enum by code
     */
    public static FundBackOrderStatusEnum getByCode(String code) {
        for (FundBackOrderStatusEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Setter method for property code.
     *
     * @param code value to be assigned to property code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }

    /**
     * Setter method for property description.
     *
     * @param description value to be assigned to property description
     */
    public void setDescription(String description) {
        this.description = description;
    }
}
