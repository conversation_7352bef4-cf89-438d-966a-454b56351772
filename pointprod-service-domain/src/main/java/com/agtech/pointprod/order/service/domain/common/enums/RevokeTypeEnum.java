package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

/**
 * revoke type
 * 
 * <AUTHOR>
 * @version $Id: RevokeTypeEnum.java, v 0.1 2025年6月20日 15:21:32 zhongqiang Exp $
 */
public enum RevokeTypeEnum {

	/** revoke by payer */
	PAYER_REVOKE("PAYER_REVOKE", "payer"),

	/** revoke by payee */
	PAYEE_REVOKE("PAYEE_REVOKE", "payee"),

	/** revoke by accept timeout */
	ACCEPT_TIMEOUT("ACCEPT_TIMEOUT", "accept timeout"),

	/** revoke by accept failed */
	ACCEPT_FAILED("ACCEPT_FAILED", "accept failed"),

	;

	/** code */
	private String code;

	/** description */
	private String description;

	RevokeTypeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * get IdentifyIdType enum by code
	 */
	public static RevokeTypeEnum getByCode(String code) {
		for (RevokeTypeEnum type : values()) {
			if (StringUtil.equals(type.getCode(), code)) {
				return type;
			}
		}
		return null;
	}

	/**
	 * Getter method for property code.
	 *
	 * @return property value of code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * Setter method for property code.
	 *
	 * @param code value to be assigned to property code
	 */
	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * Getter method for property description.
	 *
	 * @return property value of description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * Setter method for property description.
	 *
	 * @param description value to be assigned to property description
	 */
	public void setDescription(String description) {
		this.description = description;
	}

}
