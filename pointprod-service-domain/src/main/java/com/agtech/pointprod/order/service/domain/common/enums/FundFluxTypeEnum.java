package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

import lombok.Getter;

/**
 * 资金交换类型枚举
 * <AUTHOR>
 */
@Getter
public enum FundFluxTypeEnum {
	/**
	 * 
	 */
    PAY("PAY", "付款"),
    ACCEPT("ACCEPT", "收款"),
    REVOKE("REVOKE", "撤销"),
    FUND_BACK("FUND_BACK", "资金退回");

	/** code */
    private String code;

    /** description */
    private String description;

    FundFluxTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get FundFluxType enum by code
     */
    public static FundFluxTypeEnum getByCode(String code) {
        for (FundFluxTypeEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }
}