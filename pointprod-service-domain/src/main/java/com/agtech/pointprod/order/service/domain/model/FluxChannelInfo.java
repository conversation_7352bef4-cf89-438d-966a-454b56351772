package com.agtech.pointprod.order.service.domain.model;

import java.io.Serializable;

import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.pointprod.order.service.domain.common.enums.AssetToolNameEnum;
import com.agtech.pointprod.order.service.domain.common.enums.PayMethodEnum;

/**
 * fund channel info
 * 
 * <AUTHOR>
 * @version $Id: FluxChannelInfo.java, v 0.1 2025年6月23日 10:52:34 zhongqiang Exp $
 */
public class FluxChannelInfo implements Serializable {

    private static final long  serialVersionUID = -3104681072496803188L;

    /** asset tool */
    private AssetToolNameEnum  assetTool;

    /** asset tool id */
    private String             assetToolId;

    /** account no. */
    private String             accountNo;

    /** pay amount */
    private MultiCurrencyMoney amount;

    /** pay method */
    private PayMethodEnum      payMethod;

    /**
     * Getter method for property assetTool.
     *
     * @return property value of assetTool
     */
    public AssetToolNameEnum getAssetTool() {
        return assetTool;
    }

    /**
     * Setter method for property assetTool.
     *
     * @param assetTool value to be assigned to property assetTool
     */
    public void setAssetTool(AssetToolNameEnum assetTool) {
        this.assetTool = assetTool;
    }

    /**
     * Getter method for property assetToolId.
     *
     * @return property value of assetToolId
     */
    public String getAssetToolId() {
        return assetToolId;
    }

    /**
     * Setter method for property assetToolId.
     *
     * @param assetToolId value to be assigned to property assetToolId
     */
    public void setAssetToolId(String assetToolId) {
        this.assetToolId = assetToolId;
    }

    /**
     * Getter method for property accountNo.
     *
     * @return property value of accountNo
     */
    public String getAccountNo() {
        return accountNo;
    }

    /**
     * Setter method for property accountNo.
     *
     * @param accountNo value to be assigned to property accountNo
     */
    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    /**
     * Getter method for property amount.
     *
     * @return property value of amount
     */
    public MultiCurrencyMoney getAmount() {
        return amount;
    }

    /**
     * Setter method for property amount.
     *
     * @param amount value to be assigned to property amount
     */
    public void setAmount(MultiCurrencyMoney amount) {
        this.amount = amount;
    }

    /**
     * Getter method for property payMethod.
     *
     * @return property value of payMethod
     */
    public PayMethodEnum getPayMethod() {
        return payMethod;
    }

    /**
     * Setter method for property payMethod.
     *
     * @param payMethod value to be assigned to property payMethod
     */
    public void setPayMethod(PayMethodEnum payMethod) {
        this.payMethod = payMethod;
    }

}
