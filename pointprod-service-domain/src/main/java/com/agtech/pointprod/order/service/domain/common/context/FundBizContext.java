package com.agtech.pointprod.order.service.domain.common.context;

import java.io.Serializable;
/**
 * 业务上下文
 * 
 * <AUTHOR>
 * @version $Id: FundBizContext.java, v 0.1 2025年6月23日 15:33:12 zhongqiang Exp $
 */
public class FundBizContext implements Serializable {

	private static final long serialVersionUID = 412540968746095573L;
	/**
	 * MPay的custId
	 */
	private String userId;
	/**
	 * 前端传递的语言
	 */
	private String lang = "zh-MO";

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getLang() {
		return lang;
	}

	public void setLang(String lang) {
		this.lang = lang;
	}

}
