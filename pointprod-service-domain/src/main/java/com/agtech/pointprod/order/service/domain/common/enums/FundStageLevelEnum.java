package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

/**
 * An enumeration of fund stage level.
 * 
 * <AUTHOR>
 * @version $Id: FundStageLevelEnum.java, v 0.1 2025年6月20日 15:16:18 zhongqiang Exp $
 */
public enum FundStageLevelEnum {

	/** one stage, just once payment */
	ONE_STAGE("ONE_STAGE", "one stage, just once payment"),

	/** two stage, include pay and accept */
	TWO_STAGE("TWO_STAGE", "two stage, include pay and accept"),

	;

	/** mode code */
	private String code;

	/** mode description */
	private String description;

	FundStageLevelEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * get FundMode enum by code
	 */
	public static FundStageLevelEnum getByCode(String code) {
		for (FundStageLevelEnum mode : values()) {
			if (StringUtil.equals(mode.getCode(), code)) {
				return mode;
			}
		}
		return null;
	}

	/**
	 * Getter method for property code.
	 *
	 * @return property value of code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * Setter method for property code.
	 *
	 * @param code value to be assigned to property code
	 */
	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * Getter method for property description.
	 *
	 * @return property value of description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * Setter method for property description.
	 *
	 * @param description value to be assigned to property description
	 */
	public void setDescription(String description) {
		this.description = description;
	}
}
