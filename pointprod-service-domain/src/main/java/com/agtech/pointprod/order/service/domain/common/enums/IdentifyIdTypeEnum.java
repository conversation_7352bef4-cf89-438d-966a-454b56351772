package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

/**
 * 身份id类型枚举
 * 
 * <AUTHOR>
 * @version $Id: IdentifyIdTypeEnum.java, v 0.1 2025年6月23日 11:19:58 zhongqiang Exp $
 */
public enum IdentifyIdTypeEnum {


	/** 手机号 */
	MOBILE_NO("MOBILE_NO", "手机号"),

    ;

    /** code */
    private String code;

    /** description */
    private String description;

    IdentifyIdTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get IdentifyIdType enum by code
     */
    public static IdentifyIdTypeEnum getByCode(String code) {
        for (IdentifyIdTypeEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Setter method for property code.
     *
     * @param code value to be assigned to property code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }

    /**
     * Setter method for property description.
     *
     * @param description value to be assigned to property description
     */
    public void setDescription(String description) {
        this.description = description;
    }


}
