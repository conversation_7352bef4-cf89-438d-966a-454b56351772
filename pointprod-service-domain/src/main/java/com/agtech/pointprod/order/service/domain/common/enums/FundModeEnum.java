package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;


/**
 * An enumeration of fund mode.
 * 
 * <AUTHOR>
 * @version $Id: FundModeEnum.java, v 0.1 2025年6月20日 15:17:33 zhongqiang Exp $
 */
public enum FundModeEnum {

	/** one to one fund mode */
	OTO("OTO", "one to one"),

	/** one to many fund mode */
	OTM("OTM", "one to many"),

	/** many to one fund mode */
	MTO("MTO", "many to one"),

	;

	/** mode code */
	private String code;

	/** mode description */
	private String description;

	FundModeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * get FundMode enum by code
	 */
	public static FundModeEnum getByCode(String code) {
		for (FundModeEnum mode : values()) {
			if (StringUtil.equals(mode.getCode(), code)) {
				return mode;
			}
		}
		return null;
	}

	/**
	 * Getter method for property code.
	 *
	 * @return property value of code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * Setter method for property code.
	 *
	 * @param code value to be assigned to property code
	 */
	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * Getter method for property description.
	 *
	 * @return property value of description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * Setter method for property description.
	 *
	 * @param description value to be assigned to property description
	 */
	public void setDescription(String description) {
		this.description = description;
	}
}
