package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

/**
 * An enumeration of accept order status.
 * 
 * <AUTHOR>
 * @version $Id: AcceptOrderStatusEnum.java, v 0.1 2025年6月20日 15:26:44 zhongqiang Exp $
 */
public enum AcceptOrderStatusEnum {

	/** accept order initial */
    INIT("INIT", "accept order initial"),

    /** accept order accept */
    ACCEPT("ACCEPT", "fund order pay success"),

    /** fund order close */
    CLOSE("CLOSE", "fund order close"),

    /** accept order success */
    SUCCESS("SUCCESS", "fund order success"),

    ;

    /** code */
    private String code;

    /** description */
    private String description;

    AcceptOrderStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get AcceptOrderStatus enum by code
     */
    public static AcceptOrderStatusEnum getByCode(String code) {
        for (AcceptOrderStatusEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Setter method for property code.
     *
     * @param code value to be assigned to property code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }

    /**
     * Setter method for property description.
     *
     * @param description value to be assigned to property description
     */
    public void setDescription(String description) {
        this.description = description;
    }
}
