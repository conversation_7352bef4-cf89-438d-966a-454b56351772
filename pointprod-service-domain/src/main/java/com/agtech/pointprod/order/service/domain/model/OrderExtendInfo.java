package com.agtech.pointprod.order.service.domain.model;

import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * 订单扩展信息
 * 
 * <AUTHOR>
 * @version $Id: OrderExtendInfo.java, v 0.1 2025年1月20日 OrderExtendInfo Exp $
 */
@Getter
@Setter
public class OrderExtendInfo {

    /**
     * 关闭订单信息
     */
    private ClosedMsg closedMsg;

    @Getter
    @Setter
    public static class ClosedMsg {
    
        private PointProdBizErrorCodeEnum errorCode;
        private String msgTw;
        private String msgEn;
    }
}