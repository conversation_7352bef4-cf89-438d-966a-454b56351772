package com.agtech.pointprod.order.service.domain.statemachine;

import java.util.HashMap;
import java.util.Map;

import com.agtech.common.domain.statemachine.StateMachine;
/**
 * 状态机管理器
 * 
 * <AUTHOR>
 * @version $Id: FundStateMachineManager.java, v 0.1 2025年6月24日 16:21:45 zhongqiang Exp $
 */
public class FundStateMachineManager {

	/** STATE_MAP */
	private static final Map<String, StateMachine> STATE_MAP = new HashMap<String, StateMachine>();

	static {

		// 积分转赠状态机
		STATE_MAP.put(StateBizType.TRANSFER_MCOIN.getCode(), OrderStateModel.getStateTransitionDef());

		// pay state machine
		STATE_MAP.put(StateBizType.PAY.getCode(), PayStateModel.getStateTransitionDef());

		// accept state machine
		STATE_MAP.put(StateBizType.ACCEPT.getCode(), AcceptStateModel.getStateTransitionDef());

	}

	/**
	 * 根据业务类型的产品码获取状态机
	 * 
	 * @param stateBizType 业务类型
	 * @return 状态机
	 */
	public static StateMachine getStateMachine(StateBizType stateBizType) {
		return STATE_MAP.get(stateBizType.getCode());
	}

}
