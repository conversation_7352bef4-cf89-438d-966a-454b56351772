package com.agtech.pointprod.order.service.domain.service.impl;

import javax.annotation.Resource;

import com.agtech.pointprod.order.service.domain.common.context.OrderBizContext;
import com.agtech.pointprod.order.service.domain.common.context.OrderBizContextHolder;
import com.agtech.pointprod.order.service.domain.common.enums.ActionCodeEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.OrderExtendInfo;
import com.agtech.pointprod.order.service.domain.model.OrderStatusChangeMessage;
import com.agtech.pointprod.order.service.domain.model.Payment;
import com.agtech.pointprod.order.service.domain.model.PaymentResultUnknownQuery;
import com.agtech.pointprod.order.service.domain.model.OrderExtendInfo.ClosedMsg;
import com.agtech.pointprod.order.service.domain.service.McoinTransferDomainService;
import com.agtech.pointprod.order.service.domain.service.OrderDomainService;
import com.agtech.pointprod.order.service.domain.service.PaymentApplicationService;
import com.agtech.pointprod.order.service.domain.service.PaymentLockDomainService;
import com.agtech.pointprod.order.service.domain.service.TransferProcessDomainService;
import com.agtech.pointprod.order.service.facade.dto.req.PaymentReq;
import com.agtech.pointprod.service.domain.common.enums.MessageQueueEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.common.enums.TaskResouceTypeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;
import com.agtech.pointprod.service.domain.model.TaskRetry;
import com.agtech.pointprod.service.domain.service.BlackListDomainService;
import com.agtech.pointprod.service.domain.service.MessageReliabilityDomainService;
import com.agtech.pointprod.service.domain.service.UserInfoQueryDomainService;

import lombok.extern.slf4j.Slf4j;

/**
 * 支付应用服务实现
 * 协调Payment聚合根和其他领域服务完成支付业务用例
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class PaymentApplicationServiceImpl implements PaymentApplicationService {

    @Resource
    private PaymentLockDomainService paymentLockDomainService;
    
    @Resource
    private MessageReliabilityDomainService messageReliabilityDomainService;
    
    @Resource
    private UserInfoQueryDomainService userInfoQueryDomainService;
    
    @Resource
    private McoinTransferDomainService mcoinTransferDomainService;
    
    @Resource
    private TransferProcessDomainService transferProcessDomainService;

    @Resource
    private BlackListDomainService blackListDomainService;
    
    @Resource
    private OrderDomainService orderDomainService;

    @Override
    public Payment lockOrderAndCreatePayment(String orderId, String userId) {
        // 使用PaymentLockDomainService锁定订单
        BaseOrderInfo orderInfo = paymentLockDomainService.lockOrderForPayment(orderId, userId);
        
        // 创建Payment聚合根
        Payment payment = Payment.create(orderInfo, blackListDomainService);

        // 锁单
        payment.lockOrder();
        
        log.info("订单锁定成功并创建Payment聚合根, orderId={}", orderInfo.getFundOrderId());
        return payment;
    }

    @Override
    public void queryAndSetUserInfo(Payment payment, PaymentReq request, String userId) {
        log.info("开始查询并设置用户信息, orderId={}, userId={}", payment.getOrderInfo().getFundOrderId(), userId);
        try {
            // 1. 异步查询用户信息
            UserInfoQueryDomainService.UserInfoQueryResult queryResult = 
                    userInfoQueryDomainService.queryUserInfoAsync(
                            payment.getOrderInfo(), 
                            request.getOrderId(), 
                            request.getSecurityId());
            
            // 2. 等待异步查询完成并获取结果
            UserInfoQueryDomainService.SyncUserInfoResult syncResult = 
                    userInfoQueryDomainService.waitForResults(queryResult);
            
            // 3. 将查询结果设置到Payment聚合根
            payment.setUserInfo(syncResult.getPayerUserInfos(), syncResult.getPayeeUserInfos());
            
            payment.setMcoinAccounts(syncResult.getPayerMcoinAccounts(),syncResult.getPayeeMcoinAccounts());
            
            // 4. 设置安全校验结果
            payment.setSecurityValidations(syncResult.getSecurityValidations());
            
            log.info("用户信息查询并设置完成, orderId={}, 付款人数量={}, 收款人数量={}", 
                    payment.getOrderInfo().getFundOrderId(), 
                    syncResult.getPayerUserInfos().size(),
                    syncResult.getPayeeUserInfos().size());
                    
        } catch (PointProdBizException e) {
            payment.markPaymentFailed("查询用户信息失败: " + e.getMessage());
            // 业务异常直接抛出，保持原有错误码和错误信息
            log.error("查询并设置用户信息业务异常, orderId={}, userId={}, errorCode={}, errorMsg={}", 
                    payment.getOrderInfo().getFundOrderId(), userId, 
                    e.getResultCode() != null ? e.getResultCode().getResultCode() : "UNKNOWN", 
                    e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            payment.markPaymentFailed("查询用户信息系统异常: " + e.getMessage());
            // 其他系统异常，使用更具体的错误码
            log.error("查询并设置用户信息系统异常, orderId={}, userId={}, exceptionType={}", 
                    payment.getOrderInfo().getFundOrderId(), userId, e.getClass().getSimpleName(), e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
    }

    @Override
    public void validatePaymentConditions(Payment payment) {
        // 使用Payment聚合根的一对一转账校验方法
        payment.validateConditions();
        
        log.info("一对一转账条件校验通过, orderId={}", payment.getOrderInfo().getFundOrderId());
    }

    @Override
    public void validatePaymentQueryConditions(Payment payment) {
        payment.validateQueryConditions();
        
        log.info("一对一转账查询条件校验通过, orderId={}", payment.getOrderInfo().getFundOrderId());
    }

    @Override
    public McoinTransferResult executeTransfer(Payment payment) {
        log.info("开始执行转账, orderId={}", payment.getOrderInfo().getFundOrderId());
        
        try {
            // 获取付款人和收款人信息（一对一转账场景）
            if (payment.getPayerUserInfos().isEmpty() || payment.getPayeeUserInfos().isEmpty()) {
                log.error("付款人或收款人信息为空, orderId={}", payment.getOrderInfo().getFundOrderId());
                throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
            }

            // 转账发起处理
            transferProcessDomainService.processOneToOneTransferInitiation(
                payment.getOrderInfo(),
                payment.getPayerUserInfos().get(0),
                payment.getPayeeUserInfos().get(0),
                payment.getPayerMcoinAccounts().get(0),
                payment.getPayeeMcoinAccounts().get(0)
            );

            // 调用领域服务执行一对一转账
            McoinTransferResult transferResult = mcoinTransferDomainService.executeOneToOneTransfer(
                    payment.getOrderInfo(),
                    payment.getPayerUserInfos().get(0),
                    payment.getPayeeUserInfos().get(0)
            );
            
            // 将转账结果设置到Payment聚合根
            payment.executeTransfer(transferResult);
            
            log.info("转账执行完成, orderId={}, transferResult={}", 
                    payment.getOrderInfo().getFundOrderId(), transferResult);
            
            return transferResult;
            
        } catch (PointProdBizException e) {
            // 标记支付失败
            payment.markPaymentFailed("转账执行失败: " + e.getMessage());
            log.error("转账执行失败, orderId={}, errorCode={}, errorMsg={}", 
                    payment.getOrderInfo().getFundOrderId(), 
                    e.getResultCode() != null ? e.getResultCode().getResultCode() : "UNKNOWN", 
                    e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            // 标记支付失败
            payment.markPaymentFailed("转账执行异常: " + e.getMessage());
            log.error("转账执行异常, orderId={}", payment.getOrderInfo().getFundOrderId(), e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAY_RESULT_UNKNOWN);
        }
    }

    

    @Override
    public McoinTransferResult queryTransfer(Payment payment) {
        log.info("开始查询转账结果, orderId={}", payment.getOrderInfo().getFundOrderId());
        // 调用领域服务执行一对一转账
        McoinTransferResult transferResult = mcoinTransferDomainService.queryOneToOneTransfer(
                payment.getOrderInfo()
        );
        
        // 将转账结果设置到Payment聚合根
        payment.executeTransfer(transferResult);
        
        log.info("转账执行完成, orderId={}, transferResult={}", 
                payment.getOrderInfo().getFundOrderId(), transferResult);
            
        return transferResult;
    }

    @Override
    public void handlePaymentSuccess(Payment payment, String userId) {
        // 标记支付成功
        payment.markPaymentSuccess();

        // 订单状态变更
        payment.getOrderInfo().setOrderStatus(FundOrderStatusEnum.SUCCESS);
        
        // 调用支付成功处理领域服务
        transferProcessDomainService.processOneToOneTransferSuccess(
                payment.getOrderInfo(),
                payment.getTransferResult()
        );
        
        log.info("支付成功处理完成, orderId={}, userId={}", payment.getOrderInfo().getFundOrderId(), userId);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void handlePaymentFailure(Exception exception, String orderId, String userId) {
        if (exception instanceof PointProdBizException &&
                (((PointProdBizException) exception)).getResultCode() == PointProdBizErrorCodeEnum.PAY_RESULT_UNKNOWN) {
            log.info("支付结果未知，等待处理, orderId={}, userId={}}",orderId, userId);
            PaymentResultUnknownQuery query = new PaymentResultUnknownQuery();
            query.setResourceId(orderId);
            query.setUserId(userId);
            TaskRetry taskRetry = messageReliabilityDomainService.ensureMessageDelivery(TaskResouceTypeEnum.MQ_DELIVERY_PAYMENT_RESULT_UNKNOWN_QUERY,
                    MessageQueueEnum.PAYMENT_RESULT_UNKNOWN_QUERY, query);
            if (taskRetry == null) {
                log.error("[payment-result-unknown-query] Failed to ensure message delivery for orderId: {}", orderId);
                return;
            }
            boolean sendMessageAndMarkDelivered = messageReliabilityDomainService.sendMessageAndMarkDelivered(taskRetry.getTaskRetryId(), query.getClass());
            if (!sendMessageAndMarkDelivered) {
                log.error("[payment-result-unknown-query] Failed to send message for messageId: {}", taskRetry.getTaskRetryId());
            }
        } else {
            // 需要关单的情况
            log.info("支付异常需要关单, orderId={}, userId={}, exception={}",
                    orderId, userId, exception.getMessage());
            try {
                log.info("开始执行订单关单, orderId={}, userId={}", orderId, userId);

                // 查询订单信息
                BaseOrderInfo orderInfo = orderDomainService.getFundOrderForUpdate(orderId, userId);
                if (orderInfo == null) {
                    log.error("订单不存在，无法关单, orderId={}", orderId);
                    return;
                }

                // 检查订单状态，只有待支付状态的订单才能关单
                if (orderInfo.isClosed()) {
                    log.info("订单已关闭，无需重复关单, orderId={}, status={}",
                            orderId, orderInfo.getOrderStatus().getCode());
                    return;
                }

                // 设置扩展信息
                OrderExtendInfo extendInfo = orderInfo.getExtendInfo();
                if (extendInfo == null) {
                    extendInfo = new OrderExtendInfo();
                }
                ClosedMsg closedMsg = new ClosedMsg();
                if (exception instanceof PointProdBizException) {
                    PointProdBizException bizException = (PointProdBizException) exception;
                    closedMsg.setErrorCode((PointProdBizErrorCodeEnum)bizException.getResultCode());
                } else {
                    closedMsg.setErrorCode(PointProdBizErrorCodeEnum.SYS_ERROR);
                }
                closedMsg.setMsgEn(closedMsg.getErrorCode().getErrorMessage());
                closedMsg.setMsgTw(closedMsg.getErrorCode().getErrorMessageTw());
                extendInfo.setClosedMsg(closedMsg);

                orderInfo.setExtendInfo(extendInfo);

                //初始化订单上下文
                OrderBizContext orderBizContext = new OrderBizContext();
                orderBizContext.setAction(ActionCodeEnum.PAY_ORDER_FAILED);
                OrderBizContextHolder.set(orderBizContext);
                // 执行关单操作
                boolean closeResult = orderDomainService.closeFundOrder(orderInfo);
                if (closeResult) {
                    log.info("订单关单成功, orderId={}", orderId);
                } else {
                    log.error("订单关单失败, orderId={}", orderId);
                }
            } catch (Exception ex) {
                log.error("执行订单关单操作失败, orderId={}, userId={}", orderId, userId, ex);
            } finally {
                OrderBizContextHolder.clear();
            }
        }
    }
    


    @Override
    public String sendOrderStatusChangeMessage(OrderStatusChangeMessage message) {
        // 保存订单状态变更消息
        TaskRetry taskRetry = messageReliabilityDomainService.ensureMessageDelivery(
            TaskResouceTypeEnum.MQ_DELIVERY_ORDER_STATUS_CHANGED,
            MessageQueueEnum.ORDER_STATUS_CHANGED,
            message
        );
        
        log.info("订单状态变更消息已保存, orderId={}, messageId={}", 
                message.getResourceId(), taskRetry.getTaskRetryId());
        return taskRetry.getTaskRetryId();
    }
}