package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

/**
 * An enumeration of revoke order status.
 * 
 * <AUTHOR>
 * @version $Id: RevokeOrderStatusEnum.java, v 0.1 2025年6月20日 15:20:39 zhongqiang Exp $
 */
public enum RevokeOrderStatusEnum {

	/** revoke order initial */
    INIT("INIT", "revoke order initial"),

    /** revoke order failed */
    FAILED("FAILED", "revoke order failed"),

    /** revoke order success */
    SUCCESS("SUCCESS", "revoke order success"),

    ;

    /** code */
    private String code;

    /** description */
    private String description;

    RevokeOrderStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get RevokeOrderStatus enum by code
     */
    public static RevokeOrderStatusEnum getByCode(String code) {
        for (RevokeOrderStatusEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Setter method for property code.
     *
     * @param code value to be assigned to property code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }

    /**
     * Setter method for property description.
     *
     * @param description value to be assigned to property description
     */
    public void setDescription(String description) {
        this.description = description;
    }
}
