package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.pointprod.order.service.facade.event.FundAction;

/**
 * 资金系统动作
 * 
 * <AUTHOR>
 * @version $Id: ActionCodeEnum.java, v 0.1 2025年6月27日 17:17:38 zhongqiang Exp $
 */
public enum ActionCodeEnum implements FundAction {

//	PAY_FAIL("PAY_FAIL", "支付失败", "payment failure"),
//	PAY("PAY", "发起支付", "pay"), 
//	ACCEPT("ACCEPT", "收款", "accept"), 
//	ORDER_CLOSE("ORDER_CLOSE", "订单关闭", "close an order"),
	
	/** 订单 */
	ORDER_CREATE("ORDER_CREATE", "订单创建", "order create"), 
	ORDER_QUERY("ORDER_QUERY", "订单查询", "order query"), 
	BASE_ORDER_SUCCESS("BASE_ORDER_SUCCESS", "主订单成功", "base order success"), 
	ORDER_REVOKE("ORDER_REVOKE", "撤销订单", "accept order revoke"), 
	
	PAY_RESOlVE("PAY_RESOlVE", "pay resolve", "pay resolve"),
	CHECK_PAYABLE("CHECK_PAYABLE", "check payable", "check payable"),
	
	/** 支付 */
	PAY_ORDER_SUCCESS("PAY_ORDER_SUCCESS", "支付单成功", "pay order success"), 
	PAY_ORDER_FAILED("PAY_ORDER_FAILED", "支付单失败", "pay order failed"), 
	PAY_TIMEOUT_CLOSE("PAY_TIMEOUT_CLOSE", "由于支付超时,主订单关闭", "base order close cause for pay timeout"), 
	PAY_ORDER_REPEAT_PAY("PAY_ORDER_REPEAT_PAY", "支付单重复支付", "pay order repeat pay"), 
	PAY_ORDER_INVALID_STATUS("PAY_ORDER_INVALID_STATUS", "支付单无效状态", "pay order invalid status"), 
	
	BASE_ORDER_PAY_SUCCESS("BASE_ORDER_PAY_SUCCESS", "base order pay success", "base order pay success"), 
	BASE_ORDER_PAY_FAILED("BASE_ORDER_PAY_FAILED", "base order pay failed", "base order pay failed"), 
	
	
	
	/**    收款单据action  */
	ACCEPT_ORDER_APPLY("ACCEPT_ORDER_APPLY", "收款单申请", "accept order apply"), 
	ACCEPT_ORDER_ACCEPTED("ACCEPT_ORDER_ACCEPTED", "收款单支付已受理", "accept order accepted by payment"), 
	ACCEPT_ORDER_SUCCESS("ACCEPT_ORDER_SUCCESS", "收款单收款成功", "accept order success"), 
	ACCEPT_ORDER_FAILED("ACCEPT_ORDER_FAILED", "收款单收款成功", "accept order failed"), 
	ACCEPT_TIMEOUT_CLOSE("ACCEPT_TIMEOUT_CLOSE", "收款单超时关单", "accept timeout close"),
	ACCEPT_PRE_FINISH("ACCEPT_PRE_FINISH", "主单成功", "base order success"),
	ACCEPT_FAILED_REVOKE("ACCEPT_FAILED_REVOKE", "收单失败撤销", "accept failed revoke"),
	
	
	/**  REVOKE    */
	REVOKE_ORDER_SUCCESS("REVOKE_ORDER_SUCCESS", "REVOKE_ORDER_SUCCESS", "revoke order success"), 
	REVOKE_ORDER_FAILED("REVOKE_ORDER_FAILED", "REVOKE_ORDER_FAILED", "revoke order failed"), 
	
	/**  FUND BACK    */
	FUND_BACK_ORDER_SUCCESS("FUND_BACK_ORDER_SUCCESS", "原路退成功", "fund back order success"), 
	FUND_BACK_ORDER_FAILED("FUND_BACK_ORDER_FAILED", "原路退失败", "fund back order failed"), 
	;

	private final String code;
	private final String chineseName;
	private final String description;

	private ActionCodeEnum(String code, String chineseName, String description) {
		this.code = code;
		this.chineseName = chineseName;
		this.description = description;
	}
	@Override
	public String getCode() {
		return this.code;
	}
	@Override
	public String getDescription() {
		return this.description;
	}

	public String getChineseName() {
		return this.chineseName;
	}

	public static ActionCodeEnum getByCode(String code) {
		for (ActionCodeEnum action : values()) {
			if (action.getCode().equals(code)) {
				return action;
			}
		}
		return null;
	}
}
