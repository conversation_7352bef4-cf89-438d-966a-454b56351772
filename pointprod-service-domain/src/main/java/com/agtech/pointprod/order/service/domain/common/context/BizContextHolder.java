package com.agtech.pointprod.order.service.domain.common.context;

/**
 * 业务上下文的本地线程hold类
 * 
 * <AUTHOR>
 * @version $Id: BizContextHolder.java, v 0.1 2025年6月23日 15:37:41 zhongqiang Exp $
 */
public class BizContextHolder {

	/** 本地线程 */
    private static ThreadLocal<FundBizContext> contextLocal = new ThreadLocal<FundBizContext>();

    /**
     * 获取操作上下文
     *
     * @return 业务上下文
     */
    public static FundBizContext get() {
        return contextLocal.get();
    }
    
    /**
     * 获取上下文中的语言
     * 
     * @return
     */
    public static String getLang() {
    	FundBizContext fundBizContext = contextLocal.get();
    	if(fundBizContext!=null) {
    		return fundBizContext.getLang();
    	}
    	return "zh-MO";
    }

    /**
     * 获取上下文中的用戶id
     *
     * @return 用户id
     */
    public static String getUserId() {
        FundBizContext fundBizContext = contextLocal.get();
        if(fundBizContext!=null) {
            return fundBizContext.getUserId();
        }
        return null;
    }

    /**
     * 设置资金上下文
     *
     * @param context 
     */
    public static void set(FundBizContext context) {
        contextLocal.set(context);
    }

    /**
     * 清理上下文
     */
    public static void clear() {

        contextLocal.remove();
    }

    /**
     * 初始化业务上下文
     * 
     */
    public static void init() {
        contextLocal.set(new FundBizContext());
    }


}
