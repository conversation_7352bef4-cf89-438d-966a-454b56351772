package com.agtech.pointprod.order.service.domain.service;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;

/**
 * 支付锁定领域服务
 * 负责订单锁定相关的业务逻辑
 * 
 * <AUTHOR>
 */
public interface PaymentLockDomainService {

    /**
     * 为支付锁定订单
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 锁定的订单信息
     */
    BaseOrderInfo lockOrderForPayment(String orderId, String userId);

}