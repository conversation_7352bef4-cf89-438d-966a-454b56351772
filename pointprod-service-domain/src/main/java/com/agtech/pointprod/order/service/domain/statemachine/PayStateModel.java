package com.agtech.pointprod.order.service.domain.statemachine;

import com.agtech.common.domain.statemachine.StateMachine;
import com.agtech.common.domain.statemachine.Transition;
import com.agtech.pointprod.order.service.domain.common.enums.ActionCodeEnum;
import com.agtech.pointprod.order.service.domain.common.enums.PayOrderStatusEnum;

/**
 * 支付单状态机
 * 
 * <AUTHOR>
 * @version $Id: PayStateModel.java, v 0.1 2025年6月24日 16:25:53 zhongqiang Exp $
 */
public class PayStateModel {

	/** 状态机 */
	private static final StateMachine STATE_TRANSITION_DEF = new StateMachine(new Transition[] {
			/** 支付失败 */
			new Transition(PayOrderStatusEnum.INIT.getCode(), ActionCodeEnum.PAY_ORDER_FAILED.getCode(), PayOrderStatusEnum.CLOSE.getCode()),

			/** 超时关单 */
			new Transition(PayOrderStatusEnum.INIT.getCode(), ActionCodeEnum.PAY_TIMEOUT_CLOSE.getCode(), PayOrderStatusEnum.CLOSE.getCode()),

			/** 支付成功 */
			new Transition(PayOrderStatusEnum.INIT.getCode(), ActionCodeEnum.PAY_ORDER_SUCCESS.getCode(), PayOrderStatusEnum.SUCCESS.getCode()) });

	/**
	 * 获取支付状态机
	 * 
	 * @return 支付状态机
	 */
	public static StateMachine getStateTransitionDef() {

		return STATE_TRANSITION_DEF;
	}
}
