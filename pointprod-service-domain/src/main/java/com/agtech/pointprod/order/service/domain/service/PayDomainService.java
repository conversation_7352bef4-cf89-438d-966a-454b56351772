package com.agtech.pointprod.order.service.domain.service;

import java.util.Date;
import java.util.List;

import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;

public interface PayDomainService {
    

    /**
     * 分页查询资金支付记录列表
     *
     * @param userId 用户ID
     * @param subOrderType 资金流向类型 (PAY:转出, ACCEPT:收到)
     * @param startTime 查询开始时间
     * @param currentPage 当前页数
     * @param perPage 每页数量
     * @return 资金支付记录列表
     */
    List<String> queryFundPayList(String userId, String subOrderType, Date startTime, Integer currentPage, Integer perPage);

    /**
     * 统计用户的转赠记录总数
     * 
     * @param userId 用户ID
     * @param subOrderType 子订单类型 (PAY/ACCEPT)
     * @param startTime 查询开始时间
     * @return 记录总数
     */
    Long countTransferRecords(String userId, String subOrderType, Date startTime);

    /**
     * 创建一对一资金流转（ACCEPT状态）
     * @param payOrderInfo 支付订单信息
     * @param acceptOrderInfo 收款订单信息
     * @param payerAccount 支付用户信息
     * @param payeeAccount 收款用户信息
     * @return 是否成功
     */
   boolean createOneToOneTransferFlux(PayOrderInfo payOrderInfo, AcceptOrderInfo acceptOrderInfo,
                                      McoinAccount payerAccount, McoinAccount payeeAccount);

    /**
     * 更新一对一资金流转状态为成功
     * @param fundOrderId 资金订单ID
     * @param transferResult 资金转账结果
     * @return 是否成功
     */
    boolean updateOneToOneTransferFluxToSuccess(String fundOrderId, McoinTransferResult transferResult);
}
