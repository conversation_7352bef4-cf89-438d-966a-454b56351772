package com.agtech.pointprod.order.service.domain.model;

import java.util.Date;

import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.service.domain.model.TaskData;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 订单状态变更消息领域对象
 * 用于封装订单状态变更时需要发送的消息内容
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class OrderStatusChangeMessage extends TaskData{
    /**
     * 订单状态
     */
    private FundOrderStatusEnum orderStatus;
    
    /**
     * 订单创建时间
     */
    private Date statusChangeTime;
    
    public static OrderStatusChangeMessage fromBaseOrderInfo(BaseOrderInfo baseOrderInfo) {
        OrderStatusChangeMessage message = new OrderStatusChangeMessage();
        message.setResourceId(baseOrderInfo.getFundOrderId());
        message.setUserId(baseOrderInfo.getActorUserId());
        message.setOrderStatus(baseOrderInfo.getOrderStatus());
        message.setStatusChangeTime(baseOrderInfo.getModifiedTime());
        return message;
    }
}