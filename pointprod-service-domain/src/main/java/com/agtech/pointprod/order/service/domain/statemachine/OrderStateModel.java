package com.agtech.pointprod.order.service.domain.statemachine;

import com.agtech.common.domain.statemachine.StateMachine;
import com.agtech.common.domain.statemachine.Transition;
import com.agtech.pointprod.order.service.domain.common.enums.ActionCodeEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;

/**
 * 订单状态机
 * 
 * <AUTHOR>
 * @version $Id: OrderStateModel.java, v 0.1 2025年6月24日 16:45:37 zhongqiang Exp $
 */
public class OrderStateModel {

	/** 状态机 */
	private static final StateMachine STATE_TRANSITION_DEF = new StateMachine(new Transition[] {
			/** 一阶段支付失败 */
			new Transition(FundOrderStatusEnum.INIT.getCode(), ActionCodeEnum.PAY_ORDER_FAILED.getCode(), FundOrderStatusEnum.CLOSE.getCode()),
			
			/** 一阶段支付成功 init-->success */
			new Transition(FundOrderStatusEnum.INIT.getCode(), ActionCodeEnum.PAY_ORDER_SUCCESS.getCode(), FundOrderStatusEnum.SUCCESS.getCode()),

			/** 支付单超时关单 */
			new Transition(FundOrderStatusEnum.INIT.getCode(), ActionCodeEnum.PAY_TIMEOUT_CLOSE.getCode(), FundOrderStatusEnum.CLOSE.getCode()),
			
			/** 二阶段付款人支付成功 */
			new Transition(FundOrderStatusEnum.INIT.getCode(), ActionCodeEnum.PAY_ORDER_SUCCESS.getCode(), FundOrderStatusEnum.PAY_SUCCESS.getCode()),
			/** 二阶段收款人收款成功,pay_success-->success */
			new Transition(FundOrderStatusEnum.PAY_SUCCESS.getCode(), ActionCodeEnum.ACCEPT_ORDER_SUCCESS.getCode(), FundOrderStatusEnum.SUCCESS.getCode()),
			
			/** 二阶段收款失败,pay_success-->close */
			new Transition(FundOrderStatusEnum.PAY_SUCCESS.getCode(), ActionCodeEnum.ACCEPT_ORDER_FAILED.getCode(), FundOrderStatusEnum.CLOSE.getCode())
	});

	/**
	 * 获取支付状态机
	 * 
	 * @return 支付状态机
	 */
	public static StateMachine getStateTransitionDef() {

		return STATE_TRANSITION_DEF;
	}


}
