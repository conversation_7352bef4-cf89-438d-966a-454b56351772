package com.agtech.pointprod.order.service.domain.statemachine;

import com.agtech.common.domain.statemachine.StateMachine;
import com.agtech.common.domain.statemachine.Transition;
import com.agtech.pointprod.order.service.domain.common.enums.AcceptOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.ActionCodeEnum;

/**
 * 收款单状态机
 * 
 * <AUTHOR>
 * @version $Id: AcceptStateModel.java, v 0.1 2025年6月24日 16:41:54 zhongqiang Exp
 *          $
 */
public class AcceptStateModel {

	/** 状态机 */
	private static final StateMachine STATE_TRANSITION_DEF = new StateMachine(new Transition[] {

			/** 一阶段付款方支付成功，收款方受理成功 */
			new Transition(AcceptOrderStatusEnum.INIT.getCode(), ActionCodeEnum.PAY_ORDER_SUCCESS.getCode(),
					AcceptOrderStatusEnum.SUCCESS.getCode()),
			/** 一阶段支付超时关单驱动收款单超时关单 */
			new Transition(AcceptOrderStatusEnum.INIT.getCode(), ActionCodeEnum.PAY_TIMEOUT_CLOSE.getCode(),
					AcceptOrderStatusEnum.CLOSE.getCode()),

			/** 2阶段超时关单 */
			new Transition(AcceptOrderStatusEnum.INIT.getCode(), ActionCodeEnum.ACCEPT_TIMEOUT_CLOSE.getCode(),
					AcceptOrderStatusEnum.CLOSE.getCode()),
			/** 2阶段收款单支付已受理 */
			new Transition(AcceptOrderStatusEnum.INIT.getCode(), ActionCodeEnum.ACCEPT_ORDER_ACCEPTED.getCode(),
					AcceptOrderStatusEnum.ACCEPT.getCode()),

			/** 2阶段收款单收款成功 */
			new Transition(AcceptOrderStatusEnum.ACCEPT.getCode(), ActionCodeEnum.ACCEPT_ORDER_SUCCESS.getCode(),
					AcceptOrderStatusEnum.SUCCESS.getCode()),
			/** 2阶段用户主动收款失败 */
			new Transition(AcceptOrderStatusEnum.ACCEPT.getCode(), ActionCodeEnum.ACCEPT_ORDER_FAILED.getCode(),
					AcceptOrderStatusEnum.CLOSE.getCode()) 
			});

	/**
	 * 获取支付状态机
	 * 
	 * @return 支付状态机
	 */
	public static StateMachine getStateTransitionDef() {

		return STATE_TRANSITION_DEF;
	}

}
