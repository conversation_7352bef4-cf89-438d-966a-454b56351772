package com.agtech.pointprod.order.service.domain.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum OrderTokenUseStatusEnum {
	/**
	 * 待使用
	 */
    WAITING_USE("WAITING_USE", "待使用"),
    USED("USED", "已使用")
    ;

    private final String value;

    private final String desc;

    OrderTokenUseStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
