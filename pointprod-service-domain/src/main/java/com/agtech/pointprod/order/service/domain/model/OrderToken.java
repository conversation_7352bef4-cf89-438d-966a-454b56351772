package com.agtech.pointprod.order.service.domain.model;

import com.agtech.pointprod.order.service.domain.common.enums.OrderTokenUseStatusEnum;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

/**
 * Table: order_token
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderToken {
    /**
     * Column: token_id
     * Type: VARCHAR(64)
     * Remark: token id
     */
    private String tokenId;

    /**
     * Column: user_id
     * Type: VARCHAR(64)
     * Remark: 用户id
     */
    private String userId;

    /**
     * Column: order_type
     * Type: VARCHAR(16)
     * Remark: 订单类型
     */
    private String orderType;

    /**
     * Column: token
     * Type: VARCHAR(64)
     * Remark: token
     */
    private String token;

    /**
     * Column: use_status
     * Type: VARCHAR(16)
     * Remark: 使用状态：WAITING_USE/USED
     */
    private String useStatus;

    /**
     * Column: gmt_create
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date gmtCreate;

    /**
     * Column: gmt_modified
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date gmtModified;

    public boolean isWaitingUse() {
        return OrderTokenUseStatusEnum.WAITING_USE.getValue().equals(useStatus);
    }
}