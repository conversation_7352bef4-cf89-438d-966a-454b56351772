package com.agtech.pointprod.order.service.domain.service;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;

/**
 * 积分转账领域服务
 * 负责处理积分转账的核心业务逻辑
 * 
 * <AUTHOR>
 */
public interface McoinTransferDomainService {
    
    /**
     * 执行一对一积分转账
     * 
     * @param orderInfo 订单信息
     * @param payerUserInfo 付款人信息
     * @param payeeUserInfo 收款人信息
     * @return 转账结果
     */
    McoinTransferResult executeOneToOneTransfer(BaseOrderInfo orderInfo, 
                                               MPayUserInfo payerUserInfo, 
                                               MPayUserInfo payeeUserInfo);

    /**
     * 一对一积分转账查詢
     * 
     * @param orderInfo 订单信息
     * @param payerUserInfo 付款人信息
     * @param payeeUserInfo 收款人信息
     * @return 转账结果
     */
    McoinTransferResult queryOneToOneTransfer(BaseOrderInfo orderInfo);

    
    /**
     * 校验转账前置条件
     * 
     * @param orderInfo 订单信息
     * @param payerUserInfo 付款人信息
     * @param payeeUserInfo 收款人信息
     */
    void validateTransferPreconditions(BaseOrderInfo orderInfo, 
                                     MPayUserInfo payerUserInfo, 
                                     MPayUserInfo payeeUserInfo);
}