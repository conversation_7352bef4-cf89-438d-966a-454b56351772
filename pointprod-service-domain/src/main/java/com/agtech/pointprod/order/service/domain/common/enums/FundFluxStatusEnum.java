package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

import lombok.Getter;

/**
 * 资金交换明细状态枚举
 * <AUTHOR>
 */
@Getter
public enum FundFluxStatusEnum {
	/**
	 * 支付已受理
	 */
    ACCEPT("ACCEPT", "支付已受理"),
    SUCCESS("SUCCESS", "支付成功"),
    FAILED("FAILED", "支付失败"),
    REFUND("REFUND", "退款");

	/** code */
    private String code;

    /** description */
    private String description;

    FundFluxStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get FundOrderStatus enum by code
     */
    public static FundFluxStatusEnum getByCode(String code) {
        for (FundFluxStatusEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }

}