package com.agtech.pointprod.order.service.domain.common.context;

import com.agtech.pointprod.order.service.domain.common.enums.ActionCodeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 业务上下文
 * 
 * <AUTHOR>
 * @version $Id: FundBizContext.java, v 0.1 2025年6月23日 15:33:12 zhongqiang Exp $
 */
@Getter
@Setter
public class OrderBizContext implements Serializable {

	private static final long serialVersionUID = 412540968746095577L;

	/**
	 * 动作枚举
	 */
	private ActionCodeEnum action;
}
