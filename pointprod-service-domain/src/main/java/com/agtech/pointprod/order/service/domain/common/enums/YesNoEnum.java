package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;


/**
 * <AUTHOR>
 */

public enum YesNoEnum {

    /**
     * 阶段等级
     */
    Yes("1", "Yes"),
    No("0", "0"),
    ;

	/** code */
    private String code;

    /** description */
    private String description;

    YesNoEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get YesNo enum by code
     */
    public static YesNoEnum getByCode(String code) {
        for (YesNoEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Setter method for property code.
     *
     * @param code value to be assigned to property code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }

    /**
     * Setter method for property description.
     *
     * @param description value to be assigned to property description
     */
    public void setDescription(String description) {
        this.description = description;
    }
}
