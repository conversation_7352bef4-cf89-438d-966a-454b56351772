package com.agtech.pointprod.order.service.domain.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.agtech.pointprod.order.service.domain.common.enums.AssetToolNameEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundFluxStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundFluxTypeEnum;
import com.agtech.pointprod.order.service.domain.common.enums.PayMethodEnum;
import com.agtech.pointprod.order.service.domain.gateway.PayGateway;
import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.FluxChannelInfo;
import com.agtech.pointprod.order.service.domain.model.FluxDetailInfo;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;
import com.agtech.pointprod.order.service.domain.service.PayDomainService;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class PayDomainServiceImpl implements PayDomainService {

    @Resource
    private PayGateway payGateway;

    @Override
    public List<String> queryFundPayList(String userId, String subOrderType, Date startTime, Integer currentPage, Integer perPage) {
        return payGateway.queryFundPayList(userId, subOrderType, startTime, currentPage, perPage);
    }

    @Override
    public Long countTransferRecords(String userId, String subOrderType, Date startTime) {
        return payGateway.countTransferRecords(userId, subOrderType, startTime);
    }

    @Override
    public boolean createOneToOneTransferFlux(PayOrderInfo payOrderInfo, AcceptOrderInfo acceptOrderInfo,
                                              McoinAccount payerAccount, McoinAccount payeeAccount) {
        String payerFluxId = payGateway.createNewFundFluxId(payOrderInfo.getUserId());
        String payeeFluxId = payGateway.createNewFundFluxId(acceptOrderInfo.getUserId());

        // 插入付款人资产（ACCEPT状态）
        FluxDetailInfo payerFlux = new FluxDetailInfo();
        payerFlux.setFundFluxId(payerFluxId);
        payerFlux.setFundOrderId(payOrderInfo.getFundOrderId());
        payerFlux.setSubOrderId(payOrderInfo.getPayOrderId());
        payerFlux.setActorRoleId(payOrderInfo.getUserId());
        payerFlux.setFundFluxType(FundFluxTypeEnum.PAY);
        payerFlux.setFluxStatus(FundFluxStatusEnum.ACCEPT);
        payerFlux.setRelatedFundFluxId(payeeFluxId);
        payerFlux.setFluxAcceptTime(new Date());
        FluxChannelInfo payerChannelInfo = new FluxChannelInfo();
        payerChannelInfo.setAccountNo(payerAccount.getAccountNo());
        payerChannelInfo.setAssetTool(AssetToolNameEnum.MCOIN);
        payerChannelInfo.setAmount(payOrderInfo.getPayAmount());
        payerChannelInfo.setPayMethod(PayMethodEnum.BALANCE);
        payerFlux.setChannelInfoList(Collections.singletonList(payerChannelInfo));
        boolean payerCreateResult = payGateway.createFlux(payerFlux);
        if (!payerCreateResult) {
            return false;
        }
        
        // 插入收款人资产（ACCEPT状态）
        FluxDetailInfo payeeFlux = new FluxDetailInfo();
        payeeFlux.setFundFluxId(payeeFluxId);
        payeeFlux.setFundOrderId(acceptOrderInfo.getFundOrderId());
        payeeFlux.setSubOrderId(acceptOrderInfo.getAcceptOrderId());
        payeeFlux.setActorRoleId(acceptOrderInfo.getUserId());
        payeeFlux.setFundFluxType(FundFluxTypeEnum.ACCEPT);
        payeeFlux.setFluxStatus(FundFluxStatusEnum.ACCEPT);
        payeeFlux.setRelatedFundFluxId(payerFluxId);
        payeeFlux.setFluxAcceptTime(new Date());
        FluxChannelInfo payeeChannelInfo = new FluxChannelInfo();
        payeeChannelInfo.setAccountNo(payeeAccount.getAccountNo());
        payeeChannelInfo.setAssetTool(AssetToolNameEnum.MCOIN);
        payeeChannelInfo.setAmount(payOrderInfo.getPayAmount());
        payeeChannelInfo.setPayMethod(PayMethodEnum.BALANCE);
        payeeFlux.setChannelInfoList(Collections.singletonList(payeeChannelInfo));
        return payGateway.createFlux(payeeFlux);
    }

    @Override
    public boolean updateOneToOneTransferFluxToSuccess(String fundOrderId, McoinTransferResult transferResult) {
        try {
            // 查询flux记录
            List<FluxDetailInfo> fluxList = payGateway.queryFluxByFundOrderId(fundOrderId);
            
            // 更新flux状态
            Date completeTime = new Date();
            for (FluxDetailInfo flux : fluxList) {
                if (FundFluxStatusEnum.ACCEPT.equals(flux.getFluxStatus())) {
                    // 更新状态为成功并设置完成时间
                    boolean updatedFluxStatusAndCompleteTime = payGateway.updateFluxStatusAndCompleteTime(
                            flux.getFundFluxId(), FundFluxStatusEnum.SUCCESS, completeTime);
                    if (!updatedFluxStatusAndCompleteTime) {
                        log.error("Failed to update flux status for fundFluxId: {}", flux.getFundFluxId());
                        return false;
                    }

                    // 根据flux类型更新对应的资产流水ID和关联流水ID
                    if (FundFluxTypeEnum.PAY.equals(flux.getFundFluxType())) {
                        // 付款人flux：assetFluxId是付款人订单ID，relationFluxId是收款人订单ID
                        boolean updated = payGateway.updateFluxAssetFluxId(flux.getFundFluxId(),
                                transferResult.getPayerOrderId(), transferResult.getPayeeOrderId());
                        if (!updated) {
                            log.error("Failed to update assetFluxId for fundFluxId: {}", flux.getFundFluxId());
                            return false;
                        }
                    } else if (FundFluxTypeEnum.ACCEPT.equals(flux.getFundFluxType())) {
                        // 收款人flux：assetFluxId是收款人订单ID，relationFluxId是付款人订单ID
                        boolean updated = payGateway.updateFluxAssetFluxId(flux.getFundFluxId(),
                                transferResult.getPayeeOrderId(), transferResult.getPayerOrderId());
                        if (!updated) {
                            log.error("Failed to update assetFluxId for fundFluxId: {}", flux.getFundFluxId());
                            return false;
                        }
                    }
                }
            }
            log.info("Successfully updated transfer flux to success status for fundOrderId: {}", fundOrderId);
            return true;
        } catch (Exception e) {
            log.error("Failed to update transfer flux to success status for fundOrderId: {}, error: {}", 
                    fundOrderId, e.getMessage(), e);
            return false;
        }
    }

}
