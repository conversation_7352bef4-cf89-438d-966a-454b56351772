package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;
import lombok.Getter;

/**
 * 资金类型枚举
 * 
 * <AUTHOR>
 * @version $Id: FundTypeEnum.java, v 0.1 2025年6月20日 16:19:35 zhongqiang Exp $
 */
@Getter
public enum FundTypeEnum {

	/** mcoin转赠 */
	TRANSFER_MCOIN("TRANSFER_MCOIN", "積分轉贈", "mCoin Transfer"),
	/** 群发红包 */
	GROUP_LUCKY_MONEY("GROUP_LUCKY_MONEY", "群發紅包", "group lucky money"),
	;

	/** code */
	private String code;

	/** description */
	private String description;

	/** description */
	private String descriptionEn;

	FundTypeEnum(String code, String description, String descriptionEn) {
		this.code = code;
		this.description = description;
		this.descriptionEn = descriptionEn;
	}

	/**
	 * get FundType enum by code
	 */
	public static FundTypeEnum getByCode(String code) {
		for (FundTypeEnum type : values()) {
			if (StringUtil.equals(type.getCode(), code)) {
				return type;
			}
		}
		return null;
	}

}
