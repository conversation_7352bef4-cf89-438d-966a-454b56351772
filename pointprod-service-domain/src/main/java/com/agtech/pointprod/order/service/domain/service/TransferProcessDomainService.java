package com.agtech.pointprod.order.service.domain.service;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;

/**
 * 转账处理领域服务
 * 负责处理转账前、转账中、转账后的完整业务逻辑，包括订单状态更新和积分流水创建
 * 
 * <AUTHOR>
 */
public interface TransferProcessDomainService {


    /**
     * 处理一对一转账发起支付前的处理逻辑
     * 
     * @param fundOrder 基础订单信息
     * @param payerMPayUserInfo 付款人用户信息
     * @param payeeMPayUserInfo 收款人用户信息
     * @param payerMcoinAccount 付款人积分账户
     * @param payeeMcoinAccount 收款人积分账户
     */
    void processOneToOneTransferInitiation(BaseOrderInfo fundOrder, 
                                       MPayUserInfo payerMPayUserInfo,
                                       MPayUserInfo payeeMPayUserInfo, 
                                       McoinAccount payerMcoinAccount,
                                       McoinAccount payeeMcoinAccount);


    /**
     * 处理一对一转账支付成功后的业务逻辑
     * 
     * @param fundOrder 基础订单信息
     * @param payerMPayUserInfo 付款人用户信息
     * @param payeeMPayUserInfo 收款人用户信息
     * @param payerMcoinAccount 付款人积分账户
     * @param payeeMcoinAccount 收款人积分账户
     * @param transferResult 转账结果
     */
    void processOneToOneTransferSuccess(BaseOrderInfo fundOrder, 
                                       McoinTransferResult transferResult);
}