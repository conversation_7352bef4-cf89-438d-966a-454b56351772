package com.agtech.pointprod.order.service.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.agtech.common.domain.model.BaseDomainModel;
import com.agtech.common.domain.statemachine.StateMachine;
import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.pointprod.order.service.domain.common.context.OrderBizContextHolder;
import com.agtech.pointprod.order.service.domain.common.enums.ActionCodeEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.PayOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.statemachine.FundStateMachineManager;
import com.agtech.pointprod.order.service.domain.statemachine.StateBizType;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.util.AssertUtil;

/**
 * 支付单
 * 
 * <AUTHOR>
 * @version $Id: PayOrderInfo.java, v 0.1 2025年6月20日 17:25:35 zhongqiang Exp $
 */
public class PayOrderInfo extends BaseDomainModel implements Serializable {

	private static final long serialVersionUID = 840658785357925956L;

	/** fund order id */
	private String fundOrderId;

	/** pay order id */
	private String payOrderId;

	/** payer info */
	private PayerParticipantInfo payer;

	/**
	 * pay order status
	 * {@link com.alipay.ap.fundcenter.common.service.facade.enums.PayOrderStatusEnum}
	 */
	private PayOrderStatusEnum payStatus;

	/** pay mount */
	private MultiCurrencyMoney payAmount;

	/** extend info */
	private String payExtendInfo;

	/** flux detail info list */
	private List<FluxDetailInfo> fluxDetailInfoList;
	
	/**
	 * 付款时间
	 */
	private Date payTime;
	
	/** create time */
	private Date createdTime;
	

	@Override
	protected StateMachine getStateMachine() {

		return FundStateMachineManager.getStateMachine(StateBizType.PAY);
	}

	@Override
	protected String getCurrentStatus() {
		return payStatus.getCode();
	}

	@Override
	protected void doTransferStatus(String targetStatus) {
		// 状态迁转
		payStatus = PayOrderStatusEnum.getByCode(targetStatus);
	}
	
	public void oneStagePaySuccess(BaseOrderInfo fundOrder, String payerUserId, MultiCurrencyMoney amount) {
		
		//添加支付单
//        fundOrder.addPayBill(this);

//        FundBackCheckResultCode fundBackCheckResult = fundOrder.fundBackCheck(
//        		payerUserId, amount);
//        //
//        if (fundBackCheckResult != FundBackCheckResultCode.CHECK_PASSED) {
//            //
//            fundBack(acquireBase, fundBackCheckResult, request);
//        } else {
            // 
//            payNormal(acquireBase, request);

            fundOrder.tryPay(this);
//        }
	}

	public void oneStagePayTimeout(BaseOrderInfo fundOrder) {

		tryClose();

		fundOrder.tryClose();

	}

	private void tryClose() {

		transferStatus(FundOrderStatusEnum.CLOSE.getCode());

	}

	@Override
	public void transferStatus(String targetStatus) {
		// 模型操作合法性验证
		acceptAction();

		// 模型状态签转合法性检查
		transferStatusCheck(targetStatus);

		// 模型状态实际迁转
		doTransferStatus(targetStatus);

	}

	@Override
	public void acceptAction() {
		//获取对应的状态机
		StateMachine stateMachine = getStateMachine();
		ActionCodeEnum action = OrderBizContextHolder.getAction();
		//根据状态机判断是否接受当前操作
		boolean isValid = stateMachine.checkAction(getCurrentStatus(), action.getCode());
		AssertUtil.assertTrue(isValid, PointProdBizErrorCodeEnum.ACTION_NOT_ACCEPTED,
				"模型拒絕當前操作",
				"action[" + action.getCode() + "]  is not accepted ");
	}

	@Override
	public void transferStatusCheck(String targetStatus) {

		StateMachine stateMachine = getStateMachine();

		ActionCodeEnum action = OrderBizContextHolder.getAction();

		boolean isValid = stateMachine.checkTransition(getCurrentStatus(), action.getCode(), targetStatus);

		AssertUtil.assertTrue(isValid, PointProdBizErrorCodeEnum.ACTION_NOT_ACCEPTED,
				"拒絕狀態轉換",
				"model status transition illegal when pay order [" + this.getPayOrderId() + "] at status[" + getCurrentStatus() + "], and target status is[" + targetStatus + "]");
	}
	
	/**
	 * 获取付款人用户id
	 */
	public String getUserId() {
		
		if(payer!=null) {
			return payer.getUserId();
		}
		return null;
	}
	
	
	

	public String getFundOrderId() {
		return fundOrderId;
	}

	public void setFundOrderId(String fundOrderId) {
		this.fundOrderId = fundOrderId;
	}

	/**
	 * Getter method for property payOrderId.
	 *
	 * @return property value of payOrderId
	 */
	public String getPayOrderId() {
		return payOrderId;
	}

	/**
	 * Setter method for property payOrderId.
	 *
	 * @param payOrderId value to be assigned to property payOrderId
	 */
	public void setPayOrderId(String payOrderId) {
		this.payOrderId = payOrderId;
	}

	/**
	 * Getter method for property payer.
	 *
	 * @return property value of payer
	 */
	public PayerParticipantInfo getPayer() {
		return payer;
	}

	/**
	 * Setter method for property payer.
	 *
	 * @param payer value to be assigned to property payer
	 */
	public void setPayer(PayerParticipantInfo payer) {
		this.payer = payer;
	}

	public PayOrderStatusEnum getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(PayOrderStatusEnum payStatus) {
		this.payStatus = payStatus;
	}

	/**
	 * Getter method for property payAmount.
	 *
	 * @return property value of payAmount
	 */
	public MultiCurrencyMoney getPayAmount() {
		return payAmount;
	}

	/**
	 * Setter method for property payAmount.
	 *
	 * @param payAmount value to be assigned to property payAmount
	 */
	public void setPayAmount(MultiCurrencyMoney payAmount) {
		this.payAmount = payAmount;
	}

	/**
	 * Getter method for property payExtendInfo.
	 *
	 * @return property value of payExtendInfo
	 */
	public String getPayExtendInfo() {
		return payExtendInfo;
	}

	/**
	 * Setter method for property payExtendInfo.
	 *
	 * @param payExtendInfo value to be assigned to property payExtendInfo
	 */
	public void setPayExtendInfo(String payExtendInfo) {
		this.payExtendInfo = payExtendInfo;
	}

	/**
	 * Getter method for property fluxDetailInfoList.
	 *
	 * @return property value of fluxDetailInfoList
	 */
	public List<FluxDetailInfo> getFluxDetailInfoList() {
		return fluxDetailInfoList;
	}

	/**
	 * Setter method for property fluxDetailInfoList.
	 *
	 * @param fluxDetailInfoList value to be assigned to property fluxDetailInfoList
	 */
	public void setFluxDetailInfoList(List<FluxDetailInfo> fluxDetailInfoList) {
		this.fluxDetailInfoList = fluxDetailInfoList;
	}

	public Date getPayTime() {
		return payTime;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}

	public Date getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
	}

	/**
	 * 判断当前支付单是否为成功的支付单
	 * 
	 * @return
	 */
	public boolean isSuccess() {
		
		return PayOrderStatusEnum.SUCCESS.equals(getPayStatus());
	}
	
	
}
