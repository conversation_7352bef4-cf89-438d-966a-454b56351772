package com.agtech.pointprod.order.service.domain.model;

import java.util.Date;
import java.util.List;

import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.service.domain.common.enums.MPayUserStatusEnum;
import com.agtech.pointprod.service.domain.common.enums.McoinUserStatusEnum;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;
import com.agtech.pointprod.service.domain.service.BlackListDomainService;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 支付聚合根
 * 封装支付相关的核心业务逻辑
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@Slf4j
public class Payment {
    
    /** 订单信息 */
    private BaseOrderInfo orderInfo;

    /** 付款人信息列表（支持多个付款人） */
    private List<MPayUserInfo> payerUserInfos;
    
    /** 收款人信息列表（支持多个收款人） */
    private List<MPayUserInfo> payeeUserInfos;
    
    /** 付款人积分账户列表（支持多个付款人） */
    private List<McoinAccount> payerMcoinAccounts;
    
    /** 收款人积分账户列表（支持多个收款人） */
    private List<McoinAccount> payeeMcoinAccounts;
    
    /** 安全校验结果列表 */
    private List<Boolean> securityValidations;
    
    /** 转账结果 */
    private McoinTransferResult transferResult;
    
    /** 支付状态 */
    private PaymentStatus status;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新时间 */
    private Date updateTime;
    
    /** 黑名单领域服务 */
    private BlackListDomainService blackListDomainService;
    

    
    /**
     * 支付状态枚举
     */
    public enum PaymentStatus {
        INITIALIZED("初始化"),
        LOCKED("已锁定"),
        VALIDATED("已校验"),
        TRANSFERRED("已转账"),
        SUCCESS("支付成功"),
        FAILED("支付失败");
        
        private final String description;
        
        PaymentStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 创建支付聚合（带依赖注入）
     * 
     * @param orderInfo 订单信息
     * @param blackListDomainService 黑名单领域服务
     * @return 支付聚合
     */
    public static Payment create(BaseOrderInfo orderInfo, BlackListDomainService blackListDomainService) {
        Payment payment = new Payment();
        payment.orderInfo = orderInfo;
        payment.status = PaymentStatus.INITIALIZED;
        payment.createTime = new Date();
        payment.updateTime = new Date();
        payment.blackListDomainService = blackListDomainService;
        return payment;
    }

    /**
     * 锁定订单
     */
    public void lockOrder() {
        validateStatus(PaymentStatus.INITIALIZED);
        
        if (orderInfo == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        
        this.status = PaymentStatus.LOCKED;
        this.updateTime = new Date();
        
        log.info("订单锁定成功, orderId={}, status={}", orderInfo.getFundOrderId(), status);
    }
    
    /**
     * 设置用户信息列表（支持多个用户）
     * 
     * @param payerUserInfos 付款人信息列表
     * @param payeeUserInfos 收款人信息列表
     */
    public void setUserInfo(List<MPayUserInfo> payerUserInfos, List<MPayUserInfo> payeeUserInfos) {
        validateStatus(PaymentStatus.LOCKED);
        
        if (payerUserInfos == null || payerUserInfos.isEmpty()) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_STATUS_INVALID);
        }
        
        if (payeeUserInfos == null || payeeUserInfos.isEmpty()) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_STATUS_INVALID);
        }
        
        // 校验每个用户信息
        for (MPayUserInfo payerUserInfo : payerUserInfos) {
            validatePayerUserStatus(payerUserInfo);
        }
        
        for (MPayUserInfo payeeUserInfo : payeeUserInfos) {
            validatePayeeUserStatus(payeeUserInfo);
        }
        
        this.payerUserInfos = payerUserInfos;
        this.payeeUserInfos = payeeUserInfos;
        
        this.updateTime = new Date();
        
        log.info("用户信息列表设置成功, 付款人数量={}, 收款人数量={}", 
                payerUserInfos.size(), payeeUserInfos.size());
    }

    /**
     * 设置积分账户信息列表（支持多个账户）
     * 
     * @param payerMcoinAccounts 付款人积分账户列表
     * @param payeeMcoinAccounts 收款人积分账户列表
     */
    public void setMcoinAccounts(List<McoinAccount> payerMcoinAccounts, List<McoinAccount> payeeMcoinAccounts) {
        if (payerMcoinAccounts == null || payerMcoinAccounts.isEmpty()) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_STATUS_INVALID);
        }
        
        if (payeeMcoinAccounts == null || payeeMcoinAccounts.isEmpty()) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_STATUS_INVALID);
        }
        
        // 校验每个积分账户
        for (McoinAccount payerAccount : payerMcoinAccounts) {
            validatePayerAccountStatus(payerAccount);
        }
        
        for (McoinAccount payeeAccount : payeeMcoinAccounts) {
            validatePayeeAccountStatus(payeeAccount);
        }
        
        this.payerMcoinAccounts = payerMcoinAccounts;
        this.payeeMcoinAccounts = payeeMcoinAccounts;
        
        this.updateTime = new Date();
        
        log.info("积分账户信息列表设置成功, 付款人账户数量={}, 收款人账户数量={}", 
                payerMcoinAccounts.size(), payeeMcoinAccounts.size());
    }
    
    /**
     * 设置安全校验结果
     * 
     * @param securityValidations 安全校验结果列表
     */
    public void setSecurityValidations(List<Boolean> securityValidations) {
        this.securityValidations = securityValidations;
        this.updateTime = new Date();
        
        // 检查是否有校验失败的情况
        boolean hasFailure = securityValidations != null && 
                securityValidations.stream().anyMatch(result -> result == null || !result);
        
        if (hasFailure) {
            log.error("安全校验存在失败项, orderId={}", 
                    orderInfo != null ? orderInfo.getFundOrderId() : "unknown");
            
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAY_FAIL);
        }
        log.info("安全校验结果设置完成, 校验数量={}, 是否有失败={}", 
                securityValidations != null ? securityValidations.size() : 0, hasFailure);
    }
    
    /**
     * 校验转账条件
     */
    public void validateConditions() {
        validateStatus(PaymentStatus.LOCKED);
        
        // 校验是否为一对一转账
        validateOneToOneTransfer();
        
        // 校验订单状态
        validateOrderStatus();
        
        // 校验用户状态
        validateUserStatus();
        
        // 校验积分余额
        validateMcoinBalance();
        
        // 校验黑名单
        validateBlackList();
        
        this.status = PaymentStatus.VALIDATED;
        this.updateTime = new Date();
        
        log.info("一对一转账条件校验通过, orderId={}", orderInfo.getFundOrderId());
    }


    /**
     * 校验转账条件
     */
    public void validateQueryConditions() {
        validateStatus(PaymentStatus.LOCKED);
        
        // 校验是否为一对一转账
        validateOneToOneTransfer();
        
        // 校验订单状态
        validateOrderStatus();
        
        this.status = PaymentStatus.VALIDATED;
        this.updateTime = new Date();
        
        log.info("一对一转账条件校验通过, orderId={}", orderInfo.getFundOrderId());
    }
    
    /**
     * 执行转账
     * 
     * @param transferResult 转账结果
     */
    public void executeTransfer(McoinTransferResult transferResult) {
        validateStatus(PaymentStatus.VALIDATED);
        
        if (transferResult == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAY_RESULT_UNKNOWN);
        }
        
        this.transferResult = transferResult;
        this.status = PaymentStatus.TRANSFERRED;
        this.updateTime = new Date();
        
        log.info("转账执行完成, orderId={}, payerOrderId={}, payeeOrderId={}", 
                orderInfo.getFundOrderId(), transferResult.getPayerOrderId(), transferResult.getPayeeOrderId());
    }
    
    /**
     * 标记支付成功
     */
    public void markPaymentSuccess() {
        validateStatus(PaymentStatus.TRANSFERRED);
        
        // 更新订单状态为支付成功
        if (orderInfo != null) {
            // 这里可以根据业务需要设置具体的成功状态
            log.info("订单状态更新为支付成功, orderId={}", orderInfo.getFundOrderId());
        }
        
        this.status = PaymentStatus.SUCCESS;
        this.updateTime = new Date();
    }
    
    /**
     * 标记支付失败
     * 
     * @param reason 失败原因
     */
    public void markPaymentFailed(String reason) {
        this.status = PaymentStatus.FAILED;
        this.updateTime = new Date();
        
        log.error("支付标记为失败, orderId={}, reason={}", 
                orderInfo != null ? orderInfo.getFundOrderId() : "unknown", reason);
    }
    
    /**
     * 创建订单状态变更消息
     * 
     * @param userId 用户ID
     * @return 订单状态变更消息
     */
    public OrderStatusChangeMessage createStatusChangeMessage(String userId) {
        if (orderInfo == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        OrderStatusChangeMessage message = new OrderStatusChangeMessage();
        message.setResourceId(orderInfo.getFundOrderId());
        message.setUserId(userId);
        message.setOrderStatus(orderInfo.getOrderStatus());
        message.setStatusChangeTime(new Date());
        return message;
    }
    
    /**
     * 检查是否可以执行转账
     * 
     * @return 是否可以转账
     */
    public boolean canExecuteTransfer() {
        return status == PaymentStatus.VALIDATED && 
               payerUserInfos != null && !payerUserInfos.isEmpty() &&
               payeeUserInfos != null &&  !payeeUserInfos.isEmpty() &&
               payerMcoinAccounts != null &&  !payerMcoinAccounts.isEmpty() &&
               payeeMcoinAccounts != null && !payeeMcoinAccounts.isEmpty();
    }
    
    /**
     * 获取支付金额
     * 
     * @return 支付金额
     */
    public Long getPaymentAmount() {
        if (orderInfo == null || orderInfo.getPayOrderInfoList() == null || 
            orderInfo.getPayOrderInfoList().isEmpty()) {
            return 0L;
        }
        
        return orderInfo.getPayOrderInfoList().stream()
            .mapToLong(payOrder -> payOrder.getPayAmount() != null ? payOrder.getPayAmount().getAmount().longValue() : 0L)
            .sum();
    }
    
    /**
     * 校验状态
     * 
     * @param expectedStatus 期望状态
     */
    private void validateStatus(PaymentStatus expectedStatus) {
        if (this.status != expectedStatus) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_STATUS_NOT_WAITING_PAY);
        }
    }

    
    /**
     * 校验付款人用户状态
     * 
     * @param payerUserInfo 付款人信息
     */
    private void validatePayerUserStatus(MPayUserInfo payerUserInfo) {
        if (payerUserInfo.getStatus() == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_STATUS_INVALID);
        }

        if(MPayUserStatusEnum.FREEZE.getValue().equals(payerUserInfo.getStatus())){
            log.info("付款人mpay用戶狀態為凍結, custId={}", payerUserInfo.getCustId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_ACCOUNT_FREEZE);
        }
        if(MPayUserStatusEnum.CANCEL.getValue().equals(payerUserInfo.getStatus())){
            log.info("付款人mpay用戶狀態為註銷, custId={}", payerUserInfo.getCustId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_ACCOUNT_CANCEL);
        }

        // 付款人必须是正常状态
        if (!MPayUserStatusEnum.NORMAL.getValue().equals(payerUserInfo.getStatus())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_STATUS_INVALID);
        }

        if (payerUserInfo.isBlackList()) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_IN_BLACK_LIST);
        }
        
        log.info("付款人状态校验通过, payerId={}, status={}", payerUserInfo.getCustId(), payerUserInfo.getStatus());
    }
    
    /**
     * 校验收款人用户状态
     * 
     * @param payeeUserInfo 收款人信息
     */
    private void validatePayeeUserStatus(MPayUserInfo payeeUserInfo) {
        if (payeeUserInfo.getStatus() == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_STATUS_INVALID);
        }


        if(MPayUserStatusEnum.FREEZE.getValue().equals(payeeUserInfo.getStatus())){
            log.info("收款人mpay用戶狀態為凍結, custId={}", payeeUserInfo.getCustId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_ACCOUNT_FREEZE);
        }
        if(MPayUserStatusEnum.CANCEL.getValue().equals(payeeUserInfo.getStatus())){
            log.info("收款人mpay用戶狀態為註銷, custId={}", payeeUserInfo.getCustId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_ACCOUNT_CANCEL);
        }
        
        // 收款人必须是正常状态
        if (!MPayUserStatusEnum.NORMAL.getValue().equals(payeeUserInfo.getStatus())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_STATUS_INVALID);
        }

        if (payeeUserInfo.isBlackList()) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_IN_BLACK_LIST);
        }
        
        log.info("收款人状态校验通过, payeeId={}, status={}", payeeUserInfo.getCustId(), payeeUserInfo.getStatus());
    }

    
    /**
     * 校验付款人账户状态
     * 
     * @param payerMcoinAccount 付款人积分账户
     */
    private void validatePayerAccountStatus(McoinAccount payerMcoinAccount) {
        if (payerMcoinAccount.getUserStatus() == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_STATUS_INVALID);
        }
        
        // 付款人账户必须是正常状态
        if (!McoinUserStatusEnum.NORMAL.getValue().equals(payerMcoinAccount.getUserStatus())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_STATUS_INVALID);
        }
        
        log.info("付款人账户状态校验通过, accountNo={}, status={}", 
                payerMcoinAccount.getAccountNo(), payerMcoinAccount.getUserStatus());
    }
    
    /**
     * 校验收款人账户状态
     * 
     * @param payeeMcoinAccount 收款人积分账户
     */
    private void validatePayeeAccountStatus(McoinAccount payeeMcoinAccount) {
        if (payeeMcoinAccount.getUserStatus() == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_STATUS_INVALID);
        }
        
        // 收款人账户必须是正常状态
        if (!McoinUserStatusEnum.NORMAL.getValue().equals(payeeMcoinAccount.getUserStatus())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_STATUS_INVALID);
        }
        
        log.info("收款人账户状态校验通过, accountNo={}, status={}", 
                payeeMcoinAccount.getAccountNo(), payeeMcoinAccount.getUserStatus());
    }
    
    /**
     * 校验订单状态
     */
    private void validateOrderStatus() {
        if (orderInfo.getOrderStatus() == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_STATUS_NOT_WAITING_PAY);
        }
        
        // 校验订单初始状态
        validateOrderInitialStatus();
        
        // 检查订单是否已过期
        if (orderInfo.getPayExpiryTime() != null && 
            orderInfo.getPayExpiryTime().before(new Date())) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_TOKEN_INVALID);
        }
    }
    
    /**
     * 校验订单初始状态
     */
    private void validateOrderInitialStatus() {
        // 订单必须是初始状态
        if (!FundOrderStatusEnum.INIT.getCode().equals(orderInfo.getOrderStatus().getCode())) {
            log.error("订单状态不正确, orderId={}, currentStatus={}, expectedStatus={}", 
                    orderInfo.getFundOrderId(), orderInfo.getOrderStatus(), FundOrderStatusEnum.INIT);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_STATUS_NOT_WAITING_PAY);
        }
        
        log.info("订单初始状态校验通过, orderId={}, status={}", 
                orderInfo.getFundOrderId(), orderInfo.getOrderStatus());
    }
    
    /**
     * 校验一对一转账
     */
    private void validateOneToOneTransfer() {
        if (orderInfo == null) {
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        
        // 校验付款人数量
        if (orderInfo.getPayOrderInfoList() == null || orderInfo.getPayOrderInfoList().size() != 1) {
            log.error("付款人数量不为1, orderId={}, payerCount={}", 
                    orderInfo.getFundOrderId(), 
                    orderInfo.getPayOrderInfoList() != null ? orderInfo.getPayOrderInfoList().size() : 0);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        
        // 校验收款人数量
        if (orderInfo.getAcceptOrderInfoList() == null || orderInfo.getAcceptOrderInfoList().size() != 1) {
            log.error("收款人数量不为1, orderId={}, payeeCount={}", 
                    orderInfo.getFundOrderId(), 
                    orderInfo.getAcceptOrderInfoList() != null ? orderInfo.getAcceptOrderInfoList().size() : 0);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        
        log.info("一对一转账校验通过, orderId={}, payerCount=1, payeeCount=1", orderInfo.getFundOrderId());
    }
    

    
    /**
     * 校验用户状态
     */
    private void validateUserStatus() {
        // 校验付款人状态
        for (MPayUserInfo payerUserInfo : payerUserInfos) {
            if (payerUserInfo != null && !isUserStatusValid(payerUserInfo)) {
                throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_STATUS_INVALID);
            }
        }
        
        // 校验收款人状态
        for (MPayUserInfo payeeUserInfo : payeeUserInfos) {
            if (payeeUserInfo != null && !isUserStatusValid(payeeUserInfo)) {
                throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_STATUS_INVALID);
            }
        }
    }
    
    /**
     * 校验积分余额
     */
    private void validateMcoinBalance() {
        if (payerMcoinAccounts == null || payerMcoinAccounts.isEmpty()) {
            log.info("付款人积分账户为空, orderId={}", orderInfo.getFundOrderId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_STATUS_INVALID);
        }
        
        // 校验付款人积分账户余额
        Long paymentAmount = getPaymentAmount();
        for (McoinAccount payerMcoinAccount : payerMcoinAccounts) {
            if (paymentAmount > 0 && 
                (payerMcoinAccount.getBalance() == null || payerMcoinAccount.getBalance() < paymentAmount)) {
                throw new PointProdBizException(PointProdBizErrorCodeEnum.MCOIN_USER_BALANCE_INSUFFICIENT);
            }
        }
    }
    
    /**
     * 检查用户状态是否有效
     * 
     * @param userInfo 用户信息
     * @return 是否有效
     */
    private boolean isUserStatusValid(MPayUserInfo userInfo) {
        // 这里可以根据具体的用户状态字段进行判断
        return userInfo != null && MPayUserStatusEnum.NORMAL.getValue().equals(userInfo.getStatus());
    }
    
    /**
     * 校验黑名单
     */
    private void validateBlackList() {
        if (blackListDomainService == null) {
            log.error("黑名单服务未注入，跳过黑名单校验");
            return;
        }
        for (MPayUserInfo payerUserInfo : payerUserInfos) {
            if (payerUserInfo == null) {
                throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
            }
            
            // 校验付款人是否在黑名单中
            boolean payerInBlackList = blackListDomainService.isInBlackList(payerUserInfo.getCustId());
            if (payerInBlackList) {
                log.info("付款人在黑名单中, payerId={}", payerUserInfo.getCustId());
                throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYER_IN_BLACK_LIST);
            }
            log.info("黑名单校验通过, payerId={}", payerUserInfo.getCustId());
        }
        for (MPayUserInfo payeeUserInfo : payeeUserInfos) {
            if (payeeUserInfo == null) {
                throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
            }
            // 校验收款人是否在黑名单中
            boolean payeeInBlackList = blackListDomainService.isInBlackList(payeeUserInfo.getCustId());
            if (payeeInBlackList) {
                log.info("收款人在黑名单中, payeeId={}", payeeUserInfo.getCustId());
                throw new PointProdBizException(PointProdBizErrorCodeEnum.PAYEE_IN_BLACK_LIST);
            }
        
            log.info("黑名单校验通过, payeeId={}", payeeUserInfo.getCustId());
        }
    }
    


}