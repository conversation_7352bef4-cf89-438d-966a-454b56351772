package com.agtech.pointprod.order.service.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.agtech.common.domain.model.BaseDomainModel;
import com.agtech.common.domain.statemachine.StateMachine;
import com.agtech.common.lang.money.MultiCurrencyMoney;
import com.agtech.pointprod.order.service.domain.common.enums.AcceptOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.statemachine.FundStateMachineManager;
import com.agtech.pointprod.order.service.domain.statemachine.StateBizType;

/**
 * accept order info
 * 
 * <AUTHOR>
 * @version $Id: AcceptOrderInfo.java, v 0.1 2025年6月23日 11:48:33 zhongqiang Exp
 *          $
 */
public class AcceptOrderInfo extends BaseDomainModel implements Serializable {

	private static final long serialVersionUID = -4264393071948746186L;

	/** fund order id */
	private String fundOrderId;

	/** accept order id */
	private String acceptOrderId;

	/** payee info */
	private PayeeParticipantInfo payee;

	/** accept order status */
	private AcceptOrderStatusEnum acceptStatus;

	/** accept amount */
	private MultiCurrencyMoney acceptAmount;

	/** extend info */
	private String acceptExtendInfo;

	/** flux detail info list */
	private List<FluxDetailInfo> fluxDetailInfoList;
	/**
	 * 收款时间
	 */
	private Date acceptTime;
	
	/** create time */
	private Date createdTime;

	

	@Override
	protected StateMachine getStateMachine() {
		StateBizType bizType = StateBizType.ACCEPT;
        return FundStateMachineManager.getStateMachine(bizType);
	}

	@Override
	protected String getCurrentStatus() {
		return acceptStatus.getCode();
	}

	@Override
	protected void doTransferStatus(String targetStatus) {
		//状态迁转
		acceptStatus = AcceptOrderStatusEnum.getByCode(targetStatus);
	}

	/**
     * 获取收款人用户id
     */
	public String getUserId() {
        if(payee!=null) {
            return payee.getUserId();
        }
        return null;
    }

	/**
	 * Getter method for property acceptOrderId.
	 *
	 * @return property value of acceptOrderId
	 */
	public String getAcceptOrderId() {
		return acceptOrderId;
	}

	/**
	 * Setter method for property acceptOrderId.
	 *
	 * @param acceptOrderId value to be assigned to property acceptOrderId
	 */
	public void setAcceptOrderId(String acceptOrderId) {
		this.acceptOrderId = acceptOrderId;
	}

	/**
	 * Getter method for property payee.
	 *
	 * @return property value of payee
	 */
	public PayeeParticipantInfo getPayee() {
		return payee;
	}

	/**
	 * Setter method for property payee.
	 *
	 * @param payee value to be assigned to property payee
	 */
	public void setPayee(PayeeParticipantInfo payee) {
		this.payee = payee;
	}


	public AcceptOrderStatusEnum getAcceptStatus() {
		return acceptStatus;
	}

	public void setAcceptStatus(AcceptOrderStatusEnum acceptStatus) {
		this.acceptStatus = acceptStatus;
	}

	/**
	 * Getter method for property acceptAmount.
	 *
	 * @return property value of acceptAmount
	 */
	public MultiCurrencyMoney getAcceptAmount() {
		return acceptAmount;
	}

	/**
	 * Setter method for property acceptAmount.
	 *
	 * @param acceptAmount value to be assigned to property acceptAmount
	 */
	public void setAcceptAmount(MultiCurrencyMoney acceptAmount) {
		this.acceptAmount = acceptAmount;
	}

	/**
	 * Getter method for property acceptExtendInfo.
	 *
	 * @return property value of acceptExtendInfo
	 */
	public String getAcceptExtendInfo() {
		return acceptExtendInfo;
	}

	/**
	 * Setter method for property acceptExtendInfo.
	 *
	 * @param acceptExtendInfo value to be assigned to property acceptExtendInfo
	 */
	public void setAcceptExtendInfo(String acceptExtendInfo) {
		this.acceptExtendInfo = acceptExtendInfo;
	}

	/**
	 * Getter method for property fluxDetailInfoList.
	 *
	 * @return property value of fluxDetailInfoList
	 */
	public List<FluxDetailInfo> getFluxDetailInfoList() {
		return fluxDetailInfoList;
	}

	/**
	 * Setter method for property fluxDetailInfoList.
	 *
	 * @param fluxDetailInfoList value to be assigned to property fluxDetailInfoList
	 */
	public void setFluxDetailInfoList(List<FluxDetailInfo> fluxDetailInfoList) {
		this.fluxDetailInfoList = fluxDetailInfoList;
	}

	public String getFundOrderId() {
		return fundOrderId;
	}

	public void setFundOrderId(String fundOrderId) {
		this.fundOrderId = fundOrderId;
	}

	public Date getAcceptTime() {
		return acceptTime;
	}

	public void setAcceptTime(Date acceptTime) {
		this.acceptTime = acceptTime;
	}

	public Date getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
	}

}
