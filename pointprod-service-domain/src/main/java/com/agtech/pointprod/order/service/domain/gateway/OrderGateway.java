package com.agtech.pointprod.order.service.domain.gateway;

import java.util.List;
import java.util.Map;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.FundOrderEnv;
import com.agtech.pointprod.order.service.domain.model.FundUnique;
import com.agtech.pointprod.order.service.domain.model.OrderExtendInfo;
import com.agtech.pointprod.order.service.domain.model.OrderToken;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;

/**
 * <AUTHOR>
 * @version v1.0, 2025/6/11 20:31
 */
public interface OrderGateway {
    /**
     * 查询订单token並加鎖
     * @param custId 用户ID
     * @param orderType 订单类型 @see OrderTypeEnum
     * @return 订单token
     */
    OrderToken getOrderTokenForUpdate(String custId, String orderType);

    /**
     * 创建订单token
     * @param custId 用户ID
     * @param orderType 订单类型
     * @return 订单token
     */
    OrderToken createOrderToken(String custId, String orderType);

    /**
     * 更新订单token
     * @param orderToken 订单token
     * @return 订单token
     */
    OrderToken updateOrderToken(OrderToken orderToken);

    /**
     * 查询订单token
     * @param userId 用户ID
     * @param orderToken 订单token
     * @return 订单token
     */
    OrderToken getOrderToken(String userId, String orderToken);

    /**
     * 查询订单信息並加鎖
     * @param orderId 订单ID
     * @return 订单信息
     */
    BaseOrderInfo getFundOrderForUpdate(String orderId, String userId);

    /**
     * 批量查询订单信息
     * @param fundOrderIds 订单ID列表
     * @return 订单信息Map，key为订单ID，value为订单信息
     */
    Map<String, BaseOrderInfo> queryFundOrdersByIds(List<String> fundOrderIds);

    /**
     * 创建订单冪等記錄
     * @param fundUnique 订单冪等記錄
     * @return 是否成功
     */
    boolean createFundUnique(FundUnique fundUnique);

    /**
     * 创建订单ID
     * @param userId 用户ID
     * @return 订单ID
     */
    String createNewOrderId(String userId);

    /**
     * 创建订单
     * @param baseOrderInfo 订单信息
     * @return 是否成功
     */
    boolean createOrder(BaseOrderInfo baseOrderInfo);

    /**
     * 获取订单过期时间配置
     * @return 订单过期时间
     */
    Integer getOrderExpireSeconds();

    /**
     * 设置订单token狀態為已使用
     * @param orderToken 订单token
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean setOrderTokenUsed(String orderToken, String userId);

    /**
     * 创建订单环境業務ID
     * @param custId 用户ID
     * @return 订单环境ID
     */
    String createNewOrderEnvId(String custId);

    /**
     * 创建订单环境
     * @param fundOrderEnv 订单环境
     * @return 是否成功
     */
    boolean createOrderEnv(FundOrderEnv fundOrderEnv);

    /**
     * 创建任务重试ID
     * @param userId 用户ID
     * @return 任务重试ID
     */
    String createNewTaskRetryId(String userId);

    /**
     * 修改订单状态
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateFundOrderStatus(String orderId, String userId, String fromStatus, String toStatus);

    /**
     * 更新订单状态和扩展信息
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @param extendInfo 扩展信息
     * @return 是否成功
     */
    boolean updateStatusAndExtendInfo(String orderId, String userId, FundOrderStatusEnum fromStatus, FundOrderStatusEnum toStatus, OrderExtendInfo extendInfo);

    /**
     * 查询订单信息
     * @param orderId 订单ID
     * @return 订单信息
     */
    BaseOrderInfo getFundOrder(String orderId);
}
