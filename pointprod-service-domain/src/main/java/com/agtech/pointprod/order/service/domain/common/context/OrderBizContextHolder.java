package com.agtech.pointprod.order.service.domain.common.context;

import com.agtech.pointprod.order.service.domain.common.enums.ActionCodeEnum;

/**
 * 订单业务上下文的本地线程hold类
 * 
 * <AUTHOR>
 * @version $Id: OrderBizContextHolder.java, v 0.1 2025年7月2日 15:37:41 zhongqigang.zqg Exp $
 */
public class OrderBizContextHolder {

	/** 本地线程 */
    private static final ThreadLocal<OrderBizContext> CONTEXT_LOCAL = new ThreadLocal<>();

    /**
     * 获取操作上下文
     *
     * @return 业务上下文
     */
    public static OrderBizContext get() {
        return CONTEXT_LOCAL.get();
    }
    
    /**
     * 获取上下文中的动作类型
     * 
     * @return 动作类型
     */
    public static ActionCodeEnum getAction() {
    	OrderBizContext orderBizContext = CONTEXT_LOCAL.get();
    	if(orderBizContext!=null) {
    		return orderBizContext.getAction();
    	}
    	return null;
    }

    /**
     * 设置订单上下文
     *
     * @param context 订单上下文
     */
    public static void set(OrderBizContext context) {
        CONTEXT_LOCAL.set(context);
    }

    /**
     * 清理上下文
     */
    public static void clear() {

        CONTEXT_LOCAL.remove();
    }

    /**
     * 初始化业务上下文
     * 
     */
    public static void init() {
        CONTEXT_LOCAL.set(new OrderBizContext());
    }


}
