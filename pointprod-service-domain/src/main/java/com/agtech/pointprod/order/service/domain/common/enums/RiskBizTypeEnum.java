package com.agtech.pointprod.order.service.domain.common.enums;

/**
 * <AUTHOR>
 * @date 2025/7/22
 */
public enum RiskBizTypeEnum {
    /** 积分转赠平台留言检查 **/
    MCOIN_TRANSFER_REMARK("MCOIN_TRANSFER_REMARK", "mcoin积分转赠留言");

    /** 以系统 + 检查内容作为code **/
    private  String code;
    private  String description;

    RiskBizTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据 code 获取枚举实例的方法
    public static RiskBizTypeEnum getByCode(String code) {
        for (RiskBizTypeEnum bizType : values()) {
            if (bizType.getCode().equals(code)) {
                return bizType;
            }
        }
        return null;
    }
}
