package com.agtech.pointprod.order.service.domain.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.service.OrderDomainService;
import com.agtech.pointprod.order.service.domain.service.PaymentLockDomainService;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.util.AssertUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 支付锁定领域服务实现
 * 实现订单锁定的核心业务逻辑
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class PaymentLockDomainServiceImpl implements PaymentLockDomainService {

    @Resource
    private OrderDomainService orderDomainService;

    @Override
    public BaseOrderInfo lockOrderForPayment(String orderId, String userId) {
        log.info("开始锁定订单用于支付, orderId={}, userId={}", orderId, userId);
        
        // 参数校验
        AssertUtil.assertNotBlank(orderId, PointProdBizErrorCodeEnum.PARAMS_ERROR, "系统异常", "System error");
        AssertUtil.assertNotBlank(userId, PointProdBizErrorCodeEnum.PARAMS_ERROR, "系统异常", "System error");
        
        // 一锁：加锁查询订单信息
        BaseOrderInfo orderInfo = orderDomainService.getFundOrderForUpdate(orderId, userId);
        
        // 二判：判断订单是否存在
        AssertUtil.assertTrue(orderInfo != null, PointProdBizErrorCodeEnum.FUND_ORDER_NOT_FOUND);
        
        // 校验订单支付权限和状态
        validateOrderForPayment(orderInfo, userId);
        
        log.info("订单锁定成功, orderId={}, userId={}", orderId, userId);
        return orderInfo;
    }

    /**
     * 校验订单支付权限和状态
     * 
     * @param orderInfo 订单信息
     * @param userId 用户ID
     */
    private void validateOrderForPayment(BaseOrderInfo orderInfo, String userId) {
        // 校验用户是否有权限支付此订单
        AssertUtil.assertTrue(userId.equals(orderInfo.getActorUserId()), PointProdBizErrorCodeEnum.ORDER_USER_NOT_MATCH);
        
        // 校验订单状态是否允许支付
        AssertUtil.assertTrue(!orderInfo.isClosed() && !orderInfo.isSuccess(), PointProdBizErrorCodeEnum.ORDER_STATUS_NOT_WAITING_PAY);
        
        // 检查订单是否已过期
        if (orderInfo.getPayExpiryTime() != null && 
            orderInfo.getPayExpiryTime().before(new Date())) {
            throw new PointProdBizException(
                    PointProdBizErrorCodeEnum.ORDER_TOKEN_INVALID);
        }
    }
}