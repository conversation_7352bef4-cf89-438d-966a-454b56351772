package com.agtech.pointprod.order.service.domain.service.impl;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;
import com.agtech.pointprod.order.service.domain.service.OrderDomainService;
import com.agtech.pointprod.order.service.domain.service.PayDomainService;
import com.agtech.pointprod.order.service.domain.service.TransferProcessDomainService;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.agtech.pointprod.service.domain.model.McoinTransferResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 支付成功处理领域服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class TransferProcessDomainServiceImpl implements TransferProcessDomainService {

    @Resource
    private OrderDomainService orderDomainService;

    @Resource
    private PayDomainService payDomainService;


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void processOneToOneTransferInitiation(BaseOrderInfo fundOrder, MPayUserInfo payerMPayUserInfo,
            MPayUserInfo payeeMPayUserInfo, McoinAccount payerMcoinAccount, McoinAccount payeeMcoinAccount) {
        log.info("支付发起处理开始, orderId:{}", fundOrder.getFundOrderId());
        
        PayOrderInfo payOrderInfo = fundOrder.getPayOrderInfoList().get(0);
        AcceptOrderInfo acceptOrderInfo = fundOrder.getAcceptOrderInfoList().get(0);
        boolean createResult = payDomainService.createOneToOneTransferFlux(payOrderInfo, acceptOrderInfo,
                payerMcoinAccount, payeeMcoinAccount);
        
        if (!createResult) {
            log.error("创建积分流水异常，订单号：{}", fundOrder.getFundOrderId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAY_SUCCESS_INSERT_FLUX_EXCEPTION);
        }
        
        log.info("支付发起处理完成, orderId:{}", fundOrder.getFundOrderId());
    }

    @Override
    public void processOneToOneTransferSuccess(BaseOrderInfo fundOrder, 
                                             McoinTransferResult transferResult) {
        log.info("支付成功处理开始, orderId:{}", fundOrder.getFundOrderId());
        
        // 更新订单支付成功状态
        updateOrderPaySuccessStatus(fundOrder);
        
        // 积分流水更新为SUCCESS状态并设置资产流水ID
        boolean updateResult = payDomainService.updateOneToOneTransferFluxToSuccess(fundOrder.getFundOrderId(), transferResult);
        
        if (!updateResult) {
            log.error("更新积分流水状态异常，订单号：{}", fundOrder.getFundOrderId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAY_SUCCESS_INSERT_FLUX_EXCEPTION);
        }
        
        log.info("支付成功处理完成, orderId:{}", fundOrder.getFundOrderId());
    }
    
    /**
     * 更新订单支付成功状态
     */
    private void updateOrderPaySuccessStatus(BaseOrderInfo fundOrder) {
        PayOrderInfo payOrderInfo = fundOrder.getPayOrderInfoList().get(0);
        AcceptOrderInfo acceptOrderInfo = fundOrder.getAcceptOrderInfoList().get(0);
        List<String> payOrderIds = Collections.singletonList(payOrderInfo.getPayOrderId());
        List<String> acceptOrderIds = Collections.singletonList(acceptOrderInfo.getAcceptOrderId());
        
        boolean updated = orderDomainService.updateOrderPaySuccessStatus(fundOrder.getFundOrderId(),
                fundOrder.getActorUserId(), payOrderIds, acceptOrderIds);
        
        if (!updated) {
            log.error("更新订单支付成功状态异常，订单号：{}", fundOrder.getFundOrderId());
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PAY_SUCCESS_UPDATE_STATUS_EXCEPTION);
        }
    }
    

}