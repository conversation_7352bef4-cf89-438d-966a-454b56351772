package com.agtech.pointprod.order.service.domain.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.agtech.common.util.CollectionUtil;
import com.agtech.pointprod.service.domain.gateway.*;
import com.agtech.pointprod.service.domain.model.TransferRule;
import org.springframework.stereotype.Service;

import com.agtech.common.lang.util.StringUtil;
import com.agtech.common.util.date.ZonedDateUtil;
import com.agtech.pointprod.order.service.domain.common.enums.AcceptOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundTypeEnum;
import com.agtech.pointprod.order.service.domain.common.enums.PayOrderStatusEnum;
import com.agtech.pointprod.order.service.domain.gateway.OrderGateway;
import com.agtech.pointprod.order.service.domain.gateway.PayGateway;
import com.agtech.pointprod.order.service.domain.model.AcceptOrderInfo;
import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.FundUnique;
import com.agtech.pointprod.order.service.domain.model.OrderToken;
import com.agtech.pointprod.order.service.domain.model.PayOrderInfo;
import com.agtech.pointprod.order.service.domain.service.OrderDomainService;
import com.agtech.pointprod.service.domain.common.enums.PointProdBizErrorCodeEnum;
import com.agtech.pointprod.service.domain.exception.PointProdBizException;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;
import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version OrderDomainService.java, v0.1 2025/6/18 14:29 zhongqigang Exp $
 */
@Service
@Slf4j
public class OrderDomainServiceImpl implements OrderDomainService {
    @Resource
    private OrderGateway orderGateway;

    @Resource
    private McoinAccountGateway mcoinAccountGateway;

    @Resource
    private MPayUserInfoGateway mPayUserInfoGateway;

    @Resource
    private PayGateway payGateway;

    @Resource
    private TaskRetryGateway taskRetryGateway;

    @Resource
    private RiskGateway riskGateway;

    @Resource
    private TransferRuleGateway transferRuleGateway;

    @Override
    public String getOrderToken(String custId) {
        OrderToken orderToken = orderGateway.getOrderTokenForUpdate(custId, FundTypeEnum.TRANSFER_MCOIN.getCode());
        if(orderToken == null){
            orderToken = orderGateway.createOrderToken(custId, FundTypeEnum.TRANSFER_MCOIN.getCode());
        }else if(!orderToken.isWaitingUse()){
            orderToken = orderGateway.updateOrderToken(orderToken);
        }
        return orderToken.getToken();
    }

    @Override
    public OrderToken getOrderToken(String userId, String orderToken) {
        return orderGateway.getOrderToken(userId, orderToken);
    }

    @Override
    public BaseOrderInfo getFundOrder(String orderId) {
        return orderGateway.getFundOrder(orderId);
    }

    @Override
    public BaseOrderInfo getFundOrderForUpdate(String orderId, String userId) {
        return orderGateway.getFundOrderForUpdate(orderId, userId);
    }

    @Override
    public Map<String, BaseOrderInfo> queryFundOrdersByIds(List<String> fundOrderIds) {
        return orderGateway.queryFundOrdersByIds(fundOrderIds);
    }

    @Override
    public McoinAccount queryMcoinUserInfo(String custId) {
        if(StringUtil.isBlank(custId)){
            return null;
        }
        return mcoinAccountGateway.queryMcoinAccount(custId);
    }

    @Override
    public MPayUserInfo queryMpayUserInfo(String custId) {
        if(StringUtil.isBlank(custId)){
            return null;
        }
        return mPayUserInfoGateway.getUserMsg(custId);
    }

    @Override
    public void checkRepeat(String orderToken, String custId) {
        FundUnique fundUnique = new FundUnique();
        fundUnique.setGlobalUniqueId("");
        fundUnique.setRequestId(orderToken);
        fundUnique.setBizId(custId);
        fundUnique.setBizType("");
        fundUnique.setIsDeleted(0);
        Date now = ZonedDateUtil.now();
        fundUnique.setGmtCreate(now);
        fundUnique.setGmtModified(now);
        try {
            boolean success = orderGateway.createFundUnique(fundUnique);
            if(!success){
                throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
            }
        } catch (Exception e) {
            log.error("checkRepeat error, errMsg:{}", e.getMessage(), e);
            throw new PointProdBizException(PointProdBizErrorCodeEnum.PLACE_ORDER_REQUEST_REPEAT);
        }
    }

    @Override
    public void createOrder(BaseOrderInfo baseOrderInfo) {
        //赋值订单时间(接收到下单请求时的时间与落库的时间中间有时间差，以落库之前的时间为准)
        Date now = ZonedDateUtil.now();
        Date orderExpiryTime = ZonedDateUtil.add(now, Calendar.SECOND, orderGateway.getOrderExpireSeconds());
        baseOrderInfo.setAcceptExpiryTime(orderExpiryTime);
        baseOrderInfo.setPayExpiryTime(orderExpiryTime);
        baseOrderInfo.setCompleteTime(now);
        baseOrderInfo.setCreatedTime(now);
        baseOrderInfo.setModifiedTime(now);
        boolean success = orderGateway.createOrder(baseOrderInfo);
        if(!success){
            log.error("创建订单主单失败, 完整订单信息:{}", JSON.toJSONString(baseOrderInfo));
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }

        for (PayOrderInfo payOrderInfo : baseOrderInfo.getPayOrderInfoList()) {
            payOrderInfo.setPayTime(now);
            payOrderInfo.setCreatedTime(now);
            success = payGateway.createPayOrder(payOrderInfo);
            if(!success){
                log.error("创建付款单失败, 完整订单信息:{}", JSON.toJSONString(baseOrderInfo));
                throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
            }
        }
        for (AcceptOrderInfo acceptOrderInfo : baseOrderInfo.getAcceptOrderInfoList()) {
            acceptOrderInfo.setAcceptTime(now);
            acceptOrderInfo.setCreatedTime(now);
            success = payGateway.createAcceptOrder(acceptOrderInfo);
            if(!success){
                log.error("创建收款单失败, 完整订单信息:{}", JSON.toJSONString(baseOrderInfo));
                throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
            }
        }

        if(baseOrderInfo.getFundOrderEnv() != null) {
            baseOrderInfo.getFundOrderEnv().setGmtCreate(now);
            baseOrderInfo.getFundOrderEnv().setGmtModified(now);
            success = orderGateway.createOrderEnv(baseOrderInfo.getFundOrderEnv());
            if(!success){
                log.error("创建订单环境信息失败, 完整订单信息:{}", JSON.toJSONString(baseOrderInfo));
                throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
            }
        }
    }

    @Override
    public void setOrderTokenUsed(String orderToken, String userId) {
        boolean success = orderGateway.setOrderTokenUsed(orderToken, userId);
        if(!success){
            log.error("setOrderTokenUsed fail");
            throw new PointProdBizException(PointProdBizErrorCodeEnum.SYS_ERROR);
        }
    }


    @Override
    public boolean closeFundOrder(BaseOrderInfo baseOrderInfo) {
        //状态机推进到CLOSE
        for (PayOrderInfo payOrderInfo : baseOrderInfo.getPayOrderInfoList()) {
            //付款单内部推进订单状态机到CLOSE
            payOrderInfo.oneStagePayTimeout(baseOrderInfo);
        }
        for (AcceptOrderInfo acceptOrderInfo : baseOrderInfo.getAcceptOrderInfoList()) {
            //收款单的状态直接设置为CLOSE
            acceptOrderInfo.setAcceptStatus(AcceptOrderStatusEnum.CLOSE);
        }

        //执行订单状态更新操作, 这里可以直接更新订单状态为CLOSE, 暂时保持原样，更新条件带上fromStatus
        boolean updateFundOrderStatusSuccess = orderGateway.updateStatusAndExtendInfo(baseOrderInfo.getFundOrderId(), 
                baseOrderInfo.getActorUserId(), FundOrderStatusEnum.INIT, FundOrderStatusEnum.CLOSE,
                baseOrderInfo.getExtendInfo());

        //执行付款状态更新操作
        List<String> fundPayIds = new ArrayList<>();
        fundPayIds.addAll(baseOrderInfo.getPayOrderInfoList().stream().map(PayOrderInfo::getPayOrderId).collect(Collectors.toList()));
        fundPayIds.addAll(baseOrderInfo.getAcceptOrderInfoList().stream().map(AcceptOrderInfo::getAcceptOrderId).collect(Collectors.toList()));
        boolean updateFundPayStatusSuccess = payGateway.updateFundPayStatus(fundPayIds, PayOrderStatusEnum.INIT.getCode(), PayOrderStatusEnum.CLOSE.getCode());

        return updateFundOrderStatusSuccess && updateFundPayStatusSuccess;
    }


    @Override
    public boolean updateOrderPaySuccessStatus(String orderId, String userId, List<String> payOrderIds, List<String> acceptOrderIds) {
    	
        // 更新订单状态
        boolean updatedFundOrderStatus = orderGateway.updateFundOrderStatus(orderId, userId,
                FundOrderStatusEnum.INIT.getCode(), FundOrderStatusEnum.SUCCESS.getCode());
        if (!updatedFundOrderStatus){
           return false;
        }
        // 更新付款方订单支付状态
        boolean updatedPayerPayStatus = payGateway.updateFundPayStatus(payOrderIds,
                PayOrderStatusEnum.INIT.getCode(), PayOrderStatusEnum.SUCCESS.getCode());
        if (!updatedPayerPayStatus){
            return false;
        }
        // 跟收款方订单支付状态
        return payGateway.updateFundPayStatus(acceptOrderIds,
                AcceptOrderStatusEnum.INIT.getCode(), AcceptOrderStatusEnum.SUCCESS.getCode());
    }

    @Override
    public String validateOrderMemo(String memo) {
        return riskGateway.validateText(memo);
    }

    @Override
    public boolean checkPayerTransferRule(BaseOrderInfo baseOrderInfo){
        if(baseOrderInfo==null || CollectionUtil.isEmpty(baseOrderInfo.getPayOrderInfoList())
                || baseOrderInfo.getPayOrderInfoList().get(0).getPayer()==null
                || baseOrderInfo.getPayOrderInfoList().get(0).getPayer().getExtInfo()==null){
            log.error("checkPayerTransferRule fail, baseOrderInfo is: {}", JSON.toJSONString(baseOrderInfo));
            return false;
        }

        String userLevel = baseOrderInfo.getPayOrderInfoList().get(0).getPayer().getExtInfo().getUserLevel();
        TransferRule transferRule = transferRuleGateway.queryTransferRuleByGrade(userLevel);
        if(transferRule==null || StringUtil.isBlank(transferRule.getOptionalInfo())){
            log.error("checkPayerTransferRule fail, transferRule is: {}, baseOrderInfo is: {}", JSON.toJSONString(transferRule), JSON.toJSONString(baseOrderInfo));
            return false;
        }
        if(transferRule.transferOptionals().stream().noneMatch(transferOptional -> transferOptional.getValue().equals(baseOrderInfo.getFundAmount().getAmount().intValue()))){
            log.error("checkPayerTransferRule fail, transferRule is: {}, baseOrderInfo is: {}", JSON.toJSONString(transferRule), JSON.toJSONString(baseOrderInfo));
            return false;
        }
        log.info("checkPayerTransferRule success, userId is:{}, userLevel is: {}, transfer amount:{}, transferRule is: {}",
                baseOrderInfo.getPayOrderInfoList().get(0).getPayer().getUserId(), userLevel, baseOrderInfo.getFundAmount().getAmount(), transferRule.getOptionalInfo());
        return true;
    }
}
