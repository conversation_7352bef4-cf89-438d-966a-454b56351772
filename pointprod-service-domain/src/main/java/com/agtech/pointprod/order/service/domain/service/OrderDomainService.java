package com.agtech.pointprod.order.service.domain.service;

import java.util.List;
import java.util.Map;

import com.agtech.pointprod.order.service.domain.model.BaseOrderInfo;
import com.agtech.pointprod.order.service.domain.model.OrderToken;
import com.agtech.pointprod.service.domain.model.MPayUserInfo;
import com.agtech.pointprod.service.domain.model.McoinAccount;

/**
 * 订单领域服务
 * <AUTHOR>
 * @version OrderDomainService.java, v0.1 2025/6/18 14:29 zhongqigang Exp $
 */
public interface OrderDomainService {
    /**
     * 查询订单token
     * @param custId 用户ID
     * @return 订单token
     */
    String getOrderToken(String custId);

    /**
     * 查询订单token
     * @param userId 用户ID
     * @param orderToken 订单token
     * @return 订单token
     */
    OrderToken getOrderToken(String userId, String orderToken);

    /**
     * 查询订单信息
     * @param orderId 订单ID
     * @return 订单信息
     */
    BaseOrderInfo getFundOrder(String orderId);

    /**
     * 查询订单信息並加鎖
     * @param orderId 订单ID
     * @return 订单信息
     */
    BaseOrderInfo getFundOrderForUpdate(String orderId, String userId);

    /**
     * 批量查询订单信息
     * @param fundOrderIds 订单ID列表
     * @return 订单信息Map，key为订单ID，value为订单信息
     */
    Map<String, BaseOrderInfo> queryFundOrdersByIds(List<String> fundOrderIds);

    /**
     * 查询mcoin用户信息
     * @param custId 用户ID
     * @return mcoin用户信息
     */
    McoinAccount queryMcoinUserInfo(String custId);

    /**
     * 查询MPay用户信息
     * @param custId 用户ID
     * @return MPay用户信息
     */
    MPayUserInfo queryMpayUserInfo(String custId);

    /**
     * 检测订单請求是否重复
     * @param orderToken 订单token
     * @param custId 用户ID
     */
    void checkRepeat(String orderToken, String custId);

    /**
     * 創建訂單的核心邏輯
     * @param baseOrderInfo 订单领域模型
     */
    void createOrder(BaseOrderInfo baseOrderInfo);

    /**
     * 设置订单token狀態為已使用
     * @param orderToken 订单token
     * @param userId 用户ID
     */
    void setOrderTokenUsed(String orderToken, String userId);


    /**
     * 关闭订单
     * @param baseOrderInfo 订单领域模型
     * @return 是否成功
     */
    boolean closeFundOrder(BaseOrderInfo baseOrderInfo);


    /**
     * 更新订单支付成功状态
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateOrderPaySuccessStatus(String orderId, String userId, List<String> payOrderIds, List<String> acceptOrderIds);

    /**
     * 请求内容安全服务，校验订单转赠留言是否合法
     * @param memo 订单留言
     * @return 验证结果, 为null时表示验证通过, 不为null时表示转赠留言存在敏感内容
     */
    String validateOrderMemo(String memo);

    /**
     * 校验付款人转赠规则, 付款人用户等级对应的转赠规则不存在或者不匹配则下单失败
     * @param baseOrderInfo 订单领域模型
     * @return 是否成功
     */
    boolean checkPayerTransferRule(BaseOrderInfo baseOrderInfo);
}
