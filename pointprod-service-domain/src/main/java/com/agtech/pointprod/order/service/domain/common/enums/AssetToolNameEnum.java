package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

/**
 * 资金工具
 * 
 * <AUTHOR>
 * @version $Id: AssetToolNameEnum.java, v 0.1 2025年6月23日 11:12:08 zhongqiang Exp $
 */
public enum AssetToolNameEnum {
	
	/** 积分余额 */
	MCOIN("MCOIN", "積分餘額", "mCoin Balance"),
	;

	/** mode code */
	private String code;

	/** mode description */
	private String description;
	
	/** English description */
	private String englishDescription;

	AssetToolNameEnum(String code, String description, String englishDescription) {
		this.code = code;
		this.description = description;
		this.englishDescription = englishDescription;
	}

	/**
	 * get FundMode enum by code
	 */
	public static AssetToolNameEnum getByCode(String code) {
		for (AssetToolNameEnum mode : values()) {
			if (StringUtil.equals(mode.getCode(), code)) {
				return mode;
			}
		}
		return null;
	}

	/**
	 * Getter method for property code.
	 *
	 * @return property value of code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * Setter method for property code.
	 *
	 * @param code value to be assigned to property code
	 */
	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * Getter method for property description.
	 *
	 * @return property value of description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * Setter method for property description.
	 *
	 * @param description value to be assigned to property description
	 */
	public void setDescription(String description) {
		this.description = description;
	}
	
	/**
	 * Getter method for property englishDescription.
	 *
	 * @return property value of englishDescription
	 */
	public String getEnglishDescription() {
		return englishDescription;
	}

	/**
	 * Setter method for property englishDescription.
	 *
	 * @param englishDescription value to be assigned to property englishDescription
	 */
	public void setEnglishDescription(String englishDescription) {
		this.englishDescription = englishDescription;
	}
}
