package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

/**
 * 资金流向类型枚举
 */
public enum SubOrderTypeEnum {
    
    /**
     * 支付 - 转出
     */
    PAY("PAY", "支付"),
    
    /**
     * 接收 - 收到
     */
    ACCEPT("ACCEPT", "接收");
    
	/** code */
    private String code;

    /** description */
    private String description;

    SubOrderTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get AcceptOrderStatus enum by code
     */
    public static SubOrderTypeEnum getByCode(String code) {
        for (SubOrderTypeEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }


    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据值获取枚举
     * @param value 枚举值
     * @return 枚举对象
     */
    public static SubOrderTypeEnum fromValue(String value) {
        for (SubOrderTypeEnum fluxType : SubOrderTypeEnum.values()) {
            if (fluxType.getCode().equals(value)) {
                return fluxType;
            }
        }
        return null;
    }

} 