package com.agtech.pointprod.order.service.domain.model;

import java.io.Serializable;

import com.agtech.pointprod.order.service.domain.common.enums.AssetToolNameEnum;
import com.agtech.pointprod.order.service.domain.common.enums.IdentifyIdTypeEnum;
import com.agtech.pointprod.service.domain.model.ParticipantUser;

/**
 * 收款人信息
 * 
 * <AUTHOR>
 * @version $Id: PayeeParticipantInfo.java, v 0.1 2025年6月23日 11:50:00 zhongqiang
 *          Exp $
 */
public class PayeeParticipantInfo implements Serializable {

	private static final long serialVersionUID = -8885499868517556888L;

	private String userId;

	private AssetToolNameEnum assetTool;

	private String accountNo;

	private IdentifyIdTypeEnum identifyIdType;

	private String identifyId;
	/**
	 * 收款人扩展信息
	 */
	private ParticipantUser payeeExtInfo;
	
	private PayeeParticipantInfo() {
		
	}
	public PayeeParticipantInfo(String userId, AssetToolNameEnum assetTool, String accountNo,
			IdentifyIdTypeEnum identifyIdType, String identifyId, ParticipantUser payeeExtInfo) {
		super();
		this.userId = userId;
		this.assetTool = assetTool;
		this.accountNo = accountNo;
		this.identifyIdType = identifyIdType;
		this.identifyId = identifyId;
		this.payeeExtInfo = payeeExtInfo;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public AssetToolNameEnum getAssetTool() {
		return assetTool;
	}

	public void setAssetTool(AssetToolNameEnum assetTool) {
		this.assetTool = assetTool;
	}

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public IdentifyIdTypeEnum getIdentifyIdType() {
		return identifyIdType;
	}

	public void setIdentifyIdType(IdentifyIdTypeEnum identifyIdType) {
		this.identifyIdType = identifyIdType;
	}

	public String getIdentifyId() {
		return identifyId;
	}

	public void setIdentifyId(String identifyId) {
		this.identifyId = identifyId;
	}

	public ParticipantUser getPayeeExtInfo() {
		return payeeExtInfo;
	}

	public void setPayeeExtInfo(ParticipantUser payeeExtInfo) {
		this.payeeExtInfo = payeeExtInfo;
	}
}