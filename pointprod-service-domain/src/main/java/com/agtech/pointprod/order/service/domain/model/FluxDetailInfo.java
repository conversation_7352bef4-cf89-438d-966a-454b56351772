package com.agtech.pointprod.order.service.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.agtech.pointprod.order.service.domain.common.enums.FundFluxStatusEnum;
import com.agtech.pointprod.order.service.domain.common.enums.FundFluxTypeEnum;


/**
 * flux detail info
 * 
 * <AUTHOR>
 * @version $Id: FluxDetailInfo.java, v 0.1 2025年6月23日 10:53:29 zhongqiang Exp $
 */
public class FluxDetailInfo implements Serializable {

    private static final long     serialVersionUID = 8409141916108822336L;

    /** fund flux id */
    private String                fundFluxId;

    /** fund base order id */
    private String                fundOrderId;

    /** sub order id for PayOrder,AcceptOrder,RevokeOrder,FundBackOrder */
    private String                subOrderId;

    /** actor role id */
    private String                actorRoleId;

    /** fund flux type */
    private FundFluxTypeEnum     fundFluxType;

    /** assetFluxId from AssetFlux */
    private String                assetFluxId;

    /** flux status */
    private FundFluxStatusEnum    fluxStatus;

    /** related fundFluxId, such as refund pay fundFluxId */
    private String                relatedFundFluxId;

    /** flux accept time */
    private Date                  fluxAcceptTime;

    /** flux complete time */
    private Date                  fluxCompleteTime;

    /** flux error code */
    private String                fluxErrorCode;

    /** channel info list */
    private List<FluxChannelInfo> channelInfoList;

    /**
     * Getter method for property fundFluxId.
     *
     * @return property value of fundFluxId
     */
    public String getFundFluxId() {
        return fundFluxId;
    }

    /**
     * Setter method for property fundFluxId.
     *
     * @param fundFluxId value to be assigned to property fundFluxId
     */
    public void setFundFluxId(String fundFluxId) {
        this.fundFluxId = fundFluxId;
    }

    /**
     * Getter method for property fundOrderId.
     *
     * @return property value of fundOrderId
     */
    public String getFundOrderId() {
        return fundOrderId;
    }

    /**
     * Setter method for property fundOrderId.
     *
     * @param fundOrderId value to be assigned to property fundOrderId
     */
    public void setFundOrderId(String fundOrderId) {
        this.fundOrderId = fundOrderId;
    }

    /**
     * Getter method for property subOrderId.
     *
     * @return property value of subOrderId
     */
    public String getSubOrderId() {
        return subOrderId;
    }

    /**
     * Setter method for property subOrderId.
     *
     * @param subOrderId value to be assigned to property subOrderId
     */
    public void setSubOrderId(String subOrderId) {
        this.subOrderId = subOrderId;
    }

    /**
     * Getter method for property actorRoleId.
     *
     * @return property value of actorRoleId
     */
    public String getActorRoleId() {
        return actorRoleId;
    }

    /**
     * Setter method for property actorRoleId.
     *
     * @param actorRoleId value to be assigned to property actorRoleId
     */
    public void setActorRoleId(String actorRoleId) {
        this.actorRoleId = actorRoleId;
    }

    /**
     * Getter method for property fundFluxType.
     *
     * @return property value of fundFluxType
     */
    public FundFluxTypeEnum getFundFluxType() {
        return fundFluxType;
    }

    /**
     * Setter method for property fundFluxType.
     *
     * @param fundFluxType value to be assigned to property fundFluxType
     */
    public void setFundFluxType(FundFluxTypeEnum fundFluxType) {
        this.fundFluxType = fundFluxType;
    }

    /**
     * Getter method for property assetFluxId.
     *
     * @return property value of assetFluxId
     */
    public String getAssetFluxId() {
        return assetFluxId;
    }

    /**
     * Setter method for property assetFluxId.
     *
     * @param assetFluxId value to be assigned to property assetFluxId
     */
    public void setAssetFluxId(String assetFluxId) {
        this.assetFluxId = assetFluxId;
    }

    /**
     * Getter method for property fluxStatus.
     *
     * @return property value of fluxStatus
     */
    public FundFluxStatusEnum getFluxStatus() {
        return fluxStatus;
    }

    /**
     * Setter method for property fluxStatus.
     *
     * @param fluxStatus value to be assigned to property fluxStatus
     */
    public void setFluxStatus(FundFluxStatusEnum fluxStatus) {
        this.fluxStatus = fluxStatus;
    }

    /**
     * Getter method for property relatedFundFluxId.
     *
     * @return property value of relatedFundFluxId
     */
    public String getRelatedFundFluxId() {
        return relatedFundFluxId;
    }

    /**
     * Setter method for property relatedFundFluxId.
     *
     * @param relatedFundFluxId value to be assigned to property relatedFundFluxId
     */
    public void setRelatedFundFluxId(String relatedFundFluxId) {
        this.relatedFundFluxId = relatedFundFluxId;
    }

    /**
     * Getter method for property fluxAcceptTime.
     *
     * @return property value of fluxAcceptTime
     */
    public Date getFluxAcceptTime() {
        return fluxAcceptTime;
    }

    /**
     * Setter method for property fluxAcceptTime.
     *
     * @param fluxAcceptTime value to be assigned to property fluxAcceptTime
     */
    public void setFluxAcceptTime(Date fluxAcceptTime) {
        this.fluxAcceptTime = fluxAcceptTime;
    }

    /**
     * Getter method for property fluxCompleteTime.
     *
     * @return property value of fluxCompleteTime
     */
    public Date getFluxCompleteTime() {
        return fluxCompleteTime;
    }

    /**
     * Setter method for property fluxCompleteTime.
     *
     * @param fluxCompleteTime value to be assigned to property fluxCompleteTime
     */
    public void setFluxCompleteTime(Date fluxCompleteTime) {
        this.fluxCompleteTime = fluxCompleteTime;
    }

    /**
     * Getter method for property fluxErrorCode.
     *
     * @return property value of fluxErrorCode
     */
    public String getFluxErrorCode() {
        return fluxErrorCode;
    }

    /**
     * Setter method for property fluxErrorCode.
     *
     * @param fluxErrorCode value to be assigned to property fluxErrorCode
     */
    public void setFluxErrorCode(String fluxErrorCode) {
        this.fluxErrorCode = fluxErrorCode;
    }

    /**
     * Getter method for property channelInfoList.
     *
     * @return property value of channelInfoList
     */
    public List<FluxChannelInfo> getChannelInfoList() {
        return channelInfoList;
    }

    /**
     * Setter method for property channelInfoList.
     *
     * @param channelInfoList value to be assigned to property channelInfoList
     */
    public void setChannelInfoList(List<FluxChannelInfo> channelInfoList) {
        this.channelInfoList = channelInfoList;
    }

}
