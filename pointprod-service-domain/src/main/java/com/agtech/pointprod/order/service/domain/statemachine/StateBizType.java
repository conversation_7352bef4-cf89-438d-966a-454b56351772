package com.agtech.pointprod.order.service.domain.statemachine;

/**
 * 状态机支持的业务类型
 * 
 * <AUTHOR>
 * @version $Id: StateBizType.java, v 0.1 2025年6月24日 16:15:59 zhongqiang Exp $
 */
public enum StateBizType {
	
	/**
	 * 转赠状态机
	 */
	TRANSFER_MCOIN("TRANSFER_MCOIN", "TRANSFER_MCOIN"),
	
	//*********支付业务状态机****************/
    /** 支付单状态机 */
    PAY("PAY", "PAY"),
    
    /**
     * 收款单据状态机
     */
    ACCEPT("ACCEPT", "ACCEPT"),
    
    ;

    /** 简码 */
    private final String code;

    /** 描述 */
    private final String description;

    /**
     * 私有构造方法
     *
     * @param code          简码
     * @param description   描述
     */
    private StateBizType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the description.
     */
    public String getDescription() {
        return description;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code  简码
     * @return      枚举
     */
    public static StateBizType getByCode(String code) {
        for (StateBizType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
