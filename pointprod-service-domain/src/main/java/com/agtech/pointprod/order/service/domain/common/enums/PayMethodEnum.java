package com.agtech.pointprod.order.service.domain.common.enums;

import com.agtech.common.lang.util.StringUtil;

/**
 * 支付方式枚举
 * 
 * <AUTHOR>
 * @version $Id: PayMethodEnum.java, v 0.1 2025年6月30日 11:36:39 zhongqiang Exp $
 */
public enum PayMethodEnum {

	/** 余额支付 */
	BALANCE("BALANCE", "balance pay"),
    /** 借记卡支付 */
	DEBIT_CARD("DEBIT_CARD", "debit card pay"),

    ;

    /** code */
    private String code;

    /** description */
    private String description;

    PayMethodEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * get PayMethod enum by code
     */
    public static PayMethodEnum getByCode(String code) {
        for (PayMethodEnum type : values()) {
            if (StringUtil.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Getter method for property code.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Setter method for property code.
     *
     * @param code value to be assigned to property code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * Getter method for property description.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }

    /**
     * Setter method for property description.
     *
     * @param description value to be assigned to property description
     */
    public void setDescription(String description) {
        this.description = description;
    }

}
