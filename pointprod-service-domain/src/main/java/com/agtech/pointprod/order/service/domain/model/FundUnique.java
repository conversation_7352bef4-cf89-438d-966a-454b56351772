package com.agtech.pointprod.order.service.domain.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Table: fund_unique
 * <AUTHOR>
 */
@Getter
@Setter
public class FundUnique {
    /**
     * Column: global_unique_id
     * Type: VARCHAR(64)
     * Remark: 幂等业务id
     */
    private String globalUniqueId;

    /**
     * Column: request_id
     * Type: VARCHAR(64)
     * Remark: 请求id
     */
    private String requestId;

    /**
     * Column: biz_id
     * Type: VARCHAR(256)
     * Remark: 业务id
     */
    private String bizId;

    /**
     * Column: biz_type
     * Type: VARCHAR(64)
     * Remark: 业务类型
     */
    private String bizType;

    /**
     * Column: is_deleted
     * Type: TINYINT(3)
     * Default value: 0
     * Remark: 是否删除
     */
    private Integer isDeleted;

    /**
     * Column: gmt_create
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date gmtCreate;

    /**
     * Column: gmt_modified
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date gmtModified;
}