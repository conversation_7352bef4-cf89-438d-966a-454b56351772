package com.agtech.pointprod.order.service.domain.model;

import java.util.List;

import com.agtech.common.lang.money.MultiCurrencyMoney;

/**
 * 系统自动撤销
 * 
 * <AUTHOR>
 * @version $Id: RevokeOrderInfo.java, v 0.1 2025年6月23日 11:52:03 zhongqiang Exp $
 */
public class RevokeOrderInfo extends BaseOrderInfo {

	private static final long serialVersionUID = -3336570906380091862L;

	/** revoke order id */
	private String revokeOrderId;

	/** pay order id */
	private String payOrderId;

	/** revoke status */
	private String revokeStatus;

	/** revoke amount */
	private MultiCurrencyMoney revokeAmount;

	/** revoke reason */
	private String revokeReason;

	/** reference asset flux id */
	private String refAssetFluxId;

	/** fund flux details */
	private List<FluxDetailInfo> fundFluxDetails;

	/**
	 * Getter method for property revokeOrderId.
	 *
	 * @return property value of revokeOrderId
	 */
	public String getRevokeOrderId() {
		return revokeOrderId;
	}

	/**
	 * Setter method for property revokeOrderId.
	 *
	 * @param revokeOrderId value to be assigned to property revokeOrderId
	 */
	public void setRevokeOrderId(String revokeOrderId) {
		this.revokeOrderId = revokeOrderId;
	}

	/**
	 * Getter method for property payOrderId.
	 *
	 * @return property value of payOrderId
	 */
	public String getPayOrderId() {
		return payOrderId;
	}

	/**
	 * Setter method for property payOrderId.
	 *
	 * @param payOrderId value to be assigned to property payOrderId
	 */
	public void setPayOrderId(String payOrderId) {
		this.payOrderId = payOrderId;
	}

	/**
	 * Getter method for property revokeStatus.
	 *
	 * @return property value of revokeStatus
	 */
	public String getRevokeStatus() {
		return revokeStatus;
	}

	/**
	 * Setter method for property revokeStatus.
	 *
	 * @param revokeStatus value to be assigned to property revokeStatus
	 */
	public void setRevokeStatus(String revokeStatus) {
		this.revokeStatus = revokeStatus;
	}

	/**
	 * Getter method for property revokeAmount.
	 *
	 * @return property value of revokeAmount
	 */
	public MultiCurrencyMoney getRevokeAmount() {
		return revokeAmount;
	}

	/**
	 * Setter method for property revokeAmount.
	 *
	 * @param revokeAmount value to be assigned to property revokeAmount
	 */
	public void setRevokeAmount(MultiCurrencyMoney revokeAmount) {
		this.revokeAmount = revokeAmount;
	}

	/**
	 * Getter method for property revokeReason.
	 *
	 * @return property value of revokeReason
	 */
	public String getRevokeReason() {
		return revokeReason;
	}

	/**
	 * Setter method for property revokeReason.
	 *
	 * @param revokeReason value to be assigned to property revokeReason
	 */
	public void setRevokeReason(String revokeReason) {
		this.revokeReason = revokeReason;
	}

	/**
	 * Getter method for property refAssetFluxId.
	 *
	 * @return property value of refAssetFluxId
	 */
	public String getRefAssetFluxId() {
		return refAssetFluxId;
	}

	/**
	 * Setter method for property refAssetFluxId.
	 *
	 * @param refAssetFluxId value to be assigned to property refAssetFluxId
	 */
	public void setRefAssetFluxId(String refAssetFluxId) {
		this.refAssetFluxId = refAssetFluxId;
	}

	/**
	 * Getter method for property fundFluxDetails.
	 *
	 * @return property value of fundFluxDetails
	 */
	public List<FluxDetailInfo> getFundFluxDetails() {
		return fundFluxDetails;
	}

	/**
	 * Setter method for property fundFluxDetails.
	 *
	 * @param fundFluxDetails value to be assigned to property fundFluxDetails
	 */
	public void setFundFluxDetails(List<FluxDetailInfo> fundFluxDetails) {
		this.fundFluxDetails = fundFluxDetails;
	}
}
