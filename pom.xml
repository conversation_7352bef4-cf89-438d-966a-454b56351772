<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.agtech</groupId>
    <artifactId>pointprod-service</artifactId>
    <version>0.0.1</version>
    <packaging>pom</packaging>
    <name>pointprod-service</name>
    <description>pointprod-service</description>

    <modules>
        <module>pointprod-service-app</module>
        <module>pointprod-service-domain</module>
        <module>pointprod-service-facade</module>
        <module>pointprod-service-infrastructure</module>
        <module>pointprod-service-test</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.6.14</spring-boot.version>
        <lombok.version>1.18.24</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <maven-surefire-plugin.version>3.0.0</maven-surefire-plugin.version>
        <spring-boot.version>2.6.14</spring-boot.version>
        <spring-cloud.version>2021.0.5</spring-cloud.version>
        <com-alibaba-cloud.version>2021.0.5.0</com-alibaba-cloud.version>
        <mybatis-starter.version>2.2.2</mybatis-starter.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <fastjson2.version>2.0.46</fastjson2.version>
        <mpass-common-lang.version>1.0.18</mpass-common-lang.version>
        <mysql-connector-java.version>8.0.25</mysql-connector-java.version>
        <!--   1.18存在丢traceId的问题，阿里云建议升级到最新版本，无需其他变更 0.1.18 -> 0.1.29    -->
        <aliyun-log-logback-appender.version>0.1.29</aliyun-log-logback-appender.version>
        <mariaDB4j.version>2.5.3</mariaDB4j.version>
        <dynamic-datasource-spring-boot-starter.version>4.2.0</dynamic-datasource-spring-boot-starter.version>
        <amqp-client.version>5.13.1</amqp-client.version>
        <xxl-job.version>2.3.1</xxl-job.version>
        <springboot-redis.version>2.6.14</springboot-redis.version>
        <sentinel.version>1.8.6</sentinel.version>
        <gateway.result.version>1.1.20231213</gateway.result.version>
        <m2e.apt.activation>jdt_apt</m2e.apt.activation>
        <aliyun-oss.version>3.17.1</aliyun-oss.version>

        <mockito.version>4.5.1</mockito.version>

    </properties>

    <dependencyManagement>
        <dependencies>

            <!--Project modules-->
            <dependency>
                <groupId>com.agtech</groupId>
                <artifactId>pointprod-service-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.agtech</groupId>
                <artifactId>pointprod-service-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.agtech</groupId>
                <artifactId>pointprod-service-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.agtech</groupId>
                <artifactId>pointprod-service-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.agtech</groupId>
                <artifactId>pointprod-service-test</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--Project modules End-->

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.github.openfeign</groupId>
                        <artifactId>feign-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.github.openfeign</groupId>
                        <artifactId>feign-slf4j</artifactId>
                    </exclusion>
                </exclusions>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-core</artifactId>
                <version>12.0</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-slf4j</artifactId>
                <version>12.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${com-alibaba-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- Add missing fastjson (original version) -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>

            <!-- Add Apache Commons Collections 4 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>

            <!-- Add Groovy for script execution -->
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>3.0.17</version>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <!-- 如果是Spring Boot项目，确保该依赖在编译时生效 -->
                <scope>provided</scope>
            </dependency>


            <dependency>
                <groupId>com.agtech.common</groupId>
                <artifactId>agtech-common-lang</artifactId>
                <version>1.2.1</version>
            </dependency>
            <dependency>
                <groupId>com.agtech.common</groupId>
                <artifactId>mpass-commom-lang</artifactId>
                <version>${mpass-common-lang.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
                <scope>runtime</scope>
            </dependency>

            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>aliyun-log-logback-appender</artifactId>
                <version>${aliyun-log-logback-appender.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.vorburger.mariaDB4j</groupId>
                <artifactId>mariaDB4j</artifactId>
                <version>${mariaDB4j.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.rabbitmq</groupId>
                <artifactId>amqp-client</artifactId>
                <version>${amqp-client.version}</version> <!-- 支持开源所有版本 -->
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring</artifactId>
                <version>4.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${springboot-redis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>4.34.0.ALL</version>
                <exclusions>
                    <exclusion>
                        <artifactId>bcprov-jdk15on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-oss.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>



    <!-- 环境 -->
    <profiles>
        <!-- 本地开发 -->
        <profile>
            <id>local</id>
            <activation>
                <!--默认激活配置-->
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!--当前环境-->
                <profile.name>local</profile.name>
                <config.namespace>local</config.namespace>
                <config.group>MCOIN_GROUP</config.group>
                <!--Nacos服务地址-->
                <config.server-addr>172.16.0.90:8858</config.server-addr>
                <config.username>nacos</config.username>
                <config.passwd>nacos</config.passwd>
                <sls.endpoint>cn-hongkong-intranet.log.aliyuncs.com</sls.endpoint>
                <sls.accessKeyId>LTAI5tSD7cp1NgfCA1GF3M7u</sls.accessKeyId>
                <sls.accessKeySecret>******************************</sls.accessKeySecret>
                <sls.project>mpay-dev</sls.project>
                <sls.logStore>pointprod-local</sls.logStore>
                <spring.cloud.sentinel.transport.dashboard>127.0.0.1:8080</spring.cloud.sentinel.transport.dashboard>
            </properties>
        </profile>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <!--当前环境-->
                <profile.name>dev</profile.name>
                <!--Nacos服务地址-->
                <config.namespace>dev</config.namespace>
                <config.group>MCOIN_GROUP</config.group>
                <config.server-addr>gateway-kms-nacos-2-2-3-headless.b-middleware:8848</config.server-addr>
                <config.username>nacos</config.username>
                <config.passwd>nacos</config.passwd>
                <sls.endpoint>cn-hongkong-intranet.log.aliyuncs.com</sls.endpoint>
                <sls.accessKeyId>LTAI5tSD7cp1NgfCA1GF3M7u</sls.accessKeyId>
                <sls.accessKeySecret>******************************</sls.accessKeySecret>
                <sls.project>mpay-dev</sls.project>
                <sls.logStore>pointprod-dev</sls.logStore>
                <spring.cloud.sentinel.transport.dashboard>sentinel-dashboard.gateway:8080</spring.cloud.sentinel.transport.dashboard>
            </properties>
        </profile>
        <!-- 测试环境 -->
        <profile>
            <id>sit</id>
            <properties>
                <!--当前环境-->
                <profile.name>sit</profile.name>
                <!--Nacos服务地址-->
                <config.namespace>sit</config.namespace>
                <config.group>MCOIN_GROUP</config.group>
                <config.server-addr>gateway-kms-nacos-2-2-3-headless.b-middleware:8848</config.server-addr>
                <config.username>nacos</config.username>
                <config.passwd>nacos</config.passwd>
                <sls.endpoint>cn-hongkong-intranet.log.aliyuncs.com</sls.endpoint>
                <sls.accessKeyId>LTAI5tDtdKZkbH7Xsbgd8M4j</sls.accessKeyId>
                <sls.accessKeySecret>******************************</sls.accessKeySecret>
                <sls.project>mpay-sit</sls.project>
                <sls.logStore>pointprod-sit</sls.logStore>
                <spring.cloud.sentinel.transport.dashboard>sentinel-dashboard.gateway:8080</spring.cloud.sentinel.transport.dashboard>
            </properties>
        </profile>
        <!-- UAT环境 -->
        <profile>
            <id>uat</id>
            <properties>
                <!--当前环境-->
                <profile.name>uat</profile.name>
                <!--Nacos服务地址-->
                <config.namespace>uat</config.namespace>
                <config.group>MCOIN_GROUP</config.group>
                <config.server-addr>gateway-kms-nacos-2-2-3-headless.gateway-middleware:88488</config.server-addr>
                <config.username>nacos</config.username>
                <config.passwd>Nacos@123</config.passwd>
                <sls.endpoint>cn-hongkong-intranet.log.aliyuncs.com</sls.endpoint>
                <sls.accessKeyId>LTAI5tDtdKZkbH7Xsbgd8M4j</sls.accessKeyId>
                <sls.accessKeySecret>******************************</sls.accessKeySecret>
                <sls.project>mpay-sit</sls.project>
                <sls.logStore>pointprod-sit</sls.logStore>
                <spring.cloud.sentinel.transport.dashboard>sentinel-dashboard.gateway:8080</spring.cloud.sentinel.transport.dashboard>
            </properties>
        </profile>
        <!-- 生产 -->
        <profile>
            <id>prd</id>
            <properties>
                <!--当前环境-->
                <profile.name>prd</profile.name>
                <!--Nacos服务地址-->
                <config.namespace>prd</config.namespace>
                <config.group>MCOIN_GROUP</config.group>
                <config.server-addr>nacos:8848</config.server-addr>
                <config.username>nacos</config.username>
                <config.passwd>nacos</config.passwd>
                <sls.endpoint>cn-hongkong-intranet.log.aliyuncs.com</sls.endpoint>
                <sls.accessKeyId>LTAI5tSD7cp1NgfCA1GF3M7u</sls.accessKeyId>
                <sls.accessKeySecret>******************************</sls.accessKeySecret>
                <sls.project>mpay-uat</sls.project>
                <sls.logStore>pointprod-uat</sls.logStore>
                <spring.cloud.sentinel.transport.dashboard>sentinel-dashboard.gateway:8080</spring.cloud.sentinel.transport.dashboard>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.json</include>
                    <include>**/*.ftl</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <!-- 打包跳过测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://172.31.159.213:8081/repository/maven-releases/</url>
            <releases>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://172.31.159.213:8081/repository/maven-snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>maven-releases</name>
            <url>http://172.31.159.213:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://172.31.159.213:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


</project>
