# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Build and Package
```bash
# Clean and compile all modules
mvn clean compile

# Package all modules (skips tests by default)
mvn clean package

# Package with specific profile
mvn clean package -P dev
mvn clean package -P sit
mvn clean package -P uat
mvn clean package -P prd
```

### Testing
```bash
# Run all tests
mvn test

# Run tests in specific module
mvn test -pl pointprod-service-test

# Run specific test class
mvn test -Dtest=ContractControllerTest

# Run tests with specific profile
mvn test -P dev
```

### Development Profiles
- `local` (default): Local development environment
- `dev`: Development environment  
- `sit`: System integration testing
- `uat`: User acceptance testing
- `prd`: Production environment

### Running the Application
```bash
# Run with local profile (default)
cd pointprod-service-app
mvn spring-boot:run

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## Project Architecture

### Multi-Module Structure
This is a Maven multi-module Spring Boot project following Domain-Driven Design (DDD) principles:

1. **pointprod-service-app** (Application Layer)
   - Entry point with main class `PointProdServiceAppApplication`
   - Controllers for HTTP endpoints
   - Application services for orchestrating business operations
   - Processing steps pattern for complex business flows
   - Spring configuration classes

2. **pointprod-service-domain** (Domain Layer)
   - Core business logic and domain models
   - Domain services containing business rules
   - Gateway interfaces for external dependencies
   - Business enums and constants
   - No dependencies on infrastructure or frameworks

3. **pointprod-service-facade** (Facade Layer)
   - API contracts and DTOs for external communication
   - Request/Response objects for service interfaces
   - API definitions for external consumers

4. **pointprod-service-infrastructure** (Infrastructure Layer)
   - Data access implementation with MyBatis-Plus
   - Repository pattern implementations
   - Gateway implementations for external services
   - Database entities (DO objects)
   - External service integrations using Feign

5. **pointprod-service-test** (Test Layer)
   - Unit and integration tests
   - Test configurations and data setup
   - Base test classes for common functionality

### Key Architectural Patterns

#### Clean Architecture
- **Dependency Direction**: App → Domain ← Infrastructure
- Each layer has specific responsibilities
- Domain layer is isolated from framework concerns

#### Repository Pattern
- Domain defines repository interfaces in `gateway/`
- Infrastructure implements repositories accessing databases
- Enables testability and technology independence

#### Processing Steps Pattern
- Complex business operations broken into steps in `processing/step/`
- Context objects carry data between steps
- Enables better testability and maintainability

#### Gateway Pattern
- Abstracts external service dependencies
- Domain defines contracts, infrastructure implements
- Supports multi-table aggregation in memory (no JOINs in DAOs)

## Key Coding Standards

### Dependency Injection
- Always use `@Resource` annotation, not `@Autowired`
- Apply to private fields for dependency injection

### Data Objects vs Domain Models
- **DO (Data Objects)**: Database entities ending with `DO`, contain database IDs
- **Domain Models**: Business entities with no DB-specific fields, use business identifiers

### Naming Conventions
- Controllers: `*Controller`
- Services: `*Service` (interface), `*ServiceImpl` (implementation)
- Repositories: `*Repository` (interface), `*RepositoryImpl` (implementation)
- DTOs: `*Req` (requests), `*Rsp` (responses)
- Data Objects: `*DO`
- Enums: `*Enum`

### Required Annotations
- Use `@Getter` and `@Setter` for all data classes
- Use Lombok to reduce boilerplate code
- All DO fields must have JavaDoc with column name and business description

### Error Handling
- Use `AssertUtil` for validation
- Throw `PointProdBizException` with specific error codes from `PointProdBizErrorCodeEnum`
- Handle globally in `GlobalExceptionHandler`

### Data Access Rules
- DAOs must only access single tables (no JOINs)
- Use Lambda expressions with `LambdaQueryWrapper` for queries
- Multi-table operations handled in Gateway layer with in-memory aggregation
- Use `java.util.Date` for all time fields with `ZonedDateUtil` utilities

## Key Technologies
- **Framework**: Spring Boot 2.6.14 with Spring Cloud
- **Database**: MyBatis-Plus with MySQL 8.0
- **Cache**: Redis
- **Message Queue**: RabbitMQ with AMQP
- **Job Scheduling**: XXL-Job
- **Service Discovery**: Alibaba Nacos
- **Monitoring**: Alibaba Sentinel
- **Build Tool**: Maven 3.x
- **Java Version**: 1.8

## Development Workflow

### Adding New Features
1. **Domain First**: Start with domain models and services
2. **Gateway Contracts**: Define repository/gateway interfaces
3. **Infrastructure**: Implement data access and external integrations
4. **Application Layer**: Create controllers and application services
5. **Facade**: Define DTOs and API contracts if needed

### External Service Integration
- Create service-specific packages under `infrastructure.integration.{serviceName}`
- Use Feign clients with `@FeignClient` annotation
- Implement translator services with `ClientTemplate.execute()` pattern
- Create Anti-Corruption Layer (ACL) to protect domain from external changes

### Testing Strategy
- Extend `BaseUnitTest` for unit tests
- Use `data.sql` and `schema.sql` for test data setup
- Test configurations in `bootstrap-test.yaml`
- MariaDB4j for embedded database testing