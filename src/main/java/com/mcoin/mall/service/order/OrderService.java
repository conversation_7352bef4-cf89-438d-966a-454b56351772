package com.mcoin.mall.service.order;

import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.bean.FookPlatformOrderrefundRecord;
import com.mcoin.mall.bean.FookStores;
import com.mcoin.mall.model.CodeStatusRequest;
import com.mcoin.mall.model.OrderDetailRequest;
import com.mcoin.mall.model.OrderDetailResponse;
import com.mcoin.mall.model.OrderRefundApprovalRequest;
import com.mcoin.mall.model.OrderRefundApprovalResponse;
import com.mcoin.mall.model.RefundCodeRequest;
import com.mcoin.mall.model.RefundCodeResponse;
import com.mcoin.mall.model.RefundHandlingRequest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.UpdateCouponCodeRequest;
import com.mcoin.mall.model.UpdateCouponCodeResponse;

import javax.validation.Valid;
import java.util.List;

public interface OrderService {
    FookPlatformOrderinfo getOrderInfoByCode(String code,Integer userid);

    List<FookStores> getStoresByProductId(Integer orderInfoId);


    OrderDetailResponse getOrderDetail(OrderDetailRequest request);

    Response<List<UpdateCouponCodeResponse>> updateCode(UpdateCouponCodeRequest request);

    RefundCodeResponse getRefundCode(RefundCodeRequest request);

    void updRefundHandling(RefundHandlingRequest request);

    int updOrderClose(Integer orderId);

    int updOrderClose(FookPlatformOrder order);

    int updOrderStatus(Integer orderId);

    OrderRefundApprovalResponse approvalRefund(OrderRefundApprovalRequest request);

    void sendRefundMsg(FookPlatformOrderrefundRecord refundRecord);

    Response<List<UpdateCouponCodeResponse>> queryCodeStatus(@Valid CodeStatusRequest request);
}
