package com.mcoin.mall.service.order.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookBusinessProductTranslations;
import com.mcoin.mall.bean.FookBusinessProductstock;
import com.mcoin.mall.bean.FookMqLocal;
import com.mcoin.mall.bean.FookPayLog;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformOrdercode;
import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.bean.FookPlatformOrderrefund;
import com.mcoin.mall.bean.FookPlatformOrderrefundRecord;
import com.mcoin.mall.bean.FookProductSnappingRecord;
import com.mcoin.mall.bean.FookProductStockLog;
import com.mcoin.mall.bean.FookStores;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.AllowRefund;
import com.mcoin.mall.constant.CouponSynStatus;
import com.mcoin.mall.constant.IsApplyStoresStatus;
import com.mcoin.mall.constant.IsWholeEnum;
import com.mcoin.mall.constant.MPayResponseCodeEnum;
import com.mcoin.mall.constant.MqLocalResourceType;
import com.mcoin.mall.constant.Numbers;
import com.mcoin.mall.constant.PlatformOrderStatusEnum;
import com.mcoin.mall.constant.RefundResponseCodeEnum;
import com.mcoin.mall.constant.RefundSceneEnum;
import com.mcoin.mall.constant.SnappingRecordStatus;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessProductTranslationsDao;
import com.mcoin.mall.dao.FookBusinessProductstockDao;
import com.mcoin.mall.dao.FookPayLogDao;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeDao;
import com.mcoin.mall.dao.FookPlatformOrderinfoDao;
import com.mcoin.mall.dao.FookPlatformOrderrefundDao;
import com.mcoin.mall.dao.FookPlatformOrderrefundRecordDao;
import com.mcoin.mall.dao.FookProductSnappingRecordDao;
import com.mcoin.mall.dao.FookProductStockLogDao;
import com.mcoin.mall.dao.FookStoresDao;
import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.exception.RetryException;
import com.mcoin.mall.model.CodeStatusRequest;
import com.mcoin.mall.model.OrderDetailRequest;
import com.mcoin.mall.model.OrderDetailResponse;
import com.mcoin.mall.model.OrderRefundApprovalRequest;
import com.mcoin.mall.model.OrderRefundApprovalResponse;
import com.mcoin.mall.model.RefundCodeRequest;
import com.mcoin.mall.model.RefundCodeResponse;
import com.mcoin.mall.model.RefundHandlingRequest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.UpdateCouponCodeRequest;
import com.mcoin.mall.model.UpdateCouponCodeResponse;
import com.mcoin.mall.mq.model.CouponSyncMessage;
import com.mcoin.mall.mq.model.RefundMessage;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.service.base.MqLocalService;
import com.mcoin.mall.service.chennel.MPayChannelService;
import com.mcoin.mall.service.chennel.vo.MPayPaySuccessVo;
import com.mcoin.mall.service.common.AtomicSeqService;
import com.mcoin.mall.service.order.OrderService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.DistanceUtil;
import com.mcoin.mall.util.HtmlUtil;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.MoneyUtil;
import com.mcoin.mall.util.OssUtil;
import com.mcoin.mall.util.TimeIntervalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.defaultIfNull;
import static com.mcoin.mall.util.JodaTimeUtil.format;
import static java.lang.Integer.parseInt;
import static java.lang.String.format;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.defaultIfBlank;

@Service
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Resource
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;

    @Resource
    private FookPlatformOrderDao fookPlatformOrderDao;

    @Resource
    private FookStoresDao fookStoresDao;

    @Resource
    private FookBusinessProductDao fookBusinessProductDao;

    @Resource
    private FookBusinessDao fookBusinessDao;

    @Resource
    private FookPlatformOrderrefundDao fookPlatformOrderrefundDao;

    @Resource
    private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;
    @Resource
    private SettingsDao settingsDao;
    @Resource
    private FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;
    @Resource
    private FookPlatformOrderrefundRecordDao fookPlatformOrderrefundRecordDao;
    @Resource
    private MessageSource messageSource;
    @Resource
    private ContextHolder contextHolder;
    @Resource
    private AtomicSeqService atomicSeqService;
    @Resource
    private FookProductSnappingRecordDao fookProductSnappingRecordDao;
    @Resource
    private FookBusinessProductstockDao fookBusinessProductstockDao;
    @Resource
    private FookProductStockLogDao fookProductStockLogDao;
    @Resource
    private FookPayLogDao fookPayLogDao;
    @Resource
    private MPayChannelService mPayChannelService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private RabbitTemplate refundTemplate;

    @Resource
    private MqLocalService mqLocalService;



    @Override
    public FookPlatformOrderinfo getOrderInfoByCode(String code, Integer userid) {
        List<FookPlatformOrdercode> originOrdercodes = this.fookPlatformOrdercodeDao.getOrderCode(code);
        List<FookPlatformOrdercode> ordercodes = originOrdercodes.stream()
                .filter(o -> Objects.equals(o.getUserid(), userid))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ordercodes)) {
            FookPlatformOrdercode ordercode = ordercodes.get(0);
            if (ordercodes.size() > 1) {
                // 出现重复券码，只取未使用的
                Integer unUsed = 1;
                ordercode = ordercodes.stream()
                        .filter(o -> Objects.equals(o.getStatus(), unUsed))
                        .findFirst().orElse(ordercodes.get(0));
                log.info("code出现多条重复券码 {}", JSON.toJSONString(originOrdercodes));
            }
            if (null != ordercode) {
                return this.fookPlatformOrderinfoDao.selectByPrimaryKey(ordercode.getOrderinfoId());
            }
        }
        return null;
    }

    @Override
    public List<FookStores> getStoresByProductId(Integer productId) {
        return this.fookStoresDao.getStoresByProductId(productId);
    }

    @Override
    public OrderDetailResponse getOrderDetail(OrderDetailRequest request) {
        OrderDetailResponse response = new OrderDetailResponse();
        OrderDetailResponse.OrderInfo orderInfoItem = new OrderDetailResponse.OrderInfo();
        OrderDetailResponse.BusinessProduct productItem = new OrderDetailResponse.BusinessProduct();
        OrderDetailResponse.OrderRefund refundItem = new OrderDetailResponse.OrderRefund();
        List<OrderDetailResponse.KeyStoresItem> keyStores = new ArrayList<>();
        List<OrderDetailResponse.StoresItem> storesItemList = new ArrayList<>();
        OrderDetailResponse.Order orderItem = new OrderDetailResponse.Order();
        Locale locale = contextHolder.getLocale();
        UserInfo userInfo = contextHolder.getAuthUserInfo();
        String lang = locale.getLanguage();
        lang = StringUtils.isBlank(lang) ? McoinMall.LANG_CN : lang;
        // 訂單核銷碼記錄表
        FookPlatformOrdercode orderCode = fookPlatformOrdercodeDao.selectByPrimaryKey(request.getCodeid());
        if (orderCode == null) {
            log.info("orderCode為空");
            throw new BusinessException(Response.Code.BAD_REQUEST, "訂單不存在");
        }
        if (orderCode.getUserid() != null && userInfo.getUserId() != orderCode.getUserid()) {
            log.info("非本人");
            throw new BusinessException(Response.Code.BAD_REQUEST, "非本人");
        }
        FookPlatformOrderinfo orderInfo = fookPlatformOrderinfoDao.selectByPrimaryKey(orderCode.getOrderinfoId());
        if (orderInfo == null) {
            log.info("orderInfo為空");
            throw new BusinessException(Response.Code.BAD_REQUEST, "訂單不存在");
        }
        FookPlatformOrder order = fookPlatformOrderDao.selectByPrimaryKey(orderInfo.getOrderid());
        FookBusinessProduct product = fookBusinessProductDao
                .selectByPrimaryKeyWithTranslations(orderInfo.getProdcutid(), lang);
        if (product == null) {
            log.error("product為空");
            throw new BusinessException(Response.Code.BAD_REQUEST, "福利不存在");
        }
        List<FookStores> storeList = fookStoresDao.getApplyStores(lang, orderInfo.getProdcutid(),
                IsApplyStoresStatus.YES.ordinal());
        FookPlatformOrderrefund orderRefund = fookPlatformOrderrefundDao.selectByPrimaryKey(orderCode.getRefundid());
        // 核銷店鋪信息
        FookStores shop = fookStoresDao.selectByPrimaryKeyWithTranslations(lang, orderCode.getShopid());

        String cafeFreeProductId = settingsDao.getValue("site.cafe_free_product_id");
        Integer[] cafeFreeProductIdArray = new Integer[] { 1419, 2524, 2539, 2551, 2987, 2997 };
        List<Integer> productIdList = Arrays.asList(cafeFreeProductIdArray);
        if (StringUtils.isNotBlank(cafeFreeProductId)) {
            try {
                productIdList = Arrays.asList(cafeFreeProductId.split(",")).stream().map(e -> Integer.parseInt(e))
                        .collect(Collectors.toList());
            } catch (Exception e) {
                log.info("使用默認的cafe_free_product_id");
            }
        }
        String qrcode = orderCode.getCode();
        if (productIdList.contains(orderInfo.getProdcutid()) && StringUtils.isNotBlank(qrcode)
                && qrcode.length() >= 16) {
            qrcode = qrcode.substring(4, 16);
        }
        // 亞洲萬裡通替換退款原因zh（兌換失敗）
        if (orderInfo.getMilesMilage() != null && orderRefund != null) {
            orderRefund.setRefundReason(
                    messageSource.getMessage("message.ordersaveinfo.miles_exchange_failure", null, locale));
        }
        if (McoinMall.LANG_EN.equals(lang)) {
            FookBusinessProductTranslations pro = fookBusinessProductTranslationsDao
                    .getTranslationsById(product.getId(), lang);
            if (pro != null) {
                if (StringUtils.isNotBlank(pro.getTTitle())) {
                    orderInfo.setTitleSnapshots(pro.getTTitle());
                }
                if (StringUtils.isNotBlank(pro.getTTnc())) {
                    orderInfo.setTnc(pro.getTTnc());
                }
            }
        }

        if (isNotEmpty(storeList)) {
            log.info("門店不為空");
            // 排序之前先賦值，門店id和門店名稱
            response.setStoreId(shop != null ? shop.getId() : storeList.get(0).getId());
            response.setStoreName(shop != null ? shop.getName() : storeList.get(0).getName());
            for (FookStores e : storeList) {
                OrderDetailResponse.StoresItem storesItem2 = new OrderDetailResponse.StoresItem();
                storesItem2.setId(e.getId());
                storesItem2.setBusinessId(e.getBusinessId() == null ? 0 : e.getBusinessId());
                storesItem2.setName(e.getName());
                storesItem2.setPhone(e.getPhone());
                storesItem2.setAddress(e.getAddress());
                double distanceNum = DistanceUtil.getDistance(request.getLot(), request.getLat(), e.getLongitude(),
                        e.getDimension());
                String distance = DistanceUtil.getDistanceStr(distanceNum);
                if (distanceNum >= 1) {
                    storesItem2.setDistance(distance);
                    storesItem2.setDistanceNum(new BigDecimal(format("%.1f", distanceNum)));
                    e.setDistance(distance);
                    e.setDistanceNum(distanceNum);
                } else if (distanceNum > 0 && distanceNum < 1) {
                    storesItem2.setDistance(distance);
                    storesItem2.setDistanceNum(new BigDecimal(format("%.3f", distanceNum)));
                    e.setDistance(distance);
                    e.setDistanceNum(distanceNum);
                } else {
                    storesItem2.setDistance("");
                }
                storesItemList.add(storesItem2);
            }
            if (StringUtils.isNotBlank(request.getLot()) && StringUtils.isNotBlank(request.getLat())) {
                // 對距離進行排序
                storesItemList = storesItemList.stream()
                        .sorted(Comparator.comparing(OrderDetailResponse.StoresItem::getDistanceNum))
                        .collect(Collectors.toList());
                // storeList =
                // storeList.stream().sorted(Comparator.comparing(FookStores::getDistanceNum)).collect(Collectors.toList());
            }
            for (FookStores e : storeList) {
                // 亞洲萬裡通
                OrderDetailResponse.KeyStoresItem keyStoresItem = new OrderDetailResponse.KeyStoresItem();
                OrderDetailResponse.Stores storesItem = new OrderDetailResponse.Stores();
                keyStoresItem.setId(e.getId());
                storesItem.setId(e.getId());
                storesItem.setName(e.getName());
                keyStoresItem.setStores(storesItem);
                keyStores.add(keyStoresItem);
            }
        }

        String available = "";
        int vaildMode = Optional.ofNullable(orderInfo.getValidMode()).orElse(1);
        if (vaildMode == 1) {
            available = messageSource.getMessage("message.order.available_date",
                    new Object[] { format(orderInfo.getVaildEndTime()) }, locale);
            // 天數
        } else if (vaildMode == 2) {
            available = messageSource.getMessage("message.order.available_day",
                    new Object[] { orderInfo.getDayNum() }, locale);
        }
        // 周六日是否可以使用（1可用0不可用）
        String isWeekend = "";
        if (Optional.ofNullable(orderInfo.getIsWeekend()).orElse(0) == 1) {
            isWeekend = messageSource.getMessage("message.order.for_weekends", null, locale);
        } else {
            isWeekend = messageSource.getMessage("message.order.not_for_weekends", null, locale);
        }
        // 节假日是否可用（1可用0不可用）
        String isVacation = "";
        if (Optional.ofNullable(orderInfo.getIsVacation()).orElse(0) == 1) {
            isVacation = messageSource.getMessage("message.order.applicable_holidays", null, locale);
        } else {
            isVacation = messageSource.getMessage("message.order.not_applicable_holidays", null, locale);
        }
        isVacation = isVacation + messageSource.getMessage("message.order.is_refund", null, locale);
        int status = Optional.ofNullable(orderCode.getStatus()).orElse(1);
        if (status == 1) {
            if (orderCode.getRefundStatus() != null && orderCode.getRefundStatus() == 2) {
                // 退款中
                status = 5;
            } else if (DateUtil.compare(new Date(), orderInfo.getVaildEndTime()) > 0) {
                // 已過期
                status = 4;
            }
        }
        String statusTips = null;
        switch (status) {
            case 1:
                statusTips = "message.basic.unused";
                break;
            case 2:
                statusTips = "message.basic.used";
                break;
            case 3:
                statusTips = "message.basic.refunded";
                break;
            case 4:
                statusTips = "message.basic.expired";
                break;
            case 5:
                statusTips = "message.basic.refunding";
                break;
            default:
                // 其他情况
                break;
        }
        statusTips = messageSource.getMessage(statusTips, null, locale);
        int buyAgain = Optional.ofNullable(product.getShelfStatus()).orElse(2);
        // 獲取mFood商家ID
        int mFoodBusinessId = parseInt(defaultIfBlank(settingsDao.getValue("site.mfood_business_id"), "0"));
        // 控制mCoin兌換mFood的券碼複製按鈕是否顯示
        int ifCopy = (product.getBusinessid() != null && product.getBusinessid() == mFoodBusinessId) ? 1 : 0;
        orderInfoItem.setId(orderInfo.getId());
        orderInfoItem.setType(orderInfo.getType());
        orderInfoItem.setImageSnapshots(OssUtil.initOssImage(orderInfo.getImageSnapshots()));
        orderInfoItem.setProdcutid(orderInfo.getProdcutid());
        orderInfoItem.setTitleSnapshots(orderInfo.getTitleSnapshots());
        orderInfoItem.setIsVoucher(orderInfo.getIsVoucher());
        productItem.setId(product.getId());
        productItem.setStores(storesItemList);
        orderInfoItem.setBusinessProduct(productItem);
        String finalTnc = "";
        if (StringUtils.isNotBlank(orderInfo.getTnc())) {
            finalTnc = HtmlUtil.replaceNewLine(orderInfo.getTnc());
        }
        orderInfoItem.setTnc(finalTnc);
        orderInfoItem.setOrderid(orderInfo.getOrderid());
        orderInfoItem.setMilesMilage(orderInfo.getMilesMilage());
        orderItem.setId(order.getId());
        orderItem.setOrderNo(order.getOrderNo());
        orderItem.setCreateTime(JodaTimeUtil.format(order.getCreateTime()));
        orderItem.setPaymentTime(JodaTimeUtil.format(order.getPaymentTime()));
        orderInfoItem.setOrder(orderItem);
        orderInfoItem.setMilesMember(orderInfo.getMilesMember());
        orderInfoItem.setMilesFirst(orderInfo.getMilesFirst());
        orderInfoItem.setMilesName(orderInfo.getMilesName());
        orderInfoItem.setKeyStores(keyStores);
        response.setId(orderCode.getId());
        response.setCode(orderCode.getCode());
        response.setOrderInfo(orderInfoItem);
        response.setQrcode(qrcode);
        // TODO 时间为空时会返回当前时间
        response.setUserTime(JodaTimeUtil.format(orderCode.getUserTime()));
        response.setVaildStartTime(JodaTimeUtil.format(orderInfo.getVaildStartTime()));
        response.setVaildEndTime(JodaTimeUtil.format(orderInfo.getVaildEndTime()));

        response.setAvailable(available);
        response.setApplicable(isWeekend + isVacation);
        response.setIsAllowRefund(DateUtil.compare(new Date(), orderInfo.getVaildEndTime()) < 0 && orderRefund == null
                ? orderInfo.getIsAllowRefund()
                : 0);
        List<FookPlatformOrdercode> orderCodeList = fookPlatformOrdercodeDao.selectByOrderId(orderCode.getOrderid());
        response.setIsWhole(isNotEmpty(orderCodeList) ? IsWholeEnum.WHOLE.getCode() : IsWholeEnum.NOT_WHOLE.getCode()); // mpay只允許全退款
        response.setApportionMpayintegral(
                orderCode.getApportionMpayintegral() != null ? orderCode.getApportionMpayintegral() : 0);
        response.setApportionBillFinalAmount(orderCode.getApportionBillFinalAmount() != null
                ? String.valueOf(orderCode.getApportionBillFinalAmount())
                : "0");
        response.setStatus(status);
        response.setStatusTips(statusTips);
        if (orderRefund != null) {
            refundItem.setId(orderRefund.getId());
            refundItem.setMpayintegral(orderRefund.getMpayintegral());
            refundItem.setRefundOrderno(orderRefund.getRefundOrderno());
            refundItem.setRefundAmount(String.valueOf(orderRefund.getRefundAmount()));
            refundItem.setApplicationTime(JodaTimeUtil.format(orderRefund.getApplicationTime()));
            refundItem.setRefundTime(JodaTimeUtil.format(orderRefund.getRefundTime()));
        }
        response.setOrderRefund(refundItem);
        response.setBuyAgain(buyAgain);
        response.setIfCopy(ifCopy);
        FookBusiness business = this.fookBusinessDao.selectByPrimaryKey(order.getSellerid());
        if (null != business) {
            response.setBusinessName(business.getName());
        }
        return response;
    }

    @Override
    public Response<List<UpdateCouponCodeResponse>> updateCode(UpdateCouponCodeRequest request) {
        int userId = contextHolder.getAuthUserInfo().getUserId();
        String code = request.getCode();
        // 原php代码还有查询fook_multiple_writeoff表，返回订单及券信息的流程。后经确认该表已废弃，目前流程为直接查询fook_platform_ordercode表
        FookPlatformOrdercode orderCode = fookPlatformOrdercodeDao
                .selectOne(new LambdaQueryWrapper<FookPlatformOrdercode>()
                        .eq(FookPlatformOrdercode::getCode, code)
                        .eq(FookPlatformOrdercode::getUserid, userId)
                        .last("limit 1"));

        // 数据为空或不是已使用状态返回错误码0
        if (orderCode == null || orderCode.getStatus() != 2) {
            return Responses.ok(new ArrayList<UpdateCouponCodeResponse>(), Response.Status.FAILED.ordinal(), "error");
        }
        log.info("用户userId[{}]核销码code[{}]查询状态为已使用，使用时间[{}]", userId, code, orderCode.getUserTime());
        return Responses.ok(new ArrayList<UpdateCouponCodeResponse>());
    }

    @Override
    public RefundCodeResponse getRefundCode(RefundCodeRequest request) {
        int userId = contextHolder.getAuthUserInfo().getUserId();
        Locale locale = contextHolder.getLocale();
        String language = locale.getLanguage();
        String codeId = request.getCodeId();
        log.info("refundCode请求的codeId={}", codeId);
        // 查询券信息
        FookPlatformOrdercode orderCode = fookPlatformOrdercodeDao
                .selectOne(new LambdaQueryWrapper<FookPlatformOrdercode>()
                        .eq(FookPlatformOrdercode::getId, codeId).eq(FookPlatformOrdercode::getUserid, userId));

        if (orderCode == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "not orderCode data");
        }
        // 查询可退券数量
        int refundNumber = fookPlatformOrdercodeDao.selectCount(new LambdaQueryWrapper<FookPlatformOrdercode>()
                .eq(FookPlatformOrdercode::getUserid, userId)
                .eq(FookPlatformOrdercode::getOrderinfoId, orderCode.getOrderinfoId())
                .eq(FookPlatformOrdercode::getStatus, 1).eq(FookPlatformOrdercode::getRefundStatus, 1)
                .isNull(FookPlatformOrdercode::getRefundid));
        // 查询订单信息
        FookPlatformOrderinfo orderInfo = fookPlatformOrderinfoDao
                .selectOne(new LambdaQueryWrapper<FookPlatformOrderinfo>()
                        .eq(FookPlatformOrderinfo::getId, orderCode.getOrderinfoId()));
        if (orderInfo == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "not orderInfo data");
        }
        // 查询产品信息
        FookBusinessProduct product = fookBusinessProductDao.selectOne(new LambdaQueryWrapper<FookBusinessProduct>()
                .eq(FookBusinessProduct::getId, orderInfo.getProdcutid()));
        if (product == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "not product data");
        }
        // 查询产品翻译信息
        FookBusinessProductTranslations translations = fookBusinessProductTranslationsDao
                .selectOne(new LambdaQueryWrapper<FookBusinessProductTranslations>()
                        .eq(FookBusinessProductTranslations::getBusinessProductId, product.getId())
                        .eq(FookBusinessProductTranslations::getLocale, language)
                        .last("limit 1"));
        // 查询门店信息
        Integer storeId = null;
        String storeName = "";
        List<FookStores> fookStores = fookStoresDao.selectList(new LambdaQueryWrapper<FookStores>()
                .eq(FookStores::getBusinessId, product.getBusinessid()).eq(FookStores::getEnable, 1));
        if (fookStores != null && fookStores.size() > 0) {
            storeId = fookStores.get(0).getId();
            storeName = fookStores.get(0).getName();
        }
        // 组装响应信息
        RefundCodeResponse resp = new RefundCodeResponse();
        resp.setId(orderInfo.getId());
        resp.setRefundNumber(refundNumber);
        resp.setOrderInfoId(orderCode.getOrderinfoId());
        // resp.setApportionBillAmount(orderCode.getApportionBillAmount());
        // resp.setType(orderInfo.getType());
        // resp.setRetailPrice(orderInfo.getRetailPrice());
        resp.setPoint(orderCode.getApportionMpayintegral() == null ? 0 : orderCode.getApportionMpayintegral());
        resp.setPreferential(orderCode.getApportionBillFinalAmount());
        resp.setStoreId(storeId + "");
        resp.setStoreName(storeName);
        String titleSnapshots = orderInfo.getTitleSnapshots();
        if (translations != null && StringUtils.isNotEmpty(translations.getTTitle())) {
            titleSnapshots = translations.getTTitle();
        }
        resp.setTitleSnapshots(titleSnapshots);
        resp.setImageSnapshots(OssUtil.getProductImg(orderInfo.getImageSnapshots()));
        FookBusiness business = this.fookBusinessDao.selectByPrimaryKey(product.getBusinessid());
        if (null != business) {
            resp.setBusinessName(business.getName());
        }
        resp.setProductId(product.getId());
        List<FookStores> applyStores = this.fookStoresDao.getApplyStores(language, product.getId(),
                IsApplyStoresStatus.YES.ordinal());
        List<RefundCodeResponse.StoresItem> storeItems = Lists.newArrayList();
        resp.setStores(storeItems);
        if (CollectionUtils.isNotEmpty(applyStores)) {
            for (FookStores store : applyStores) {
                RefundCodeResponse.StoresItem storeItem = new RefundCodeResponse.StoresItem();
                storeItem.setId(store.getId());
                storeItem.setName(store.getName());
                storeItems.add(storeItem);
            }
        }
        // RefundCodeResponse.FookBusinessProduct productResp =
        // BusinessProductMapping.INSTANCE.getFookBusinessProduct(product);
        // 国际化处理
        // String title = productResp.getTitle();
        // String tnc = productResp.getTnc();
        // String receiveMethod = productResp.getReceiveMethod();
        // if (McoinMall.LANG_EN.equals(language) && translations != null) {
        // if (StringUtils.isNotEmpty(translations.getTTitle())) {
        // title = translations.getTTitle();
        // }
        // if (StringUtils.isNotEmpty(translations.getTTnc())) {
        // tnc = translations.getTTnc();
        // }
        // if (StringUtils.isNotEmpty(translations.getTReceiveMethod())) {
        // receiveMethod = translations.getTReceiveMethod();
        // }
        // }
        // productResp.setTitle(title);
        // productResp.setTnc(tnc);
        // productResp.setReceiveMethod(receiveMethod);
        // 额外字段
        // String typeName = "";
        // switch (product.getType()) {
        // case 1:
        // typeName = messageSource.getMessage("message.category.pickpocket_welfare",
        // null, locale);
        // break;
        // case 2:
        // typeName = messageSource.getMessage("message.category.new_welfare", null,
        // locale);
        // break;
        // case 3:
        // typeName = messageSource.getMessage("message.category.cash_welfare", null,
        // locale);
        // break;
        // case 4:
        // typeName = messageSource.getMessage("message.category.one_welfare", null,
        // locale);
        // break;
        // case 5:
        // typeName = messageSource.getMessage("message.category.integral_welfare",
        // null, locale);
        // break;
        // case 6:
        // typeName = messageSource.getMessage("message.category.m_welfare", null,
        // locale);
        // break;
        // }
        // productResp.setTypeName(typeName);
        // 以下两字段（M幣選項和排序）跟php开发确定为暂不使用字段，故不做原逻辑转义，直接返回。min直接返回null
        // String momecoinsOption = product.getMomecoinsOption();
        //// productResp.setMpayOption(momecoinsOption);
        // String sort = "";
        // if(StringUtils.isNotEmpty(momecoinsOption)) {
        // if(momecoinsOption.lastIndexOf(",")!=-1){
        // sort = momecoinsOption.substring(momecoinsOption.lastIndexOf(","),
        // momecoinsOption.length());
        // }else {
        // sort = momecoinsOption;
        // }
        // }
        // productResp.setSort(sort);
        // productResp.setMin(new Min());
        // RefundCodeResponse.FookBusinessProductTranslations translationsResp =
        // BusinessProductMapping.INSTANCE.getProductTranslations(translations);
        // productResp.setTranslations(translationsResp);
        // resp.setBusinessProduct(productResp);
        return resp;
    }

    @Override
    public void updRefundHandling(RefundHandlingRequest request) {
        Locale locale = contextHolder.getLocale();
        UserInfo userInfo = contextHolder.getAuthUserInfo();
        FookPlatformOrderinfo orderinfo = fookPlatformOrderinfoDao.selectByPrimaryKey(request.getOrderInfoId());
        if (orderinfo == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.no_order", null, locale));
        }
        // 判断是否允许退款
        if (AllowRefund.NOT_ALLOW.ordinal() == orderinfo.getIsAllowRefund()) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.mb", null, locale));
        }
        Date time = new Date();
        if (time.getTime() > orderinfo.getVaildEndTime().getTime()) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.overdue_refund", null, locale));
        }
        FookPlatformOrder order = fookPlatformOrderDao.selectByPrimaryKey(orderinfo.getOrderid());
        if (order == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.nodata", null, locale));
        }
        // 校验订单转台
        if (PlatformOrderStatusEnum.PAID.getStatus() != order.getStatus()) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.mb", null, locale));
        }
        // 不允許重新申請退款
        FookPlatformOrderrefund orderrefund = fookPlatformOrderrefundDao.selectByPrimaryKey(orderinfo.getRefundid());
        if (orderrefund != null) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.refund_requested", null, locale));
        }
        int number = fookPlatformOrdercodeDao.selectCount(
                new LambdaQueryWrapper<FookPlatformOrdercode>()
                        .eq(FookPlatformOrdercode::getUserid, userInfo.getUserId())
                        .eq(FookPlatformOrdercode::getOrderinfoId, request.getOrderInfoId())
                        .eq(FookPlatformOrdercode::getStatus, 1)
                        .eq(FookPlatformOrdercode::getRefundStatus, 1));
        if (number <= 0) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.no_order", null, locale));
        }
        List<FookPlatformOrdercode> ordercodes = fookPlatformOrdercodeDao.selectList(
                new LambdaQueryWrapper<FookPlatformOrdercode>()
                        .eq(FookPlatformOrdercode::getUserid, userInfo.getUserId())
                        .eq(FookPlatformOrdercode::getOrderinfoId, request.getOrderInfoId())
                        .eq(FookPlatformOrdercode::getStatus, 1)
                        .eq(FookPlatformOrdercode::getRefundStatus, 1)
                        .last("limit " + number));
        if (ordercodes.isEmpty()) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.no_order", null, locale));
        }
        // 退款金額
        BigDecimal refundAmount = ordercodes.get(0).getApportionBillFinalAmount()
                .multiply(BigDecimal.valueOf(ordercodes.size()));
        if (refundAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.mb", null, locale));
        }
        String refund_resson_front = defaultIfBlank(request.getRefund_reason(), "我不想買了");// 前台退款原因
        String customer_remark = "";// 客服備註
        String user_remark = request.getUser_remark();

        BigDecimal apportion_bill_final_amount = ordercodes.stream()
                .map(FookPlatformOrdercode::getApportionBillFinalAmount)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);// 實際退款金額
        int mpayintegral = ordercodes.stream()
                .map(FookPlatformOrdercode::getApportionMpayintegral)
                .mapToInt(Integer::intValue).sum();// 澳門通積分

        int mpay_val = order.getPointRatio();
        BigDecimal refund_point_amount = MoneyUtil.divide(BigDecimal.valueOf(mpayintegral),
                BigDecimal.valueOf(mpay_val));// 澳門通積分金额

        // 退款表退回途徑處理
        int return_route = 0;
        switch (order.getPaymentType()) {
            // 訂單表-付款方式(0M幣,1支付寶支付,2微信支付,3信用卡支付,4MPay澳門錢包)
            // 退款表-退回途徑(0.M幣 1.MPay 2.信用卡 3.微信 4.支付寶)
            case 1:
                return_route = 4;
                break;
            case 2:
                return_route = 3;
                break;
            case 3:
                return_route = 2;
                break;
            case 4:
                return_route = 1;
                break;
            default:
                return_route = 0;
                break;
        }
        String refund_reason = "MPay系統自動退款";
        // 退款表
        orderrefund = new FookPlatformOrderrefund();
        orderrefund.setAreaid(1);
        orderrefund.setBusinessid(order.getSellerid());
        orderrefund.setUserid(order.getUserid());
        orderrefund.setOrderid(order.getId());
        orderrefund.setOrdercodeId(Joiner.on(",").join(ordercodes.stream().map(FookPlatformOrdercode::getId)
                .collect(Collectors.toList())));// 訂單核銷碼記錄Ids
        orderrefund.setRefundOrderno(atomicSeqService.getOrderNo());
        orderrefund.setRefundAmount(refundAmount);// 退款金額
        orderrefund.setCurrency(order.getCurrency());
        orderrefund.setApplicationTime(new Date());// 申請退款時間
        // 狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，
        // 6、已到賬，7、到賬失敗，8取消退款，9轉入人工退款，10人工退款成功）
        orderrefund.setStatus(1);
        // 支付平台處理狀態(0-审核中 1-處理 2-不成功 3-成功)
        orderrefund.setPlatformDealStatus(0);
        orderrefund.setRefundRessonFront(refund_resson_front);// 前台退款原因
        orderrefund.setCustomerRemark(customer_remark);// 客服備註
        orderrefund.setReturnRoute(return_route);// 退回途徑(0.M幣 1.MPay 2.信用卡 3.微信 4.支付寶)
        orderrefund.setRefundScore(refund_point_amount);// 退款返還積分
        orderrefund.setActualRefundAmount(apportion_bill_final_amount);// 實際退款金額
        orderrefund.setUserRemark(user_remark);
        orderrefund.setMpayintegral(Long.valueOf(mpayintegral));// 澳門通積分
        orderrefund.setRefundReason(refund_reason);// 退款原因

        int rtRefund = fookPlatformOrderrefundDao.insert(orderrefund);
        log.info("插入退款订单， rt: {}", rtRefund);

        if (orderrefund.getId() != null) {
            for (FookPlatformOrdercode ordercode : ordercodes) {
                FookPlatformOrdercode updOrderCode = new FookPlatformOrdercode();
                updOrderCode.setRefundStatus(2);
                updOrderCode.setRefundid(orderrefund.getId());
                int rtCode = fookPlatformOrdercodeDao.update(updOrderCode,
                        new LambdaQueryWrapper<FookPlatformOrdercode>()
                                .eq(FookPlatformOrdercode::getId, ordercode.getId()));
                log.info("更新ordercode refund_status = 2，rt: {}, ordercodeId: {}", rtCode, ordercode.getId());
                // 插入同步日志
                mPayChannelService.insertCouponSync(order.getId(), ordercode.getId(),
                        CouponSyncMessage.Operation.OrderCode, CouponSynStatus.FAILED, "");
            }

            FookPlatformOrderrefundRecord refundRecode = new FookPlatformOrderrefundRecord();
            refundRecode.setRefundid(orderrefund.getId());// 退款訂單Id
            // 舊狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，6、已到賬，7、到賬失敗，8取消退款）
            refundRecode.setOldStatus(1);
            // 新狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，6、已到賬，7、到賬失敗，8取消退款）
            refundRecode.setNewStatus(1);
            refundRecode.setOperationTime(new Date());// 操作時間
            refundRecode.setOperationType(3);
            refundRecode.setOperationId(userInfo.getUserId());
            fookPlatformOrderrefundRecordDao.insertSelective(refundRecode);
            log.info("更改訂單表的退款狀態");
            int totalCount = fookPlatformOrdercodeDao.selectCount(
                    new LambdaQueryWrapper<FookPlatformOrdercode>()
                            .eq(FookPlatformOrdercode::getOrderid, order.getId()));
            int refundCount = fookPlatformOrdercodeDao.selectCount(
                    new LambdaQueryWrapper<FookPlatformOrdercode>()
                            .eq(FookPlatformOrdercode::getOrderid, order.getId())
                            .isNotNull(FookPlatformOrdercode::getRefundid));
            int refund_status;
            if (totalCount == refundCount) {
                refund_status = 3;
            } else {
                refund_status = 2;
            }
            FookPlatformOrder updOrder = new FookPlatformOrder();
            updOrder.setId(order.getId());
            updOrder.setRefundStatus(refund_status);
            int rtUpdCode = fookPlatformOrderDao.updateByPrimaryKeySelective(updOrder);
            log.info("更新订单：{} refund_status = {} rt: {}", order.getId(), refund_status, rtUpdCode);
        }
    }

    @Override
    public int updOrderClose(Integer orderId) {
        FookPlatformOrder order = fookPlatformOrderDao.selectByPrimaryKey(orderId);
        return updOrderClose(order);
    }

    @Override
    public int updOrderClose(FookPlatformOrder order) {
        if (order == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "订单不存在");
        }
        Integer orderId = order.getId();
        if (PlatformOrderStatusEnum.UNPAID.getStatus() != order.getStatus()) {
            log.info("订单不可关单: {}", order);
            return 0;
        }
        // 根據未支付狀態更新為失效訂單
        FookPlatformOrder updOrder = new FookPlatformOrder();
        updOrder.setStatus(PlatformOrderStatusEnum.INVALID_ORDER.getStatus());
        int rt = fookPlatformOrderDao.update(updOrder, new LambdaQueryWrapper<FookPlatformOrder>()
                .eq(FookPlatformOrder::getId, orderId)
                .eq(FookPlatformOrder::getStatus, PlatformOrderStatusEnum.UNPAID.getStatus()));
        log.info("根據未支付狀態更新為失效訂單,rt:{}", rt);
        if (rt == 0) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "订单不可关单");
        }
        // 查询抢购记录
        FookProductSnappingRecord queryRecord = fookProductSnappingRecordDao.selectOne(
                new LambdaQueryWrapper<FookProductSnappingRecord>()
                        .eq(FookProductSnappingRecord::getOrderId, orderId)
                        .eq(FookProductSnappingRecord::getStatus, SnappingRecordStatus.VALID.getStatus())
                        .last("limit 1"));
        if (queryRecord != null) {
            log.info("訂單失效同時更新搶購紀錄: {}", queryRecord);
            FookProductSnappingRecord updRecord = new FookProductSnappingRecord();
            updRecord.setStatus(SnappingRecordStatus.INVALID.getStatus());
            fookProductSnappingRecordDao.update(updRecord, new LambdaUpdateWrapper<FookProductSnappingRecord>()
                    .eq(FookProductSnappingRecord::getOrderId, orderId)
                    .eq(FookProductSnappingRecord::getStatus, SnappingRecordStatus.VALID.getStatus()));
        }
        FookPlatformOrderinfo orderinfo = fookPlatformOrderinfoDao
                .selectOne(new LambdaQueryWrapper<FookPlatformOrderinfo>()
                        .eq(FookPlatformOrderinfo::getOrderid, orderId).last("limit 1"));
        FookBusinessProduct product = fookBusinessProductDao.selectByPrimaryKey(orderinfo.getProdcutid());
        int stock = countStock(orderinfo.getProdcutid());
        int totalcount = defaultIfNull(product.getStock(), 0) +
                defaultIfNull(product.getActualSales(), 0) +
                defaultIfNull(orderinfo.getNumber(), 0);
        if (stock >= totalcount) {

            int rtIncrStack = fookBusinessProductDao.updateUnlockStock(orderinfo.getProdcutid(), orderinfo.getNumber());
            log.info("增加库存，productId: {}, stack: {}, rt: {}", orderinfo.getProdcutid(), orderinfo.getNumber(),
                    rtIncrStack);
            FookBusinessProductstock productstock = new FookBusinessProductstock();
            productstock.setProductid(orderinfo.getProdcutid());
            productstock.setNumber(orderinfo.getNumber());
            // 理由Id(1新增初始化庫存,2加減庫存,3取消訂單補庫存,4刪除訂單補庫存,5下單扣庫存,6下單過程中異常補庫存,7取消訂單補庫存,8用戶更改訂單補庫存,9用戶更改訂單扣庫存,99失效訂單補庫存)
            productstock.setType(7);
            productstock.setOperationid(order.getUserid());
            productstock.setUserid(order.getUserid());
            productstock.setOrderid(orderinfo.getOrderid());
            productstock.setCreateTime(new Date());
            productstock.setStatus(0);
            fookBusinessProductstockDao.insert(productstock);

            FookProductStockLog stockLog = fookProductStockLogDao.selectFirstOrderById(orderinfo.getProdcutid());
            log.info("防止上批庫存購買的申請退款加回庫存而前端顯示有庫存實際搶不到: {}", stockLog);
            if (stockLog != null) {
                if (order.getCreateTime() != null && stockLog.getCreatedAt() != null) {
                    if (order.getCreateTime().compareTo(stockLog.getCreatedAt()) < 0) {
                        int nowStock = parseInt(stockLog.getNowStock()) + orderinfo.getNumber();
                        stockLog.setNowStock(nowStock + "");
                        int stk = parseInt(stockLog.getStock()) + orderinfo.getNumber();
                        stockLog.setStock(stk + "");
                        fookProductStockLogDao.updateByPrimaryKeySelective(stockLog);
                    }
                }
            }
        }
        return 1;
    }

    public int countStock(int productId) {
        List<FookProductStockLog> stockLogs = fookProductStockLogDao.selectList(
                new LambdaQueryWrapper<FookProductStockLog>()
                        .eq(FookProductStockLog::getProductId, productId)
                        .orderByAsc(FookProductStockLog::getId));
        int total = stockLogs.stream().filter(e -> e.getPe() == 2)
                .map(e -> parseInt(e.getNum())).mapToInt(Integer::intValue).sum();
        total -= stockLogs.stream().filter(e -> e.getPe() == 1)
                .map(e -> parseInt(e.getNum())).mapToInt(Integer::intValue).sum();

        if (CollectionUtils.isNotEmpty(stockLogs)) {
            FookProductStockLog fookProductStockLog = stockLogs.get(0);
            int nowStock = parseInt(fookProductStockLog.getNowStock());
            if (nowStock > 0) {
                total += nowStock;
            }
        }
        return total;
    }

    @Override
    public int updOrderStatus(Integer orderId) {
        FookPlatformOrder order = fookPlatformOrderDao.selectByPrimaryKey(orderId);
        if (order == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "订单不存在");
        }
        if (PlatformOrderStatusEnum.UNPAID.getStatus() != order.getStatus()) {
            log.info("订单状态不是待支付，order：{}", order);
            return 0;
        }
        FookPayLog payLog = fookPayLogDao.selectOne(new LambdaQueryWrapper<FookPayLog>()
                .eq(FookPayLog::getOrderid, order.getId())
                .eq(FookPayLog::getUid, order.getUserid())
                .eq(FookPayLog::getOparemtion, "pay")
                .eq(FookPayLog::getType, "macaupay")
                .orderByDesc(FookPayLog::getId)
                .last("limit 1"));
        if (payLog == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "支付日志不存在");
        }
        String statusResp = mPayChannelService.queryPay(payLog.getUuid(), "");
        JSONObject statusJson = JSON.parseObject(statusResp);
        if (statusJson != null & "0000".equals(statusJson.getString("respCode")) && statusJson.containsKey("Data")) {
            JSONArray datas = statusJson.getJSONArray("Data");
            if (datas != null && datas.size() > 0) {
                JSONObject paramater = datas.getJSONObject(0);
                String tradeStatus = paramater.getString("trade_status");
                if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                    MPayPaySuccessVo successVo = new MPayPaySuccessVo();
                    successVo.setOutTradeNo(paramater.getString("out_trade_no"));
                    successVo.setTradeStatus(tradeStatus);
                    successVo.setTradeNo(paramater.getString("trade_no"));
                    successVo.setNotifyTime(paramater.getString("gmt_payment"));
                    successVo.setBuyerPayAmount(paramater.getString("buyer_pay_amount"));
                    successVo.setPointTradeNo(paramater.getString("point_trade_no"));

                    successVo.setRequest(statusResp);
                    mPayChannelService.updPaySuccess(successVo);
                } else if ("WAIT_BUYER_PAY".equals(tradeStatus) || "TRADE_DEAL".equals(tradeStatus)) {
                    throw new RetryException("等待用户支付，再次重试");
                } else if ("TRADE_CLOSED".equals(tradeStatus)) {
                    // 关闭订单
                    updOrderClose(order);
                }
            }
        }
        // 成功
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderRefundApprovalResponse approvalRefund(OrderRefundApprovalRequest request) {
        OrderRefundApprovalResponse result = new OrderRefundApprovalResponse();
        // 查詢檔條退款信息
        FookPlatformOrderrefund orderRefund = this.fookPlatformOrderrefundDao.selectByPrimaryKey(request.getId());
        // 校验不能为空
        if (orderRefund == null) {
            result.setCode(101);
            log.info("退款信息不存在,{}", request.getId());
            return result;
        }
        log.info("退款信息：{}", JSONUtil.toJsonStr(orderRefund));
        // 判斷是否已同意退款
        if (orderRefund.getStatus() == 2) {
            result.setCode(1005);
            return result;
        }
        // 判斷是否已拒絕退款
        if (orderRefund.getStatus() == 3) {
            result.setCode(1006);
            return result;
        }

        // 判斷是否已退款到賬
        if (orderRefund.getStatus() == 6) {
            result.setStatus(200);
            return result;
        }

        if (orderRefund.getStatus() == 1) {
            switch (request.getType()) {
                case 1:
                    // 同意退款
                    log.info("同意退款");
                    // 退款表 更新退款表狀態
                    FookPlatformOrderrefund updRefund = new FookPlatformOrderrefund();
                    updRefund.setStatus(2);
                    updRefund.setPlatformDealStatus(1);
                    int updResult = this.fookPlatformOrderrefundDao.update(updRefund,
                            new LambdaQueryWrapper<FookPlatformOrderrefund>()
                                    .eq(FookPlatformOrderrefund::getId, orderRefund.getId())
                                    .eq(FookPlatformOrderrefund::getStatus, 1));
                    if (updResult > 0) {
                        String outTradeNo = atomicSeqService.getOutTradeNo();
                        // 退款記錄表
                        FookPlatformOrderrefundRecord refundRecord = new FookPlatformOrderrefundRecord();
                        // 退款訂單Id
                        refundRecord.setRefundid(request.getId());
                        // 舊狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，6、已到賬，7、到賬失敗，8取消退款）
                        refundRecord.setOldStatus(orderRefund.getStatus());
                        // 新狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，6、已到賬，7、到賬失敗，8取消退款）
                        refundRecord.setNewStatus(2);
                        refundRecord.setOperationTime(new Date());// 操作時間
                        refundRecord.setOperationType(request.getOperationType());
                        refundRecord.setOperationId(request.getOperationId());
                        refundRecord.setOutRequestNo(outTradeNo);
                        fookPlatformOrderrefundRecordDao.insertSelective(refundRecord);
                        // 更改訂單表的退款狀態
                        log.info("更改訂單表的退款狀態");
                        int totalCount = fookPlatformOrdercodeDao.selectCount(
                                new LambdaQueryWrapper<FookPlatformOrdercode>()
                                        .eq(FookPlatformOrdercode::getOrderid, orderRefund.getOrderid()));
                        int refundCount = fookPlatformOrdercodeDao.selectCount(
                                new LambdaQueryWrapper<FookPlatformOrdercode>()
                                        .eq(FookPlatformOrdercode::getOrderid, orderRefund.getOrderid())
                                        .isNotNull(FookPlatformOrdercode::getRefundid));
                        int refund_status;
                        if (totalCount == refundCount) {
                            // 子訂單全退款
                            refund_status = 3;
                        } else {
                            // 部分退款
                            refund_status = 2;
                        }
                        FookPlatformOrder updOrder = new FookPlatformOrder();
                        updOrder.setId(orderRefund.getOrderid());
                        updOrder.setRefundStatus(refund_status);
                        int rtUpdCode = fookPlatformOrderDao.updateByPrimaryKeySelective(updOrder);
                        log.info("更新订单：{} refund_status = {} rt: {}",
                                orderRefund.getOrderid(), refund_status, rtUpdCode);
                        // return $this->MpayRefunds($id);RefundMessage retryMessage = new
                        RefundMessage retryMessage = new RefundMessage();
                        retryMessage.setRefundId(orderRefund.getId());
                        retryMessage.setOutRequestNo(outTradeNo);
                        retryMessage.setRefundScene(request.getRefundScene());
                        if (RefundSceneEnum.APPROVAL_REFUND.getCode().equals(request.getRefundScene())) {
                            try {
                                int rd = mPayChannelService.updRefund(retryMessage);
                                if (rd < 0) {
                                    log.warn("执行退款失败 {}", orderRefund.getId());
                                    result.setCode(RefundResponseCodeEnum.REFUND_FAILED.getCode());
                                    return result;
                                }
                            } catch (Exception e) {
                                log.warn("执行退款失败 {}", orderRefund.getId());
                                log.error("执行退款异常", e);
                                result.setCode(RefundResponseCodeEnum.REFUND_FAILED.getCode());
                                return result;
                            }

                        } else {
                            String gradientRetryInterval = ConfigUtils.getProperty("mcoin.gradient.interval." + MqLocalResourceType.REFUND.getType(), "1m,10m,1h,4h,12h,24h,48h");
                            String[] intervals = gradientRetryInterval.split(",");
                            FookMqLocal record = new FookMqLocal();
                            record.setResourceId(Convert.toStr(orderRefund.getId()));
                            record.setResourceType(MqLocalResourceType.REFUND.getType());
                            record.setTryCount(Numbers.ZERO.getIntValue());
                            record.setNextRetry(DateUtil.offsetMillisecond(new Date(), TimeIntervalUtil.parseIntervalToMillis(intervals[Numbers.ZERO.getIntValue()])));
                            record.setTemplateName("refundTemplate");
                            record.setMessageBody(JSON.toJSONString(retryMessage));
                            mqLocalService.saveMqLocal(record);
                            retryMessage.setMqLocalId(record.getId());
                            applicationContext.publishEvent(retryMessage);
                        }
                    } else {
                        log.info("退款记录状态已变，可能被其他流程抢先退了，不执行退款 {}", orderRefund.getId());
                    }
                    break;
                case 2:
                    // 不同意退款
                    log.info("不同意退款");
                    // 退款表 更新退款表狀態
                    FookPlatformOrderrefund refuseRefund = new FookPlatformOrderrefund();
                    refuseRefund.setId(orderRefund.getId());
                    // 不同意退款
                    refuseRefund.setStatus(3);
                    // 支付平台處理狀態(0-审核中 1-處理 2-不成功 3-成功)
                    refuseRefund.setPlatformDealStatus(2);
                    this.fookPlatformOrderrefundDao.updateByPrimaryKeySelective(refuseRefund);
                    // 退款記錄表
                    FookPlatformOrderrefundRecord refuseRecode = new FookPlatformOrderrefundRecord();
                    // 退款訂單Id
                    refuseRecode.setRefundid(request.getId());
                    // 舊狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，6、已到賬，7、到賬失敗，8取消退款）
                    refuseRecode.setOldStatus(orderRefund.getStatus());
                    // 新狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，6、已到賬，7、到賬失敗，8取消退款）
                    refuseRecode.setNewStatus(3);
                    refuseRecode.setOperationTime(new Date());// 操作時間
                    refuseRecode.setOperationType(request.getOperationType());
                    refuseRecode.setOperationId(request.getOperationId());
                    fookPlatformOrderrefundRecordDao.insertSelective(refuseRecode);
                    // 訂單核銷碼記錄表
                    this.fookPlatformOrdercodeDao.updateStatusByRefund(orderRefund.getId(), 1);
                    // 更改訂單表的退款狀態
                    log.info("更改訂單表的退款狀態");
                    int refuseTotalCount = fookPlatformOrdercodeDao.selectCount(
                            new LambdaQueryWrapper<FookPlatformOrdercode>()
                                    .eq(FookPlatformOrdercode::getOrderid, orderRefund.getOrderid()));
                    int refuseRefundCount = fookPlatformOrdercodeDao.selectCount(
                            new LambdaQueryWrapper<FookPlatformOrdercode>()
                                    .eq(FookPlatformOrdercode::getOrderid, orderRefund.getOrderid())
                                    .eq(FookPlatformOrdercode::getStatus, 3)
                                    .eq(FookPlatformOrdercode::getRefundStatus, 3));
                    int refuse_refund_status;
                    if (refuseTotalCount == refuseRefundCount) {
                        // 子訂單全退款
                        refuse_refund_status = 3;
                    } else if (refuseRefundCount == 0) {
                        // 未退款
                        refuse_refund_status = 0;
                    } else {
                        // 部分退款
                        refuse_refund_status = 2;
                    }
                    FookPlatformOrder updRefuseOrder = new FookPlatformOrder();
                    updRefuseOrder.setId(orderRefund.getOrderid());
                    updRefuseOrder.setRefundStatus(refuse_refund_status);
                    FookPlatformOrder order = fookPlatformOrderDao.selectById(orderRefund.getOrderid());
                    String couponResponse = mPayChannelService.queryCoupon(orderRefund.getUserid(), order.getOrderNo());
                    if (StringUtils.isNotBlank(couponResponse)) {
                        JSONObject couponJson = JSON.parseObject(couponResponse);
                        if (MPayResponseCodeEnum.SUCCESS.getCode().equals(couponJson.getString("code"))) {
                            JSONObject couponJsonData = couponJson.getJSONObject("Data");
                            if (couponJsonData != null && !"01".equals(couponJsonData.getString("status"))) {
                                updRefuseOrder.setMpayCouponsStatus(1);
                            }
                        }
                    }
                    int rtRefuseUpdCode = fookPlatformOrderDao.updateByPrimaryKeySelective(updRefuseOrder);
                    log.info("更新订单：{} refund_status = {} rt: {}",
                            orderRefund.getOrderid(), refuse_refund_status, rtRefuseUpdCode);
                    List<FookPlatformOrdercode> refundOrderCodes = fookPlatformOrdercodeDao.selectList(
                            new LambdaQueryWrapper<FookPlatformOrdercode>()
                                    .eq(FookPlatformOrdercode::getOrderid, orderRefund.getOrderid())
                                    .eq(FookPlatformOrdercode::getRefundid, orderRefund.getId()));
                    for (FookPlatformOrdercode ordercode : refundOrderCodes) {
                        mPayChannelService.insertCouponSync(orderRefund.getOrderid(), ordercode.getId(),
                                CouponSyncMessage.Operation.OrderCode, CouponSynStatus.FAILED, "");
                    }
                    break;
                default:
                    break;
            }

        }

        result.setStatus(Response.Code.SUCCESS.get());
        return result;
    }


    @Override
    public void sendRefundMsg(FookPlatformOrderrefundRecord record) {
        RefundMessage retryMessage = new RefundMessage();
        retryMessage.setRefundId(record.getRefundid());
        retryMessage.setOutRequestNo(record.getOutRequestNo());
        Message msg = new Message(JSON.toJSONBytes(retryMessage));
        refundTemplate.convertAndSend(msg);
        log.info("重试退款订单发送消息：（{}ms）消息：{}", 0, new String(msg.getBody()));

    }

    @Override
    public Response<List<UpdateCouponCodeResponse>> queryCodeStatus(CodeStatusRequest request) {
        int userId = contextHolder.getAuthUserInfo().getUserId();
        Integer codeId = request.getId();
        FookPlatformOrdercode orderCode = fookPlatformOrdercodeDao
                .selectOne(new LambdaQueryWrapper<FookPlatformOrdercode>()
                        .eq(FookPlatformOrdercode::getId, codeId)
                        .eq(FookPlatformOrdercode::getUserid, userId)
                        .last("limit 1"));

        // 数据为空或不是已使用状态返回错误码0
        if (orderCode == null || orderCode.getStatus() != 2) {
            return Responses.ok(new ArrayList<UpdateCouponCodeResponse>(), Response.Status.FAILED.ordinal(), "error");
        }
        log.info("用户userId[{}]核销码code id[{}]查询状态为已使用，使用时间[{}]", userId, codeId, orderCode.getUserTime());
        return Responses.ok(new ArrayList<UpdateCouponCodeResponse>());
    }
}
