package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel(description = "查询券码状态-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CodeStatusRequest {
    @NotNull
    @ApiModelProperty(value = "券码id", required = true)
    private Integer id;

}
