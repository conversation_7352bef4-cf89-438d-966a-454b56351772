---
description: 
globs: 
alwaysApply: true
---
# Coding Standards

## Naming Conventions

### Classes
- **Controllers**: End with `Controller` 
- **Services**: End with `Service` for interfaces, `ServiceImpl` for implementations
- **Repositories**: End with `Repository` for interfaces, `RepositoryImpl` for implementations
- **DAOs**: End with `DAO` for interfaces, `DAOImpl` for implementations
- **Mappers**: End with `Mapper`
- **Domain Objects**: No suffix
- **Data Objects**: End with `DO`
- **DTOs**: End with `Req` for requests, `Rsp` for responses
- **Enums**: End with `Enum`

### Packages
- **Base Package**: `com.agtech.pointprod.service`
- **Controllers**: `*.controller`
- **Services**: `*.service` and `*.service.impl`
- **Domain Models**: `*.domain.model`
- **DTOs**: `*.facade.dto.req` and `*.facade.dto.rsp`

## Code Quality Standards

### Dependency Injection
- **Use @Resource**: Always use `@Resource` annotation, not `@Autowired`
- **Import**: Use `import javax.annotation.Resource;`
- **Field Injection**: Apply `@Resource` to private fields

### Lombok Usage
- **Required**: Use `@Getter` and `@Setter` for all data classes
- **Optional**: Use `@ToString` for DTOs for better debugging
- **Avoid**: Explicit default constructors - Lombok handles this

### Constants and Enums
- **No Magic Numbers**: Extract all numeric values and string literals to constants or enums
- **Boolean Fields**: Use `YesOrNoEnum` for boolean-like fields (e.g., `isDeleted`)
- **Numeric Values**: Use `NumbersEnum` for general numeric operations
- **Status/Type Fields**: Create dedicated enums (e.g., `OrderStatusEnum`)

### Data Object Field Documentation
- **Required**: All fields in DO classes must have JavaDoc comments with:
  - Column name, Type, Default value (if applicable), Business description

### Time Field Standards
- **Universal Type**: Use `java.util.Date` for all time-related fields
- **Utility**: Use `ZonedDateUtil` for time operations

### Domain Model Standards
- **No Database IDs**: Domain models should not contain database-specific primary keys
- **Business Keys**: Use business identifiers (e.g., `notificationId` instead of `id`)

## Technical Implementation Standards

### Data Access Layer
- **DAO Interface**: Extend `IService<DO>` from MyBatis-Plus
- **DAO Implementation**: Extend `ServiceImpl<Mapper, DO>`
- **Query Implementation**: Use Lambda expressions with `LambdaQueryWrapper`
- **Single Table Rule**: DAOs must only access one table - no JOINs

### Layer Responsibilities
1. **Controllers**: HTTP requests, validation, response formatting
2. **Application Services**: Orchestrate business operations
3. **Domain Services**: Core business logic, rules
4. **Repositories**: Data access abstraction
5. **Gateway Layer**: Multi-table aggregation (in memory)
6. **DAOs**: Single-table operations

### Gateway Pattern
- **Purpose**: Handle complex business queries requiring data from multiple tables
- **Memory Aggregation**: Query tables separately, aggregate in memory
- **Domain Objects Only**: Use domain objects, never DO objects

### Error Handling
- **Specific Error Codes**: Create dedicated codes in `PointProdBizErrorCodeEnum`
- **Internationalization**: Support multiple languages in error messages
- **Throwing**: Use `AssertUtil`
- **Throwing message language**: Use zh_MO and english
