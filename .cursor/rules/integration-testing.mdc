---
description: 
globs: 
alwaysApply: true
---
# Integration Testing Rules

## Core Testing Principles

1. **Base Class Inheritance**
   - All test classes must extend `BaseUnitTest`
   - Do not create custom test base classes

2. **Code Understanding Process**
   - First review controller, service, and repository implementations
   - Study the domain models and database schema
   - Understand the complete request/response flow
   - Examine related database tables in `schema.sql` 
   - Map entity relationships before writing tests

3. **Test Quality Standards**
   - Aim for >85% code coverage
   - Test positive scenarios thoroughly
   - Include edge cases and error conditions
   - Verify actual database state changes
   - Test input validation and error handling
   - Check response objects for expected values

4. **Data Validation Approach**
   - Use direct DAO queries to verify database state
   - Compare service responses with database state
   - Use JSON assertions for complex objects
   - Validate all non-trivial fields in response objects

5. **Test Structure**
   ```java
   @Slf4j
   public class ServiceNameTest extends BaseUnitTest {
       
       @Autowired
       private ServiceController serviceController;
       
       @BeforeEach
       public void initTestData() {
           source("sql/service_test.sql");
       }
       
       @AfterEach
       public void cleanUp() {
           RequestContextHolder.resetRequestAttributes();
       }
       
       // Helper methods for test setup
       private void setUserContext(String userId) {
           MockHttpServletRequest request = new MockHttpServletRequest();
           request.addHeader("MPayCustId", userId);
           // Add other required headers
           RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
       }
       
       @Test
       public void testMethodName_WithScenarioDescription() {
           // Setup
           setUserContext("userId");
           
           // Execute
           GenericResult<Response> result = serviceController.methodName(request);
           
           // Log response for debugging
           log.info("result={}", JSONObject.toJSONString(result));
           
           // Verify
           Assert.assertTrue(result.isSuccess());
           Assert.assertNotNull(result.getValue());
           // Additional assertions
       }
   }
   ```

6. **Mock Usage Guidelines**
   - Avoid @Mock/@InjectMocks in test classes
   - Define mockable components in BaseUnitTest using @MockBean
   - Configure mock behaviors in test methods or setup
   - Verify critical mock interactions

7. **Logging Practices**
   - Use SLF4J logger with @Slf4j annotation
   - Log test data and responses at appropriate levels
   - Avoid System.out.println completely
   - Include meaningful log context

8. **Test Execution**
   - Always run tests with `mvn test` before committing
   - Fix failing tests before adding new ones
   - Document any environment requirements in test class
