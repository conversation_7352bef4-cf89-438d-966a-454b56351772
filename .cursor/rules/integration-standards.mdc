---
description: 
globs: 
alwaysApply: true
---
# Integration Standards

## Directory Structure
- **Service-Based**: Each external service gets its own subdirectory under `*.infrastructure.integration.{serviceName}`
- **Subdirectories**: 
  - `translator/`: Feign clients and service interfaces
  - `translator/impl/`: Service implementations
  - `dto/`: Service-specific DTOs
  - `adapter/`: Data transformation adapters
  - `acl/`: Anti-Corruption Layer implementations

## Naming Conventions
- **Feign Clients**: End with `Client` (e.g., `MPayClient`)
- **Translator Services**: End with `TranslatorService`/`TranslatorServiceImpl`
- **Integration DTOs**: Use descriptive names with `Request`/`Response` suffixes

## Feign Client Standards
- **@FeignClient**: Use with `name` and `url` parameters
- **Configuration**: Use placeholder syntax for URLs (e.g., `${mcoin.mall.URL:}`)
- **Interface Only**: Feign clients must be interfaces, not classes

## Translator Service Pattern
- **Interface-Implementation**: Create interfaces in `translator/` with implementations in `translator/impl/`
- **Template Usage**: Always use `ClientTemplate.execute()` for external service calls
- **Callback**: Implement `BaseWrapperClientCallback` for error handling
- **Return Types**: Return `GenericResult<T>` for all operations

## Anti-Corruption Layer (ACL) Pattern
- **Purpose**: Protect domain layer from external service changes
- **Translation**: Convert between external and internal data formats
- **Interface Stability**: Define stable interfaces for domain layer

## Error Handling
- **Template-Based**: Let `ClientTemplate` handle exceptions (network, timeout)
- **Business Validation**: Validate responses in `isSuccess()` method
- **Logging**: Use digest logs for monitoring

## Best Practices
1. **Layered Architecture**: Clear separation between domain logic and external service integration
2. **Template Pattern**: Use `ClientTemplate` for consistent error handling
3. **Domain Protection**: Protect domain models from external changes
4. **Configuration**: Use Spring properties for flexible service configuration
