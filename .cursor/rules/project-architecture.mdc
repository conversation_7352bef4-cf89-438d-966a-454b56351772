---
description: 
globs: 
alwaysApply: true
---
# Project Architecture

## Overview
Multi-module Maven Spring Boot project following Domain-Driven Design (DDD) principles with clean architecture.

## Module Structure

### 1. pointprod-service-app (Application Layer)
- **Purpose**: Entry point, controllers, application services, configuration
- **Key Components**:
  - **Controllers**: HTTP endpoints in `controller/`
  - **Application Services**: Orchestrate business operations in `service/`
  - **Processing Steps**: Business process steps in `processing/step/`
  - **Context Objects**: Data exchange between steps in `processing/`
  - **Configuration**: Spring configuration in `config/`

### 2. pointprod-service-domain (Domain Layer)
- **Purpose**: Core business logic, domain models, business rules
- **Key Components**:
  - **Models**: Business entities in `model/`
  - **Gateways**: Domain interfaces in `gateway/`
  - **Services**: Domain services in `service/`
  - **Enums**: Business enums in `common/enums/`

### 3. pointprod-service-facade (Facade Layer)
- **Purpose**: API contracts, DTOs for external communication
- **Key Components**:
  - **APIs**: Interface definitions in `api/`
  - **DTOs**: Request/Response objects in `dto/req/` and `dto/rsp/`

### 4. pointprod-service-infrastructure (Infrastructure Layer)
- **Purpose**: Data access, external integrations
- **Key Components**:
  - **DAOs**: Data access in `dal/daointerface/`
  - **Data Objects**: Database entities in `dal/daoobject/`
  - **Mappers**: MyBatis mappers in `dal/mapper/`
  - **Repositories**: Domain repository implementations in `repository/`
  - **Gateway Implementations**: In `gateway/impl/`
  - **Integration**: External services in `integration/`

### 5. pointprod-service-test (Test Layer)
- **Purpose**: Unit tests, integration tests
- **Base Test**: `BaseUnitTest.java` for test setup

## Architecture Principles
1. **Dependency Direction**: App → Domain ← Infrastructure, Facade → Domain
2. **Clean Architecture**: Each layer has specific responsibilities
3. **Domain-Driven Design**: Business logic isolated in domain layer
4. **Repository Pattern**: Used for data access abstraction
5. **Gateway Pattern**: Used for external service abstraction
