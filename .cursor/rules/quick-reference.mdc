---
description: 
globs: 
alwaysApply: true
---
# Quick Reference Guide

## Key Entry Points
- **Main Class**: [PointProdServiceAppApplication.java](mdc:pointprod-service-app/src/main/java/com/agtech/pointprod/service/app/PointProdServiceAppApplication.java)
- **Configuration**: [bootstrap.yaml](mdc:pointprod-service-app/src/main/resources/bootstrap.yaml)

## Sample Controllers
- [ContractController.java](mdc:pointprod-service-app/src/main/java/com/agtech/pointprod/service/app/controller/ContractController.java)
- [GrayController.java](mdc:pointprod-service-app/src/main/java/com/agtech/pointprod/service/app/controller/GrayController.java)

## Business Domain Examples
- **Contract Domain**: [Contract.java](mdc:pointprod-service-domain/src/main/java/com/agtech/pointprod/service/domain/model/Contract.java)
- **Repository**: [ContractRepository.java](mdc:pointprod-service-infrastructure/src/main/java/com/agtech/pointprod/service/infrastructure/repository/ContractRepository.java)
- **DTOs**: [QueryContractReq.java](mdc:pointprod-service-facade/src/main/java/com/agtech/pointprod/service/facade/dto/req/QueryContractReq.java)

## Key Configuration
- [MybatisPlusConfig.java](mdc:pointprod-service-app/src/main/java/com/agtech/pointprod/service/app/config/MybatisPlusConfig.java)
- [XxlJobConfig.java](mdc:pointprod-service-app/src/main/java/com/agtech/pointprod/service/app/config/XxlJobConfig.java)

## Error Handling
- [GlobalExceptionHandler.java](mdc:pointprod-service-app/src/main/java/com/agtech/pointprod/service/app/aop/GlobalExceptionHandler.java)
- [PointProdBizException.java](mdc:pointprod-service-domain/src/main/java/com/agtech/pointprod/service/domain/exception/PointProdBizException.java)

## Utilities
- [AssertUtil.java](mdc:pointprod-service-domain/src/main/java/com/agtech/pointprod/service/domain/util/AssertUtil.java)
- [ResultUtil.java](mdc:pointprod-service-domain/src/main/java/com/agtech/pointprod/service/domain/util/ResultUtil.java)

## Testing
- [BaseUnitTest.java](mdc:pointprod-service-test/src/test/java/com/agtech/pointprod/service/test/BaseUnitTest.java)
- [bootstrap-test.yaml](mdc:pointprod-service-test/src/test/resources/bootstrap-test.yaml)

## Maven Modules
- **App Module**: [pointprod-service-app/pom.xml](mdc:pointprod-service-app/pom.xml)
- **Domain Module**: [pointprod-service-domain/pom.xml](mdc:pointprod-service-domain/pom.xml)
- **Facade Module**: [pointprod-service-facade/pom.xml](mdc:pointprod-service-facade/pom.xml)
- **Infrastructure Module**: [pointprod-service-infrastructure/pom.xml](mdc:pointprod-service-infrastructure/pom.xml)
- **Test Module**: [pointprod-service-test/pom.xml](mdc:pointprod-service-test/pom.xml)
