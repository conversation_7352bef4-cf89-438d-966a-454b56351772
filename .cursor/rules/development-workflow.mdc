---
description: 
globs: 
alwaysApply: true
---
# Development Workflow

## Adding New Features

### Adding a New Entity
When adding a new business entity (e.g., `Product`):

1. **Domain Layer**:
   - Create model in `model/` (e.g., `Product.java`)
   - Create gateway interface in `gateway/` (e.g., `ProductGateway.java`)

2. **Infrastructure Layer**:
   - Create DO in `dal/daoobject/` (e.g., `ProductDO.java`)
   - Create DAO interface in `dal/daointerface/` (e.g., `ProductDAO.java`)
   - Create MyBatis mapper in `dal/mapper/` (e.g., `ProductMapper.java`)
   - Create repository in `repository/` (e.g., `ProductRepository.java`)
   - Implement gateway in `gateway/impl/` (e.g., `ProductGatewayImpl.java`)

3. **Facade Layer**:
   - Create DTOs in `dto/req/` and `dto/rsp/`
   - Create facade interface in `api/` if needed

4. **Application Layer**:
   - Create application service in `service/` (e.g., `ProductService.java`)
   - Create controller in `controller/` (e.g., `ProductController.java`)

### Using Processing Steps Pattern
For complex business operations:
- Create context objects in `processing/` (e.g., `ProductContext.java`)
- Add steps in `processing/step/` following the step pattern

## Testing Strategy

### Unit Tests
- Use `BaseUnitTest.java` as the base test class
- Test data: `data.sql` and `schema.sql`
- Naming: Test classes end with `Test`, methods use `should[ExpectedBehavior]When[StateUnderTest]`

## Configuration Management
- Spring Config: Classes in `config/` directory
- Properties: `bootstrap.yaml` (main), `bootstrap-test.yaml` (test)

## Common Patterns

### Template Pattern
- Use `PointProdServiceTemplate.java` for common operations
- Implement `PointProdServiceCallback.java` for custom logic

### Logging
- Use digest logging with `ServiceDigestLogWrapHelper.java`

### Error Handling
- Use `AssertUtil` for validation
- Throw `PointProdBizException` with specific error codes
- Handle in `GlobalExceptionHandler`
