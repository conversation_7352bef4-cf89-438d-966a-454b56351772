# Nacos配置示例 - 重试策略配置
# 配置文件名: pointprod-retry-strategy.yml
# Group: DEFAULT_GROUP
# Data ID: pointprod-retry-strategy

task-retry:
  # 重试批处理大小配置
  batch-size: 100                         # 每批处理的失败任务数量，默认100

  strategy:
    # 默认重试策略配置
    default-config:
      max-retry-count: 3                    # 默认最大重试次数
      initial-delay-seconds: 60              # 默认初始重试间隔（秒）
      max-delay-seconds: 3600                # 默认最大重试间隔（秒）
      backoff-multiplier: 2.0                # 默认重试间隔倍数（指数退避）
      enable-exponential-backoff: true       # 默认启用指数退避

    # 针对不同任务类型的重试策略配置
    task-configs:
      # MQ订单状态变更消息重试策略
      MQ_ORDER_STATUS_CHANGED_MSG:
        max-retry-count: 5                   # 订单状态变更消息重试5次
        initial-delay-seconds: 30            # 初始延迟30秒
        max-delay-seconds: 1800              # 最大延迟30分钟
        backoff-multiplier: 2.0              # 指数退避倍数2.0
        enable-exponential-backoff: true     # 启用指数退避

      # MQ订单创建消息重试策略
      MQ_ORDER_CREATED_MSG:
        max-retry-count: 3                   # 订单创建消息重试3次
        initial-delay-seconds: 60            # 初始延迟60秒
        max-delay-seconds: 3600              # 最大延迟1小时
        backoff-multiplier: 2.0              # 指数退避倍数2.0
        enable-exponential-backoff: true     # 启用指数退避

      # MQ订单支付超时消息重试策略
      MQ_ORDER_PAYMENT_TIMEOUT_MSG:
        max-retry-count: 3                   # 支付超时消息重试3次
        initial-delay-seconds: 120           # 初始延迟2分钟
        max-delay-seconds: 7200              # 最大延迟2小时
        backoff-multiplier: 3.0              # 指数退避倍数3.0
        enable-exponential-backoff: true     # 启用指数退避
        

# 配置说明：
# 1. max-retry-count: 最大重试次数，达到此次数后任务将被标记为失败
# 2. initial-delay-seconds: 初始重试延迟时间（秒）
# 3. max-delay-seconds: 最大重试延迟时间（秒），防止延迟时间过长
# 4. backoff-multiplier: 指数退避倍数，每次重试延迟时间 = 上次延迟时间 * 倍数
# 5. enable-exponential-backoff: 是否启用指数退避，false时使用固定延迟
#
# 重试延迟计算公式（启用指数退避时）：
# delay = min(initial_delay * (backoff_multiplier ^ retry_count), max_delay)
#
# 示例：MQ_ORDER_STATUS_CHANGED_MSG的重试时间序列
# 第1次重试: 30秒后
# 第2次重试: 60秒后 (30 * 2^1)
# 第3次重试: 120秒后 (30 * 2^2)
# 第4次重试: 240秒后 (30 * 2^3)
# 第5次重试: 480秒后 (30 * 2^4)
# 超过5次后标记为失败